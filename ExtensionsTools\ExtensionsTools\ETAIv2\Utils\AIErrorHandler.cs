using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using ET.ETAIv2.Interfaces;
using ET.ETAIv2.Exceptions;
using ET;

namespace ET.ETAIv2.Utils
{
    /// <summary>
    /// AI错误处理器
    /// </summary>
    public class AIErrorHandler : IAIErrorHandler
    {
        private readonly IAILogger _logger;

        public AIErrorHandler(IAILogger logger = null)
        {
            _logger = logger ?? new AILogger();
        }

        /// <summary>
        /// 执行带重试的操作
        /// </summary>
        public async Task<T> ExecuteWithRetryAsync<T>(
            Func<Task<T>> operation,
            int maxRetries = 3,
            TimeSpan delay = default,
            CancellationToken cancellationToken = default)
        {
            if (operation == null)
                throw new ArgumentNullException(nameof(operation));

            var exceptions = new List<Exception>();
            var actualDelay = delay == default ? TimeSpan.FromSeconds(1) : delay;

            for (int attempt = 0; attempt <= maxRetries; attempt++)
            {
                try
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    return await operation();
                }
                catch (OperationCanceledException)
                {
                    throw; // 不重试取消操作
                }
                catch (Exception ex)
                {
                    exceptions.Add(ex);
                    
                    if (attempt == maxRetries || !ShouldRetry(ex))
                    {
                        LogError(ex, $"操作失败，已重试 {attempt} 次");
                        
                        if (exceptions.Count == 1)
                            throw;
                        
                        throw new AggregateException($"操作在 {attempt + 1} 次尝试后失败", exceptions);
                    }

                    _logger?.LogWarning($"操作失败，第 {attempt + 1} 次重试: {ex.Message}");
                    
                    // 指数退避
                    var retryDelay = TimeSpan.FromMilliseconds(actualDelay.TotalMilliseconds * Math.Pow(2, attempt));
                    await Task.Delay(retryDelay, cancellationToken);
                }
            }

            // 这行代码理论上不会执行到
            throw new InvalidOperationException("重试逻辑错误");
        }

        /// <summary>
        /// 判断是否应该重试
        /// </summary>
        public bool ShouldRetry(Exception exception)
        {
            if (exception == null)
                return false;

            // 不重试的异常类型
            if (exception is OperationCanceledException ||
                exception is ArgumentException ||
                exception is ArgumentNullException ||
                exception is ConfigurationException ||
                exception is DataValidationException)
            {
                return false;
            }

            // 网络相关异常通常可以重试
            if (exception is NetworkException ||
                exception is SocketException ||
                exception is WebException ||
                exception is TaskCanceledException ||
                exception is TimeoutException)
            {
                return true;
            }

            // AI API异常根据状态码判断
            if (exception is AIAPIException apiEx)
            {
                if (apiEx.StatusCode.HasValue)
                {
                    var statusCode = apiEx.StatusCode.Value;
                    // 重试的HTTP状态码
                    return statusCode == 408 ||  // Request Timeout
                           statusCode == 429 ||  // Too Many Requests
                           statusCode == 500 ||  // Internal Server Error
                           statusCode == 502 ||  // Bad Gateway
                           statusCode == 503 ||  // Service Unavailable
                           statusCode == 504;    // Gateway Timeout
                }
                return true; // 没有状态码的API异常默认重试
            }

            // 文件处理异常根据具体情况判断
            if (exception is FileProcessingException fileEx)
            {
                // 文件不存在、格式不支持等不重试
                if (exception.Message.Contains("不存在") ||
                    exception.Message.Contains("不支持") ||
                    exception.Message.Contains("格式"))
                {
                    return false;
                }
                return true;
            }

            // Excel集成异常通常不重试
            if (exception is ExcelIntegrationException)
            {
                return false;
            }

            // 其他异常根据内部异常判断
            if (exception.InnerException != null)
            {
                return ShouldRetry(exception.InnerException);
            }

            // 默认不重试未知异常
            return false;
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        public void LogError(Exception exception, string context = null)
        {
            if (exception == null)
                return;

            try
            {
                var message = string.IsNullOrEmpty(context) 
                    ? $"发生错误: {exception.Message}"
                    : $"{context}: {exception.Message}";

                _logger?.LogError(message, exception);
                
                // 同时使用现有的日志系统
                ETLogManager.Error(exception);
            }
            catch
            {
                // 记录日志失败时不抛出异常，避免影响主要逻辑
            }
        }

        /// <summary>
        /// 包装异常为AI处理异常
        /// </summary>
        public AIProcessingException WrapException(Exception exception, string context = null)
        {
            if (exception is AIProcessingException aiEx)
                return aiEx;

            if (exception is SocketException || exception is WebException)
            {
                return new NetworkException($"网络连接失败: {exception.Message}", exception);
            }

            if (exception is TimeoutException || exception is TaskCanceledException)
            {
                return new NetworkException($"请求超时: {exception.Message}", exception);
            }

            if (exception is ArgumentException || exception is ArgumentNullException)
            {
                return new DataValidationException($"参数验证失败: {exception.Message}", exception);
            }

            if (exception is FileNotFoundException || exception is DirectoryNotFoundException)
            {
                return new FileProcessingException($"文件或目录不存在: {exception.Message}", exception);
            }

            if (exception is UnauthorizedAccessException)
            {
                return new FileProcessingException($"文件访问权限不足: {exception.Message}", exception);
            }

            // 默认包装为通用AI处理异常
            var message = string.IsNullOrEmpty(context) 
                ? $"AI处理失败: {exception.Message}"
                : $"{context}: {exception.Message}";
            
            return new AIProcessingException(message, exception);
        }

        /// <summary>
        /// 安全执行操作，捕获并包装异常
        /// </summary>
        public async Task<T> SafeExecuteAsync<T>(Func<Task<T>> operation, string context = null)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                var wrappedException = WrapException(ex, context);
                LogError(wrappedException, context);
                throw wrappedException;
            }
        }

        /// <summary>
        /// 安全执行操作，捕获并包装异常（同步版本）
        /// </summary>
        public T SafeExecute<T>(Func<T> operation, string context = null)
        {
            try
            {
                return operation();
            }
            catch (Exception ex)
            {
                var wrappedException = WrapException(ex, context);
                LogError(wrappedException, context);
                throw wrappedException;
            }
        }
    }
}
