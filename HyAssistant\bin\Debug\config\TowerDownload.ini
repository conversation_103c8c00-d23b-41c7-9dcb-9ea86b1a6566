[TowerDownload]
; ========================================
; 中国铁塔下载配置文件
; 基于mail.ini配置初始化
; ========================================

; ========================================
; HTTP请求头配置
; ========================================
User-Agent=Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Accept=application/json, text/plain, */*
Accept-Encoding=gzip, deflate
Accept-Language=zh-CN,zh;q=0.9
Content-Type=application/json;charset=UTF-8

; ========================================
; 服务器配置
; ========================================
Host=res2.chinatowercom.cn:19528
Origin=http://res2.chinatowercom.cn:19528
HomePage=http://4a.chinatowercom.cn:20000/uac/index

; ========================================
; 认证配置（从mail.ini导入）
; ========================================
Authorization=43ccfe7eced6f9cd8826c3431c28ea5b
Cookie=china-tower-token=43ccfe7eced6f9cd8826c3431c28ea5b
TOKEN=43ccfe7eced6f9cd8826c3431c28ea5b
AuthorizationLastUpdataTime=2025/07/03 10:00:00

; ========================================
; API接口配置
; ========================================
PhotoUrl=http://res2.chinatowercom.cn:19528/file/web/photoManagerController/getResInfoByStationId?stationId={stationId}&isEnergy=0&stationName={stationName}&station_code={stationCode}&page=1&size=2000
PhotoReferer=http://res2.chinatowercom.cn:19528/allPicasa?stationId={stationId}&isEnergy=0&stationName={stationName}&station_code={stationCode}
ListUrl=http://res2.chinatowercom.cn:19528/sys/config/page/select
ListReferer=http://res2.chinatowercom.cn:19528/sta-station-page?queryResTypeId=9224&resTypeId=224&resType=%E7%AB%99%E5%9D%80
ListCount=http://res2.chinatowercom.cn:19528/sys/config/page/select/count

; ========================================
; 下载配置
; ========================================
; 请求间隔时间（毫秒）
Sleep=0

; 照片存放路径
Path=D:\WW\TT

; 最大并发下载数
MaxConcurrentDownloads=3

; ========================================
; 配置说明
; ========================================
; 1. Host: 服务器主机名，如：example.com
; 2. Origin: 请求源地址，如：https://example.com
; 3. HomePage: 登录页面地址，如：https://example.com/login
; 4. PhotoUrl: 照片信息获取API地址，支持参数替换
; 5. PhotoReferer: 照片请求来源页面
; 6. ListUrl: 站点列表获取API地址
; 7. ListReferer: 站点列表请求来源页面
; 8. ListCount: 站点数量统计API地址
; 9. Path: 照片存放路径，建议使用绝对路径
; 10. Sleep: 请求间隔时间，避免频繁请求被服务器限制
; 11. MaxConcurrentDownloads: 最大并发下载数，建议不超过5
; 
; 注意事项：
; - 认证相关配置（Authorization、Cookie、TOKEN）会在登录后自动更新
; - 服务器配置和API接口配置需要根据实际环境手动配置
; - 照片存放路径必须配置，程序会在此路径下创建SQLite数据库
