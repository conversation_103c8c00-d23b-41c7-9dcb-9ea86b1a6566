using System;
using System.Collections.Generic;

namespace ET.ETWebAutomation.Models
{
    /// <summary>
    /// 会话数据模型
    /// </summary>
    public class ETWebSessionData
    {
        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 会话创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivityTime { get; set; }

        /// <summary>
        /// 会话过期时间
        /// </summary>
        public DateTime ExpiryTime { get; set; }

        /// <summary>
        /// 会话是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 会话是否已过期
        /// </summary>
        public bool IsExpired => DateTime.Now > ExpiryTime;

        /// <summary>
        /// 会话剩余时间（分钟）
        /// </summary>
        public double RemainingMinutes => Math.Max(0, (ExpiryTime - DateTime.Now).TotalMinutes);

        /// <summary>
        /// 认证Token
        /// </summary>
        public string AuthToken { get; set; }

        /// <summary>
        /// 刷新Token
        /// </summary>
        public string RefreshToken { get; set; }

        /// <summary>
        /// Cookie数据
        /// </summary>
        public Dictionary<string, string> Cookies { get; set; }

        /// <summary>
        /// 会话状态
        /// </summary>
        public SessionStatus Status { get; set; }

        /// <summary>
        /// 客户端IP地址
        /// </summary>
        public string ClientIP { get; set; }

        /// <summary>
        /// 用户代理字符串
        /// </summary>
        public string UserAgent { get; set; }

        /// <summary>
        /// 登录方式
        /// </summary>
        public string LoginMethod { get; set; }

        /// <summary>
        /// 心跳间隔（秒）
        /// </summary>
        public int HeartbeatInterval { get; set; }

        /// <summary>
        /// 最后心跳时间
        /// </summary>
        public DateTime LastHeartbeatTime { get; set; }

        /// <summary>
        /// 心跳失败次数
        /// </summary>
        public int HeartbeatFailureCount { get; set; }

        /// <summary>
        /// 最大心跳失败次数
        /// </summary>
        public int MaxHeartbeatFailures { get; set; }

        /// <summary>
        /// 会话数据
        /// </summary>
        public Dictionary<string, object> SessionVariables { get; set; }

        /// <summary>
        /// 用户权限列表
        /// </summary>
        public List<string> Permissions { get; set; }

        /// <summary>
        /// 用户角色列表
        /// </summary>
        public List<string> Roles { get; set; }

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtendedProperties { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ETWebSessionData()
        {
            SessionId = Guid.NewGuid().ToString();
            CreatedTime = DateTime.Now;
            LastActivityTime = DateTime.Now;
            ExpiryTime = DateTime.Now.AddHours(8); // 默认8小时过期
            IsValid = true;
            Status = SessionStatus.Active;
            HeartbeatInterval = 300; // 默认5分钟心跳
            LastHeartbeatTime = DateTime.Now;
            HeartbeatFailureCount = 0;
            MaxHeartbeatFailures = 3;
            
            Cookies = new Dictionary<string, string>();
            SessionVariables = new Dictionary<string, object>();
            Permissions = new List<string>();
            Roles = new List<string>();
            ExtendedProperties = new Dictionary<string, object>();
        }

        /// <summary>
        /// 构造函数（指定用户信息）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="username">用户名</param>
        public ETWebSessionData(string userId, string username) : this()
        {
            UserId = userId;
            Username = username;
        }

        /// <summary>
        /// 更新最后活动时间
        /// </summary>
        public void UpdateLastActivity()
        {
            LastActivityTime = DateTime.Now;
        }

        /// <summary>
        /// 延长会话过期时间
        /// </summary>
        /// <param name="minutes">延长的分钟数</param>
        public void ExtendSession(int minutes)
        {
            ExpiryTime = ExpiryTime.AddMinutes(minutes);
            UpdateLastActivity();
        }

        /// <summary>
        /// 刷新会话
        /// </summary>
        /// <param name="newExpiryTime">新的过期时间</param>
        public void RefreshSession(DateTime? newExpiryTime = null)
        {
            if (newExpiryTime.HasValue)
            {
                ExpiryTime = newExpiryTime.Value;
            }
            else
            {
                ExpiryTime = DateTime.Now.AddHours(8); // 默认延长8小时
            }
            
            UpdateLastActivity();
            IsValid = true;
            Status = SessionStatus.Active;
            HeartbeatFailureCount = 0;
        }

        /// <summary>
        /// 记录心跳
        /// </summary>
        /// <param name="success">心跳是否成功</param>
        public void RecordHeartbeat(bool success)
        {
            LastHeartbeatTime = DateTime.Now;
            
            if (success)
            {
                HeartbeatFailureCount = 0;
                UpdateLastActivity();
            }
            else
            {
                HeartbeatFailureCount++;
                
                if (HeartbeatFailureCount >= MaxHeartbeatFailures)
                {
                    InvalidateSession("心跳失败次数过多");
                }
            }
        }

        /// <summary>
        /// 使会话无效
        /// </summary>
        /// <param name="reason">无效原因</param>
        public void InvalidateSession(string reason = null)
        {
            IsValid = false;
            Status = SessionStatus.Invalid;
            SetExtendedProperty("InvalidationReason", reason);
            SetExtendedProperty("InvalidationTime", DateTime.Now);
        }

        /// <summary>
        /// 检查会话是否需要刷新
        /// </summary>
        /// <param name="refreshThresholdMinutes">刷新阈值（分钟）</param>
        /// <returns>是否需要刷新</returns>
        public bool NeedsRefresh(int refreshThresholdMinutes = 30)
        {
            return RemainingMinutes <= refreshThresholdMinutes;
        }

        /// <summary>
        /// 检查心跳是否超时
        /// </summary>
        /// <returns>是否超时</returns>
        public bool IsHeartbeatTimeout()
        {
            var timeoutThreshold = HeartbeatInterval * 2; // 超时阈值为心跳间隔的2倍
            return (DateTime.Now - LastHeartbeatTime).TotalSeconds > timeoutThreshold;
        }

        /// <summary>
        /// 获取会话变量
        /// </summary>
        /// <typeparam name="T">变量类型</typeparam>
        /// <param name="key">变量键</param>
        /// <returns>变量值</returns>
        public T GetSessionVariable<T>(string key)
        {
            if (SessionVariables.ContainsKey(key))
            {
                return (T)SessionVariables[key];
            }
            return default(T);
        }

        /// <summary>
        /// 设置会话变量
        /// </summary>
        /// <param name="key">变量键</param>
        /// <param name="value">变量值</param>
        public void SetSessionVariable(string key, object value)
        {
            if (SessionVariables.ContainsKey(key))
            {
                SessionVariables[key] = value;
            }
            else
            {
                SessionVariables.Add(key, value);
            }
        }

        /// <summary>
        /// 移除会话变量
        /// </summary>
        /// <param name="key">变量键</param>
        public void RemoveSessionVariable(string key)
        {
            if (SessionVariables.ContainsKey(key))
            {
                SessionVariables.Remove(key);
            }
        }

        /// <summary>
        /// 检查用户是否具有指定权限
        /// </summary>
        /// <param name="permission">权限名称</param>
        /// <returns>是否具有权限</returns>
        public bool HasPermission(string permission)
        {
            return Permissions.Contains(permission);
        }

        /// <summary>
        /// 检查用户是否具有指定角色
        /// </summary>
        /// <param name="role">角色名称</param>
        /// <returns>是否具有角色</returns>
        public bool HasRole(string role)
        {
            return Roles.Contains(role);
        }

        /// <summary>
        /// 获取扩展属性值
        /// </summary>
        /// <typeparam name="T">属性值类型</typeparam>
        /// <param name="propertyName">属性名称</param>
        /// <returns>属性值</returns>
        public T GetExtendedProperty<T>(string propertyName)
        {
            if (ExtendedProperties.ContainsKey(propertyName))
            {
                return (T)ExtendedProperties[propertyName];
            }
            return default(T);
        }

        /// <summary>
        /// 设置扩展属性值
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <param name="propertyValue">属性值</param>
        public void SetExtendedProperty(string propertyName, object propertyValue)
        {
            if (ExtendedProperties.ContainsKey(propertyName))
            {
                ExtendedProperties[propertyName] = propertyValue;
            }
            else
            {
                ExtendedProperties.Add(propertyName, propertyValue);
            }
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"会话[{SessionId}] - 用户: {Username} - 状态: {Status} - 剩余: {RemainingMinutes:F0}分钟";
        }

        /// <summary>
        /// 克隆会话数据对象
        /// </summary>
        /// <returns>克隆的会话数据对象</returns>
        public ETWebSessionData Clone()
        {
            var cloned = new ETWebSessionData
            {
                SessionId = this.SessionId,
                UserId = this.UserId,
                Username = this.Username,
                CreatedTime = this.CreatedTime,
                LastActivityTime = this.LastActivityTime,
                ExpiryTime = this.ExpiryTime,
                IsValid = this.IsValid,
                Status = this.Status,
                HeartbeatInterval = this.HeartbeatInterval,
                LastHeartbeatTime = this.LastHeartbeatTime,
                HeartbeatFailureCount = this.HeartbeatFailureCount,
                MaxHeartbeatFailures = this.MaxHeartbeatFailures
            };

            // 深拷贝字典和列表
            if (this.Cookies != null)
            {
                cloned.Cookies = new Dictionary<string, string>(this.Cookies);
            }

            if (this.SessionVariables != null)
            {
                cloned.SessionVariables = new Dictionary<string, object>(this.SessionVariables);
            }

            if (this.Permissions != null)
            {
                cloned.Permissions = new List<string>(this.Permissions);
            }

            if (this.Roles != null)
            {
                cloned.Roles = new List<string>(this.Roles);
            }

            if (this.ExtendedProperties != null)
            {
                cloned.ExtendedProperties = new Dictionary<string, object>(this.ExtendedProperties);
            }

            return cloned;
        }
    }

    /// <summary>
    /// 会话状态枚举
    /// </summary>
    public enum SessionStatus
    {
        /// <summary>
        /// 活跃状态
        /// </summary>
        Active,

        /// <summary>
        /// 空闲状态
        /// </summary>
        Idle,

        /// <summary>
        /// 已过期
        /// </summary>
        Expired,

        /// <summary>
        /// 无效状态
        /// </summary>
        Invalid,

        /// <summary>
        /// 已注销
        /// </summary>
        LoggedOut
    }
}
