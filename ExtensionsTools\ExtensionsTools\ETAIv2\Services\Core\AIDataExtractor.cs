using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Microsoft.Office.Interop.Excel;
using ET.ETAIv2.Models;
using ET.ETAIv2.Interfaces;
using ET.ETAIv2.Constants;
using ET.ETAIv2.Exceptions;
using ET.ETAIv2.Utils;

namespace ET.ETAIv2.Services.Core
{
    /// <summary>
    /// AI数据提取器
    /// </summary>
    public class AIDataExtractor : IAIDataExtractor
    {
        private readonly IAILogger _logger;

        public AIDataExtractor(IAILogger logger = null)
        {
            _logger = logger ?? new AILogger();
        }

        /// <summary>
        /// 从Excel范围提取数据组
        /// </summary>
        public List<AIDataGroup> ExtractDataGroups(AIDataSourceConfig config)
        {
            try
            {
                _logger.LogInfo("开始提取数据组");
                
                if (config == null)
                    throw new ArgumentNullException(nameof(config));

                ValidateRanges(config);

                var groups = new List<AIDataGroup>();

                if (config.Mode == DataSourceMode.ByRow)
                {
                    groups = ExtractByRowMode(config);
                }
                else
                {
                    groups = ExtractByColumnMode(config);
                }

                // 处理文件数据
                ProcessFileData(groups, config.FileRange);

                // 提取列级提示词
                var columnPrompts = ExtractColumnPrompts(config);
                foreach (var group in groups)
                {
                    group.ColumnPrompts = columnPrompts;
                }

                _logger.LogInfo($"数据组提取完成，共提取 {groups.Count} 个组");
                return groups;
            }
            catch (Exception ex)
            {
                _logger.LogError("数据组提取失败", ex);
                throw new ExcelIntegrationException("数据提取失败", ex);
            }
        }

        /// <summary>
        /// 提取列级提示词
        /// </summary>
        public Dictionary<string, string> ExtractColumnPrompts(AIDataSourceConfig config)
        {
            var columnPrompts = new Dictionary<string, string>();

            try
            {
                if (config.PromptRange == null)
                    return columnPrompts;

                var promptRange = config.PromptRange;
                var promptValues = promptRange.Value2 as object[,];

                if (promptValues == null)
                    return columnPrompts;

                // 获取提示词区域的起始列
                int startColumn = promptRange.Column;
                int rowCount = promptRange.Rows.Count;
                int columnCount = promptRange.Columns.Count;

                // 遍历提示词区域，提取每列的提示词
                for (int col = 1; col <= columnCount; col++)
                {
                    var columnLetter = GetColumnLetter(startColumn + col - 1);
                    var promptText = "";

                    // 合并该列所有行的提示词
                    for (int row = 1; row <= rowCount; row++)
                    {
                        var cellValue = promptValues[row, col]?.ToString()?.Trim();
                        if (!string.IsNullOrEmpty(cellValue))
                        {
                            if (!string.IsNullOrEmpty(promptText))
                                promptText += " ";
                            promptText += cellValue;
                        }
                    }

                    if (!string.IsNullOrEmpty(promptText))
                    {
                        columnPrompts[columnLetter] = promptText;
                    }
                }

                _logger.LogInfo($"提取列级提示词完成，共 {columnPrompts.Count} 个列");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"提取列级提示词失败: {ex.Message}");
            }

            return columnPrompts;
        }

        /// <summary>
        /// 按行模式提取数据组
        /// </summary>
        private List<AIDataGroup> ExtractByRowMode(AIDataSourceConfig config)
        {
            var groups = new List<AIDataGroup>();

            try
            {
                var sourceRange = config.SourceRange;
                var targetRange = config.TargetRange;
                
                var sourceValues = sourceRange.Value2 as object[,];
                var targetValues = targetRange.Value2 as object[,];

                if (sourceValues == null)
                    return groups;

                int sourceRows = sourceRange.Rows.Count;
                int sourceCols = sourceRange.Columns.Count;
                int targetCols = targetRange?.Columns.Count ?? 0;

                // 按行分组处理
                for (int row = 1; row <= sourceRows; row++)
                {
                    var group = new AIDataGroup
                    {
                        GroupId = $"row_{row}"
                    };

                    // 提取源数据单元格
                    for (int col = 1; col <= sourceCols; col++)
                    {
                        var cellAddress = GetCellAddress(sourceRange.Row + row - 1, sourceRange.Column + col - 1);
                        var cellValue = sourceValues[row, col];
                        
                        var cellData = new CellData
                        {
                            Address = cellAddress,
                            Value = cellValue,
                            DataType = DetermineCellDataType(cellValue),
                            Row = sourceRange.Row + row - 1,
                            Column = sourceRange.Column + col - 1,
                            IsFile = IsFilePath(cellValue?.ToString())
                        };

                        group.SourceCells.Add(cellData);
                    }

                    // 提取目标单元格
                    if (targetRange != null)
                    {
                        for (int col = 1; col <= targetCols; col++)
                        {
                            var cellAddress = GetCellAddress(targetRange.Row + row - 1, targetRange.Column + col - 1);
                            
                            var cellData = new CellData
                            {
                                Address = cellAddress,
                                Value = null, // 目标单元格初始为空
                                DataType = ExcelConstants.DataTypes.Text,
                                Row = targetRange.Row + row - 1,
                                Column = targetRange.Column + col - 1
                            };

                            group.TargetCells.Add(cellData);
                        }
                    }

                    groups.Add(group);
                }
            }
            catch (Exception ex)
            {
                throw new ExcelIntegrationException("按行模式提取数据失败", ex);
            }

            return groups;
        }

        /// <summary>
        /// 按列模式提取数据组
        /// </summary>
        private List<AIDataGroup> ExtractByColumnMode(AIDataSourceConfig config)
        {
            var groups = new List<AIDataGroup>();

            try
            {
                var sourceRange = config.SourceRange;
                var targetRange = config.TargetRange;
                
                var sourceValues = sourceRange.Value2 as object[,];
                var targetValues = targetRange.Value2 as object[,];

                if (sourceValues == null)
                    return groups;

                int sourceRows = sourceRange.Rows.Count;
                int sourceCols = sourceRange.Columns.Count;
                int targetRows = targetRange?.Rows.Count ?? 0;

                // 按列分组处理
                for (int col = 1; col <= sourceCols; col++)
                {
                    var group = new AIDataGroup
                    {
                        GroupId = $"col_{GetColumnLetter(sourceRange.Column + col - 1)}"
                    };

                    // 提取源数据单元格
                    for (int row = 1; row <= sourceRows; row++)
                    {
                        var cellAddress = GetCellAddress(sourceRange.Row + row - 1, sourceRange.Column + col - 1);
                        var cellValue = sourceValues[row, col];
                        
                        var cellData = new CellData
                        {
                            Address = cellAddress,
                            Value = cellValue,
                            DataType = DetermineCellDataType(cellValue),
                            Row = sourceRange.Row + row - 1,
                            Column = sourceRange.Column + col - 1,
                            IsFile = IsFilePath(cellValue?.ToString())
                        };

                        group.SourceCells.Add(cellData);
                    }

                    // 提取目标单元格
                    if (targetRange != null)
                    {
                        for (int row = 1; row <= targetRows; row++)
                        {
                            var cellAddress = GetCellAddress(targetRange.Row + row - 1, targetRange.Column + col - 1);
                            
                            var cellData = new CellData
                            {
                                Address = cellAddress,
                                Value = null, // 目标单元格初始为空
                                DataType = ExcelConstants.DataTypes.Text,
                                Row = targetRange.Row + row - 1,
                                Column = targetRange.Column + col - 1
                            };

                            group.TargetCells.Add(cellData);
                        }
                    }

                    groups.Add(group);
                }
            }
            catch (Exception ex)
            {
                throw new ExcelIntegrationException("按列模式提取数据失败", ex);
            }

            return groups;
        }

        /// <summary>
        /// 处理文件数据
        /// </summary>
        private void ProcessFileData(List<AIDataGroup> groups, Range fileRange)
        {
            if (fileRange == null || groups == null || !groups.Any())
                return;

            try
            {
                var fileValues = fileRange.Value2 as object[,];
                if (fileValues == null)
                    return;

                int fileRows = fileRange.Rows.Count;
                int fileCols = fileRange.Columns.Count;

                // 遍历文件区域，查找文件路径
                for (int row = 1; row <= fileRows; row++)
                {
                    for (int col = 1; col <= fileCols; col++)
                    {
                        var cellValue = fileValues[row, col]?.ToString()?.Trim();
                        if (IsFilePath(cellValue))
                        {
                            // 根据位置确定对应的数据组
                            var groupIndex = Math.Min(row - 1, groups.Count - 1);
                            if (groupIndex >= 0 && groupIndex < groups.Count)
                            {
                                var fileData = new FileData
                                {
                                    FilePath = cellValue,
                                    FileName = Path.GetFileName(cellValue),
                                    FileType = Path.GetExtension(cellValue)?.ToLower(),
                                    IsUploaded = false
                                };

                                groups[groupIndex].Files.Add(fileData);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"处理文件数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证区域有效性
        /// </summary>
        private void ValidateRanges(AIDataSourceConfig config)
        {
            if (config.SourceRange == null)
                throw new ArgumentException("数据源区域不能为空");

            if (config.TargetRange == null)
                throw new ArgumentException("目标区域不能为空");

            // 检查区域大小是否合理
            var sourceRows = config.SourceRange.Rows.Count;
            var sourceCols = config.SourceRange.Columns.Count;

            if (sourceRows > ExcelConstants.DefaultMaxProcessRows)
                throw new ArgumentException($"数据源行数过多，最大支持 {ExcelConstants.DefaultMaxProcessRows} 行");

            if (sourceCols > 100)
                throw new ArgumentException("数据源列数过多，最大支持 100 列");
        }

        /// <summary>
        /// 确定单元格数据类型
        /// </summary>
        private string DetermineCellDataType(object value)
        {
            if (value == null)
                return ExcelConstants.DataTypes.Text;

            if (value is double || value is int || value is float || value is decimal)
                return ExcelConstants.DataTypes.Number;

            if (value is DateTime)
                return ExcelConstants.DataTypes.Date;

            if (value is bool)
                return ExcelConstants.DataTypes.Boolean;

            var stringValue = value.ToString();
            if (stringValue.StartsWith("="))
                return ExcelConstants.DataTypes.Formula;

            if (IsFilePath(stringValue))
                return ExcelConstants.DataTypes.File;

            return ExcelConstants.DataTypes.Text;
        }

        /// <summary>
        /// 判断是否为文件路径
        /// </summary>
        private bool IsFilePath(string value)
        {
            if (string.IsNullOrEmpty(value))
                return false;

            try
            {
                var extension = Path.GetExtension(value)?.ToLower();
                return !string.IsNullOrEmpty(extension) && 
                       AIConstants.SupportedFileExtensions.Contains(extension) &&
                       (File.Exists(value) || value.Contains("\\") || value.Contains("/"));
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取单元格地址
        /// </summary>
        private string GetCellAddress(int row, int column)
        {
            return $"{GetColumnLetter(column)}{row}";
        }

        /// <summary>
        /// 获取列字母
        /// </summary>
        private string GetColumnLetter(int columnNumber)
        {
            string columnLetter = "";
            while (columnNumber > 0)
            {
                int remainder = (columnNumber - 1) % 26;
                columnLetter = (char)(65 + remainder) + columnLetter;
                columnNumber = (columnNumber - 1) / 26;
            }
            return columnLetter;
        }
    }
}
