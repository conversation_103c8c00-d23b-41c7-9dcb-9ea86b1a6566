using System;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using ET;
using HyAssistant.WebBrowserV2.Forms;
using HyAssistant.WebBrowserV2.Managers;
using HyAssistant.WebBrowserV2.Utils;
using Microsoft.Web.WebView2.WinForms;

namespace HyAssistant.WebBrowserV2.Core
{
    /// <summary>
    /// WebBrowserV2 菜单操作相关方法 (partial class) 包含所有菜单事件处理、菜单状态更新、右键菜单等菜单相关功能
    /// </summary>
    public partial class WebBrowserV2
    {
        #region 菜单事件注册和初始化

        /// <summary>
        /// 初始化标签页下拉菜单
        /// </summary>
        private void InitializeTabsDropDownMenu()
        {
            try
            {
                _tabMenuManagerV2?.RefreshIfNeeded();
                ETLogManager.Debug(this, "标签页下拉菜单初始化完成");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "初始化标签页下拉菜单");
            }
        }

        /// <summary>
        /// 安全地注册菜单事件处理程序
        /// </summary>
        /// <param name="menuItem">菜单项</param>
        /// <param name="handler">事件处理程序</param>
        private void SafeRegisterMenuEvent(ToolStripMenuItem menuItem, EventHandler handler)
        {
            try
            {
                if (menuItem != null && handler != null)
                {
                    menuItem.Click -= handler;
                    menuItem.Click += handler;
                    ETLogManager.Debug(this, $"菜单事件已注册: {menuItem.Text}");
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "注册菜单事件");
            }
        }

        /// <summary>
        /// 注册所有菜单事件处理程序
        /// </summary>
        private void RegisterMenuEventHandlers()
        {
            try
            {
                // 确保右键菜单事件正确绑定
                SafeRegisterMenuEvent(closeTabMenuItem, CloseTabMenuItem_Click);
                SafeRegisterMenuEvent(editTabSettingsMenuItem, EditTabSettingsMenuItem_Click);
                SafeRegisterMenuEvent(删除网站ToolStripMenuItem, 删除网站ToolStripMenuItem_Click);
                SafeRegisterMenuEvent(viewSessionStatusMenuItem, ViewSessionStatusMenuItem_Click);
                SafeRegisterMenuEvent(复制标签页ToolStripMenuItem, CloneTabMenuItem_Click);
                SafeRegisterMenuEvent(退出登录ToolStripMenuItem, LogoutTabMenuItem_Click);

                // 初始化标签页下拉菜单
                InitializeTabsDropDownMenu();

                ETLogManager.Info(this, "所有菜单事件处理程序注册完成");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "注册菜单事件处理程序", true);
            }
        }

        /// <summary>
        /// 标签页下拉按钮打开事件处理
        /// </summary>
        private void TabsDropDownButton_DropDownOpening(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info(this, "标签页下拉菜单打开，开始刷新菜单");
                _tabMenuManagerV2?.RefreshIfNeeded();
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "标签页下拉菜单打开处理", true);
            }
        }

        #endregion 菜单事件注册和初始化

        #region 标签页菜单管理器事件处理

        /// <summary>
        /// 标签页菜单项点击事件处理
        /// </summary>
        private void TabMenuManager_TabMenuItemClicked(object sender, WebBrowserTabConfig config)
        {
            try
            {
                TabsMenuItem_Click_Internal(config);
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "标签页菜单项点击处理", true);
            }
        }

        /// <summary>
        /// 新建标签页菜单项点击事件处理
        /// </summary>
        private void TabMenuManager_NewTabMenuItemClicked(object sender, EventArgs e)
        {
            try
            {
                新建网站ToolStripMenuItem_Click(sender, e);
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "新建标签页菜单项点击处理", true);
            }
        }

        #endregion 标签页菜单管理器事件处理

        #region 标签页菜单项点击核心逻辑

        /// <summary>
        /// 标签页菜单项点击核心逻辑（同步版本）
        /// </summary>
        /// <param name="config">标签页配置</param>
        private void TabsMenuItem_Click_Internal(WebBrowserTabConfig config)
        {
            try
            {
                // V2版本优化: 使用Task.Run避免阻塞UI线程
                _ = Task.Run(async () =>
                {
                    try
                    {
                        using (var cts = new CancellationTokenSource(TimeSpan.FromMinutes(2)))
                        {
                            await TabsMenuItem_Click_InternalAsync(config, cts.Token).ConfigureAwait(false);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        ETLogManager.Info(this, "标签页菜单项点击操作被取消或超时");
                    }
                    catch (Exception ex)
                    {
                        WebBrowserExceptionHandlerV2.HandleAsyncException(this, ex, "标签页菜单项点击异步处理");
                    }
                });
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "标签页菜单项点击内部处理", true);
            }
        }

        #endregion 标签页菜单项点击核心逻辑

        #region 主菜单项点击事件

        /// <summary>
        /// 新建标签页菜单项点击事件
        /// </summary>
        private void 新建网站ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info(this, "新建标签页菜单项被点击");

                // 创建默认配置，优先使用当前标签页配置作为模板
                WebBrowserTabConfig config = _tabManager.GetCurrentTabConfig();
                if (config == null)
                {
                    config = new WebBrowserTabConfig
                    {
                        Name = "新标签页",
                        Url = "about:blank",
                        HomeUrl = "about:blank"
                    };
                }
                else
                {
                    // 使用当前配置作为模板，但重置名称和URL
                    config = config.Clone() as WebBrowserTabConfig;
                    config.Name = "新标签页";
                    config.Url = "about:blank";
                    config.HomeUrl = "about:blank";
                }

                // 显示标签页配置窗体
                using (TabConfigFormV2 configForm = new TabConfigFormV2(config, false))
                {
                    if (configForm.ShowDialog() == DialogResult.OK)
                    {
                        // 保存配置
                        _configManager.SaveTabConfig(config);

                        // 标记菜单需要刷新
                        _tabMenuManagerV2?.MarkForRefresh();

                        // V2版本优化: 异步创建标签页，避免阻塞UI
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                using (var cts = new CancellationTokenSource(TimeSpan.FromMinutes(2)))
                                {
                                    await _tabManager.CreateNewTabAsync(config, cts.Token);
                                    ETLogManager.Info(this, $"新标签页创建成功: {config.Name}");

                                    // 在UI线程上刷新菜单
                                    BeginInvoke(new Action(() => _tabMenuManagerV2?.RefreshIfNeeded()));
                                }
                            }
                            catch (OperationCanceledException)
                            {
                                ETLogManager.Warning(this, "新建标签页操作被取消或超时");
                            }
                            catch (Exception ex)
                            {
                                WebBrowserExceptionHandlerV2.HandleAsyncException(this, ex, "异步创建新标签页");
                            }
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "新建标签页", true);
            }
        }

        /// <summary>
        /// 标签页菜单项点击事件
        /// </summary>
        private void TabsMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                if (sender is ToolStripMenuItem menuItem && menuItem.Tag is WebBrowserTabConfig config)
                {
                    // V2版本优化: 使用Task.Run避免async void，确保异常被正确处理，支持取消操作
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            using (var cts = new CancellationTokenSource(TimeSpan.FromMinutes(2))) // 2分钟超时
                            {
                                await TabsMenuItem_Click_InternalAsync(config, cts.Token).ConfigureAwait(false);
                            }
                        }
                        catch (OperationCanceledException)
                        {
                            ETLogManager.Info(this, "标签页菜单项点击操作被取消或超时");
                        }
                        catch (Exception ex)
                        {
                            // 在后台线程中处理异常
                            WebBrowserExceptionHandlerV2.HandleAsyncException(this, ex, "标签页菜单项点击异步处理");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "标签页菜单项点击", true);
            }
        }

        /// <summary>
        /// 标签页菜单项点击核心逻辑（异步安全版本）
        /// </summary>
        /// <param name="config">标签页配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        private async Task TabsMenuItem_Click_InternalAsync(WebBrowserTabConfig config, CancellationToken cancellationToken = default)
        {
            try
            {
                // V2版本优化: 检查取消令牌
                if (cancellationToken.IsCancellationRequested)
                {
                    ETLogManager.Info(this, "标签页菜单点击操作被取消");
                    return;
                }

                // 防止重复点击导致的并发问题
                if (_isCreatingTab)
                {
                    ETLogManager.Warning(this, "标签页正在创建中，忽略重复点击");
                    return;
                }

                _isCreatingTab = true;
                ETLogManager.Info(this, $"开始处理标签页菜单点击: {config.Name}");

                try
                {
                    // 再次检查取消令牌
                    cancellationToken.ThrowIfCancellationRequested();

                    // 检查标签页是否已存在
                    if (_tabManager.TabDataDictionary.ContainsKey(config.SectionId))
                    {
                        // 标签页已存在，切换到该标签页
                        _tabManager.SwitchToTab(config.SectionId);
                        ETLogManager.Info(this, $"切换到已存在的标签页: {config.Name}");
                    }
                    else
                    {
                        // 标签页不存在，创建新标签页
                        await _tabManager.CreateNewTabAsync(config, cancellationToken);
                        ETLogManager.Info(this, $"创建新标签页: {config.Name}");
                    }
                }
                finally
                {
                    _isCreatingTab = false;
                }
            }
            catch (Exception ex)
            {
                _isCreatingTab = false;
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "标签页菜单项点击核心逻辑", true);
            }
        }

        #endregion 主菜单项点击事件

        #region 右键菜单项点击事件

        /// <summary>
        /// 关闭标签页菜单项点击事件
        /// </summary>
        private async void CloseTabMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info(this, "关闭标签页菜单项被点击");
                await _tabManager.CloseCurrentTab();
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "关闭标签页", true);
            }
        }

        /// <summary>
        /// 编辑标签页设置菜单项点击事件
        /// </summary>
        private void EditTabSettingsMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info(this, "编辑标签页设置菜单项被点击");

                // 如果没有标签页，则显示新建标签页窗口
                if (tabControl1.TabPages.Count == 0)
                {
                    新建网站ToolStripMenuItem_Click(sender, e);
                    return;
                }

                // 获取当前标签页配置
                WebBrowserTabConfig currentConfig = _tabManager.GetCurrentTabConfig();
                if (currentConfig == null)
                {
                    ShowWarningMessage("无法获取当前标签页配置");
                    return;
                }

                // 显示编辑窗体
                using (TabConfigFormV2 configForm = new TabConfigFormV2(currentConfig, true))
                {
                    if (configForm.ShowDialog() == DialogResult.OK)
                    {
                        // 保存配置
                        _configManager.SaveTabConfig(currentConfig);

                        // 标记菜单需要刷新
                        _tabMenuManagerV2?.MarkForRefresh();

                        // 更新标签页
                        _tabManager.UpdateTabConfig(currentConfig);

                        // 刷新菜单
                        _tabMenuManagerV2?.RefreshIfNeeded();

                        ETLogManager.Info(this, $"标签页设置已更新: {currentConfig.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "编辑标签页设置", true);
            }
        }

        /// <summary>
        /// 删除标签页菜单项点击事件
        /// </summary>
        private async void 删除网站ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info(this, "删除网站菜单项被点击");

                // 如果没有标签页，提示用户
                if (tabControl1.TabPages.Count == 0)
                {
                    ShowInfoMessage("当前没有标签页可以删除");
                    return;
                }

                // 获取当前标签页配置
                WebBrowserTabConfig currentConfig = _tabManager.GetCurrentTabConfig();
                if (currentConfig == null)
                {
                    ShowWarningMessage("无法获取当前标签页配置");
                    return;
                }

                // 确认删除
                DialogResult result = ShowConfirmDialog(
                    $"确定要删除标签页 '{currentConfig.Name}' 吗？\n\n这将同时删除配置文件和关闭当前标签页。",
                    "确认删除");

                if (result == DialogResult.Yes)
                {
                    // 删除配置
                    _configManager.DeleteTabConfig(currentConfig.SectionId);

                    // 关闭标签页
                    await _tabManager.CloseCurrentTab();

                    ETLogManager.Info(this, $"标签页已删除: {currentConfig.Name}");
                    ShowInfoMessage($"标签页 '{currentConfig.Name}' 已删除");
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "删除标签页", true);
            }
        }

        #endregion 右键菜单项点击事件

        #region 更多右键菜单项点击事件

        /// <summary>
        /// 查看会话状态菜单项点击事件
        /// </summary>
        private void ViewSessionStatusMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info(this, "查看会话状态菜单项被点击");

                // 获取当前选中的标签页
                if (tabControl1.SelectedTab == null)
                {
                    ShowInfoMessage("当前没有选中的标签页");
                    return;
                }

                string tabName = tabControl1.SelectedTab.Name;

                // 获取会话维持器
                var sessionKeeper = _sessionManager.GetSessionKeeper(tabName);
                if (sessionKeeper == null)
                {
                    ShowInfoMessage("当前标签页没有会话维持器");
                    return;
                }

                // 显示会话状态信息
                string statusInfo = GetSessionStatusInfo(sessionKeeper);
                ShowInfoMessage(statusInfo, "会话状态");

                ETLogManager.Info(this, $"已显示标签页会话状态: {tabName}");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "查看会话状态", true);
            }
        }

        /// <summary>
        /// 复制标签页菜单项点击事件
        /// </summary>
        private async void CloneTabMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info(this, "复制标签页菜单项被点击");

                // 如果没有标签页，给出提示
                if (tabControl1.TabPages.Count == 0)
                {
                    ShowInfoMessage("当前没有标签页可以操作");
                    return;
                }

                // 获取当前标签页配置
                WebBrowserTabConfig currentConfig = _tabManager.GetCurrentTabConfig();
                if (currentConfig == null)
                {
                    ShowWarningMessage("无法获取当前标签页配置");
                    return;
                }

                // 检查当前标签页是否已经是副本
                string currentTabName = tabControl1.SelectedTab?.Name;
                if (!string.IsNullOrEmpty(currentTabName) &&
                    _tabManager.TabDataDictionary.TryGetValue(currentTabName, out WebBrowserTabManagerV2.TabDataV2 tabData) &&
                    tabData.IsClone)
                {
                    ShowWarningMessage("副本标签页不能再次复制");
                    return;
                }

                // 创建副本配置
                WebBrowserTabConfig cloneConfig = currentConfig.Clone() as WebBrowserTabConfig;
                cloneConfig.Name = $"{currentConfig.Name} - 副本";
                cloneConfig.SectionId = Guid.NewGuid().ToString();

                // 创建副本标签页
                await _tabManager.CreateCloneTab(cloneConfig);

                ETLogManager.Info(this, $"标签页复制成功: {cloneConfig.Name}");
                ShowInfoMessage($"标签页 '{currentConfig.Name}' 已复制为 '{cloneConfig.Name}'");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "复制标签页", true);
            }
        }

        /// <summary>
        /// 退出登录菜单项点击事件
        /// </summary>
        private async void LogoutTabMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info(this, "退出登录菜单项被点击");

                // 如果没有标签页，给出提示
                if (tabControl1.TabPages.Count == 0)
                {
                    ShowInfoMessage("当前没有标签页可以操作");
                    return;
                }

                // 获取当前标签页
                string currentTabName = tabControl1.SelectedTab?.Name;
                if (string.IsNullOrEmpty(currentTabName))
                {
                    ShowWarningMessage("无法获取当前标签页信息");
                    return;
                }

                // 检查是否为副本标签页
                if (_tabManager.TabDataDictionary.TryGetValue(currentTabName, out WebBrowserTabManagerV2.TabDataV2 tabData) &&
                    tabData.IsClone)
                {
                    ShowWarningMessage("副本标签页不支持退出登录操作");
                    return;
                }

                // 确认退出登录
                DialogResult result = ShowConfirmDialog(
                    "确定要退出当前标签页的登录状态吗？\n\n这将清除所有Cookie和会话信息。",
                    "确认退出登录");

                if (result == DialogResult.Yes)
                {
                    // 执行退出登录操作
                    await _tabManager.LogoutCurrentTab();

                    ETLogManager.Info(this, $"标签页退出登录成功: {currentTabName}");
                    ShowInfoMessage("已退出登录，Cookie和会话信息已清除");
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "退出登录", true);
            }
        }

        /// <summary>
        /// 管理Cookies菜单项点击事件
        /// </summary>
        private void ManageCookiesMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info(this, "管理Cookies菜单项被点击");

                // 获取当前标签页的WebView2控件
                WebView2 currentWebView = _tabManager.GetCurrentWebView();
                if (currentWebView == null)
                {
                    ShowInfoMessage("当前没有活动的标签页");
                    return;
                }

                // 获取当前标签页配置
                WebBrowserTabConfig currentConfig = _tabManager.GetCurrentTabConfig();
                if (currentConfig == null)
                {
                    ShowWarningMessage("无法获取当前标签页配置");
                    return;
                }

                // 更新标头管理器的配置
                _cookieManager.HeadersPath = currentConfig.CookiePath;
                _cookieManager.CurrentUrl = currentConfig.Url;

                // 创建并显示Cookie管理窗体
                using (CookieManagerFormV2 cookieManagerForm = new CookieManagerFormV2(_cookieManager, currentWebView, this))
                {
                    cookieManagerForm.ShowDialog();

                    // 如果路径有更新，更新TabConfig
                    if (!string.IsNullOrEmpty(_cookieManager.HeadersPath) &&
                        _cookieManager.HeadersPath != currentConfig.CookiePath)
                    {
                        currentConfig.CookiePath = _cookieManager.HeadersPath;
                        _configManager.SaveTabConfig(currentConfig);
                        ETLogManager.Info(this, $"Cookie路径已更新: {_cookieManager.HeadersPath}");
                    }
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "管理Cookies", true);
            }
        }

        /// <summary>
        /// 获取会话状态信息
        /// </summary>
        /// <param name="sessionKeeper">会话维持器</param>
        /// <returns>状态信息字符串</returns>
        private string GetSessionStatusInfo(object sessionKeeper)
        {
            try
            {
                if (sessionKeeper == null)
                    return "会话维持器为空";

                var sb = new StringBuilder();

                // 使用反射获取会话维持器的状态信息
                var type = sessionKeeper.GetType();

                // 获取基本状态信息
                var isRunningProp = type.GetProperty("IsRunning");
                var lastRefreshTimeProp = type.GetProperty("LastRefreshTime");
                var lastRefreshSuccessProp = type.GetProperty("LastRefreshSuccess");

                if (isRunningProp != null)
                {
                    bool isRunning = (bool)isRunningProp.GetValue(sessionKeeper);
                    sb.AppendLine($"运行状态: {(isRunning ? "运行中" : "已停止")}");
                }

                if (lastRefreshTimeProp != null)
                {
                    DateTime lastRefreshTime = (DateTime)lastRefreshTimeProp.GetValue(sessionKeeper);
                    if (lastRefreshTime != DateTime.MinValue)
                    {
                        sb.AppendLine($"最后刷新时间: {lastRefreshTime:yyyy-MM-dd HH:mm:ss}");
                        sb.AppendLine($"距今: {(DateTime.Now - lastRefreshTime).TotalMinutes:F1} 分钟");
                    }
                    else
                    {
                        sb.AppendLine("最后刷新时间: 从未刷新");
                    }
                }

                if (lastRefreshSuccessProp != null)
                {
                    bool lastRefreshSuccess = (bool)lastRefreshSuccessProp.GetValue(sessionKeeper);
                    sb.AppendLine($"最后刷新结果: {(lastRefreshSuccess ? "成功" : "失败")}");
                }

                // 尝试获取定时器状态
                var getTimerStatusMethod = type.GetMethod("GetTimerStatus");
                if (getTimerStatusMethod != null)
                {
                    string timerStatus = (string)getTimerStatusMethod.Invoke(sessionKeeper, null);
                    sb.AppendLine($"定时器状态: {timerStatus}");
                }

                return sb.ToString().TrimEnd();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"获取会话状态信息失败: {ex.Message}");
                return $"获取状态信息失败: {ex.Message}";
            }
        }

        #endregion 更多右键菜单项点击事件

        #region 右键菜单管理

        /// <summary>
        /// 显示标签页右键菜单
        /// </summary>
        /// <param name="location">菜单显示位置</param>
        private void ShowTabContextMenu(Point location)
        {
            try
            {
                // 使用预定义的tabContextMenu
                if (tabContextMenu != null)
                {
                    // 获取当前标签页信息
                    string currentTabName = tabControl1.SelectedTab?.Name;
                    bool isCloneTab = false;

                    if (!string.IsNullOrEmpty(currentTabName) &&
                        _tabManager.TabDataDictionary.TryGetValue(currentTabName, out WebBrowserTabManagerV2.TabDataV2 tabData))
                    {
                        isCloneTab = tabData.IsClone;
                        ETLogManager.Debug(this, $"右键菜单: 标签页 {tabData.Config.Name} (SectionId: {currentTabName}) {(isCloneTab ? "是副本" : "非副本")}");
                    }

                    // 动态启用/禁用菜单项
                    UpdateContextMenuItemsState(currentTabName, isCloneTab);

                    // 显示菜单
                    tabContextMenu.Show(tabControl1, location);
                }
                else
                {
                    // 备用方案：创建临时菜单
                    CreateTemporaryContextMenu(location);
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "显示标签页右键菜单");
            }
        }

        /// <summary>
        /// 更新右键菜单项状态
        /// </summary>
        /// <param name="currentTabName">当前标签页名称</param>
        /// <param name="isCloneTab">是否为副本标签页</param>
        private void UpdateContextMenuItemsState(string currentTabName, bool isCloneTab)
        {
            try
            {
                foreach (ToolStripItem item in tabContextMenu.Items)
                {
                    if (item is ToolStripMenuItem menuItem)
                    {
                        // 基本规则：标签页不为空时菜单项启用
                        bool shouldEnable = !string.IsNullOrEmpty(currentTabName);

                        // 特殊规则：如果标签页是副本，禁用特定菜单项
                        if (isCloneTab)
                        {
                            if (menuItem == 复制标签页ToolStripMenuItem ||
                                menuItem == 退出登录ToolStripMenuItem ||
                                menuItem == editTabSettingsMenuItem ||
                                menuItem == 删除网站ToolStripMenuItem)
                            {
                                shouldEnable = false;
                            }
                        }

                        // 设置菜单项启用状态
                        menuItem.Enabled = shouldEnable;
                    }
                }

                ETLogManager.Debug(this, $"右键菜单项状态已更新，副本标签页: {isCloneTab}");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "更新右键菜单项状态");
            }
        }

        /// <summary>
        /// 创建临时右键菜单
        /// </summary>
        /// <param name="location">菜单显示位置</param>
        private void CreateTemporaryContextMenu(Point location)
        {
            try
            {
                ETLogManager.Warning(this, "tabContextMenu为空，创建临时右键菜单");

                using (ContextMenuStrip menuStrip = new ContextMenuStrip())
                {
                    // 添加关闭标签页菜单项
                    ToolStripMenuItem closeMenuItem = new ToolStripMenuItem("关闭标签页");
                    closeMenuItem.Click += CloseTabMenuItem_Click;
                    menuStrip.Items.Add(closeMenuItem);

                    // 添加编辑标签页设置菜单项
                    ToolStripMenuItem editSettingsMenuItem = new ToolStripMenuItem("编辑标签页设置");
                    editSettingsMenuItem.Click += EditTabSettingsMenuItem_Click;
                    menuStrip.Items.Add(editSettingsMenuItem);

                    // 添加查看会话状态菜单项
                    ToolStripMenuItem viewSessionStatusMenuItem = new ToolStripMenuItem("查看会话状态");
                    viewSessionStatusMenuItem.Click += ViewSessionStatusMenuItem_Click;
                    menuStrip.Items.Add(viewSessionStatusMenuItem);

                    // 显示临时菜单
                    menuStrip.Show(tabControl1, location);
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "创建临时右键菜单");
            }
        }

        #endregion 右键菜单管理
    }
}