using System;
using System.Collections.Generic;
using System.Linq;
using ET.ETAIv2.Models;
using ET.ETAIv2.Constants;
using ET.ETAIv2.Interfaces;

namespace ET.ETAIv2.Utils
{
    /// <summary>
    /// JSON Schema辅助类
    /// </summary>
    public class JsonSchemaHelper
    {
        private readonly IAILogger _logger;

        public JsonSchemaHelper(IAILogger logger = null)
        {
            _logger = logger ?? new AILogger();
        }

        /// <summary>
        /// 为Excel分析结果生成JSON Schema
        /// </summary>
        public string GenerateExcelAnalysisSchema(List<AIDataGroup> dataGroups)
        {
            try
            {
                if (dataGroups == null || !dataGroups.Any())
                {
                    return AIConstants.JsonSchemas.ExcelAnalysisResult;
                }

                // 分析目标单元格结构
                var targetCellStructure = AnalyzeTargetCellStructure(dataGroups);
                
                // 生成动态Schema
                var schema = BuildDynamicSchema(targetCellStructure);
                
                _logger.LogInfo($"生成JSON Schema完成，包含 {targetCellStructure.Count} 个字段");
                return schema;
            }
            catch (Exception ex)
            {
                _logger.LogError("生成JSON Schema失败", ex);
                return AIConstants.JsonSchemas.ExcelAnalysisResult;
            }
        }

        /// <summary>
        /// 验证JSON响应是否符合Schema
        /// </summary>
        public bool ValidateJsonResponse(string jsonResponse, string schema)
        {
            try
            {
                if (string.IsNullOrEmpty(jsonResponse) || string.IsNullOrEmpty(schema))
                    return false;

                // TODO: 实现JSON Schema验证
                // 可以使用 Newtonsoft.Json.Schema 或 JsonSchema.Net
                
                // 临时实现：简单的JSON格式验证
                var isValidJson = IsValidJson(jsonResponse);
                
                _logger.LogDebug($"JSON响应验证结果: {isValidJson}");
                return isValidJson;
            }
            catch (Exception ex)
            {
                _logger.LogError("JSON响应验证失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 分析目标单元格结构
        /// </summary>
        private Dictionary<string, string> AnalyzeTargetCellStructure(List<AIDataGroup> dataGroups)
        {
            var structure = new Dictionary<string, string>();

            foreach (var group in dataGroups)
            {
                foreach (var targetCell in group.TargetCells)
                {
                    var columnLetter = GetColumnLetter(targetCell.Column);
                    if (!structure.ContainsKey(columnLetter))
                    {
                        // 根据列级提示词或数据类型推断字段类型
                        var fieldType = InferFieldType(group, columnLetter);
                        structure[columnLetter] = fieldType;
                    }
                }
            }

            return structure;
        }

        /// <summary>
        /// 推断字段类型
        /// </summary>
        private string InferFieldType(AIDataGroup group, string columnLetter)
        {
            // 检查列级提示词
            if (group.ColumnPrompts.TryGetValue(columnLetter, out var prompt))
            {
                var lowerPrompt = prompt.ToLower();
                
                if (lowerPrompt.Contains("数字") || lowerPrompt.Contains("金额") || lowerPrompt.Contains("价格"))
                    return "number";
                
                if (lowerPrompt.Contains("日期") || lowerPrompt.Contains("时间"))
                    return "string"; // 日期作为字符串处理
                
                if (lowerPrompt.Contains("是否") || lowerPrompt.Contains("布尔"))
                    return "boolean";
            }

            // 默认为字符串类型
            return "string";
        }

        /// <summary>
        /// 构建动态Schema
        /// </summary>
        private string BuildDynamicSchema(Dictionary<string, string> structure)
        {
            var properties = new List<string>();
            var required = new List<string>();

            foreach (var kvp in structure)
            {
                var fieldName = $"column_{kvp.Key}";
                var fieldType = kvp.Value;
                
                properties.Add($"\"{fieldName}\": {{\"type\": \"{fieldType}\"}}");
                required.Add($"\"{fieldName}\"");
            }

            var propertiesString = string.Join(",\n                    ", properties);
            var requiredString = string.Join(", ", required);

            return $@"{{
    ""type"": ""object"",
    ""properties"": {{
        ""results"": {{
            ""type"": ""array"",
            ""items"": {{
                ""type"": ""object"",
                ""properties"": {{
                    ""groupId"": {{""type"": ""string""}},
                    ""values"": {{
                        ""type"": ""object"",
                        ""properties"": {{
                            {propertiesString}
                        }},
                        ""required"": [{requiredString}]
                    }},
                    ""processingInfo"": {{""type"": ""string""}},
                    ""confidence"": {{
                        ""type"": ""number"",
                        ""minimum"": 0,
                        ""maximum"": 1
                    }}
                }},
                ""required"": [""groupId"", ""values""]
            }}
        }}
    }},
    ""required"": [""results""]
}}";
        }

        /// <summary>
        /// 验证JSON格式
        /// </summary>
        private bool IsValidJson(string jsonString)
        {
            try
            {
                Newtonsoft.Json.Linq.JToken.Parse(jsonString);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取列字母
        /// </summary>
        private string GetColumnLetter(int columnNumber)
        {
            string columnLetter = "";
            while (columnNumber > 0)
            {
                int remainder = (columnNumber - 1) % 26;
                columnLetter = (char)(65 + remainder) + columnLetter;
                columnNumber = (columnNumber - 1) / 26;
            }
            return columnLetter;
        }

        /// <summary>
        /// 生成Chat API的系统提示词
        /// </summary>
        public string GenerateChatSystemPrompt(AIRequest request, string schema)
        {
            var prompt = new List<string>
            {
                AIConstants.SystemPrompts.DefaultExcelAnalysis,
                "",
                "请严格按照以下JSON Schema格式返回结果：",
                schema,
                ""
            };

            if (!string.IsNullOrEmpty(request.GlobalPrompt))
            {
                prompt.Add("全局指令：");
                prompt.Add(request.GlobalPrompt);
                prompt.Add("");
            }

            prompt.Add("数据分析要求：");
            prompt.Add("1. 仔细分析每组数据的特征和模式");
            prompt.Add("2. 根据列级提示词生成相应的分析结果");
            prompt.Add("3. 如果有文件内容，结合文件信息进行分析");
            prompt.Add("4. 确保返回的JSON格式严格符合Schema要求");
            prompt.Add("5. 为每个结果提供适当的置信度评分");

            return string.Join("\n", prompt);
        }

        /// <summary>
        /// 生成Assistant API的指令
        /// </summary>
        public string GenerateAssistantInstructions(AIRequest request)
        {
            var instructions = new List<string>
            {
                "你是一个专业的Excel数据分析助手。",
                "你的任务是分析用户提供的Excel数据，并根据要求生成相应的分析结果。",
                "",
                "工作流程：",
                "1. 仔细阅读全局指令和列级提示词",
                "2. 分析每组数据的特征和关联性",
                "3. 如果有文件，深入分析文件内容",
                "4. 生成准确、有用的分析结果",
                "5. 返回结构化的JSON格式响应",
                "",
                "注意事项：",
                "- 确保分析结果的准确性和相关性",
                "- 充分利用文件搜索功能获取相关信息",
                "- 保持结果的一致性和逻辑性",
                "- 提供适当的置信度评估"
            };

            if (!string.IsNullOrEmpty(request.GlobalPrompt))
            {
                instructions.Add("");
                instructions.Add("特殊指令：");
                instructions.Add(request.GlobalPrompt);
            }

            return string.Join("\n", instructions);
        }

        /// <summary>
        /// 格式化数据组为文本
        /// </summary>
        public string FormatDataGroupsAsText(List<AIDataGroup> dataGroups)
        {
            var content = new List<string>();

            foreach (var group in dataGroups)
            {
                content.Add($"=== 数据组 {group.GroupId} ===");
                
                // 源数据
                if (group.SourceCells.Any())
                {
                    content.Add("源数据：");
                    foreach (var cell in group.SourceCells)
                    {
                        content.Add($"  {cell.Address}: {cell.Value} ({cell.DataType})");
                    }
                }

                // 列级提示词
                if (group.ColumnPrompts.Any())
                {
                    content.Add("列级指令：");
                    foreach (var prompt in group.ColumnPrompts)
                    {
                        content.Add($"  列{prompt.Key}: {prompt.Value}");
                    }
                }

                // 文件信息
                if (group.Files.Any())
                {
                    content.Add("相关文件：");
                    foreach (var file in group.Files)
                    {
                        if (file.IsUploaded)
                        {
                            content.Add($"  {file.FileName} (已上传，ID: {file.OpenAIFileId})");
                        }
                        else
                        {
                            content.Add($"  {file.FileName}:");
                            content.Add($"    {file.Content}");
                        }
                    }
                }

                content.Add("");
            }

            return string.Join("\n", content);
        }
    }
}
