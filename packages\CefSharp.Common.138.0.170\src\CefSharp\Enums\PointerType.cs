// Copyright © 2019 The CefSharp Authors. All rights reserved.
//
// Use of this source code is governed by a BSD-style license that can be found in the LICENSE file.

namespace CefSharp.Enums
{
    /// <summary>
    /// The device type that caused the event.
    /// </summary>
    public enum PointerType
    {
        /// <summary>
        /// An enum constant representing the touch option.
        /// </summary>
        Touch = 0,
        /// <summary>
        /// An enum constant representing the mouse option.
        /// </summary>
        Mouse,
        /// <summary>
        /// An enum constant representing the pen option.
        /// </summary>
        Pen,
        /// <summary>
        /// An enum constant representing the eraser option.
        /// </summary>
        Eraser,
        /// <summary>
        /// An enum constant representing the unknown option.
        /// </summary>
        Unknown
    }
}
