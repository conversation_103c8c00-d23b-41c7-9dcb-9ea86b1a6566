using ET;
using ET.ETLoginWebBrowser;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// 标头管理窗体，用于显示当前标签页的HTTP请求标头（兼容Cookie显示）
    /// </summary>
    public partial class CookieManagerForm : Form
    {


        #region 字段和属性
        /// <summary>
        /// 标头管理器（兼容Cookie管理）
        /// </summary>
        readonly WebBrowserCookieManager _cookieManager;

        /// <summary>
        /// WebView2控件
        /// </summary>
        readonly WebView2 _webView;

        /// <summary>
        /// 用于记录的对象
        /// </summary>
        readonly object _logSource;
        #endregion

        #region 构造方法
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="cookieManager">标头管理器（兼容Cookie管理器）</param>
        /// <param name="webView">WebView2控件</param>
        /// <param name="logSource">用于记录日志的对象</param>
        public CookieManagerForm(WebBrowserCookieManager cookieManager, WebView2 webView, object logSource = null)
        {
            InitializeComponent();

            _cookieManager = cookieManager ?? throw new ArgumentNullException(nameof(cookieManager), "Cookie管理器不能为空");
            _webView = webView ?? throw new ArgumentNullException(nameof(webView), "WebView2控件不能为空");
            _logSource = logSource ?? this;

            // 设置窗体图标
            try
            {
                Icon = System.Drawing.Icon.ExtractAssociatedIcon(Application.ExecutablePath);
            }
            catch { }

            // 注册事件处理程序
            Load += CookieManagerForm_Load;

            // 注册WebView2的源变更事件
            _webView.SourceChanged += new EventHandler<CoreWebView2SourceChangedEventArgs>(WebView_SourceChanged);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // 取消注册事件
                if (_webView != null)
                {
                    _webView.SourceChanged -= new EventHandler<CoreWebView2SourceChangedEventArgs>(WebView_SourceChanged);
                }

                if (components != null)
                {
                    components.Dispose();
                }
            }
            base.Dispose(disposing);
        }
        #endregion

        #region 事件处理
        /// <summary>
        /// 窗体加载事件
        /// </summary>
        async void CookieManagerForm_Load(object sender, EventArgs e)
        {
            try
            {
                // 获取当前标签页的URL
                txtUrl.Text = _webView.Source?.ToString() ?? string.Empty;
                _cookieManager.CurrentUrl = txtUrl.Text;

                // 获取当前标签页的Cookie
                await GetCurrentCookiesAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                SetStatus($"加载Cookie失败: {ex.Message}", true);
                ETLogManager.Error(_logSource, $"加载Cookie失败: {ex.Message}");
            }
        }

        /// <summary>
        /// WebView源变更事件处理
        /// </summary>
        void WebView_SourceChanged(object sender, CoreWebView2SourceChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => WebView_SourceChanged(sender, e)));
                return;
            }

            try
            {
                // 更新URL并获取新的Cookie
                txtUrl.Text = _webView.Source?.ToString() ?? string.Empty;
                _cookieManager.CurrentUrl = txtUrl.Text;

                // 使用 Task.Run 来异步执行 GetCurrentCookiesAsync
                Task.Run(async () =>
                {
                    try
                    {
                        await GetCurrentCookiesAsync().ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"WebView源变更获取Cookie失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"WebView源变更处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 复制当前登录信息按钮点击事件
        /// </summary>
        async void BtnGetCookies_Click(object sender, EventArgs e)
        {
            try
            {
                await GetCurrentLoginInfoAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                SetStatus($"获取登录信息时发生错误: {ex.Message}", true);
                ETLogManager.Error(_logSource, $"获取登录信息时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新标头/Cookie按钮点击事件
        /// </summary>
        async void btnUpdateCookies_Click(object sender, EventArgs e)
        {
            try
            {
                SetStatus("正在更新标头数据...");

                // 解析文本框中的JSON
                string json = txtCookies.Text.Trim();
                if (string.IsNullOrEmpty(json))
                {
                    SetStatus("标头数据为空", true);
                    return;
                }

                try
                {
                    // 预处理JSON，移除所有尾部可能含有的额外字符
                    json = CleanJsonString(json);

                    // 首先尝试解析为新的完整登录信息格式（三部分格式）
                    try
                    {
                        var loginInfo = JsonConvert.DeserializeObject<dynamic>(json);
                        if (loginInfo != null && loginInfo.url != null && loginInfo.headers != null && loginInfo.cookies != null)
                        {
                            // 这是新的三部分格式，cookies字段现在直接是CookieItem数组
                            string cookiesArrayJson = JsonConvert.SerializeObject(loginInfo.cookies);
                            var cookieItems = JsonConvert.DeserializeObject<List<CookieItem>>(cookiesArrayJson);

                            if (cookieItems != null && cookieItems.Count > 0)
                            {
                                // 构造CookieData对象用于处理
                                var extractedCookieData = new CookieData
                                {
                                    Url = loginInfo.url.ToString(),
                                    Cookies = cookieItems
                                };

                                ETLogManager.Info(_logSource, $"检测到新的三部分登录信息格式，提取到{cookieItems.Count}个Cookie");
                                await ProcessCookieData(extractedCookieData);
                                return;
                            }
                        }
                    }
                    catch
                    {
                        // 不是三部分格式，继续尝试其他格式
                    }

                    // 其次尝试解析为老版本Cookie数据格式
                    CookieData cookieData = null;
                    try
                    {
                        cookieData = JsonConvert.DeserializeObject<CookieData>(json);
                    }
                    catch
                    {
                        // 忽略Cookie格式解析失败，继续尝试标头格式
                    }

                    if (cookieData != null && cookieData.Cookies != null && cookieData.Cookies.Count > 0)
                    {
                        // 处理老版本Cookie数据
                        ETLogManager.Info(_logSource, $"检测到老版本Cookie格式，包含{cookieData.Cookies.Count}个Cookie");
                        await ProcessCookieData(cookieData);
                        return;
                    }

                    // 最后尝试解析为简单标头数据格式
                    var headersData = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
                    if (headersData != null && headersData.Count > 0)
                    {
                        // 处理标头数据
                        ETLogManager.Info(_logSource, $"检测到标头数据格式，包含{headersData.Count}个标头项");
                        await ProcessHeadersData(headersData);
                        return;
                    }

                    SetStatus("无法解析登录信息、Cookie或标头数据", true);
                    return;
                }
                catch (JsonException ex)
                {
                    SetStatus($"JSON解析失败: {ex.Message}", true);
                    ETLogManager.Error(_logSource, $"JSON解析失败: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                SetStatus($"更新标头/Cookie失败: {ex.Message}", true);
                ETLogManager.Error(_logSource, $"更新标头/Cookie失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理Cookie数据（BtnGetCookies_Click的标准格式）
        /// </summary>
        /// <param name="cookieData">Cookie数据</param>
        private async Task ProcessCookieData(CookieData cookieData)
        {
            try
            {
                // 确保在UI线程上执行
                if (InvokeRequired)
                {
                    TaskCompletionSource<bool> tcs = new TaskCompletionSource<bool>();
                    Invoke(new Action(async () =>
                    {
                        try
                        {
                            await ProcessCookieDataInternal(cookieData);
                            tcs.SetResult(true);
                        }
                        catch (Exception ex)
                        {
                            tcs.SetException(ex);
                        }
                    }));
                    await tcs.Task;
                    return;
                }

                await ProcessCookieDataInternal(cookieData);
            }
            catch (Exception ex)
            {
                SetStatus($"处理Cookie数据失败: {ex.Message}", true);
                ETLogManager.Error(_logSource, $"处理Cookie数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 在UI线程上处理Cookie数据的内部方法
        /// </summary>
        /// <param name="cookieData">Cookie数据</param>
        private async Task ProcessCookieDataInternal(CookieData cookieData)
        {
            try
            {
                // 获取JSON中的URL
                string url = cookieData.Url;
                if (!string.IsNullOrEmpty(url))
                {
                    // 更新URL文本框
                    txtUrl.Text = url;
                    _cookieManager.CurrentUrl = url;
                }
                else
                {
                    // 如果JSON中没有URL，则使用当前标签页的URL
                    url = _webView.Source?.ToString() ?? string.Empty;
                    cookieData.Url = url;
                    if (!string.IsNullOrEmpty(url))
                    {
                        txtUrl.Text = url;
                        _cookieManager.CurrentUrl = url;
                    }
                }

                // 验证和修复Cookie数据
                FixCookieData(cookieData, url);

                // 设置Cookie到WebView2
                await _cookieManager.SetCookiesToWebView2Async(_webView, cookieData).ConfigureAwait(true);

                // 等待较长延迟确保Cookie设置生效
                await Task.Delay(500).ConfigureAwait(true);

                // 测试：验证Cookie设置是否有效（CookieManagerForm中的验证）
                await VerifyCookiesInForm(url, cookieData.Cookies.Count).ConfigureAwait(true);

                // 跳转到URL
                if (!string.IsNullOrEmpty(url))
                {
                    if (_webView.CoreWebView2 != null)
                    {
                        _webView.CoreWebView2.Navigate(url);
                        ETLogManager.Info(_logSource, $"手动导航到URL: {url}");
                    }
                }

                SetStatus($"已更新{cookieData.Cookies.Count}个Cookie并导航到URL");
            }
            catch (Exception ex)
            {
                SetStatus($"处理Cookie数据失败: {ex.Message}", true);
                ETLogManager.Error(_logSource, $"处理Cookie数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 修复Cookie数据中的Domain和Path问题
        /// </summary>
        /// <param name="cookieData">Cookie数据</param>
        /// <param name="url">目标URL</param>
        private void FixCookieData(CookieData cookieData, string url)
        {
            try
            {
                if (cookieData?.Cookies == null || string.IsNullOrEmpty(url))
                    return;

                Uri uri = new Uri(url);
                string host = uri.Host;

                foreach (var cookie in cookieData.Cookies)
                {
                    // 修复Domain
                    if (string.IsNullOrEmpty(cookie.Domain))
                    {
                        cookie.Domain = host;
                    }
                    else if (cookie.Domain.StartsWith("."))
                    {
                        // 确保Domain格式正确
                        if (!host.EndsWith(cookie.Domain.Substring(1)))
                        {
                            cookie.Domain = host;
                        }
                    }
                    else if (cookie.Domain != host && !host.EndsWith("." + cookie.Domain))
                    {
                        // Domain不匹配，使用当前host
                        cookie.Domain = host;
                    }

                    // 修复Path
                    if (string.IsNullOrEmpty(cookie.Path))
                    {
                        cookie.Path = "/";
                    }

                    ETLogManager.Info(_logSource, $"修复Cookie: {cookie.Name} -> Domain: {cookie.Domain}, Path: {cookie.Path}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(_logSource, $"修复Cookie数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理标头数据
        /// </summary>
        /// <param name="headersData">标头数据字典</param>
        private async Task ProcessHeadersData(Dictionary<string, string> headersData)
        {
            try
            {
                // 确保在UI线程上执行
                if (InvokeRequired)
                {
                    TaskCompletionSource<bool> tcs = new TaskCompletionSource<bool>();
                    Invoke(new Action(async () =>
                    {
                        try
                        {
                            await ProcessHeadersDataInternal(headersData);
                            tcs.SetResult(true);
                        }
                        catch (Exception ex)
                        {
                            tcs.SetException(ex);
                        }
                    }));
                    await tcs.Task;
                    return;
                }

                await ProcessHeadersDataInternal(headersData);
            }
            catch (Exception ex)
            {
                SetStatus($"处理标头数据失败: {ex.Message}", true);
                ETLogManager.Error(_logSource, $"处理标头数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 在UI线程上处理标头数据的内部方法
        /// </summary>
        /// <param name="headersData">标头数据字典</param>
        private async Task ProcessHeadersDataInternal(Dictionary<string, string> headersData)
        {
            try
            {
                // 获取目标URL - url字段就是页面URL
                string targetUrl = string.Empty;
                if (headersData.ContainsKey("url"))
                {
                    targetUrl = headersData["url"];
                    ETLogManager.Info(_logSource, $"使用url作为目标URL: {targetUrl}");
                }
                else if (headersData.ContainsKey("page-url"))
                {
                    targetUrl = headersData["page-url"];
                    ETLogManager.Info(_logSource, $"使用page-url作为目标URL: {targetUrl}");
                }

                // 如果还是没有URL，使用当前WebView的URL
                if (string.IsNullOrEmpty(targetUrl))
                {
                    targetUrl = _webView.Source?.ToString() ?? string.Empty;
                    if (!string.IsNullOrEmpty(targetUrl))
                    {
                        headersData["page-url"] = targetUrl;
                        ETLogManager.Info(_logSource, $"使用当前WebView URL: {targetUrl}");
                    }
                }

                if (!string.IsNullOrEmpty(targetUrl))
                {
                    // 更新URL文本框和Cookie管理器
                    txtUrl.Text = targetUrl;
                    _cookieManager.CurrentUrl = targetUrl;
                    _cookieManager.SetCurrentUrl(targetUrl);
                }

                // 处理Cookie数据 - 如果标头中包含Cookie
                if (headersData.ContainsKey("cookie"))
                {
                    string cookieHeader = headersData["cookie"];
                    if (!string.IsNullOrEmpty(cookieHeader))
                    {
                        ETLogManager.Info(_logSource, $"处理Cookie标头: {cookieHeader}");

                        // 将Cookie标头转换为CookieData格式并设置
                        var cookieData = ConvertCookieHeaderToCookieData(cookieHeader, targetUrl);
                        if (cookieData != null && cookieData.Cookies.Count > 0)
                        {
                            // 修复Cookie数据
                            FixCookieData(cookieData, targetUrl);

                            // 设置Cookie到WebView2
                            await _cookieManager.SetCookiesToWebView2Async(_webView, cookieData).ConfigureAwait(true);
                            ETLogManager.Info(_logSource, $"已设置{cookieData.Cookies.Count}个Cookie到WebView2");

                            // 等待Cookie设置生效
                            await Task.Delay(500).ConfigureAwait(true);

                            // 测试：验证标头中的Cookie设置是否有效
                            await VerifyCookiesInForm(targetUrl, cookieData.Cookies.Count).ConfigureAwait(true);
                        }
                    }
                }

                // 保存标头数据到文件
                // 通过SetCurrentUrl方法更新标头数据，这会自动设置_headersJson
                _cookieManager.SetCurrentUrl(targetUrl);

                // 注意：不再保存老格式的标头数据，只使用新格式

                // 跳转到目标URL
                if (!string.IsNullOrEmpty(targetUrl))
                {
                    if (_webView.CoreWebView2 != null)
                    {
                        // 先停止当前导航
                        try
                        {
                            _webView.CoreWebView2.Stop();
                        }
                        catch { }

                        // 导航到目标URL
                        _webView.CoreWebView2.Navigate(targetUrl);
                        ETLogManager.Info(_logSource, $"导航到目标URL: {targetUrl}");

                        SetStatus($"已更新标头数据（包含{headersData.Count}个标头项）并导航到: {targetUrl}");
                    }
                    else
                    {
                        SetStatus("WebView2未初始化，无法导航", true);
                    }
                }
                else
                {
                    SetStatus($"已更新标头数据（包含{headersData.Count}个标头项），但未找到有效URL");
                }
            }
            catch (Exception ex)
            {
                SetStatus($"处理标头数据失败: {ex.Message}", true);
                ETLogManager.Error(_logSource, $"处理标头数据失败: {ex.Message}");
            }
        }



        /// <summary>
        /// 在CookieManagerForm中验证Cookie设置是否有效
        /// </summary>
        /// <param name="targetUrl">目标URL</param>
        /// <param name="expectedCookieCount">期望的Cookie数量</param>
        private async Task VerifyCookiesInForm(string targetUrl, int expectedCookieCount)
        {
            try
            {
                if (_webView?.CoreWebView2 == null || string.IsNullOrEmpty(targetUrl))
                {
                    ETLogManager.Warning(_logSource, "无法验证Cookie设置：WebView2或URL为空");
                    return;
                }

                // 等待一小段时间确保Cookie设置生效
                await Task.Delay(300).ConfigureAwait(true);

                // 获取当前URL的所有Cookie
                var currentCookies = await _webView.CoreWebView2.CookieManager.GetCookiesAsync(targetUrl).ConfigureAwait(true);

                ETLogManager.Info(_logSource, $"=== CookieManagerForm Cookie验证 ===");
                ETLogManager.Info(_logSource, $"目标URL: {targetUrl}");
                ETLogManager.Info(_logSource, $"期望Cookie数量: {expectedCookieCount}");
                ETLogManager.Info(_logSource, $"实际Cookie数量: {currentCookies.Count}");

                if (currentCookies.Count > 0)
                {
                    ETLogManager.Info(_logSource, "实际设置的Cookie列表:");
                    foreach (var cookie in currentCookies)
                    {
                        ETLogManager.Info(_logSource, $"  ✓ {cookie.Name}={cookie.Value} (Domain: {cookie.Domain}, Path: {cookie.Path})");
                    }

                    if (currentCookies.Count >= expectedCookieCount)
                    {
                        ETLogManager.Info(_logSource, "✅ Cookie设置验证成功！");
                    }
                    else
                    {
                        ETLogManager.Warning(_logSource, $"⚠️ Cookie数量不匹配：期望{expectedCookieCount}个，实际{currentCookies.Count}个");
                    }
                }
                else
                {
                    ETLogManager.Error(_logSource, "❌ 严重错误：WebView2中没有找到任何Cookie！");
                }

                ETLogManager.Info(_logSource, "=== Cookie验证完成 ===");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"验证Cookie设置时出错: {ex.Message}");
            }
        }



        /// <summary>
        /// 将Cookie标头转换为CookieData格式
        /// </summary>
        /// <param name="cookieHeader">Cookie标头字符串</param>
        /// <param name="url">URL</param>
        /// <returns>CookieData对象</returns>
        private CookieData ConvertCookieHeaderToCookieData(string cookieHeader, string url)
        {
            try
            {
                var cookieData = new CookieData
                {
                    Url = url,
                    Cookies = new List<CookieItem>()
                };

                if (string.IsNullOrEmpty(cookieHeader))
                    return cookieData;

                // 解析Cookie标头
                var cookiePairs = cookieHeader.Split(';');
                foreach (var pair in cookiePairs)
                {
                    var trimmedPair = pair.Trim();
                    if (string.IsNullOrEmpty(trimmedPair))
                        continue;

                    var equalIndex = trimmedPair.IndexOf('=');
                    if (equalIndex > 0)
                    {
                        var name = trimmedPair.Substring(0, equalIndex).Trim();
                        var value = trimmedPair.Substring(equalIndex + 1).Trim();

                        var cookieItem = new CookieItem
                        {
                            Name = name,
                            Value = value,
                            Domain = !string.IsNullOrEmpty(url) ? new Uri(url).Host : string.Empty,
                            Path = "/"
                        };

                        cookieData.Cookies.Add(cookieItem);
                    }
                }

                return cookieData;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"转换Cookie标头失败: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region 辅助方法
        /// <summary>
        /// 清理JSON字符串，移除可能导致解析错误的额外字符
        /// </summary>
        string CleanJsonString(string json)
        {
            try
            {
                // 尝试验证是否为有效的JSON
                JsonConvert.DeserializeObject(json);
                return json;
            }
            catch (JsonException)
            {
                // 如果解析失败，尝试清理字符串
                try
                {
                    // 找到最后一个大括号
                    int lastBrace = json.LastIndexOf('}');
                    if (lastBrace > 0 && lastBrace < json.Length - 1)
                    {
                        // 只保留到最后一个大括号的内容
                        return json.Substring(0, lastBrace + 1);
                    }
                }
                catch
                {
                    // 如果清理过程出错，返回原始字符串
                }
                return json;
            }
        }

        /// <summary>
        /// 获取当前登录信息（包含URL、标头信息、Cookie数据三个部分）
        /// </summary>
        async Task GetCurrentLoginInfoAsync()
        {
            try
            {
                SetStatus("正在获取完整登录信息...");

                // 获取当前URL
                string currentUrl = _webView.Source?.ToString() ?? string.Empty;
                if (string.IsNullOrEmpty(currentUrl))
                {
                    SetStatus("当前URL为空，无法获取登录信息", true);
                    return;
                }

                // 使用通用模块直接获取新格式的登录信息
                var options = new ETWebBrowserJsonFormatter.HeadersOptions
                {
                    Source = "CookieManagerForm",
                    RequestType = "Page"
                };

                string loginInfoJson = await ETWebBrowserJsonFormatter.CreateLoginInfoJsonFromWebViewAsync(
                    _webView,
                    null, // 不传递额外标头，让通用模块自动处理
                    options).ConfigureAwait(false);

                // 解析JSON以获取统计信息
                var loginInfo = ETWebBrowserJsonFormatter.ParseLoginInfoJson(loginInfoJson);

                // 确保在UI线程更新UI元素
                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        txtCookies.Text = loginInfoJson;
                        SetStatus($"已获取完整登录信息：URL + {loginInfo.Headers?.Count ?? 0}个标头 + {loginInfo.Cookies?.Count ?? 0}个Cookie");
                    }));
                }
                else
                {
                    txtCookies.Text = loginInfoJson;
                    SetStatus($"已获取完整登录信息：URL + {loginInfo.Headers?.Count ?? 0}个标头 + {loginInfo.Cookies?.Count ?? 0}个Cookie");
                }
            }
            catch (Exception ex)
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => SetStatus($"获取登录信息失败: {ex.Message}", true)));
                }
                else
                {
                    SetStatus($"获取登录信息失败: {ex.Message}", true);
                }
                ETLogManager.Error(_logSource, $"获取登录信息失败: {ex.Message}");
            }
        }



        /// <summary>
        /// 获取当前标签页的Cookie数据（显示新格式数据）
        /// </summary>
        async Task GetCurrentCookiesAsync()
        {
            try
            {
                SetStatus("正在获取登录信息...");

                // 直接调用GetCurrentLoginInfoAsync来获取新格式数据
                await GetCurrentLoginInfoAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => SetStatus($"获取登录信息失败: {ex.Message}", true)));
                }
                else
                {
                    SetStatus($"获取登录信息失败: {ex.Message}", true);
                }
                ETLogManager.Error(_logSource, $"获取登录信息失败: {ex.Message}");
            }
        }









        /// <summary>
        /// 设置状态栏信息
        /// </summary>
        void SetStatus(string message, bool isError = false)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => SetStatus(message, isError)));
                return;
            }

            lblStatus.Text = message;
            lblStatus.ForeColor = isError ? Color.Red : SystemColors.ControlText;
        }
        /// <summary>
        /// 将老格式Cookie数据转换为新格式（使用通用模块）
        /// </summary>
        /// <param name="oldData">老格式Cookie数据</param>
        /// <returns>新格式JSON字符串</returns>
        private string ConvertOldFormatToNew(CookieData oldData)
        {
            try
            {
                var options = new ETWebBrowserJsonFormatter.HeadersOptions
                {
                    Source = "CookieManagerForm",
                    RequestType = "Page"
                };

                // 转换HyAssistant.CookieData为ET.ETLoginWebBrowser.CookieData
                var etCookieData = ConvertToETCookieData(oldData);
                return ETWebBrowserJsonFormatter.CreateLoginInfoJson(
                    etCookieData.Url,
                    null, // headers参数，ET.ETLoginWebBrowser.CookieData没有Headers属性
                    etCookieData.Cookies,
                    options);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"转换老格式数据失败: {ex.Message}");

                // 使用通用模块的错误处理
                var etCookieItems = ConvertToETCookieItems(oldData?.Cookies);
                return ETWebBrowserJsonFormatter.CreateLoginInfoJson(
                    oldData?.Url ?? string.Empty,
                    new Dictionary<string, string> { ["error"] = $"转换失败: {ex.Message}" },
                    etCookieItems,
                    new ETWebBrowserJsonFormatter.HeadersOptions { Source = "CookieManagerForm" });
            }
        }

        /// <summary>
        /// 转换HyAssistant.CookieData为ET.ETLoginWebBrowser.CookieData
        /// </summary>
        /// <param name="hyData">HyAssistant的CookieData</param>
        /// <returns>ET的CookieData</returns>
        private ET.ETLoginWebBrowser.CookieData ConvertToETCookieData(CookieData hyData)
        {
            if (hyData == null) return null;

            return new ET.ETLoginWebBrowser.CookieData
            {
                Url = hyData.Url,
                Cookies = ConvertToETCookieItems(hyData.Cookies)
            };
        }

        /// <summary>
        /// 转换HyAssistant.CookieItem列表为ET.ETLoginWebBrowser.CookieItem列表
        /// </summary>
        /// <param name="hyItems">HyAssistant的CookieItem列表</param>
        /// <returns>ET的CookieItem列表</returns>
        private List<ET.ETLoginWebBrowser.CookieItem> ConvertToETCookieItems(List<CookieItem> hyItems)
        {
            if (hyItems == null) return new List<ET.ETLoginWebBrowser.CookieItem>();

            var etItems = new List<ET.ETLoginWebBrowser.CookieItem>();
            foreach (var hyItem in hyItems)
            {
                etItems.Add(new ET.ETLoginWebBrowser.CookieItem
                {
                    Name = hyItem.Name,
                    Value = hyItem.Value,
                    Domain = hyItem.Domain,
                    Path = hyItem.Path,
                    Expires = hyItem.Expires,
                    HttpOnly = hyItem.HttpOnly,
                    Secure = hyItem.Secure,
                    SameSite = hyItem.SameSite
                });
            }
            return etItems;
        }

        #endregion

        void btnCopy_Click(object sender, EventArgs e)
        {
            //把txtCookies的值复制到剪贴板
            Clipboard.SetText(txtCookies.Text);
        }

        void 粘贴板内容到Cookies_Click(object sender, EventArgs e)
        {
            //把剪贴板内容写入txtCookies（支持标头数据和Cookie数据）
            HandlePasteOperation();
            //触发btnUpdateCookies_Click
            btnUpdateCookies_Click(sender, e);
        }

        #region Cookie文件处理
        /// <summary>
        /// 处理粘贴操作
        /// </summary>
        void HandlePasteOperation()
        {
            try
            {
                // 检查剪贴板中是否有文件
                if (Clipboard.ContainsFileDropList())
                {
                    StringCollection filePaths = Clipboard.GetFileDropList();

                    // 只处理第一个.dat文件
                    foreach (string filePath in filePaths)
                    {
                        if (Path.GetExtension(filePath).Equals(".dat", StringComparison.OrdinalIgnoreCase))
                        {
                            try
                            {
                                // 读取文件内容
                                string fileContent = File.ReadAllText(filePath);

                                // 验证是否为有效的JSON并转换为新格式
                                try
                                {
                                    // 预处理JSON字符串
                                    fileContent = CleanJsonString(fileContent);

                                    // 使用通用模块验证和转换格式
                                    if (ETWebBrowserJsonFormatter.IsStandardFormat(fileContent))
                                    {
                                        // 已经是新格式，直接显示
                                        txtCookies.Text = fileContent;
                                        SetStatus("成功从文件加载登录信息数据（新格式）");
                                        return;
                                    }
                                    else
                                    {
                                        // 尝试解析为老格式并转换为新格式
                                        try
                                        {
                                            var oldFormatData = JsonConvert.DeserializeObject<CookieData>(fileContent);
                                            if (oldFormatData != null)
                                            {
                                                // 使用通用模块转换为新格式
                                                string newFormatJson = ConvertOldFormatToNew(oldFormatData);
                                                txtCookies.Text = newFormatJson;
                                                SetStatus("成功从文件加载Cookie数据并转换为新格式");
                                                return;
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            ETLogManager.Error(_logSource, $"解析老格式数据失败: {ex.Message}");
                                        }
                                    }
                                }
                                catch (JsonException ex)
                                {
                                    ETLogManager.Error(_logSource, $"无效的JSON格式: {ex.Message}");
                                    return;
                                }
                            }
                            catch (Exception ex)
                            {
                                ETLogManager.Error(_logSource, $"读取文件失败: {ex.Message}");
                                return;
                            }
                        }
                    }
                }

                // 如果没有文件或没有找到有效的.dat文件，执行默认的文本粘贴
                if (Clipboard.ContainsText())
                {
                    string clipboardText = Clipboard.GetText();
                    // 尝试清理并验证JSON
                    try
                    {
                        clipboardText = CleanJsonString(clipboardText);

                        // 验证是否为有效的JSON格式（支持多种格式）
                        bool isValidFormat = false;
                        string formatType = "";

                        // 尝试验证新的三部分格式
                        try
                        {
                            var loginInfo = JsonConvert.DeserializeObject<dynamic>(clipboardText);
                            if (loginInfo != null && loginInfo.url != null && loginInfo.headers != null && loginInfo.cookies != null)
                            {
                                isValidFormat = true;
                                formatType = "三部分登录信息格式";
                            }
                        }
                        catch { }

                        // 如果不是三部分格式，尝试验证老版本Cookie格式
                        if (!isValidFormat)
                        {
                            try
                            {
                                var cookieData = JsonConvert.DeserializeObject<CookieData>(clipboardText);
                                if (cookieData != null && cookieData.Cookies != null)
                                {
                                    isValidFormat = true;
                                    formatType = "Cookie数据格式";
                                }
                            }
                            catch { }
                        }

                        // 如果不是Cookie格式，尝试验证标头格式
                        if (!isValidFormat)
                        {
                            try
                            {
                                var headersData = JsonConvert.DeserializeObject<Dictionary<string, string>>(clipboardText);
                                if (headersData != null && headersData.Count > 0)
                                {
                                    isValidFormat = true;
                                    formatType = "标头数据格式";
                                }
                            }
                            catch { }
                        }

                        if (isValidFormat)
                        {
                            txtCookies.Text = clipboardText;
                            SetStatus($"已粘贴有效的{formatType}数据");
                            ETLogManager.Info(_logSource, $"粘贴成功：{formatType}");
                        }
                        else
                        {
                            throw new InvalidOperationException("剪贴板内容不是支持的JSON格式");
                        }
                    }
                    catch (Exception ex)
                    {
                        // 如果不是有效的JSON，直接报错
                        SetStatus($"剪贴板内容不是有效的JSON格式: {ex.Message}", true);
                        ETLogManager.Error(_logSource, $"剪贴板JSON验证失败: {ex.Message}");
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"处理粘贴操作失败: {ex.Message}");
                MessageBox.Show($"处理粘贴操作失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 生成临时Cookie文件并复制到剪贴板
        /// </summary>
        void button复制配置文件到剪贴板_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证JSON格式（使用通用模块支持新旧格式）
                try
                {
                    // 预处理JSON字符串
                    string json = CleanJsonString(txtCookies.Text);

                    // 使用通用模块验证格式
                    bool isValidFormat = false;

                    // 验证新格式
                    if (ETWebBrowserJsonFormatter.IsStandardFormat(json))
                    {
                        isValidFormat = true;
                    }
                    else
                    {
                        // 验证老格式
                        try
                        {
                            JsonConvert.DeserializeObject<CookieData>(json);
                            isValidFormat = true;
                        }
                        catch
                        {
                            // 不是有效的老格式
                        }
                    }

                    if (!isValidFormat)
                    {
                        MessageBox.Show("当前内容不是有效的登录信息JSON格式", "格式错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // 如果验证通过，可能需要更新文本框内容
                    if (json != txtCookies.Text)
                    {
                        txtCookies.Text = json;
                    }
                }
                catch (JsonException ex)
                {
                    MessageBox.Show($"当前内容不是有效的JSON格式: {ex.Message}", "格式错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 确保缓存目录存在
                string webViewCacheFolder = Path.Combine(
                    Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location),
                    WebBrowserConstants.WEBVIEW_CACHE_FOLDER);
                Directory.CreateDirectory(webViewCacheFolder);

                // 生成临时文件路径
                string tempFilePath = Path.Combine(webViewCacheFolder, "cookies.dat");

                // 写入文件
                File.WriteAllText(tempFilePath, txtCookies.Text);

                // 复制文件到剪贴板
                ETFile.FileCopyToClipboard(tempFilePath);

                SetStatus("Cookie配置文件已复制到剪贴板");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"复制配置文件失败: {ex.Message}");
                MessageBox.Show($"复制配置文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        void TxtCookies_KeyDown(object sender, KeyEventArgs e)
        {
            // 检查是否按下了Ctrl+V
            if (e.Control && e.KeyCode == Keys.V)
            {
                HandlePasteOperation();
                e.Handled = true;
            }
        }


        void TxtCookies_DragEnter(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (files.Any(f => Path.GetExtension(f).Equals(".dat", StringComparison.OrdinalIgnoreCase)))
                {
                    e.Effect = DragDropEffects.Copy;
                    return;
                }
            }
            e.Effect = DragDropEffects.None;
        }
        #endregion

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);

            // 注册txtCookies的事件处理程序
            txtCookies.KeyDown += TxtCookies_KeyDown;
            txtCookies.DragDrop += TxtCookies_DragDrop;
            txtCookies.DragEnter += TxtCookies_DragEnter;
            txtCookies.AllowDrop = true;
        }
        void TxtCookies_DragDrop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                foreach (string file in files)
                {
                    if (Path.GetExtension(file).Equals(".dat", StringComparison.OrdinalIgnoreCase))
                    {
                        try
                        {
                            string content = File.ReadAllText(file);
                            // 验证JSON格式并清理
                            content = CleanJsonString(content);
                            JsonConvert.DeserializeObject<CookieData>(content);
                            txtCookies.Text = content;
                            SetStatus("成功从拖放文件加载Cookie数据");
                            break;
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"读取文件失败或格式无效: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
        }
    }
}