using ET;
using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace HyAssistant.WebBrowserV2.Utils
{
    /// <summary>
    /// WebBrowserV2版本统一日志管理器
    /// </summary>
    /// <remarks>
    /// V2版本优化：
    /// 1. 统一管理所有WebBrowser模块的日志记录
    /// 2. 提供性能监控和统计功能
    /// 3. 支持日志级别控制和过滤
    /// 4. 集成ETLogManager，确保日志记录的一致性
    /// 5. 提供异步日志记录支持，避免阻塞主线程
    /// </remarks>
    public class WebBrowserLoggingManagerV2 : IDisposable
    {
        #region 私有字段

        /// <summary>
        /// 单例实例
        /// </summary>
        private static readonly Lazy<WebBrowserLoggingManagerV2> _instance = 
            new Lazy<WebBrowserLoggingManagerV2>(() => new WebBrowserLoggingManagerV2());

        /// <summary>
        /// 日志统计信息
        /// </summary>
        private readonly ConcurrentDictionary<string, LogStatistics> _logStatistics = 
            new ConcurrentDictionary<string, LogStatistics>();

        /// <summary>
        /// 性能监控数据
        /// </summary>
        private readonly ConcurrentDictionary<string, PerformanceData> _performanceData = 
            new ConcurrentDictionary<string, PerformanceData>();

        /// <summary>
        /// 是否已释放资源
        /// </summary>
        private bool _disposed = false;

        /// <summary>
        /// 日志级别
        /// </summary>
        private LogLevel _currentLogLevel = LogLevel.Info;

        #endregion 私有字段

        #region 公共属性

        /// <summary>
        /// 获取单例实例
        /// </summary>
        public static WebBrowserLoggingManagerV2 Instance => _instance.Value;

        /// <summary>
        /// 当前日志级别
        /// </summary>
        public LogLevel CurrentLogLevel
        {
            get => _currentLogLevel;
            set => _currentLogLevel = value;
        }

        #endregion 公共属性

        #region 构造函数

        /// <summary>
        /// 私有构造函数（单例模式）
        /// </summary>
        private WebBrowserLoggingManagerV2()
        {
            try
            {
                ETLogManager.Info(this, "WebBrowserLoggingManagerV2初始化完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"WebBrowserLoggingManagerV2初始化失败: {ex.Message}");
            }
        }

        #endregion 构造函数

        #region 基础日志方法

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="source">日志源</param>
        /// <param name="message">日志消息</param>
        public void LogInfo(object source, string message)
        {
            if (_disposed || _currentLogLevel > LogLevel.Info) return;

            try
            {
                ETLogManager.Info(source, message);
                UpdateLogStatistics(source, LogLevel.Info);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"记录Info日志失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="source">日志源</param>
        /// <param name="message">日志消息</param>
        public void LogWarning(object source, string message)
        {
            if (_disposed || _currentLogLevel > LogLevel.Warning) return;

            try
            {
                ETLogManager.Warning(source, message);
                UpdateLogStatistics(source, LogLevel.Warning);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"记录Warning日志失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="source">日志源</param>
        /// <param name="message">日志消息</param>
        public void LogError(object source, string message)
        {
            if (_disposed || _currentLogLevel > LogLevel.Error) return;

            try
            {
                ETLogManager.Error(source, message);
                UpdateLogStatistics(source, LogLevel.Error);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"记录Error日志失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="source">日志源</param>
        /// <param name="message">日志消息</param>
        public void LogDebug(object source, string message)
        {
            if (_disposed || _currentLogLevel > LogLevel.Debug) return;

            try
            {
                ETLogManager.Debug(source, message);
                UpdateLogStatistics(source, LogLevel.Debug);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"记录Debug日志失败: {ex.Message}");
            }
        }

        #endregion 基础日志方法

        #region 异常日志方法

        /// <summary>
        /// 记录异常日志
        /// </summary>
        /// <param name="source">日志源</param>
        /// <param name="ex">异常对象</param>
        /// <param name="operation">操作描述</param>
        /// <param name="includeStackTrace">是否包含堆栈跟踪</param>
        public void LogException(object source, Exception ex, string operation, bool includeStackTrace = false)
        {
            if (_disposed) return;

            try
            {
                string message = $"{operation}时发生异常: {ex.Message}";
                ETLogManager.Error(source, message);

                if (includeStackTrace || IsCriticalException(ex))
                {
                    ETLogManager.Error(source, $"异常堆栈: {ex.StackTrace}");
                }

                UpdateLogStatistics(source, LogLevel.Error);
            }
            catch (Exception logEx)
            {
                Debug.WriteLine($"记录异常日志失败: {logEx.Message}");
            }
        }

        /// <summary>
        /// 记录异步异常日志
        /// </summary>
        /// <param name="source">日志源</param>
        /// <param name="ex">异常对象</param>
        /// <param name="operation">异步操作描述</param>
        public void LogAsyncException(object source, Exception ex, string operation)
        {
            if (_disposed) return;

            try
            {
                string message = $"异步操作[{operation}]异常: {ex.Message}";
                ETLogManager.Error(source, message);

                // 异步异常的特殊处理
                HandleSpecialAsyncExceptions(source, ex, operation);

                UpdateLogStatistics(source, LogLevel.Error);
            }
            catch (Exception logEx)
            {
                Debug.WriteLine($"记录异步异常日志失败: {logEx.Message}");
            }
        }

        #endregion 异常日志方法

        #region 性能监控方法

        /// <summary>
        /// 开始性能监控
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <returns>性能监控令牌</returns>
        public PerformanceToken StartPerformanceMonitoring(string operation)
        {
            return new PerformanceToken(this, operation);
        }

        /// <summary>
        /// 记录性能数据
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="elapsedMs">耗时（毫秒）</param>
        /// <param name="source">日志源</param>
        internal void RecordPerformance(string operation, long elapsedMs, object source = null)
        {
            if (_disposed) return;

            try
            {
                // 更新性能统计
                _performanceData.AddOrUpdate(operation, 
                    new PerformanceData { TotalCalls = 1, TotalTime = elapsedMs, MaxTime = elapsedMs, MinTime = elapsedMs },
                    (key, existing) => new PerformanceData
                    {
                        TotalCalls = existing.TotalCalls + 1,
                        TotalTime = existing.TotalTime + elapsedMs,
                        MaxTime = Math.Max(existing.MaxTime, elapsedMs),
                        MinTime = Math.Min(existing.MinTime, elapsedMs)
                    });

                // 记录性能日志
                string level = elapsedMs > 5000 ? "性能警告" : "性能信息";
                string message = $"[{level}] {operation} 耗时: {elapsedMs}ms";

                if (elapsedMs > 5000)
                {
                    LogWarning(source ?? this, message);
                }
                else if (_currentLogLevel <= LogLevel.Debug)
                {
                    LogDebug(source ?? this, message);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"记录性能数据失败: {ex.Message}");
            }
        }

        #endregion 性能监控方法

        #region 统计和报告方法

        /// <summary>
        /// 获取日志统计信息
        /// </summary>
        /// <returns>日志统计信息</returns>
        public string GetLogStatistics()
        {
            if (_disposed) return "日志管理器已释放";

            try
            {
                var report = new System.Text.StringBuilder();
                report.AppendLine("=== WebBrowser日志统计 ===");

                foreach (var kvp in _logStatistics)
                {
                    var stats = kvp.Value;
                    report.AppendLine($"{kvp.Key}:");
                    report.AppendLine($"  Info: {stats.InfoCount}, Warning: {stats.WarningCount}, Error: {stats.ErrorCount}, Debug: {stats.DebugCount}");
                }

                return report.ToString();
            }
            catch (Exception ex)
            {
                return $"获取日志统计失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        /// <returns>性能统计信息</returns>
        public string GetPerformanceStatistics()
        {
            if (_disposed) return "日志管理器已释放";

            try
            {
                var report = new System.Text.StringBuilder();
                report.AppendLine("=== WebBrowser性能统计 ===");

                foreach (var kvp in _performanceData)
                {
                    var perf = kvp.Value;
                    var avgTime = perf.TotalCalls > 0 ? perf.TotalTime / perf.TotalCalls : 0;
                    report.AppendLine($"{kvp.Key}:");
                    report.AppendLine($"  调用次数: {perf.TotalCalls}, 平均耗时: {avgTime}ms, 最大耗时: {perf.MaxTime}ms, 最小耗时: {perf.MinTime}ms");
                }

                return report.ToString();
            }
            catch (Exception ex)
            {
                return $"获取性能统计失败: {ex.Message}";
            }
        }

        #endregion 统计和报告方法

        #region 辅助方法

        /// <summary>
        /// 更新日志统计信息
        /// </summary>
        /// <param name="source">日志源</param>
        /// <param name="level">日志级别</param>
        private void UpdateLogStatistics(object source, LogLevel level)
        {
            try
            {
                string sourceName = source?.GetType().Name ?? "Unknown";
                _logStatistics.AddOrUpdate(sourceName,
                    new LogStatistics { InfoCount = level == LogLevel.Info ? 1 : 0, WarningCount = level == LogLevel.Warning ? 1 : 0, ErrorCount = level == LogLevel.Error ? 1 : 0, DebugCount = level == LogLevel.Debug ? 1 : 0 },
                    (key, existing) => new LogStatistics
                    {
                        InfoCount = existing.InfoCount + (level == LogLevel.Info ? 1 : 0),
                        WarningCount = existing.WarningCount + (level == LogLevel.Warning ? 1 : 0),
                        ErrorCount = existing.ErrorCount + (level == LogLevel.Error ? 1 : 0),
                        DebugCount = existing.DebugCount + (level == LogLevel.Debug ? 1 : 0)
                    });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"更新日志统计失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否为严重异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <returns>是否为严重异常</returns>
        private bool IsCriticalException(Exception ex)
        {
            return ex is OutOfMemoryException ||
                   ex is StackOverflowException ||
                   ex is AccessViolationException ||
                   ex is AppDomainUnloadedException ||
                   ex is BadImageFormatException ||
                   ex is CannotUnloadAppDomainException ||
                   ex is InvalidProgramException ||
                   ex is System.Threading.ThreadAbortException;
        }

        /// <summary>
        /// 处理特殊的异步异常
        /// </summary>
        /// <param name="source">日志源</param>
        /// <param name="ex">异常对象</param>
        /// <param name="operation">操作描述</param>
        private void HandleSpecialAsyncExceptions(object source, Exception ex, string operation)
        {
            try
            {
                if (ex is TaskCanceledException)
                {
                    ETLogManager.Warning(source, $"异步操作[{operation}]被取消");
                }
                else if (ex is TimeoutException)
                {
                    ETLogManager.Warning(source, $"异步操作[{operation}]超时");
                }
                else if (ex is AggregateException aggEx)
                {
                    foreach (var innerEx in aggEx.InnerExceptions)
                    {
                        ETLogManager.Error(source, $"聚合异常内部异常: {innerEx.Message}");
                    }
                }
            }
            catch (Exception logEx)
            {
                Debug.WriteLine($"处理特殊异步异常失败: {logEx.Message}");
            }
        }

        #endregion 辅助方法

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        ETLogManager.Info(this, "WebBrowserLoggingManagerV2资源释放完成");

                        // 清理统计数据
                        _logStatistics.Clear();
                        _performanceData.Clear();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"释放WebBrowserLoggingManagerV2资源失败: {ex.Message}");
                    }
                }

                _disposed = true;
            }
        }

        #endregion IDisposable实现
    }

    #region 辅助数据结构

    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        Debug = 0,
        Info = 1,
        Warning = 2,
        Error = 3
    }

    /// <summary>
    /// 日志统计信息
    /// </summary>
    public class LogStatistics
    {
        public int InfoCount { get; set; }
        public int WarningCount { get; set; }
        public int ErrorCount { get; set; }
        public int DebugCount { get; set; }
    }

    /// <summary>
    /// 性能数据
    /// </summary>
    public class PerformanceData
    {
        public int TotalCalls { get; set; }
        public long TotalTime { get; set; }
        public long MaxTime { get; set; }
        public long MinTime { get; set; }
    }

    /// <summary>
    /// 性能监控令牌
    /// </summary>
    public class PerformanceToken : IDisposable
    {
        private readonly WebBrowserLoggingManagerV2 _manager;
        private readonly string _operation;
        private readonly Stopwatch _stopwatch;
        private readonly object _source;
        private bool _disposed = false;

        internal PerformanceToken(WebBrowserLoggingManagerV2 manager, string operation, object source = null)
        {
            _manager = manager;
            _operation = operation;
            _source = source;
            _stopwatch = Stopwatch.StartNew();
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _stopwatch.Stop();
                _manager.RecordPerformance(_operation, _stopwatch.ElapsedMilliseconds, _source);
                _disposed = true;
            }
        }
    }

    #endregion 辅助数据结构
}
