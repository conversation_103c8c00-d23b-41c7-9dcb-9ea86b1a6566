using ET;
using ET.ETLoginWebBrowser;
using HyAssistant.WebBrowserV2.Utils;
using Microsoft.Web.WebView2.WinForms;
using Microsoft.Web.WebView2.Core;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Collections.Generic;

namespace HyAssistant.WebBrowserV2.Core
{
    /// <summary>
    /// WebBrowserV2 Cookie操作相关方法 (partial class)
    /// 包含传送标签页、粘贴标签页等Cookie相关功能
    /// </summary>
    public partial class WebBrowserV2
    {
        #region Cookie配置生成和复制

        /// <summary>
        /// 生成配置并复制到剪贴板
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        private async Task GenerateAndCopyConfigurationAsync(WebView2 webView, WebBrowserTabConfig config)
        {
            try
            {
                ETLogManager.Info(this, "开始生成Cookie配置");

                // 使用CookieTransferManagerV2获取数据
                var cookieTransferManager = new CookieTransferManagerV2(this);
                var cookieData = await cookieTransferManager.GetCookieDataAsync(webView, config);

                // 获取请求标头信息
                var headers = _cookieManagerV2.GetLastRequestHeaders();

                // 创建兼容V1格式的LoginInfo对象
                var loginInfo = new ETWebBrowserJsonFormatter.LoginInfoData
                {
                    Url = webView.Source?.ToString() ?? config.Url,
                    Cookies = ConvertV1CookiesToET(cookieData.Cookies),
                    Headers = ConvertHeadersToObject(headers)
                };

                // 生成JSON配置字符串
                string loginInfoJson = JsonConvert.SerializeObject(loginInfo, Formatting.Indented);

                // 创建临时文件路径（保持与V1版本一致）
                string webViewCacheFolder = Path.Combine(
                    Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location),
                    WebBrowserConstants.WEBVIEW_CACHE_FOLDER);

                Directory.CreateDirectory(webViewCacheFolder);
                string tempFilePath = Path.Combine(webViewCacheFolder, "cookies.dat");

                // 写入文件
                File.WriteAllText(tempFilePath, loginInfoJson);

                // 复制文件到剪贴板（保持与V1版本一致）
                ETFile.FileCopyToClipboard(tempFilePath);

                // 显示成功消息
                string successMessage = $"登录信息配置文件已复制到剪贴板 ({loginInfo.Cookies.Count}个Cookie, {loginInfo.Headers.Count}个标头)";
                UpdateStatusSafely(successMessage);
                ETLogManager.Info(this, successMessage);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"生成配置文件失败: {ex.Message}");
                throw;
            }
        }

        #endregion Cookie配置生成和复制

        #region 剪贴板数据处理

        /// <summary>
        /// 处理剪贴板数据
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        private async Task ProcessClipboardDataAsync(WebView2 webView)
        {
            try
            {
                string clipboardText = Clipboard.GetText()?.Trim();
                if (string.IsNullOrEmpty(clipboardText))
                {
                    ShowInfoMessage("剪贴板内容为空");
                    return;
                }

                // 清理JSON字符串
                clipboardText = CleanJsonStringV2(clipboardText);

                // 尝试解析Cookie数据
                CookieData cookieData = await ParseCookieDataAsync(clipboardText);
                if (cookieData == null)
                {
                    ShowWarningMessage("剪贴板中的数据不是有效的Cookie格式");
                    return;
                }

                // 确保URL存在
                string targetUrl = cookieData.Url;
                if (string.IsNullOrEmpty(targetUrl))
                {
                    targetUrl = webView.Source?.ToString() ?? "about:blank";
                    cookieData.Url = targetUrl;
                }

                // 设置Cookie到WebView2
                await SetCookiesToWebViewAsync(webView, cookieData);

                // 导航到目标URL
                await NavigateToUrlAsync(webView, targetUrl);

                // 更新地址栏
                UpdateAddressBarSafely(targetUrl);

                // 显示成功消息
                string successMessage = $"已从剪贴板粘贴{cookieData.Cookies.Count}个Cookie并导航到URL";
                UpdateStatusSafely(successMessage);
                ETLogManager.Info(this, successMessage);
            }
            catch (JsonException ex)
            {
                ETLogManager.Error(this, $"解析Cookie数据失败: {ex.Message}");
                ShowErrorMessage($"解析Cookie数据失败: {ex.Message}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"处理剪贴板数据失败: {ex.Message}");
                throw;
            }
        }



        /// <summary>
        /// 解析Cookie数据
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>Cookie数据对象</returns>
        private Task<CookieData> ParseCookieDataAsync(string json)
        {
            try
            {
                // 首先尝试解析为CookieData格式
                var cookieData = JsonConvert.DeserializeObject<CookieData>(json);
                if (cookieData?.Cookies != null && cookieData.Cookies.Count > 0)
                {
                    return Task.FromResult(cookieData);
                }

                // 尝试解析为LoginInfo格式
                var loginInfo = JsonConvert.DeserializeObject<ETWebBrowserJsonFormatter.LoginInfoData>(json);
                if (loginInfo?.Cookies != null && loginInfo.Cookies.Count > 0)
                {
                    return Task.FromResult(new CookieData
                    {
                        Url = loginInfo.Url,
                        Cookies = ConvertFromETCookieItems(loginInfo.Cookies)
                    });
                }

                return Task.FromResult<CookieData>(null);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"解析Cookie数据异常: {ex.Message}");
                return Task.FromResult<CookieData>(null);
            }
        }

        /// <summary>
        /// 转换HyAssistant.WebBrowserV2.Core.CookieItem列表为ET.ETLoginWebBrowser.CookieItem列表
        /// </summary>
        /// <param name="hyItems">HyAssistant的CookieItem列表</param>
        /// <returns>ET的CookieItem列表</returns>
        private List<ET.ETLoginWebBrowser.CookieItem> ConvertToETCookieItems(List<CookieItem> hyItems)
        {
            if (hyItems == null) return new List<ET.ETLoginWebBrowser.CookieItem>();

            var etItems = new List<ET.ETLoginWebBrowser.CookieItem>();
            foreach (var hyItem in hyItems)
            {
                etItems.Add(new ET.ETLoginWebBrowser.CookieItem
                {
                    Name = hyItem.Name,
                    Value = hyItem.Value,
                    Domain = hyItem.Domain,
                    Path = hyItem.Path,
                    Expires = hyItem.Expires,
                    HttpOnly = hyItem.HttpOnly,
                    Secure = hyItem.Secure,
                    SameSite = hyItem.SameSite
                });
            }
            return etItems;
        }

        /// <summary>
        /// 转换ET.ETLoginWebBrowser.CookieItem列表为HyAssistant.WebBrowserV2.Core.CookieItem列表
        /// </summary>
        /// <param name="etItems">ET的CookieItem列表</param>
        /// <returns>HyAssistant的CookieItem列表</returns>
        private List<CookieItem> ConvertFromETCookieItems(List<ET.ETLoginWebBrowser.CookieItem> etItems)
        {
            if (etItems == null) return new List<CookieItem>();

            var hyItems = new List<CookieItem>();
            foreach (var etItem in etItems)
            {
                hyItems.Add(new CookieItem
                {
                    Name = etItem.Name,
                    Value = etItem.Value,
                    Domain = etItem.Domain,
                    Path = etItem.Path,
                    Expires = etItem.Expires,
                    HttpOnly = etItem.HttpOnly,
                    Secure = etItem.Secure,
                    SameSite = etItem.SameSite
                });
            }
            return hyItems;
        }

        /// <summary>
        /// 转换Headers为Dictionary<string, object>格式
        /// </summary>
        /// <param name="headers">原始Headers</param>
        /// <returns>转换后的Headers</returns>
        private Dictionary<string, object> ConvertHeadersToObject(Dictionary<string, string> headers)
        {
            if (headers == null) return new Dictionary<string, object>();

            var objectHeaders = new Dictionary<string, object>();
            foreach (var header in headers)
            {
                objectHeaders[header.Key] = header.Value;
            }
            return objectHeaders;
        }

        /// <summary>
        /// 转换V1版本的HyAssistant.CookieItem列表为ET.ETLoginWebBrowser.CookieItem列表
        /// </summary>
        /// <param name="v1Items">V1版本的CookieItem列表</param>
        /// <returns>ET的CookieItem列表</returns>
        private List<ET.ETLoginWebBrowser.CookieItem> ConvertV1CookiesToET(List<HyAssistant.CookieItem> v1Items)
        {
            if (v1Items == null) return new List<ET.ETLoginWebBrowser.CookieItem>();

            var etItems = new List<ET.ETLoginWebBrowser.CookieItem>();
            foreach (var v1Item in v1Items)
            {
                etItems.Add(new ET.ETLoginWebBrowser.CookieItem
                {
                    Name = v1Item.Name,
                    Value = v1Item.Value,
                    Domain = v1Item.Domain,
                    Path = v1Item.Path,
                    Expires = v1Item.Expires,
                    HttpOnly = v1Item.HttpOnly,
                    Secure = v1Item.Secure,
                    SameSite = v1Item.SameSite
                });
            }
            return etItems;
        }



        #endregion 剪贴板数据处理

        #region Cookie设置和导航

        /// <summary>
        /// 设置Cookie到WebView2
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="cookieData">Cookie数据</param>
        private Task SetCookiesToWebViewAsync(WebView2 webView, CookieData cookieData)
        {
            try
            {
                if (cookieData?.Cookies == null || cookieData.Cookies.Count == 0)
                {
                    ETLogManager.Warning(this, "没有Cookie数据需要设置");
                    return Task.CompletedTask;
                }

                foreach (var cookie in cookieData.Cookies)
                {
                    try
                    {
                        var coreWebView2Cookie = webView.CoreWebView2.CookieManager.CreateCookie(
                            cookie.Name, cookie.Value, cookie.Domain, cookie.Path);

                        webView.CoreWebView2.CookieManager.AddOrUpdateCookie(coreWebView2Cookie);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Warning(this, $"设置Cookie失败 [{cookie.Name}]: {ex.Message}");
                    }
                }

                ETLogManager.Info(this, $"成功设置{cookieData.Cookies.Count}个Cookie");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"设置Cookie到WebView2失败: {ex.Message}");
                return Task.FromException(ex);
            }
        }

        /// <summary>
        /// 导航到指定URL
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="url">目标URL</param>
        private Task NavigateToUrlAsync(WebView2 webView, string url)
        {
            try
            {
                if (string.IsNullOrEmpty(url) || url == "about:blank")
                {
                    ETLogManager.Warning(this, "URL为空或无效，跳过导航");
                    return Task.CompletedTask;
                }

                webView.CoreWebView2.Navigate(url);
                ETLogManager.Info(this, $"导航到URL: {url}");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"导航到URL失败: {ex.Message}");
                return Task.FromException(ex);
            }
        }

        /// <summary>
        /// 更新地址栏显示
        /// </summary>
        /// <param name="url">URL</param>
        private void UpdateAddressBarSafely(string url)
        {
            try
            {
                ThreadSafeHelper.SafeInvoke(this, () =>
                {
                    if (urlTextBox != null)
                    {
                        urlTextBox.Text = url ?? string.Empty;
                    }
                });
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, $"更新地址栏失败: {ex.Message}");
            }
        }

        #endregion Cookie设置和导航
    }

    /// <summary>
    /// Cookie数据类，用于JSON序列化
    /// </summary>
    public class CookieData
    {
        /// <summary>
        /// 关联的URL
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// Cookie列表
        /// </summary>
        public List<CookieItem> Cookies { get; set; } = new List<CookieItem>();
    }

    /// <summary>
    /// Cookie项，表示单个Cookie
    /// </summary>
    public class CookieItem
    {
        /// <summary>
        /// 域名
        /// </summary>
        public string Domain { get; set; }

        /// <summary>
        /// 路径
        /// </summary>
        public string Path { get; set; } = "/";

        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 值
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime Expires { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 是否仅HTTP
        /// </summary>
        public bool HttpOnly { get; set; }

        /// <summary>
        /// 是否安全
        /// </summary>
        public bool Secure { get; set; }

        /// <summary>
        /// 同站策略
        /// </summary>
        public string SameSite { get; set; } = "None";
    }
}
