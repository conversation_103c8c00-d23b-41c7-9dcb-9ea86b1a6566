﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--
    Files are no longer copied as part of this targets file.
    For projects other than CefSharp define a target to copy the files.
    Checkout the CefSharp.Common.targets file for an example
    -->
  
  <ItemGroup>
    <CefRuntimeWin32 Include="$(MSBuildThisFileDirectory)..\runtimes\win-x86\native\*.*" />
	<_CefRuntimeWin32Additional Include="$(MSBuildThisFileDirectory)..\CEF\win-x86\**\*.*" />
	
	<CefRedist32 Include="@(CefRuntimeWin32)" />
	<CefRedist32 Include="@(_CefRuntimeWin32Additional)" />
  </ItemGroup>
  
  <Target Name="CefRedist32CopyResources">
    <PropertyGroup>
      <CefRedist32TargetDir Condition="'$(CefRedist32TargetDir)' == ''">$(TargetDir)</CefRedist32TargetDir>
    </PropertyGroup>
    <!--
    Cannot gurantee access to the main CefRuntimeWin32 ItemGroup here
    so we create a duplicate inside our target.
    https://github.com/dotnet/project-system/issues/4158
    -->
    <ItemGroup>
      <_CefRuntimeWin32 Include="$(MSBuildThisFileDirectory)..\runtimes\win-x86\native\*.*" />
	  <_CefRuntimeWin32 Include="$(MSBuildThisFileDirectory)..\CEF\win-x86\**\*.*" />
    </ItemGroup>
    <Message Importance="high" Text="Copying Chromium Embedded Framework Runtime x86 files from $(MSBuildThisFileDirectory)..\runtimes\win-x86\native to $(CefRedist32TargetDir)" />
    <Copy SourceFiles="@(_CefRuntimeWin32)" DestinationFiles="@(_CefRuntimeWin32->'$(CefRedist32TargetDir)\%(RecursiveDir)%(Filename)%(Extension)')" SkipUnchangedFiles="true" />
  </Target>
</Project>