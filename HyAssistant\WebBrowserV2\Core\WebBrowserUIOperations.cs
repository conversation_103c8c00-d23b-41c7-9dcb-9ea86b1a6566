using System;
using System.Drawing;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Forms;
using ET;
using HyAssistant.WebBrowserV2.Managers;
using HyAssistant.WebBrowserV2.Utils;
using Microsoft.Web.WebView2.WinForms;

namespace HyAssistant.WebBrowserV2.Core
{
    /// <summary>
    /// WebBrowserV2 UI操作相关方法 (partial class) 包含所有界面更新、控件操作、窗体显示等UI相关功能
    /// </summary>
    public partial class WebBrowserV2
    {
        #region UI状态更新方法

        /// <summary>
        /// 标签页切换事件处理程序
        /// </summary>
        private void TabSwitched(string tabSectionId)
        {
            ThreadSafeHelper.SafeInvoke(this, () =>
            {
                try
                {
                    ETLogManager.Info(this, $"标签页切换: {tabSectionId}");

                    // 如果标签页名称为空，表示没有标签页，清空地址栏和标题，禁用导航按钮
                    if (string.IsNullOrEmpty(tabSectionId))
                    {
                        UpdateUIForEmptyTabs();
                        return;
                    }

                    // 启用所有导航按钮
                    WebBrowserUIHelperV2.UpdateNavigationButtonState(toolStrip1, true);
                    if (tabsDropDownButton != null)
                    {
                        tabsDropDownButton.Enabled = true;
                    }

                    // 获取当前标签页数据并更新UI
                    UpdateUIForActiveTab(tabSectionId);
                }
                catch (Exception ex)
                {
                    WebBrowserExceptionHandlerV2.HandleException(this, ex, "标签页切换UI更新", true);
                }
            });
        }

        /// <summary>
        /// 更新空标签页状态的UI
        /// </summary>
        private void UpdateUIForEmptyTabs()
        {
            try
            {
                // 分别处理不同类型的控件
                WebBrowserUIHelperV2.SetToolStripTextBoxTextSafe(urlTextBox, string.Empty);
                WebBrowserUIHelperV2.SetControlTextSafe(this, "浏览器");

                if (tabsDropDownButton != null)
                {
                    tabsDropDownButton.Enabled = true;
                }

                WebBrowserUIHelperV2.UpdateNavigationButtonState(toolStrip1, false);
                WebBrowserUIHelperV2.UpdateRefreshStatusLabels(statusStrip1, null);

                ETLogManager.Info(this, "已更新空标签页状态UI");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "更新空标签页UI状态");
            }
        }

        /// <summary>
        /// 更新活动标签页的UI
        /// </summary>
        private void UpdateUIForActiveTab(string tabSectionId)
        {
            try
            {
                // 获取当前标签页数据 - 使用SectionId
                if (_tabManager.TabDataDictionary.TryGetValue(tabSectionId, out WebBrowserTabManagerV2.TabDataV2 tabData))
                {
                    // 更新地址栏
                    UpdateAddressBar(tabData);

                    // 更新窗体标题
                    UpdateWindowTitle(tabData);

                    // 更新刷新状态标签
                    WebBrowserUIHelperV2.UpdateRefreshStatusLabels(statusStrip1, tabData.Config);

                    ETLogManager.Info(this, $"已更新标签页UI: {tabData.Config?.Name}");
                }
                else
                {
                    ETLogManager.Warning(this, $"未找到标签页数据: {tabSectionId}");
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "更新活动标签页UI");
            }
        }

        /// <summary>
        /// 更新地址栏显示
        /// </summary>
        private void UpdateAddressBar(WebBrowserTabManagerV2.TabDataV2 tabData)
        {
            try
            {
                string currentUrl;
                if (tabData.WebView != null && tabData.WebView.Source != null)
                {
                    currentUrl = tabData.WebView.Source.ToString();
                }
                else if (!string.IsNullOrEmpty(tabData.LastUrl))
                {
                    currentUrl = tabData.LastUrl;
                }
                else
                {
                    currentUrl = tabData.Config?.Url ?? "about:blank";
                }

                WebBrowserUIHelperV2.SetToolStripTextBoxTextSafe(urlTextBox, currentUrl);
                ETLogManager.Debug(this, $"地址栏已更新: {currentUrl}");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "更新地址栏");
            }
        }

        /// <summary>
        /// 更新窗体标题
        /// </summary>
        private void UpdateWindowTitle(WebBrowserTabManagerV2.TabDataV2 tabData)
        {
            try
            {
                string title;
                if (tabData.WebView?.CoreWebView2 != null)
                {
                    title = tabData.WebView.CoreWebView2.DocumentTitle;
                }
                else
                {
                    title = tabData.Config?.Name ?? "未命名";
                }

                if (string.IsNullOrEmpty(title))
                {
                    title = "浏览器";
                }

                WebBrowserUIHelperV2.UpdateFormTitle(this, title);
                ETLogManager.Debug(this, $"窗体标题已更新: {title}");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "更新窗体标题");
            }
        }

        #endregion UI状态更新方法

        #region 会话日志UI操作

        /// <summary>
        /// 会话日志更新事件处理程序
        /// </summary>
        private void SessionLogUpdated(string message)
        {
            ThreadSafeHelper.SafeInvoke(this, async () =>
            {
                try
                {
                    // 使用日志助手类
                    SessionLogHelper.AppendLogSafe(sessionLogTextBox, message);

                    // 同时更新状态栏
                    if (statusLabel != null)
                    {
                        statusLabel.Text = message;
                    }

                    // 由于会话状态可能已更改，更新当前标签页的刷新状态显示
                    await UpdateCurrentTabRefreshStatusAsync();

                    ETLogManager.Debug(this, $"会话日志已更新: {message}");
                }
                catch (Exception ex)
                {
                    WebBrowserExceptionHandlerV2.HandleException(this, ex, "更新会话日志UI");
                }
            });
        }

        /// <summary>
        /// 更新当前标签页的刷新状态
        /// </summary>
        private Task UpdateCurrentTabRefreshStatusAsync()
        {
            try
            {
                if (tabControl1.SelectedTab != null)
                {
                    string sectionId = tabControl1.SelectedTab.Name;
                    if (_tabManager.TabDataDictionary.TryGetValue(sectionId, out WebBrowserTabManagerV2.TabDataV2 tabData))
                    {
                        WebBrowserUIHelperV2.UpdateRefreshStatusLabels(statusStrip1, tabData.Config);
                    }
                }
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "更新当前标签页刷新状态");
                return Task.CompletedTask;
            }
        }

        /// <summary>
        /// 显示会话日志
        /// </summary>
        private void ShowSessionLog()
        {
            try
            {
                if (sessionLogTextBox != null)
                {
                    sessionLogTextBox.Visible = true;
                    sessionLogTextBox.BringToFront();

                    // 启动鼠标监控定时器
                    if (_mouseMonitorTimer != null)
                    {
                        _mouseMonitorTimer.Start();
                    }

                    ETLogManager.Debug(this, "会话日志已显示");
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "显示会话日志");
            }
        }

        /// <summary>
        /// 隐藏会话日志
        /// </summary>
        private void HideSessionLog()
        {
            try
            {
                if (sessionLogTextBox != null)
                {
                    sessionLogTextBox.Visible = false;

                    // 停止鼠标监控定时器
                    if (_mouseMonitorTimer != null)
                    {
                        _mouseMonitorTimer.Stop();
                    }

                    ETLogManager.Debug(this, "会话日志已隐藏");
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "隐藏会话日志");
            }
        }

        #endregion 会话日志UI操作

        #region 鼠标监控和UI交互

        // 鼠标监控相关方法已移至WebBrowserEventHandlersV2.cs

        #endregion 鼠标监控和UI交互

        #region 状态栏和工具栏UI操作

        // StatusStrip和SessionLogTextBox事件处理方法已移至WebBrowserEventHandlersV2.cs

        /// <summary>
        /// 更新主菜单状态
        /// </summary>
        private void UpdateMainMenuStatus(bool isHidden)
        {
            try
            {
                // 使用静态事件通知MainForm
                WebBrowserStatusBridge.OnStatusChanged(isHidden);

                // 记录状态变更日志
                ETLogManager.Debug(this, $"WebBrowser状态已改变，isHidden={isHidden}");
            }
            catch (Exception ex)
            {
                // 记录错误但不中断操作
                ETLogManager.Warning(this, $"更新菜单状态出错: {ex.Message}");
            }
        }

        #endregion 状态栏和工具栏UI操作

        #region 窗体显示和隐藏操作

        /// <summary>
        /// 显示窗体
        /// </summary>
        public void ShowWindow()
        {
            try
            {
                ThreadSafeHelper.SafeInvoke(this, () =>
                {
                    if (WindowState == FormWindowState.Minimized)
                    {
                        WindowState = FormWindowState.Normal;
                    }

                    Show();
                    Activate();
                    BringToFront();

                    _isMinimizedToTray = false;
                    UpdateMainMenuStatus(false);

                    ETLogManager.Info(this, "WebBrowser窗体已显示");
                });
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "显示WebBrowser窗体", true);
            }
        }

        /// <summary>
        /// 隐藏窗体到托盘
        /// </summary>
        public void HideToTray()
        {
            try
            {
                ThreadSafeHelper.SafeInvoke(this, () =>
                {
                    Hide();
                    _isMinimizedToTray = true;
                    UpdateMainMenuStatus(true);

                    ETLogManager.Info(this, "WebBrowser窗体已隐藏到托盘");
                });
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "隐藏WebBrowser窗体到托盘", true);
            }
        }

        /// <summary>
        /// 真正关闭窗体
        /// </summary>
        public void DoRealClose()
        {
            try
            {
                ThreadSafeHelper.SafeInvoke(this, () =>
                {
                    _isRealClosing = true;
                    UpdateMainMenuStatus(true);
                    Close();

                    ETLogManager.Info(this, "WebBrowser窗体已真正关闭");
                });
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "真正关闭WebBrowser窗体", true);
            }
        }

        #endregion 窗体显示和隐藏操作

        #region 对话框和消息框操作

        /// <summary>
        /// 显示信息消息框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        public void ShowInfoMessage(string message, string title = "提示")
        {
            try
            {
                ThreadSafeHelper.SafeInvoke(this, () =>
                {
                    MessageBox.Show(this, message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
                });
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "显示信息消息框");
            }
        }

        /// <summary>
        /// 显示警告消息框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        public void ShowWarningMessage(string message, string title = "警告")
        {
            try
            {
                ThreadSafeHelper.SafeInvoke(this, () =>
                {
                    MessageBox.Show(this, message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                });
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "显示警告消息框");
            }
        }

        /// <summary>
        /// 显示错误消息框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        public void ShowErrorMessage(string message, string title = "错误")
        {
            try
            {
                ThreadSafeHelper.SafeInvoke(this, () =>
                {
                    MessageBox.Show(this, message, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
                });
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "显示错误消息框");
            }
        }

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        /// <returns>用户选择结果</returns>
        public DialogResult ShowConfirmDialog(string message, string title = "确认")
        {
            try
            {
                return (DialogResult)ThreadSafeHelper.SafeInvoke(this, () =>
                {
                    return MessageBox.Show(this, message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                });
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "显示确认对话框");
                return DialogResult.Cancel;
            }
        }

        #endregion 对话框和消息框操作

        #region 状态栏和光标操作

        /// <summary>
        /// 线程安全地更新状态栏
        /// </summary>
        /// <param name="message">状态消息</param>
        public void UpdateStatusSafely(string message)
        {
            try
            {
                ThreadSafeHelper.SafeInvoke(this, () =>
                {
                    if (statusLabel != null)
                    {
                        statusLabel.Text = message ?? string.Empty;
                    }
                });
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "更新状态栏");
            }
        }

        /// <summary>
        /// 线程安全地设置光标
        /// </summary>
        /// <param name="cursor">光标类型</param>
        public void SetCursorSafely(Cursor cursor)
        {
            try
            {
                ThreadSafeHelper.SafeInvoke(this, () =>
                {
                    Cursor = cursor ?? Cursors.Default;
                });
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "设置光标");
            }
        }

        #endregion 状态栏和光标操作

        #region 控件状态批量更新

        /// <summary>
        /// 批量更新导航按钮状态
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public void UpdateNavigationButtonsState(bool enabled)
        {
            try
            {
                ThreadSafeHelper.SafeInvoke(this, () =>
                {
                    WebBrowserUIHelperV2.UpdateNavigationButtonState(toolStrip1, enabled);
                    ETLogManager.Debug(this, $"导航按钮状态已更新: {enabled}");
                });
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "更新导航按钮状态");
            }
        }

        /// <summary>
        /// 批量更新控件状态
        /// </summary>
        /// <param name="updates">更新操作数组</param>
        public void BatchUpdateControls(params UIUpdateActionV2[] updates)
        {
            try
            {
                ThreadSafeHelper.SafeInvoke(this, () =>
                {
                    WebBrowserUIHelperV2.BatchUpdateControls(updates);
                    ETLogManager.Debug(this, $"批量更新了{updates?.Length ?? 0}个控件");
                });
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "批量更新控件状态");
            }
        }

        #endregion 控件状态批量更新
    }
}