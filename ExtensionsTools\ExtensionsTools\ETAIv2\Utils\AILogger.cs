using System;
using ET.ETAIv2.Interfaces;
using ET;
using System.Diagnostics;

namespace ET.ETAIv2.Utils
{
    /// <summary>
    /// AI日志记录器
    /// </summary>
    public class AILogger : IAILogger
    {
        private readonly string _logPrefix;

        public AILogger(string logPrefix = "ETAIv2")
        {
            _logPrefix = logPrefix;
        }

        /// <summary>
        /// 记录信息
        /// </summary>
        public void LogInfo(string message, params object[] args)
        {
            try
            {
                var formattedMessage = FormatMessage("INFO", message, args);
                Debug.Print(formattedMessage);

                // 可以扩展到其他日志系统 ETLogManager.Info(formattedMessage);
            }
            catch
            {
                // 记录日志失败时不抛出异常
            }
        }

        /// <summary>
        /// 记录警告
        /// </summary>
        public void LogWarning(string message, params object[] args)
        {
            try
            {
                var formattedMessage = FormatMessage("WARN", message, args);
                Debug.Print(formattedMessage);

                // 可以扩展到其他日志系统 ETLogManager.Warning(formattedMessage);
            }
            catch
            {
                // 记录日志失败时不抛出异常
            }
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        public void LogError(string message, Exception exception = null, params object[] args)
        {
            try
            {
                var formattedMessage = FormatMessage("ERROR", message, args);

                if (exception != null)
                {
                    formattedMessage += Environment.NewLine + $"异常详情: {exception}";
                }

                Console.WriteLine(formattedMessage);

                // 使用现有的日志系统
                if (exception != null)
                {
                    ETLogManager.Error(exception);
                }
                else
                {
                    ETLogManager.Error(new Exception(formattedMessage));
                }
            }
            catch
            {
                // 记录日志失败时不抛出异常
            }
        }

        /// <summary>
        /// 记录调试信息
        /// </summary>
        public void LogDebug(string message, params object[] args)
        {
            try
            {
#if DEBUG
                var formattedMessage = FormatMessage("DEBUG", message, args);
                Debug.Print(formattedMessage);
#endif
            }
            catch
            {
                // 记录日志失败时不抛出异常
            }
        }

        /// <summary>
        /// 格式化消息
        /// </summary>
        private string FormatMessage(string level, string message, params object[] args)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var formattedMessage = args?.Length > 0 ? string.Format(message, args) : message;
                return $"[{timestamp}] [{_logPrefix}] [{level}] {formattedMessage}";
            }
            catch
            {
                // 格式化失败时返回原始消息
                return $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{_logPrefix}] [{level}] {message}";
            }
        }

        /// <summary>
        /// 记录性能信息
        /// </summary>
        public void LogPerformance(string operation, TimeSpan duration, object additionalInfo = null)
        {
            try
            {
                var message = $"性能统计 - 操作: {operation}, 耗时: {duration.TotalMilliseconds:F2}ms";

                if (additionalInfo != null)
                {
                    message += $", 附加信息: {additionalInfo}";
                }

                LogInfo(message);
            }
            catch
            {
                // 记录日志失败时不抛出异常
            }
        }

        /// <summary>
        /// 记录API调用信息
        /// </summary>
        public void LogAPICall(string apiType, string endpoint, TimeSpan duration, bool success, string errorMessage = null)
        {
            try
            {
                var status = success ? "成功" : "失败";
                var message = $"API调用 - 类型: {apiType}, 端点: {endpoint}, 状态: {status}, 耗时: {duration.TotalMilliseconds:F2}ms";

                if (!success && !string.IsNullOrEmpty(errorMessage))
                {
                    message += $", 错误: {errorMessage}";
                }

                if (success)
                {
                    LogInfo(message);
                }
                else
                {
                    LogError(message);
                }
            }
            catch
            {
                // 记录日志失败时不抛出异常
            }
        }

        /// <summary>
        /// 记录文件处理信息
        /// </summary>
        public void LogFileProcessing(string operation, string filePath, long fileSize, bool success, string errorMessage = null)
        {
            try
            {
                var status = success ? "成功" : "失败";
                var message = $"文件处理 - 操作: {operation}, 文件: {filePath}, 大小: {fileSize} bytes, 状态: {status}";

                if (!success && !string.IsNullOrEmpty(errorMessage))
                {
                    message += $", 错误: {errorMessage}";
                }

                if (success)
                {
                    LogInfo(message);
                }
                else
                {
                    LogError(message);
                }
            }
            catch
            {
                // 记录日志失败时不抛出异常
            }
        }

        /// <summary>
        /// 记录Excel操作信息
        /// </summary>
        public void LogExcelOperation(string operation, string rangeAddress, int rowCount, int columnCount, bool success, string errorMessage = null)
        {
            try
            {
                var status = success ? "成功" : "失败";
                var message = $"Excel操作 - 操作: {operation}, 区域: {rangeAddress}, 行数: {rowCount}, 列数: {columnCount}, 状态: {status}";

                if (!success && !string.IsNullOrEmpty(errorMessage))
                {
                    message += $", 错误: {errorMessage}";
                }

                if (success)
                {
                    LogInfo(message);
                }
                else
                {
                    LogError(message);
                }
            }
            catch
            {
                // 记录日志失败时不抛出异常
            }
        }
    }
}