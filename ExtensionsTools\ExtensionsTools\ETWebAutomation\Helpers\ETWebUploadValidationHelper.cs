using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using ET;


namespace ET.ETWebAutomation.Helpers
{
    /// <summary>
    /// 文件上传验证辅助类
    /// </summary>
    public static class ETWebUploadValidationHelper
    {
        #region 文件基础验证
        /// <summary>
        /// 验证文件是否存在
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        public static ValidationResult ValidateFileExists(string filePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    return ValidationResult.Fail("文件路径不能为空");
                }

                if (!File.Exists(filePath))
                {
                    return ValidationResult.Fail("文件不存在");
                }

                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadValidationHelper", $"验证文件存在性失败: {ex.Message}");
                return ValidationResult.Fail($"验证文件存在性时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证文件大小
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="maxSizeMB">最大文件大小（MB）</param>
        /// <returns>验证结果</returns>
        public static ValidationResult ValidateFileSize(string filePath, int maxSizeMB)
        {
            try
            {
                var fileInfo = new FileInfo(filePath);
                var maxSizeBytes = (long)maxSizeMB * 1024 * 1024;

                if (fileInfo.Length > maxSizeBytes)
                {
                    return ValidationResult.Fail($"文件大小 {FormatFileSize(fileInfo.Length)} 超过限制 {maxSizeMB}MB");
                }

                if (fileInfo.Length == 0)
                {
                    return ValidationResult.Fail("文件为空");
                }

                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadValidationHelper", $"验证文件大小失败: {ex.Message}");
                return ValidationResult.Fail($"验证文件大小时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证文件扩展名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="allowedExtensions">允许的扩展名列表</param>
        /// <returns>验证结果</returns>
        public static ValidationResult ValidateFileExtension(string filePath, string[] allowedExtensions)
        {
            try
            {
                if (allowedExtensions == null || allowedExtensions.Length == 0)
                {
                    return ValidationResult.Success(); // 没有限制
                }

                var extension = Path.GetExtension(filePath).ToLower();
                
                if (string.IsNullOrEmpty(extension))
                {
                    return ValidationResult.Fail("文件没有扩展名");
                }

                var isAllowed = allowedExtensions.Any(ext => 
                    string.Equals(ext.ToLower(), extension, StringComparison.OrdinalIgnoreCase));

                if (!isAllowed)
                {
                    return ValidationResult.Fail($"不支持的文件类型: {extension}");
                }

                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadValidationHelper", $"验证文件扩展名失败: {ex.Message}");
                return ValidationResult.Fail($"验证文件扩展名时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证文件访问权限
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        public static ValidationResult ValidateFileAccess(string filePath)
        {
            try
            {
                using (var stream = File.OpenRead(filePath))
                {
                    // 尝试读取文件，验证访问权限
                }
                return ValidationResult.Success();
            }
            catch (UnauthorizedAccessException)
            {
                return ValidationResult.Fail("没有文件访问权限");
            }
            catch (IOException ex)
            {
                return ValidationResult.Fail($"文件访问错误: {ex.Message}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadValidationHelper", $"验证文件访问权限失败: {ex.Message}");
                return ValidationResult.Fail($"验证文件访问权限时发生错误: {ex.Message}");
            }
        }
        #endregion

        #region 文件内容验证
        /// <summary>
        /// 验证文件MIME类型
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="allowedMimeTypes">允许的MIME类型列表</param>
        /// <returns>验证结果</returns>
        public static ValidationResult ValidateFileMimeType(string filePath, string[] allowedMimeTypes = null)
        {
            try
            {
                var mimeType = GetFileMimeType(filePath);
                
                if (allowedMimeTypes != null && allowedMimeTypes.Length > 0)
                {
                    var isAllowed = allowedMimeTypes.Any(mime => 
                        string.Equals(mime, mimeType, StringComparison.OrdinalIgnoreCase));

                    if (!isAllowed)
                    {
                        return ValidationResult.Fail($"不支持的文件MIME类型: {mimeType}");
                    }
                }

                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadValidationHelper", $"验证文件MIME类型失败: {ex.Message}");
                return ValidationResult.Fail($"验证文件MIME类型时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证文件完整性（MD5校验）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="expectedMD5">期望的MD5值</param>
        /// <returns>验证结果</returns>
        public static async Task<ValidationResult> ValidateFileIntegrityAsync(string filePath, string expectedMD5)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(expectedMD5))
                {
                    return ValidationResult.Success(); // 没有提供期望值，跳过验证
                }

                var actualMD5 = await CalculateFileMD5Async(filePath);
                
                if (!string.Equals(actualMD5, expectedMD5, StringComparison.OrdinalIgnoreCase))
                {
                    return ValidationResult.Fail("文件完整性校验失败");
                }

                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadValidationHelper", $"验证文件完整性失败: {ex.Message}");
                return ValidationResult.Fail($"验证文件完整性时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查文件是否为恶意文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        public static ValidationResult ValidateFileSafety(string filePath)
        {
            try
            {
                // 检查文件头部特征
                var headerResult = ValidateFileHeader(filePath);
                if (!headerResult.IsValid)
                {
                    return headerResult;
                }

                // 检查文件名中的危险字符
                var fileName = Path.GetFileName(filePath);
                var dangerousChars = new char[] { '<', '>', ':', '"', '|', '?', '*', '\0' };
                
                if (fileName.IndexOfAny(dangerousChars) >= 0)
                {
                    return ValidationResult.Fail("文件名包含危险字符");
                }

                // 检查双重扩展名
                if (HasDoubleExtension(fileName))
                {
                    return ValidationResult.Fail("检测到可疑的双重扩展名");
                }

                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadValidationHelper", $"验证文件安全性失败: {ex.Message}");
                return ValidationResult.Fail($"验证文件安全性时发生错误: {ex.Message}");
            }
        }
        #endregion

        #region 批量验证
        /// <summary>
        /// 批量验证文件
        /// </summary>
        /// <param name="filePaths">文件路径列表</param>
        /// <param name="maxSizeMB">最大文件大小（MB）</param>
        /// <param name="allowedExtensions">允许的扩展名</param>
        /// <returns>验证结果列表</returns>
        public static List<FileValidationResult> ValidateFiles(string[] filePaths, int maxSizeMB, string[] allowedExtensions)
        {
            var results = new List<FileValidationResult>();

            if (filePaths == null || filePaths.Length == 0)
            {
                return results;
            }

            foreach (var filePath in filePaths)
            {
                var result = ValidateFile(filePath, maxSizeMB, allowedExtensions);
                results.Add(result);
            }

            return results;
        }

        /// <summary>
        /// 验证单个文件（完整验证）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="maxSizeMB">最大文件大小（MB）</param>
        /// <param name="allowedExtensions">允许的扩展名</param>
        /// <returns>文件验证结果</returns>
        public static FileValidationResult ValidateFile(string filePath, int maxSizeMB, string[] allowedExtensions)
        {
            var result = new FileValidationResult
            {
                FilePath = filePath,
                FileName = Path.GetFileName(filePath)
            };

            try
            {
                // 验证文件存在
                var existsResult = ValidateFileExists(filePath);
                if (!existsResult.IsValid)
                {
                    result.IsValid = false;
                    result.ErrorMessage = existsResult.ErrorMessage;
                    return result;
                }

                // 验证文件大小
                var sizeResult = ValidateFileSize(filePath, maxSizeMB);
                if (!sizeResult.IsValid)
                {
                    result.IsValid = false;
                    result.ErrorMessage = sizeResult.ErrorMessage;
                    return result;
                }

                // 验证文件扩展名
                var extensionResult = ValidateFileExtension(filePath, allowedExtensions);
                if (!extensionResult.IsValid)
                {
                    result.IsValid = false;
                    result.ErrorMessage = extensionResult.ErrorMessage;
                    return result;
                }

                // 验证文件访问权限
                var accessResult = ValidateFileAccess(filePath);
                if (!accessResult.IsValid)
                {
                    result.IsValid = false;
                    result.ErrorMessage = accessResult.ErrorMessage;
                    return result;
                }

                // 验证文件安全性
                var safetyResult = ValidateFileSafety(filePath);
                if (!safetyResult.IsValid)
                {
                    result.IsValid = false;
                    result.ErrorMessage = safetyResult.ErrorMessage;
                    return result;
                }

                // 获取文件信息
                var fileInfo = new FileInfo(filePath);
                result.FileSize = fileInfo.Length;
                result.IsValid = true;
                result.ErrorMessage = null;

                return result;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadValidationHelper", $"验证文件失败: {ex.Message}");
                result.IsValid = false;
                result.ErrorMessage = $"验证文件时发生错误: {ex.Message}";
                return result;
            }
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 计算文件MD5值
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>MD5值</returns>
        private static async Task<string> CalculateFileMD5Async(string filePath)
        {
            using (var md5 = MD5.Create())
            using (var stream = File.OpenRead(filePath))
            {
                var hash = await Task.Run(() => md5.ComputeHash(stream));
                return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
            }
        }

        /// <summary>
        /// 获取文件MIME类型
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>MIME类型</returns>
        private static string GetFileMimeType(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            
            // 简单的MIME类型映射
            var mimeTypes = new Dictionary<string, string>
            {
                { ".jpg", "image/jpeg" },
                { ".jpeg", "image/jpeg" },
                { ".png", "image/png" },
                { ".gif", "image/gif" },
                { ".bmp", "image/bmp" },
                { ".pdf", "application/pdf" },
                { ".doc", "application/msword" },
                { ".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document" },
                { ".xls", "application/vnd.ms-excel" },
                { ".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" },
                { ".ppt", "application/vnd.ms-powerpoint" },
                { ".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation" },
                { ".txt", "text/plain" },
                { ".zip", "application/zip" },
                { ".rar", "application/x-rar-compressed" }
            };

            return mimeTypes.ContainsKey(extension) ? mimeTypes[extension] : "application/octet-stream";
        }

        /// <summary>
        /// 验证文件头部特征
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        private static ValidationResult ValidateFileHeader(string filePath)
        {
            try
            {
                using (var stream = File.OpenRead(filePath))
                {
                    var buffer = new byte[16];
                    var bytesRead = stream.Read(buffer, 0, buffer.Length);
                    
                    if (bytesRead < 4)
                    {
                        return ValidationResult.Success(); // 文件太小，跳过验证
                    }

                    // 检查常见的恶意文件头部特征
                    var header = Encoding.ASCII.GetString(buffer, 0, Math.Min(bytesRead, 4));
                    
                    // 检查可执行文件头部
                    if (header.StartsWith("MZ") || header.StartsWith("PK"))
                    {
                        var extension = Path.GetExtension(filePath).ToLower();
                        if (extension != ".exe" && extension != ".zip" && extension != ".rar")
                        {
                            return ValidationResult.Fail("检测到可疑的文件头部特征");
                        }
                    }
                }

                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAUploadValidationHelper", $"验证文件头部失败: {ex.Message}");
                return ValidationResult.Fail($"验证文件头部时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否有双重扩展名
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>是否有双重扩展名</returns>
        private static bool HasDoubleExtension(string fileName)
        {
            var parts = fileName.Split('.');
            return parts.Length > 2 && parts[parts.Length - 2].Length <= 4;
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns>格式化后的文件大小</returns>
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
        #endregion

        #region 内部类
        /// <summary>
        /// 验证结果
        /// </summary>
        public class ValidationResult
        {
            /// <summary>
            /// 是否验证通过
            /// </summary>
            public bool IsValid { get; set; }

            /// <summary>
            /// 错误消息
            /// </summary>
            public string ErrorMessage { get; set; }

            /// <summary>
            /// 创建成功的验证结果
            /// </summary>
            public static ValidationResult Success()
            {
                return new ValidationResult { IsValid = true };
            }

            /// <summary>
            /// 创建失败的验证结果
            /// </summary>
            public static ValidationResult Fail(string errorMessage)
            {
                return new ValidationResult { IsValid = false, ErrorMessage = errorMessage };
            }
        }

        /// <summary>
        /// 文件验证结果
        /// </summary>
        public class FileValidationResult : ValidationResult
        {
            /// <summary>
            /// 文件路径
            /// </summary>
            public string FilePath { get; set; }

            /// <summary>
            /// 文件名
            /// </summary>
            public string FileName { get; set; }

            /// <summary>
            /// 文件大小
            /// </summary>
            public long FileSize { get; set; }
        }
        #endregion
    }
}
