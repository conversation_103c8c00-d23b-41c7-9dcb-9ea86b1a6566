using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ET.ETAIv2.Models;
using ET.ETAIv2.Interfaces;
using ET.ETAIv2.Constants;
using ET.ETAIv2.Exceptions;
using ET.ETAIv2.Utils;
using OpenAI;
using OpenAI.Files;
using System.ClientModel;

namespace ET.ETAIv2.Services.Core
{
    /// <summary>
    /// AI文件处理器 - 基于OpenAI 2.1库优化
    /// </summary>
    public class AIFileProcessor : IAIFileProcessor
    {
        private readonly IAILogger _logger;
        private readonly SemaphoreSlim _semaphore;
        private readonly IAIConfigManager _configManager;

        public AIFileProcessor(IAILogger logger = null, IAIConfigManager configManager = null)
        {
            _logger = logger ?? new AILogger();
            _configManager = configManager ?? new AIConfigManager();
            _semaphore = new SemaphoreSlim(3, 3); // 限制并发文件处理数量
        }

        /// <summary>
        /// 上传文件到OpenAI（接口实现）
        /// </summary>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<FileData> UploadFileAsync(string filePath, CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);

            try
            {
                _logger.LogInfo($"开始上传文件到OpenAI: {filePath}");

                // 基础验证
                ValidateFileForUpload(filePath);

                var fileInfo = new FileInfo(filePath);

                // 从配置管理器获取配置并创建ModelConfig
                var apiKey = await _configManager.GetApiKeyAsync();
                var baseUrl = await _configManager.GetBaseUrlAsync();

                if (string.IsNullOrEmpty(apiKey))
                    throw new ConfigurationException("OpenAI API密钥未配置，无法上传文件");

                // 创建临时ModelConfig用于文件上传
                var modelConfig = new AIModelConfig
                {
                    Server = "openai",
                    Model = "gpt-4", // 文件上传不依赖具体模型，使用默认值
                    APIKey = apiKey,
                    BaseURL = baseUrl
                };

                // 创建OpenAI文件客户端
                var fileClient = CreateOpenAIFileClient(modelConfig);

                // 执行文件上传 - 使用官方推荐的文件路径方式 参考官方示例：OpenAIFile uploadedFile = await
                // fileClient.UploadFileAsync(filePath, FileUploadPurpose.Assistants);
                _logger.LogInfo($"正在上传文件: {fileInfo.Name}, 大小: {fileInfo.Length} bytes");

                ClientResult<OpenAIFile> uploadResult;
                try
                {
                    // 使用正确的API签名：UploadFileAsync(string filePath, FileUploadPurpose purpose)
                    uploadResult = await fileClient.UploadFileAsync(
                        filePath,
                        FileUploadPurpose.Assistants);
                }
                catch (Exception uploadEx)
                {
                    _logger.LogError($"文件上传API调用失败: {uploadEx.Message}");

                    // 如果直接路径上传失败，重新抛出异常（因为OpenAI库只支持文件路径方式）
                    _logger.LogError("文件路径上传失败，OpenAI库不支持Stream方式上传");
                    throw;
                }

                if (uploadResult?.Value == null)
                {
                    throw new FileProcessingException("文件上传返回结果为空", filePath);
                }

                var uploadedFile = uploadResult.Value;
                _logger.LogInfo($"文件上传成功: ID={uploadedFile.Id}, 文件名={uploadedFile.Filename}, 大小={uploadedFile.SizeInBytes} bytes");

                var fileData = new FileData
                {
                    FilePath = filePath,
                    FileName = fileInfo.Name,
                    FileType = fileInfo.Extension.ToLower(),
                    FileSize = fileInfo.Length,
                    OpenAIFileId = uploadedFile.Id,
                    UploadedAt = DateTime.UtcNow,
                    IsUploaded = true,
                    Content = null // 上传模式下不存储本地内容
                };

                _logger.LogFileProcessing("上传到OpenAI", filePath, fileInfo.Length, true,
                    $"文件ID: {uploadedFile.Id}");

                return fileData;
            }
            catch (Exception ex)
            {
                // 详细的错误诊断
                string errorDetails = AnalyzeUploadError(ex, filePath);
                _logger.LogError($"文件上传失败详细信息: {errorDetails}");
                _logger.LogFileProcessing("上传到OpenAI", filePath, 0, false, ex.Message);

                throw new FileProcessingException($"文件上传失败: {ex.Message}", ex, filePath);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 上传文件到OpenAI（使用指定的ModelConfig）
        /// </summary>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<FileData> UploadFileAsync(string filePath, AIModelConfig modelConfig = null, CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);

            try
            {
                _logger.LogInfo($"开始上传文件到OpenAI: {filePath}");

                // 基础验证
                ValidateFileForUpload(filePath);

                var fileInfo = new FileInfo(filePath);

                // 创建OpenAI文件客户端，使用传入的ModelConfig或从配置管理器获取
                var fileClient = CreateOpenAIFileClient(modelConfig);

                // 执行文件上传 - 使用官方推荐的文件路径方式 参考官方示例：OpenAIFile uploadedFile = await
                // fileClient.UploadFileAsync(filePath, FileUploadPurpose.Assistants);
                _logger.LogInfo($"正在上传文件: {fileInfo.Name}, 大小: {fileInfo.Length} bytes");

                ClientResult<OpenAIFile> uploadResult;
                try
                {
                    // 使用正确的API签名：UploadFileAsync(string filePath, FileUploadPurpose purpose)
                    uploadResult = await fileClient.UploadFileAsync(
                        filePath,
                        FileUploadPurpose.Assistants);
                }
                catch (Exception uploadEx)
                {
                    _logger.LogError($"文件上传API调用失败: {uploadEx.Message}");

                    // 如果直接路径上传失败，重新抛出异常（因为OpenAI库只支持文件路径方式）
                    _logger.LogError("文件路径上传失败，OpenAI库不支持Stream方式上传");
                    throw;
                }

                if (uploadResult?.Value == null)
                {
                    throw new FileProcessingException("文件上传返回结果为空", filePath);
                }

                var uploadedFile = uploadResult.Value;
                _logger.LogInfo($"文件上传成功: ID={uploadedFile.Id}, 文件名={uploadedFile.Filename}, 大小={uploadedFile.SizeInBytes} bytes");

                var fileData = new FileData
                {
                    FilePath = filePath,
                    FileName = fileInfo.Name,
                    FileType = fileInfo.Extension.ToLower(),
                    FileSize = fileInfo.Length,
                    OpenAIFileId = uploadedFile.Id,
                    UploadedAt = DateTime.UtcNow,
                    IsUploaded = true,
                    Content = null // 上传模式下不存储本地内容
                };

                _logger.LogFileProcessing("上传到OpenAI", filePath, fileInfo.Length, true,
                    $"文件ID: {uploadedFile.Id}");

                return fileData;
            }
            catch (Exception ex)
            {
                // 详细的错误诊断
                string errorDetails = AnalyzeUploadError(ex, filePath);
                _logger.LogError($"文件上传失败详细信息: {errorDetails}");
                _logger.LogFileProcessing("上传到OpenAI", filePath, 0, false, ex.Message);

                throw new FileProcessingException($"文件上传失败: {ex.Message}", ex, filePath);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 本地读取文件内容 - 简化实现，仅返回文件信息
        /// </summary>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<FileData> ReadFileLocallyAsync(string filePath, CancellationToken cancellationToken = default)
        {
            // 在仅上传模式下，本地读取实际上也是上传到OpenAI
            _logger.LogWarning("本地读取模式已禁用，自动转换为上传模式");
            return await UploadFileAsync(filePath, cancellationToken);
        }

        /// <summary>
        /// 批量处理文件 - 简化为仅上传模式
        /// </summary>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<List<FileData>> ProcessFilesAsync(
            List<string> filePaths,
            FileProcessingMode mode,
            CancellationToken cancellationToken = default)
        {
            return await ProcessFilesAsync(filePaths, mode, null, cancellationToken);
        }

        /// <summary>
        /// 批量处理文件 - 使用指定的ModelConfig
        /// </summary>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<List<FileData>> ProcessFilesAsync(
            List<string> filePaths,
            FileProcessingMode mode,
            AIModelConfig modelConfig,
            CancellationToken cancellationToken = default)
        {
            if (filePaths == null || !filePaths.Any())
                return new List<FileData>();

            try
            {
                _logger.LogInfo($"开始批量上传文件，共 {filePaths.Count} 个文件");

                // 忽略mode参数，统一使用上传模式
                var tasks = filePaths.Select(async filePath =>
                {
                    try
                    {
                        if (modelConfig != null)
                        {
                            return await UploadFileAsync(filePath, modelConfig, cancellationToken);
                        }
                        else
                        {
                            return await UploadFileAsync(filePath, cancellationToken);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"上传文件失败: {filePath}", ex);
                        return null; // 返回null表示上传失败
                    }
                });

                var results = await Task.WhenAll(tasks);
                var successResults = results.Where(r => r != null).ToList();

                _logger.LogInfo($"批量文件上传完成，成功 {successResults.Count}/{filePaths.Count} 个文件");
                return successResults;
            }
            catch (Exception ex)
            {
                _logger.LogError("批量文件上传失败", ex);
                throw new FileProcessingException("批量文件上传失败", ex);
            }
        }

        /// <summary>
        /// 清理上传的文件（接口实现）
        /// </summary>
        /// <param name="fileIds">要清理的文件ID列表</param>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task CleanupUploadedFilesAsync(List<string> fileIds)
        {
            await CleanupUploadedFilesAsync(fileIds, null);
        }

        /// <summary>
        /// 清理上传的文件（使用指定的ModelConfig）
        /// </summary>
        /// <param name="fileIds">要清理的文件ID列表</param>
        /// <param name="modelConfig">AI模型配置，如果为null则从配置管理器获取</param>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task CleanupUploadedFilesAsync(List<string> fileIds, AIModelConfig modelConfig = null)
        {
            if (fileIds == null || !fileIds.Any())
                return;

            try
            {
                _logger.LogInfo($"开始清理上传的文件，共 {fileIds.Count} 个文件");

                // 获取或创建ModelConfig
                AIModelConfig effectiveConfig = modelConfig;
                if (effectiveConfig == null)
                {
                    // 从配置管理器获取配置并创建ModelConfig
                    var apiKey = await _configManager.GetApiKeyAsync();
                    var baseUrl = await _configManager.GetBaseUrlAsync();

                    if (string.IsNullOrEmpty(apiKey))
                    {
                        _logger.LogWarning("OpenAI API密钥未配置，无法清理文件");
                        return;
                    }

                    effectiveConfig = new AIModelConfig
                    {
                        Server = "openai",
                        Model = "gpt-4", // 文件清理不依赖具体模型，使用默认值
                        APIKey = apiKey,
                        BaseURL = baseUrl
                    };
                }

                // 创建OpenAI文件客户端
                var fileClient = CreateOpenAIFileClient(effectiveConfig);

                foreach (var fileId in fileIds)
                {
                    try
                    {
                        // 实现OpenAI文件删除
                        await fileClient.DeleteFileAsync(fileId);

                        _logger.LogInfo($"已删除文件: {fileId}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"删除文件失败 {fileId}: {ex.Message}");
                        // 清理失败不抛出异常，避免影响主要逻辑
                    }
                }

                _logger.LogInfo("文件清理完成");
            }
            catch (Exception ex)
            {
                _logger.LogError("文件清理失败", ex);
                // 清理失败不抛出异常
            }
        }

        /// <summary>
        /// 测试文件上传连接
        /// </summary>
        /// <param name="modelConfig">AI模型配置，必须提供以获取apiKey和baseUrl</param>
        /// <returns>测试结果和消息</returns>
        /// <exception cref="ArgumentNullException">当modelConfig为null时抛出</exception>
        public async Task<(bool Success, string Message)> TestUploadConnectionAsync(AIModelConfig modelConfig)
        {
            if (modelConfig == null)
                return (false, "ModelConfig不能为null，必须提供有效的配置");

            try
            {
                _logger.LogInfo("开始测试文件上传连接...");

                // 创建一个小的测试文件
                string testFilePath = Path.GetTempFileName();
                string testContent = "This is a test file for OpenAI upload connection.";
                File.WriteAllText(testFilePath, testContent);

                try
                {
                    // 尝试上传测试文件
                    var fileClient = CreateOpenAIFileClient(modelConfig);
                    var uploadResult = await fileClient.UploadFileAsync(
                        testFilePath,
                        FileUploadPurpose.Assistants);

                    if (uploadResult?.Value != null)
                    {
                        string fileId = uploadResult.Value.Id;
                        _logger.LogInfo($"测试上传成功，文件ID: {fileId}");

                        // 清理测试文件
                        try
                        {
                            await fileClient.DeleteFileAsync(fileId);
                            _logger.LogInfo("测试文件已清理");
                        }
                        catch (Exception cleanupEx)
                        {
                            _logger.LogWarning($"清理测试文件失败: {cleanupEx.Message}");
                        }

                        return (true, "文件上传连接测试成功");
                    }
                    else
                    {
                        return (false, "上传返回结果为空");
                    }
                }
                finally
                {
                    // 删除本地测试文件
                    try
                    {
                        File.Delete(testFilePath);
                    }
                    catch { }
                }
            }
            catch (Exception ex)
            {
                string errorDetails = AnalyzeUploadError(ex, "test-file");
                _logger.LogError($"文件上传连接测试失败: {errorDetails}");
                return (false, $"连接测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证文件是否支持
        /// </summary>
        public bool IsFileSupported(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            try
            {
                var extension = Path.GetExtension(filePath)?.ToLower();
                return !string.IsNullOrEmpty(extension) &&
                       AIConstants.SupportedFileExtensions.Contains(extension);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 读取PDF文件内容
        /// </summary>
        private async Task<string> ReadPdfContentAsync(string filePath, CancellationToken cancellationToken)
        {
            // TODO: 实现PDF文本提取 可以使用 PdfPig 或 iTextSharp 等库
            await Task.Delay(500, cancellationToken); // 模拟处理时间
            return $"[PDF文件内容提取需要实现: {Path.GetFileName(filePath)}]";
        }

        /// <summary>
        /// 读取Word文档内容
        /// </summary>
        private async Task<string> ReadDocxContentAsync(string filePath, CancellationToken cancellationToken)
        {
            // TODO: 实现Word文档文本提取 可以使用 DocumentFormat.OpenXml 等库
            await Task.Delay(500, cancellationToken); // 模拟处理时间
            return $"[Word文档内容提取需要实现: {Path.GetFileName(filePath)}]";
        }

        /// <summary>
        /// 读取HTML文件内容
        /// </summary>
        private async Task<string> ReadHtmlContentAsync(string filePath, CancellationToken cancellationToken)
        {
            try
            {
                await Task.CompletedTask; // 保持异步签名
                var htmlContent = File.ReadAllText(filePath);
                // TODO: 可以使用HtmlAgilityPack提取纯文本
                return htmlContent;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"读取HTML文件失败: {ex.Message}");
                return $"[HTML文件读取失败: {Path.GetFileName(filePath)}]";
            }
        }

        /// <summary>
        /// 获取文件MIME类型
        /// </summary>
        private string GetMimeType(string filePath)
        {
            var extension = Path.GetExtension(filePath)?.ToLower();
            return FileConstants.MimeTypes.TryGetValue(extension ?? "", out var mimeType)
                ? mimeType
                : "application/octet-stream";
        }

        /// <summary>
        /// 验证文件完整性
        /// </summary>
        private bool ValidateFileIntegrity(string filePath)
        {
            try
            {
                var fileInfo = new FileInfo(filePath);
                return fileInfo.Exists && fileInfo.Length > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证文件是否适合上传
        /// </summary>
        private void ValidateFileForUpload(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                throw new FileProcessingException("文件路径不能为空", filePath);

            if (!IsFileSupported(filePath))
                throw new FileProcessingException("不支持的文件类型", filePath);

            var fileInfo = new FileInfo(filePath);
            if (!fileInfo.Exists)
                throw new FileProcessingException("文件不存在", filePath);

            if (fileInfo.Length == 0)
                throw new FileProcessingException("文件为空", filePath);

            if (fileInfo.Length > AIConstants.MaxFileSize)
                throw new FileProcessingException("文件大小超过限制", filePath, fileInfo.Length, fileInfo.Extension);

            // PDF特殊检查：验证文件头
            if (fileInfo.Extension.ToLower() == ".pdf")
            {
                ValidatePdfFileHeader(filePath);
            }
        }

        /// <summary>
        /// 创建OpenAI文件客户端
        /// </summary>
        /// <param name="modelConfig">AI模型配置，必须提供以获取apiKey和baseUrl</param>
        /// <returns>OpenAI文件客户端实例</returns>
        /// <exception cref="ArgumentNullException">当modelConfig为null时抛出</exception>
        /// <exception cref="ConfigurationException">当配置无效时抛出</exception>
        private OpenAIFileClient CreateOpenAIFileClient(AIModelConfig modelConfig)
        {
            // 验证必需参数
            if (modelConfig == null)
                throw new ArgumentNullException(nameof(modelConfig), "ModelConfig不能为null，必须提供有效的配置以获取apiKey和baseUrl");

            try
            {
                // 从ModelConfig获取配置
                string apiKey = modelConfig.GetEffectiveApiKey();
                string baseUrl = modelConfig.GetEffectiveBaseUrl();

                _logger.LogInfo($"使用ModelConfig - 服务器: {modelConfig.Server}, 模型: {modelConfig.Model}");

                // 基础验证
                if (string.IsNullOrEmpty(apiKey))
                    throw new ConfigurationException("OpenAI API密钥未配置");

                _logger.LogInfo($"配置信息 - 基础URL: {baseUrl}");

                // 根据官方示例，正确创建OpenAI客户端
                // 参考: Example05_AssistantsWithVision.cs 和 Example05_AssistantsWithVisionAsync.cs
                OpenAIClient openAIClient;

                // 端点路径完全由modelConfig中的字段确定，不提供默认值
                if (!string.IsNullOrEmpty(baseUrl))
                {
                    // 使用ModelConfig中配置的端点
                    _logger.LogInfo($"使用配置的端点: {baseUrl}");
                    var clientOptions = new OpenAIClientOptions
                    {
                        Endpoint = new Uri(baseUrl)
                    };
                    openAIClient = new OpenAIClient(new ApiKeyCredential(apiKey), clientOptions);
                }
                else
                {
                    // 如果没有配置BaseURL，抛出异常要求明确配置
                    throw new ConfigurationException("BaseURL未配置，文件上传功能需要明确指定API端点。请在ModelConfig中设置正确的BaseURL。");
                }

                // 获取文件客户端 - 参考官方示例
                return openAIClient.GetOpenAIFileClient();
            }
            catch (Exception ex)
            {
                _logger.LogError($"创建OpenAI文件客户端失败: {ex.Message}", ex);
                throw new AIProcessingException($"OpenAI文件客户端初始化失败: {ex.Message}", "file_client");
            }
        }



        /// <summary>
        /// 分析上传错误
        /// </summary>
        private string AnalyzeUploadError(Exception ex, string filePath)
        {
            var analysis = new List<string>();

            analysis.Add($"错误类型: {ex.GetType().Name}");
            analysis.Add($"错误消息: {ex.Message}");

            // 检查文件信息
            try
            {
                var fileInfo = new FileInfo(filePath);
                analysis.Add($"文件存在: {fileInfo.Exists}");
                if (fileInfo.Exists)
                {
                    analysis.Add($"文件大小: {fileInfo.Length} bytes");
                    analysis.Add($"文件扩展名: {fileInfo.Extension}");
                }
            }
            catch (Exception fileEx)
            {
                analysis.Add($"文件信息获取失败: {fileEx.Message}");
            }

            // 检查网络相关错误
            if (ex.Message.Contains("Retry failed") || ex.Message.Contains("timeout") || ex.Message.Contains("network"))
            {
                analysis.Add("可能的网络问题: 检查网络连接、代理设置、防火墙");
            }

            // 检查认证相关错误
            if (ex.Message.Contains("401") || ex.Message.Contains("Unauthorized") || ex.Message.Contains("API key"))
            {
                analysis.Add("可能的认证问题: 检查API密钥是否正确");
            }

            // 检查服务端点错误 - 增强404错误分析
            if (ex.Message.Contains("404") || ex.Message.Contains("Not Found"))
            {
                analysis.Add("❌ 404错误 - 文件上传端点不存在");
                analysis.Add("🔍 可能原因:");
                analysis.Add("  1. BaseURL配置错误 - 必须使用OpenAI官方端点");
                analysis.Add("  2. 使用了不支持文件上传的AI服务商");
                analysis.Add("  3. API版本不匹配");
                analysis.Add("💡 解决方案:");
                analysis.Add("  - 确保BaseURL为: https://api.openai.com/v1/");
                analysis.Add("  - 使用OpenAI官方API密钥");
                analysis.Add("  - 检查服务商是否支持Files API");
            }

            // 检查文件大小限制
            try
            {
                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Exists && fileInfo.Length > AIConstants.MaxFileSize)
                {
                    analysis.Add($"文件过大: {fileInfo.Length} bytes > {AIConstants.MaxFileSize} bytes");
                }
            }
            catch { }

            // 添加内部异常信息
            if (ex.InnerException != null)
            {
                analysis.Add($"内部异常: {ex.InnerException.Message}");
            }

            return string.Join("; ", analysis);
        }

        /// <summary>
        /// 验证PDF文件头（简单验证，不需要完整解析）
        /// </summary>
        private void ValidatePdfFileHeader(string filePath)
        {
            try
            {
                using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
                var buffer = new byte[5];
                int bytesRead = fileStream.Read(buffer, 0, 5);

                if (bytesRead < 4)
                {
                    throw new FileProcessingException("PDF文件太小，无法验证文件头", filePath);
                }

                var header = System.Text.Encoding.ASCII.GetString(buffer, 0, Math.Min(bytesRead, 4));
                if (!header.StartsWith("%PDF"))
                {
                    throw new FileProcessingException("无效的PDF文件格式", filePath);
                }
            }
            catch (Exception ex) when (!(ex is FileProcessingException))
            {
                throw new FileProcessingException($"PDF文件验证失败: {ex.Message}", ex, filePath);
            }
        }
    }
}