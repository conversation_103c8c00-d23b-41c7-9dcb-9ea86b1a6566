# 💾 ETWebStorage 存储管理流程详解

## 📋 概述

ETWebStorage存储管理模块负责处理ETWebAutomation中的数据持久化，包括配置存储、会话存储、Cookie存储、缓存管理、数据加密、备份恢复等功能。

## 🏗️ 存储架构图

```mermaid
classDiagram
    class ETWebStorageManager {
        +string StorageRoot
        +bool EncryptionEnabled
        +Dictionary~string,IStorageProvider~ Providers
        
        +SaveData~T~(key, data, category) void
        +LoadData~T~(key, category) T
        +DeleteData(key, category) void
        +ExistsData(key, category) bool
        +ListKeys(category) List~string~
        +ClearCategory(category) void
        +CreateBackup(category) string
        +RestoreBackup(backupPath, category) void
        +CompactStorage(category) void
        
        -GetProvider(category) IStorageProvider
        -EnsureStorageDirectory(path) void
        -ValidateKey(key) bool
    }
    
    class IStorageProvider {
        <<interface>>
        +Save~T~(key, data) void
        +Load~T~(key) T
        +Delete(key) void
        +Exists(key) bool
        +ListKeys() List~string~
        +Clear() void
    }
    
    class FileStorageProvider {
        +string BasePath
        +bool UseCompression
        +bool UseEncryption
        
        +Save~T~(key, data) void
        +Load~T~(key) T
        +Delete(key) void
        +Exists(key) bool
        +ListKeys() List~string~
        +Clear() void
        
        -GetFilePath(key) string
        -CompressData(data) byte[]
        -DecompressData(data) byte[]
        -EncryptData(data) byte[]
        -DecryptData(data) byte[]
    }
    
    class MemoryStorageProvider {
        +Dictionary~string,object~ Cache
        +int MaxCacheSize
        +TimeSpan DefaultExpiry
        
        +Save~T~(key, data) void
        +Load~T~(key) T
        +Delete(key) void
        +Exists(key) bool
        +ListKeys() List~string~
        +Clear() void
        
        -CleanupExpiredItems() void
        -CheckCacheSize() void
    }
    
    class ETWebStorageConfig {
        +string StorageRoot
        +bool EnableEncryption
        +bool EnableCompression
        +int MaxCacheSize
        +TimeSpan CacheExpiry
        +int BackupRetentionDays
        +bool AutoBackup
        +string EncryptionKey
        
        +Validate() bool
        +LoadFromFile(path) void
        +SaveToFile(path) void
    }
    
    ETWebStorageManager --> IStorageProvider
    FileStorageProvider ..|> IStorageProvider
    MemoryStorageProvider ..|> IStorageProvider
    ETWebStorageManager --> ETWebStorageConfig
    ETWebStorageManager --> ETWebStorageHelper
```

## 💾 数据存储决策流程

```mermaid
flowchart TD
    A[存储请求] --> B[分析数据类型]
    B --> C{数据分类}
    C -->|配置数据| D[使用配置存储]
    C -->|会话数据| E[使用会话存储]
    C -->|缓存数据| F[使用内存存储]
    C -->|文件数据| G[使用文件存储]
    C -->|临时数据| H[使用临时存储]
    
    D --> I[检查是否需要加密]
    E --> I
    F --> J[检查缓存大小限制]
    G --> K[检查是否需要压缩]
    H --> L[设置过期时间]
    
    I --> M{是否需要加密}
    M -->|是| N[加密数据]
    M -->|否| O[直接存储]
    
    J --> P{缓存是否已满}
    P -->|是| Q[清理过期缓存]
    P -->|否| R[直接缓存]
    
    K --> S{文件是否过大}
    S -->|是| T[压缩数据]
    S -->|否| U[直接存储]
    
    L --> V[设置自动清理]
    
    N --> W[写入存储]
    O --> W
    Q --> R
    R --> X[更新缓存]
    T --> W
    U --> W
    V --> Y[写入临时存储]
    
    W --> Z[验证存储结果]
    X --> AA[返回缓存成功]
    Y --> Z
    Z --> BB{存储是否成功}
    BB -->|是| CC[返回存储成功]
    BB -->|否| DD[返回存储失败]
    
    style A fill:#e1f5fe
    style CC fill:#c8e6c9
    style AA fill:#c8e6c9
    style DD fill:#ffcdd2
```

## 🔐 数据加密存储流程

```mermaid
sequenceDiagram
    participant Client as ETWebClient
    participant Manager as ETWebStorageManager
    participant Provider as FileStorageProvider
    participant Crypto as CryptoHelper
    participant File as FileSystem
    
    Client->>Manager: SaveData(key, data, "session")
    Manager->>Manager: 验证存储键
    Manager->>Manager: 获取存储提供者
    Manager->>Provider: Save(key, data)
    
    Provider->>Provider: 检查加密配置
    alt 需要加密
        Provider->>Crypto: EncryptData(data)
        Crypto->>Crypto: 生成加密密钥
        Crypto->>Crypto: AES加密数据
        Crypto-->>Provider: 返回加密结果
    else 不需要加密
        Provider->>Provider: 使用原始数据
    end
    
    Provider->>Provider: 检查压缩配置
    alt 需要压缩
        Provider->>Provider: CompressData(data)
        Provider-->>Provider: 返回压缩结果
    end
    
    Provider->>Provider: 序列化数据
    Provider->>File: 写入文件
    File-->>Provider: 写入完成
    
    Provider->>Provider: 验证文件完整性
    Provider-->>Manager: 返回存储结果
    Manager-->>Client: 返回操作结果
    
    Note over Client,File: 数据读取流程
    Client->>Manager: LoadData(key, "session")
    Manager->>Provider: Load(key)
    Provider->>File: 读取文件
    File-->>Provider: 返回文件内容
    
    Provider->>Provider: 反序列化数据
    
    alt 数据已压缩
        Provider->>Provider: DecompressData(data)
    end
    
    alt 数据已加密
        Provider->>Crypto: DecryptData(data)
        Crypto->>Crypto: AES解密数据
        Crypto-->>Provider: 返回解密结果
    end
    
    Provider-->>Manager: 返回原始数据
    Manager-->>Client: 返回数据对象
```

### 存储管理器实现

```csharp
public class ETWebStorageManager
{
    private static readonly string DefaultStorageRoot = Path.Combine(
        Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
        "ETWebAutomation", "Storage"
    );
    
    private readonly Dictionary<string, IStorageProvider> _providers;
    private readonly ETWebStorageConfig _config;
    private readonly object _lockObject = new object();
    
    public ETWebStorageManager(ETWebStorageConfig config = null)
    {
        _config = config ?? ETWebStorageConfig.Default;
        _providers = new Dictionary<string, IStorageProvider>();
        
        InitializeProviders();
    }
    
    public void SaveData<T>(string key, T data, string category = "default")
    {
        lock (_lockObject)
        {
            try
            {
                if (!ValidateKey(key))
                    throw new ArgumentException($"无效的存储键: {key}");
                
                var provider = GetProvider(category);
                provider.Save(key, data);
                
                ETLogManager.Debug($"数据已保存: 类别={category}, 键={key}, 类型={typeof(T).Name}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"保存数据异常: 类别={category}, 键={key}, 错误={ex.Message}", ex);
                throw;
            }
        }
    }
    
    public T LoadData<T>(string key, string category = "default")
    {
        lock (_lockObject)
        {
            try
            {
                if (!ValidateKey(key))
                    return default(T);
                
                var provider = GetProvider(category);
                var data = provider.Load<T>(key);
                
                ETLogManager.Debug($"数据已加载: 类别={category}, 键={key}, 类型={typeof(T).Name}");
                return data;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"加载数据异常: 类别={category}, 键={key}, 错误={ex.Message}", ex);
                return default(T);
            }
        }
    }
    
    public void DeleteData(string key, string category = "default")
    {
        lock (_lockObject)
        {
            try
            {
                if (!ValidateKey(key))
                    return;
                
                var provider = GetProvider(category);
                provider.Delete(key);
                
                ETLogManager.Debug($"数据已删除: 类别={category}, 键={key}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"删除数据异常: 类别={category}, 键={key}, 错误={ex.Message}", ex);
            }
        }
    }
    
    public bool ExistsData(string key, string category = "default")
    {
        lock (_lockObject)
        {
            try
            {
                if (!ValidateKey(key))
                    return false;
                
                var provider = GetProvider(category);
                return provider.Exists(key);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"检查数据存在异常: 类别={category}, 键={key}, 错误={ex.Message}", ex);
                return false;
            }
        }
    }
    
    public List<string> ListKeys(string category = "default")
    {
        lock (_lockObject)
        {
            try
            {
                var provider = GetProvider(category);
                return provider.ListKeys();
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"列出存储键异常: 类别={category}, 错误={ex.Message}", ex);
                return new List<string>();
            }
        }
    }
    
    public string CreateBackup(string category = "default")
    {
        lock (_lockObject)
        {
            try
            {
                var provider = GetProvider(category);
                var backupPath = Path.Combine(_config.StorageRoot, "Backups", 
                    $"{category}_{DateTime.Now:yyyyMMdd_HHmmss}.backup");
                
                Directory.CreateDirectory(Path.GetDirectoryName(backupPath));
                
                // 获取所有数据
                var keys = provider.ListKeys();
                var backupData = new Dictionary<string, object>();
                
                foreach (var key in keys)
                {
                    try
                    {
                        var data = provider.Load<object>(key);
                        backupData[key] = data;
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Warning($"备份数据项失败: {key}, 错误: {ex.Message}");
                    }
                }
                
                // 保存备份文件
                var backupJson = ETWebJsonHelper.ToJson(backupData, true);
                var encryptedBackup = ETWebStorageHelper.EncryptData(backupJson, "backup_data");
                File.WriteAllText(backupPath, encryptedBackup);
                
                ETLogManager.Info($"备份已创建: 类别={category}, 路径={backupPath}, 数据项={backupData.Count}");
                return backupPath;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"创建备份异常: 类别={category}, 错误={ex.Message}", ex);
                throw;
            }
        }
    }
    
    public void RestoreBackup(string backupPath, string category = "default")
    {
        lock (_lockObject)
        {
            try
            {
                if (!File.Exists(backupPath))
                    throw new FileNotFoundException($"备份文件不存在: {backupPath}");
                
                // 读取备份文件
                var encryptedBackup = File.ReadAllText(backupPath);
                var backupJson = ETWebStorageHelper.DecryptData(encryptedBackup, "backup_data");
                var backupData = ETWebJsonHelper.FromJson<Dictionary<string, object>>(backupJson);
                
                var provider = GetProvider(category);
                
                // 清空现有数据
                provider.Clear();
                
                // 恢复数据
                var restoredCount = 0;
                foreach (var kvp in backupData)
                {
                    try
                    {
                        provider.Save(kvp.Key, kvp.Value);
                        restoredCount++;
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Warning($"恢复数据项失败: {kvp.Key}, 错误: {ex.Message}");
                    }
                }
                
                ETLogManager.Info($"备份已恢复: 类别={category}, 路径={backupPath}, 恢复数据项={restoredCount}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"恢复备份异常: 类别={category}, 路径={backupPath}, 错误={ex.Message}", ex);
                throw;
            }
        }
    }
    
    private void InitializeProviders()
    {
        try
        {
            // 文件存储提供者
            _providers["default"] = new FileStorageProvider(
                Path.Combine(_config.StorageRoot, "Default"),
                _config.EnableEncryption,
                _config.EnableCompression
            );
            
            _providers["config"] = new FileStorageProvider(
                Path.Combine(_config.StorageRoot, "Config"),
                _config.EnableEncryption,
                false // 配置文件不压缩
            );
            
            _providers["session"] = new FileStorageProvider(
                Path.Combine(_config.StorageRoot, "Sessions"),
                true, // 会话数据必须加密
                _config.EnableCompression
            );
            
            _providers["cache"] = new MemoryStorageProvider(
                _config.MaxCacheSize,
                _config.CacheExpiry
            );
            
            _providers["temp"] = new MemoryStorageProvider(
                1000, // 临时数据最大1000项
                TimeSpan.FromMinutes(30) // 30分钟过期
            );
            
            ETLogManager.Debug($"存储提供者已初始化: {_providers.Count} 个");
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"初始化存储提供者异常: {ex.Message}", ex);
            throw;
        }
    }
    
    private IStorageProvider GetProvider(string category)
    {
        if (_providers.TryGetValue(category, out var provider))
            return provider;
        
        // 如果没有找到指定类别的提供者，使用默认提供者
        return _providers["default"];
    }
    
    private bool ValidateKey(string key)
    {
        if (string.IsNullOrWhiteSpace(key))
            return false;
        
        // 检查键名是否包含非法字符
        var invalidChars = Path.GetInvalidFileNameChars();
        return !key.Any(c => invalidChars.Contains(c));
    }
    
    private void EnsureStorageDirectory(string path)
    {
        try
        {
            if (!Directory.Exists(path))
                Directory.CreateDirectory(path);
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"创建存储目录异常: {path}, 错误: {ex.Message}", ex);
            throw;
        }
    }
}
```

## 📁 文件存储提供者流程

```mermaid
flowchart TD
    A[文件存储请求] --> B[验证存储路径]
    B --> C[生成文件路径]
    C --> D[序列化数据]
    D --> E{是否需要压缩}
    E -->|是| F[压缩数据]
    E -->|否| G[保持原始数据]
    F --> H{是否需要加密}
    G --> H
    H -->|是| I[加密数据]
    H -->|否| J[保持明文数据]
    I --> K[写入文件]
    J --> K
    K --> L[验证文件完整性]
    L --> M{验证是否通过}
    M -->|是| N[返回存储成功]
    M -->|否| O[返回存储失败]
    
    style A fill:#e1f5fe
    style N fill:#c8e6c9
    style O fill:#ffcdd2
```

---

**📅 文档版本**: v1.0  
**🔄 最后更新**: 2024年12月  
**👨‍💻 维护团队**: ETWebAutomation开发组
