﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Aliyun.OSS.SDK" version="2.14.1" targetFramework="net48" />
  <package id="CefSharp.Common" version="138.0.170" targetFramework="net48" />
  <package id="CefSharp.WinForms" version="138.0.170" targetFramework="net48" />
  <package id="chromiumembeddedframework.runtime.win-x64" version="138.0.17" targetFramework="net48" />
  <package id="chromiumembeddedframework.runtime.win-x86" version="138.0.17" targetFramework="net48" />
  <package id="Flurl" version="4.0.0" targetFramework="net48" />
  <package id="Flurl.Http" version="4.0.2" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.6" targetFramework="net48" />
  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="9.0.5" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="9.0.5" targetFramework="net48" />
  <package id="Microsoft.Web.WebView2" version="1.0.3240.44" targetFramework="net48" />
  <package id="NetTopologySuite" version="2.6.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="OpenAI" version="2.1.0" targetFramework="net48" />
  <package id="SharpCompress" version="0.40.0" targetFramework="net48" />
  <package id="System.Buffers" version="4.6.1" targetFramework="net48" />
  <package id="System.ClientModel" version="1.4.1" targetFramework="net48" />
  <package id="System.Collections.Immutable" version="9.0.5" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="9.0.5" targetFramework="net48" />
  <package id="System.IO" version="4.3.0" targetFramework="net48" />
  <package id="System.IO.Pipelines" version="9.0.6" targetFramework="net48" />
  <package id="System.Memory" version="4.6.3" targetFramework="net48" />
  <package id="System.Memory.Data" version="9.0.6" targetFramework="net48" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.6.1" targetFramework="net48" />
  <package id="System.Runtime" version="4.3.1" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.2" targetFramework="net48" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.1" targetFramework="net48" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.2" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="9.0.6" targetFramework="net48" />
  <package id="System.Text.Json" version="9.0.6" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.6.3" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.6.1" targetFramework="net48" />
  <package id="ZstdSharp.Port" version="0.8.5" targetFramework="net48" />
</packages>