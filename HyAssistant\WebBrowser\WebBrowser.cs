using ET;
using ET.ETLoginWebBrowser;
using Microsoft.Web.WebView2.WinForms;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// WebBrowser窗体，多页签式网页浏览器控件
    /// </summary>
    public partial class WebBrowser : Form
    {
        #region 字段和属性

        private bool _isRealClosing = false;
        private bool _isMinimizedToTray = false; // 是否最小化到托盘



        /// <summary>
        /// 配置管理器
        /// </summary>
        private WebBrowserConfigManager _configManager;

        /// <summary>
        /// 标头管理器（兼容Cookie管理）
        /// </summary>
        private WebBrowserCookieManager _cookieManager;

        /// <summary>
        /// 会话管理器
        /// </summary>
        private WebBrowserSessionManager _sessionManager;

        /// <summary>
        /// 标签页管理器
        /// </summary>
        private WebBrowserTabManager _tabManager;

        /// <summary>
        /// 标签页菜单管理器
        /// </summary>
        private TabMenuManager _tabMenuManager;

        /// <summary>
        /// 窗体关闭处理器
        /// </summary>
        private FormClosingHandler _formClosingHandler;

        /// <summary>
        /// 资源管理器
        /// </summary>
        private WebBrowserResourceManager _resourceManager;

        /// <summary>
        /// 窗体真正关闭时触发的事件
        /// </summary>
        public event EventHandler RealClose;

        /// <summary>
        /// 鼠标位置监控定时器
        /// </summary>
        private System.Windows.Forms.Timer _mouseMonitorTimer;

        #endregion 字段和属性

        #region 界面

        /// <summary>
        /// 窗体大小改变事件处理
        /// </summary>
        private void WebBrowser_SizeChanged(object sender, EventArgs e)
        {
            try
            {
                if (urlTextBox != null && toolStrip1 != null)
                {
                    // 计算工具栏中除地址栏外所有控件的总宽度
                    int otherControlsWidth = 0;
                    foreach (ToolStripItem item in toolStrip1.Items)
                    {
                        if (item != urlTextBox)
                        {
                            otherControlsWidth += item.Width;
                        }
                    }

                    // 添加边距
                    otherControlsWidth += WebBrowserConstants.TOOLBAR_MARGIN;

                    // 计算地址栏应占用的宽度
                    int availableWidth = toolStrip1.Width - otherControlsWidth;

                    // 确保宽度至少为最小宽度
                    int newWidth = Math.Max(WebBrowserConstants.MIN_ADDRESS_BAR_WIDTH, availableWidth);

                    // 设置地址栏宽度
                    if (urlTextBox.Width != newWidth)
                    {
                        urlTextBox.Width = newWidth;
                    }
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandler.HandleUIException(this, ex, "调整地址栏宽度");
            }
        }

        /// <summary>
        /// 鼠标监控定时器Tick事件
        /// </summary>
        private void MouseMonitorTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                // 如果会话日志TextBox可见，检查鼠标位置
                if (sessionLogTextBox != null && sessionLogTextBox.Visible)
                {
                    // 获取当前鼠标在窗体中的位置
                    Point mousePos = PointToClient(Control.MousePosition);

                    // 如果鼠标既不在会话日志TextBox区域内，也不在状态栏区域内，则隐藏TextBox
                    if (!sessionLogTextBox.Bounds.Contains(mousePos) &&
                        !statusStrip1.Bounds.Contains(mousePos))
                    {
                        sessionLogTextBox.Visible = false;
                        _mouseMonitorTimer.Stop();
                    }
                }
                else
                {
                    // 如果TextBox已不可见，停止定时器
                    _mouseMonitorTimer.Stop();
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandler.HandleException(this, ex, "监控鼠标位置");
                _mouseMonitorTimer.Stop();
            }
        }

        /// <summary>
        /// 初始化标签页下拉菜单
        /// </summary>
        private void InitializeTabsDropDownMenu()
        {
            _tabMenuManager?.RefreshIfNeeded();
        }

        /// <summary>
        /// 标签页菜单项点击事件处理
        /// </summary>
        private void TabMenuManager_TabMenuItemClicked(object sender, WebBrowserTabConfig config)
        {
            TabsMenuItem_Click_Internal(config);
        }

        /// <summary>
        /// 新建标签页菜单项点击事件处理
        /// </summary>
        private void TabMenuManager_NewTabMenuItemClicked(object sender, EventArgs e)
        {
            新建网站ToolStripMenuItem_Click(sender, e);
        }



        /// <summary>
        /// 注册所有事件处理程序
        /// </summary>
        private void RegisterEventHandlers()
        {
            // 注册工具栏大小变化事件
            toolStrip1.SizeChanged -= ToolStrip1_SizeChanged;
            toolStrip1.SizeChanged += ToolStrip1_SizeChanged;

            // 注册会话日志事件
            sessionLogTextBox.MouseLeave -= SessionLogTextBox_MouseLeave;
            sessionLogTextBox.MouseLeave += SessionLogTextBox_MouseLeave;

            // 为工具栏上的Cookies按钮添加点击事件
            cookiesButton.Click -= ManageCookiesMenuItem_Click;
            cookiesButton.Click += ManageCookiesMenuItem_Click;

            // 注册新增的Cookie相关按钮事件
            copyCookiesConfigButton.Click -= CopyCookiesConfigButton_Click;
            copyCookiesConfigButton.Click += CopyCookiesConfigButton_Click;

            pasteCookiesButton.Click -= PasteCookiesButton_Click;
            pasteCookiesButton.Click += PasteCookiesButton_Click;

            // 确保右键菜单事件正确绑定 如果Designer中已经绑定，则此处的绑定不会重复生效
            SafeRegisterMenuEvent(closeTabMenuItem, CloseTabMenuItem_Click);
            SafeRegisterMenuEvent(editTabSettingsMenuItem, EditTabSettingsMenuItem_Click);
            SafeRegisterMenuEvent(删除网站ToolStripMenuItem, 删除网站ToolStripMenuItem_Click);
            SafeRegisterMenuEvent(viewSessionStatusMenuItem, ViewSessionStatusMenuItem_Click);

            // 绑定复制标签页菜单项点击事件
            SafeRegisterMenuEvent(复制标签页ToolStripMenuItem, CloneTabMenuItem_Click);

            // 绑定退出登录菜单项点击事件
            SafeRegisterMenuEvent(退出登录ToolStripMenuItem, LogoutTabMenuItem_Click);

            // 初始化标签页下拉菜单
            InitializeTabsDropDownMenu();
        }

        /// <summary>
        /// 安全地注册菜单事件处理程序
        /// </summary>
        private void SafeRegisterMenuEvent(ToolStripMenuItem menuItem, EventHandler handler)
        {
            if (menuItem != null)
            {
                menuItem.Click -= handler;
                menuItem.Click += handler;
            }
        }

        /// <summary>
        /// 新建标签页菜单项点击事件
        /// </summary>
        private void 新建网站ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 创建默认配置，优先使用当前标签页配置作为模板
            WebBrowserTabConfig config = _tabManager.GetCurrentTabConfig();
            if (config == null)
            {
                config = new WebBrowserTabConfig
                {
                    Name = "新标签页",
                    Url = "about:blank",
                    HomeUrl = "about:blank"
                };
            }

            try
            {
                // 显示配置窗体
                using (TabConfigForm configForm = new TabConfigForm(config, true)) // true表示是新建模式
                {
                    if (configForm.ShowDialog() == DialogResult.OK && configForm.Confirmed)
                    {
                        // 获取新的配置
                        WebBrowserTabConfig newConfig = configForm.Config;

                        // 创建新的标签页
                        _tabManager.CreateNewTab(newConfig);

                        // 标记菜单需要刷新
                        _tabMenuManager?.MarkForRefresh();

                        // 更新状态栏刷新状态信息
                        WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, newConfig);
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"新建标签页失败: {ex.Message}");
                MessageBox.Show($"新建标签页失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 界面

        #region 构造和初始化

        /// <summary>
        /// 构造函数
        /// </summary>
        public WebBrowser()
        {
            try
            {
                InitializeComponent();

                // 初始化日志
                ETLogManager.Info(this, "WebBrowser窗体已创建");

                // 添加窗体大小改变事件
                SizeChanged += WebBrowser_SizeChanged;

                // 先初始化各管理器
                InitializeManagers();

                // 初始化鼠标监控定时器
                _mouseMonitorTimer = new System.Windows.Forms.Timer
                {
                    Interval = WebBrowserConstants.MOUSE_MONITOR_INTERVAL
                };
                _mouseMonitorTimer.Tick += MouseMonitorTimer_Tick;

                // 注册到资源管理器
                _resourceManager.RegisterTimer(_mouseMonitorTimer);

                ETLogManager.Info(this, "WebBrowser构造函数执行完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"WebBrowser构造函数执行失败: {ex.Message}");
                ETLogManager.Error(this, $"堆栈跟踪: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// 初始化各管理器
        /// </summary>
        private void InitializeManagers()
        {
            try
            {
                ETLogManager.Info(this, "开始初始化各管理器");

                // 创建资源管理器
                _resourceManager = new WebBrowserResourceManager(this);
                ETLogManager.Info(this, "资源管理器创建完成");

                // 创建配置管理器
                _configManager = new WebBrowserConfigManager();
                ETLogManager.Info(this, "配置管理器创建完成");

                // 创建标头管理器（兼容Cookie管理）
                _cookieManager = new WebBrowserCookieManager(this);
                ETLogManager.Info(this, "Cookie管理器创建完成");

                // 创建会话管理器
                _sessionManager = new WebBrowserSessionManager(this);
                _sessionManager.SessionLogUpdated += SessionLogUpdated;
                ETLogManager.Info(this, "会话管理器创建完成");

                // 验证必要的UI控件
                if (tabControl1 == null)
                    throw new InvalidOperationException("tabControl1 控件未初始化");
                if (tabsDropDownButton == null)
                    throw new InvalidOperationException("tabsDropDownButton 控件未初始化");

                // 创建标签页管理器
                _tabManager = new WebBrowserTabManager(tabControl1, _configManager, _cookieManager, _sessionManager, this);
                _tabManager.TabUrlChanged += TabUrlChanged;
                _tabManager.TabTitleChanged += TabTitleChanged;
                _tabManager.TabSwitched += TabSwitched;
                ETLogManager.Info(this, "标签页管理器创建完成");

                // 创建标签页菜单管理器
                _tabMenuManager = new TabMenuManager(tabsDropDownButton, _configManager, this);
                _tabMenuManager.TabMenuItemClicked += TabMenuManager_TabMenuItemClicked;
                _tabMenuManager.NewTabMenuItemClicked += TabMenuManager_NewTabMenuItemClicked;
                ETLogManager.Info(this, "标签页菜单管理器创建完成");

                // 创建窗体关闭处理器
                _formClosingHandler = new FormClosingHandler(this, _tabManager, _configManager, _sessionManager, this);
                ETLogManager.Info(this, "窗体关闭处理器创建完成");

                ETLogManager.Info(this, "所有管理器初始化完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"初始化管理器失败: {ex.Message}");
                ETLogManager.Error(this, $"堆栈跟踪: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void WebBrowser_Load(object sender, EventArgs e)
        {
            try
            {
                // 初始调整地址栏宽度 在窗体完全加载后执行，确保所有控件已正确布局
                BeginInvoke(new Action(() => WebBrowser_SizeChanged(this, EventArgs.Empty)));

                // 初始化配置文件
                _configManager.InitConfig();

                // 移除默认标签页
                tabControl1.TabPages.Clear();

                // 注册所有事件处理程序，确保事件只注册一次
                RegisterEventHandlers();

                // 初始化标签页右键菜单
                InitializeTabContextMenu();

                // 导航按钮状态默认为禁用，直到标签页创建并初始化完成
                WebBrowserUIHelper.UpdateNavigationButtonState(toolStrip1, false);

                // 标签页下拉菜单始终可用
                tabsDropDownButton.Enabled = true;

                // 设置默认窗口标题
                Text = "浏览器";

                // 初始化状态栏信息
                WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, null);

                // 初始调整地址栏宽度已通过Spring属性自动实现

                ETLogManager.Info(this, "WebBrowser窗体加载完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"窗体加载失败: {ex.Message}");
                MessageBox.Show($"窗体加载失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 防止重复点击的标志
        private volatile bool _isCreatingTab = false;

        /// <summary>
        /// 标签页菜单项点击事件
        /// </summary>
        private void TabsMenuItem_Click(object sender, EventArgs e)
        {
            if (sender is ToolStripMenuItem menuItem && menuItem.Tag is WebBrowserTabConfig config)
            {
                TabsMenuItem_Click_Internal(config);
            }
        }

        /// <summary>
        /// 标签页菜单项点击核心逻辑
        /// </summary>
        private void TabsMenuItem_Click_Internal(WebBrowserTabConfig config)
        {
            try
            {
                // 防止重复点击导致的并发问题
                if (_isCreatingTab)
                {
                    return;
                }

                _isCreatingTab = true;
                    // 检查当前窗体是否处于最小化到托盘状态，如果是则显示窗体
                    if (_isMinimizedToTray)
                    {
                        Show();
                        WindowState = FormWindowState.Normal;
                        Activate();
                        _isMinimizedToTray = false;

                        // 给UI更新一个短暂的延迟
                        Application.DoEvents();
                    }

                    // 在打开标签页前，尝试从配置文件重新加载最新配置
                    WebBrowserTabConfig latestConfig = config;
                    if (!string.IsNullOrEmpty(config.SectionId))
                    {
                        try
                        {
                            // 强制重新加载配置文件，确保获取最新配置
                            _configManager.ReloadConfig();

                            // 从配置管理器获取最新配置
                            var allConfigs = _configManager.GetAllTabConfigs();
                            var foundConfig = allConfigs.FirstOrDefault(c => c.SectionId == config.SectionId);
                            if (foundConfig != null)
                            {
                                latestConfig = foundConfig;
                                ETLogManager.Info(this, $"已从配置文件重新加载最新配置: {latestConfig.Name}, SectionId: {latestConfig.SectionId}");
                            }
                            else
                            {
                                ETLogManager.Warning(this, $"未找到SectionId为 {config.SectionId} 的配置，使用传入配置");
                            }
                        }
                        catch (Exception loadEx)
                        {
                            ETLogManager.Warning(this, $"重新加载配置失败，使用传入配置: {loadEx.Message}");
                        }
                    }

                    // 检查是否已存在相同SectionId的标签页
                    if (_tabManager.TabDataDictionary.ContainsKey(latestConfig.SectionId) &&
                        _tabManager.TabDataDictionary[latestConfig.SectionId].TabPage != null &&
                        tabControl1.TabPages.ContainsKey(latestConfig.SectionId))
                    {
                        // 切换到已存在的标签页，并更新其配置为最新配置
                        tabControl1.SelectedTab = tabControl1.TabPages[latestConfig.SectionId];

                        // 更新现有标签页的配置为最新配置
                        _tabManager.TabDataDictionary[latestConfig.SectionId].Config = latestConfig;
                        ETLogManager.Info(this, $"已更新现有标签页配置为最新版本: {latestConfig.Name}");

                        // 更新状态栏刷新状态信息
                        WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, latestConfig);
                    }
                    else
                    {
                        // 如果标签页存在于TabDataDictionary但TabPage为null或不在TabControl中 说明标签页被释放但保留了配置信息，需要重新创建
                        if (_tabManager.TabDataDictionary.ContainsKey(latestConfig.SectionId) &&
                            (_tabManager.TabDataDictionary[latestConfig.SectionId].TabPage == null ||
                             !tabControl1.TabPages.ContainsKey(latestConfig.SectionId)))
                        {
                            // 确保先从TabDataDictionary中移除旧数据
                            _tabManager.CloseTabBySectionId(latestConfig.SectionId);
                        }

                        // 使用最新配置创建新的标签页
                        _tabManager.CreateNewTab(latestConfig);

                        // 确保导航按钮在创建标签页后启用
                        WebBrowserUIHelper.UpdateNavigationButtonState(toolStrip1, true);

                        // 更新状态栏刷新状态信息
                        WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, latestConfig);
                    }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandler.HandleException(this, ex, "打开标签页", true);
            }
            finally
            {
                // 确保在所有情况下都重置创建标志
                _isCreatingTab = false;
            }
        }

        #endregion 构造和初始化

        #region 事件处理方法

        /// <summary>
        /// 标签页URL变更事件处理程序
        /// </summary>
        private void TabUrlChanged(string tabName, string url)
        {
            WebBrowserUIHelper.SetToolStripTextBoxTextSafe(urlTextBox, url);
        }

        /// <summary>
        /// 标签页标题变更事件处理程序
        /// </summary>
        private void TabTitleChanged(string tabName, string title)
        {
            WebBrowserUIHelper.UpdateFormTitle(this, title);
        }

        /// <summary>
        /// 标签页切换事件处理程序
        /// </summary>
        private void TabSwitched(string tabSectionId)
        {
            ThreadSafeHelper.SafeInvoke(this, () =>
            {

                // 如果标签页名称为空，表示没有标签页，清空地址栏和标题，禁用导航按钮
                if (string.IsNullOrEmpty(tabSectionId))
                {
                    // 分别处理不同类型的控件
                    WebBrowserUIHelper.SetToolStripTextBoxTextSafe(urlTextBox, string.Empty);
                    WebBrowserUIHelper.SetControlTextSafe(this, "浏览器");
                    if (tabsDropDownButton != null)
                    {
                        tabsDropDownButton.Enabled = true;
                    }
                    WebBrowserUIHelper.UpdateNavigationButtonState(toolStrip1, false);
                    WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, null);
                    return;
                }

                // 启用所有导航按钮
                WebBrowserUIHelper.UpdateNavigationButtonState(toolStrip1, true);
                tabsDropDownButton.Enabled = true;

                // 获取当前标签页数据 - 使用SectionId
                if (_tabManager.TabDataDictionary.TryGetValue(tabSectionId, out WebBrowserTabManager.TabData tabData))
                {
                    try
                    {
                        // 更新地址栏
                        string currentUrl;
                        if (tabData.WebView != null && tabData.WebView.Source != null)
                        {
                            currentUrl = tabData.WebView.Source.ToString();
                        }
                        else if (!string.IsNullOrEmpty(tabData.LastUrl))
                        {
                            currentUrl = tabData.LastUrl;
                        }
                        else
                        {
                            currentUrl = tabData.Config?.Url ?? "about:blank";
                        }
                        WebBrowserUIHelper.SetToolStripTextBoxTextSafe(urlTextBox, currentUrl);

                        // 更新窗体标题
                        string title;
                        if (tabData.WebView?.CoreWebView2 != null)
                        {
                            title = tabData.WebView.CoreWebView2.DocumentTitle;
                        }
                        else
                        {
                            title = tabData.Config?.Name ?? "未命名";
                        }
                        WebBrowserUIHelper.UpdateFormTitle(this, title);

                        // 更新状态栏右侧的刷新状态信息
                        WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, tabData.Config);
                    }
                    catch (Exception ex)
                    {
                        WebBrowserExceptionHandler.HandleException(this, ex, "标签页切换");
                        Text = "浏览器";
                    }
                }
            });
        }



        /// <summary>
        /// 会话日志更新事件处理程序
        /// </summary>
        private void SessionLogUpdated(string message)
        {
            ThreadSafeHelper.SafeInvoke(this, () =>
            {
                // 使用新的日志助手类
                SessionLogHelper.AppendLog(sessionLogTextBox, message);

                // 同时更新状态栏
                statusLabel.Text = message;

                // 由于会话状态可能已更改，更新当前标签页的刷新状态显示
                if (tabControl1.SelectedTab != null)
                {
                    string tabName = tabControl1.SelectedTab.Name;
                    if (_tabManager.TabDataDictionary.TryGetValue(tabName, out WebBrowserTabManager.TabData tabData))
                    {
                        WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, tabData.Config);
                    }
                }
            });
        }



        /// <summary>
        /// TabControl选项卡切换事件
        /// </summary>
        private void TabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 委托给TabManager处理
            _tabManager.TabControl_SelectedIndexChanged(sender, e);

            // 更新状态栏刷新状态信息
            if (tabControl1.SelectedTab != null)
            {
                string tabName = tabControl1.SelectedTab.Name;
                if (_tabManager.TabDataDictionary.TryGetValue(tabName, out WebBrowserTabManager.TabData tabData))
                {
                    WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, tabData.Config);
                }
                else
                {
                    WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, null);
                }
            }
            else
            {
                WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, null);
            }
        }

        /// <summary>
        /// 关闭标签页菜单项点击事件
        /// </summary>
        private void CloseTabMenuItem_Click(object sender, EventArgs e)
        {
            // 委托给TabManager处理
            _tabManager.CloseCurrentTab();
        }

        /// <summary>
        /// 地址栏键盘事件
        /// </summary>
        private void UrlTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                // 获取当前标签页数据
                WebBrowserTabManager.TabData currentTabData = _tabManager.GetCurrentTabData();
                if (currentTabData != null)
                {
                    // 只更新LastUrl，不更新配置
                    currentTabData.LastUrl = urlTextBox.Text;
                    _tabManager.NavigateToUrl(urlTextBox.Text);
                }
                e.Handled = true;
                e.SuppressKeyPress = true;
            }
        }

        /// <summary>
        /// 后退按钮点击事件
        /// </summary>
        private void BackButton_Click(object sender, EventArgs e)
        {
            _tabManager.GoBack();
        }

        /// <summary>
        /// 前进按钮点击事件
        /// </summary>
        private void ForwardButton_Click(object sender, EventArgs e)
        {
            _tabManager.GoForward();
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, EventArgs e)
        {
            _tabManager.Refresh();
        }

        /// <summary>
        /// 停止按钮点击事件
        /// </summary>
        private void StopButton_Click(object sender, EventArgs e)
        {
            _tabManager.Stop();
        }

        /// <summary>
        /// 主页按钮点击事件
        /// </summary>
        private void HomeButton_Click(object sender, EventArgs e)
        {
            _tabManager.GoHome();
        }

        /// <summary>
        /// 窗体关闭前触发事件
        /// </summary>
        private void WebBrowser_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // 如果是真正关闭，直接返回
                if (_isRealClosing)
                    return;

                // 使用窗体关闭处理器处理复杂逻辑
                bool shouldCancel = _formClosingHandler?.HandleFormClosing(e) ?? false;

                if (shouldCancel)
                {
                    _isMinimizedToTray = true;
                }
                else
                {
                    _isRealClosing = true;
                }







            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"窗体关闭时发生异常: {ex.Message}");

                // 如果出现异常，设置为真正关闭，防止循环异常导致应用无法退出
                _isRealClosing = true;
            }
        }

        // UpdateMainMenuStatus方法，使用静态事件而非查找实例
        private void UpdateMainMenuStatus(bool isHidden)
        {
            try
            {
                // 使用静态事件通知MainForm
                WebBrowserStatusBridge.OnStatusChanged(isHidden);

                // 记录状态变更日志
                System.Diagnostics.Debug.WriteLine($"WebBrowser状态已改变，isHidden={isHidden}");
            }
            catch (Exception ex)
            {
                // 记录错误但不中断操作
                System.Diagnostics.Debug.WriteLine($"更新菜单状态出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 完全退出按钮点击事件
        /// </summary>
        private void CompleteExitButton_Click(object sender, EventArgs e)
        {
            try
            {
                // 显示确认对话框
                DialogResult result = MessageBox.Show(
                    "确定要完全退出浏览器吗？这将停止所有后台会话维持。",
                    "确认退出",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    DoRealClose();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"完全退出时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void WebBrowser_FormClosed(object sender, FormClosedEventArgs e)
        {
            try
            {
                if (_isRealClosing)
                {
                    // 停止所有会话刷新（包括WebView2和HttpClient）
                    _sessionManager.StopAllRefreshers();

                    // 清理所有标签页资源
                    _tabManager.CleanupAllTabs();

                    // 通知程序窗体已真正关闭
                    RealClose?.Invoke(this, EventArgs.Empty);

                    ETLogManager.Info(this, "WebBrowser窗体已关闭，所有资源已释放");
                }
                else
                {
                    // 非真正关闭情况下，只停止WebView2刷新，保持HttpClient会话维持
                    _sessionManager.StopAllWebView2Refreshers();
                    ETLogManager.Info(this, "WebBrowser窗体已关闭，但HttpClient会话维持保持运行");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, ex);
            }
        }

        /// <summary>
        /// StatusStrip鼠标进入事件
        /// </summary>
        private void StatusStrip1_MouseEnter(object sender, EventArgs e)
        {
            try
            {
                // 显示会话日志
                if (sessionLogTextBox != null && !sessionLogTextBox.IsDisposed && statusStrip1 != null && !statusStrip1.IsDisposed)
                {
                    // 确保状态栏位置正确
                    if (statusStrip1.Location.Y > sessionLogTextBox.Height)
                    {
                        // 设置位置在statusStrip1上方
                        sessionLogTextBox.Location = new Point(0, statusStrip1.Location.Y - sessionLogTextBox.Height);
                        sessionLogTextBox.Width = ClientSize.Width;
                        sessionLogTextBox.BringToFront();
                        sessionLogTextBox.Visible = true;

                        // 启动鼠标监控定时器
                        if (_mouseMonitorTimer != null && !_mouseMonitorTimer.Enabled)
                        {
                            _mouseMonitorTimer.Start();
                            // 精简日志：移除鼠标位置监控的Debug日志
                            // ETLogManager.Debug(this, "已启动会话日志鼠标位置监控");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, $"显示会话日志时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// StatusStrip鼠标离开事件
        /// </summary>
        private void StatusStrip1_MouseLeave(object sender, EventArgs e)
        {
            // 隐藏会话日志
            if (sessionLogTextBox != null)
            {
                // 检查鼠标是否在sessionLogTextBox上
                Point mousePos = PointToClient(Control.MousePosition);
                if (!sessionLogTextBox.Bounds.Contains(mousePos))
                {
                    sessionLogTextBox.Visible = false;
                }
            }
        }

        /// <summary>
        /// SessionLogTextBox鼠标离开事件
        /// </summary>
        private void SessionLogTextBox_MouseLeave(object sender, EventArgs e)
        {
            // 隐藏会话日志
            if (sessionLogTextBox != null)
            {
                // 获取当前鼠标位置
                Point mousePos = PointToClient(Control.MousePosition);

                // 检查鼠标是否在sessionLogTextBox的整体区域内（包括滚动条） 或者在statusStrip1上，只有都不在时才隐藏
                if (!sessionLogTextBox.Bounds.Contains(mousePos) &&
                    !statusStrip1.Bounds.Contains(mousePos))
                {
                    sessionLogTextBox.Visible = false;
                }
            }
        }

        /// <summary>
        /// 编辑标签页设置菜单项点击事件
        /// </summary>
        private void EditTabSettingsMenuItem_Click(object sender, EventArgs e)
        {
            // 如果没有标签页，则显示新建标签页窗口
            if (tabControl1.TabPages.Count == 0)
            {
                // 使用默认配置创建配置对象
                WebBrowserTabConfig config = new WebBrowserTabConfig
                {
                    Name = "新标签页",
                    Url = "about:blank",
                    HomeUrl = "about:blank"
                };

                try
                {
                    // 显示配置窗体
                    using (TabConfigForm configForm = new TabConfigForm(config, true)) // true表示是新建模式
                    {
                        if (configForm.ShowDialog() == DialogResult.OK && configForm.Confirmed)
                        {
                            // 获取新的配置
                            WebBrowserTabConfig newConfig = configForm.Config;

                            // 创建新的标签页
                            _tabManager.CreateNewTab(newConfig);

                            // 刷新标签页下拉菜单
                            InitializeTabsDropDownMenu();

                            // 更新状态栏的刷新状态信息
                            WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, newConfig);
                        }
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(this, $"新建标签页失败: {ex.Message}");
                    MessageBox.Show($"新建标签页失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                return;
            }

            // 获取当前标签页数据并检查是否为副本
            WebBrowserTabManager.TabData currentTabData = _tabManager.GetCurrentTabData();
            if (currentTabData != null && currentTabData.IsClone)
            {
                ETLogManager.Info(this, $"标签页 {currentTabData.Config.Name} 是副本，不允许编辑设置");
                MessageBox.Show("副本标签页不支持编辑设置，请使用原始标签页进行设置", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 获取当前标签页配置
            WebBrowserTabConfig currentConfig = _tabManager.GetCurrentTabConfig();
            if (currentConfig == null)
            {
                // 尝试根据选中标签页获取配置
                if (tabControl1.SelectedTab != null)
                {
                    string tabName = tabControl1.SelectedTab.Name;
                    // 检查TabManager的TabDataDictionary中是否有此标签页
                    if (_tabManager.TabDataDictionary.TryGetValue(tabName, out WebBrowserTabManager.TabData tabData))
                    {
                        currentConfig = tabData.Config;
                        ETLogManager.Info(this, $"已手动找到标签页配置: {tabName}");

                        // 检查是否为副本标签页
                        if (tabData.IsClone)
                        {
                            ETLogManager.Info(this, $"标签页 {tabName} 是副本，不允许编辑设置");
                            MessageBox.Show("副本标签页不支持编辑设置，请使用原始标签页进行设置", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            return;
                        }
                    }
                }

                // 如果仍然无法获取配置
                if (currentConfig == null)
                {
                    ETLogManager.Error(this, "无法获取当前标签页配置，将创建新标签页");
                    MessageBox.Show("无法获取当前标签页配置，将创建新标签页", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);

                    // 使用默认配置创建新标签页
                    WebBrowserTabConfig config = new WebBrowserTabConfig
                    {
                        Name = "新标签页",
                        Url = "about:blank",
                        HomeUrl = "about:blank"
                    };

                    try
                    {
                        // 显示配置窗体
                        using (TabConfigForm configForm = new TabConfigForm(config, true))
                        {
                            if (configForm.ShowDialog() == DialogResult.OK && configForm.Confirmed)
                            {
                                // 获取新的配置
                                WebBrowserTabConfig newConfig = configForm.Config;

                                // 创建新的标签页
                                _tabManager.CreateNewTab(newConfig);

                                // 刷新标签页下拉菜单
                                InitializeTabsDropDownMenu();

                                // 更新状态栏的刷新状态信息
                                WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, newConfig);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(this, $"新建标签页失败: {ex.Message}");
                        MessageBox.Show($"新建标签页失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    return;
                }
            }

            // 保存原始配置的代理设置
            string originalProxyServer = currentConfig.ProxyServer;
            int originalProxyPort = currentConfig.ProxyPort;

            try
            {
                // 显示配置窗体
                using (TabConfigForm configForm = new TabConfigForm(currentConfig))
                {
                    if (configForm.ShowDialog() == DialogResult.OK && configForm.Confirmed)
                    {
                        // 获取修改后的配置
                        WebBrowserTabConfig newConfig = configForm.Config;

                        // 检查代理设置是否发生变化
                        bool proxyChanged =
                            originalProxyServer != newConfig.ProxyServer ||
                            originalProxyPort != newConfig.ProxyPort;

                        // 更新标签页配置
                        _tabManager.UpdateCurrentTabConfig(newConfig);

                        // 刷新标签页下拉菜单
                        InitializeTabsDropDownMenu();

                        // 更新状态栏的刷新状态信息
                        WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, newConfig);

                        // 如果代理设置发生变化，提示用户重启标签页
                        if (proxyChanged)
                        {
                            string proxyMessage = string.Empty;
                            if (!string.IsNullOrEmpty(newConfig.ProxyServer) && newConfig.ProxyPort > 0)
                            {
                                proxyMessage = $"已设置代理: {newConfig.ProxyServer}:{newConfig.ProxyPort}";
                            }
                            else
                            {
                                proxyMessage = "已禁用代理";
                            }

                            DialogResult result = MessageBox.Show(
                                $"标签页 {newConfig.Name} 的代理设置已更改，但需要重新打开标签页才能生效。\n\n" +
                                $"{proxyMessage}\n\n" +
                                "要立即重新打开此标签页以应用代理设置吗？",
                                "代理设置",
                                MessageBoxButtons.YesNo,
                                MessageBoxIcon.Question);

                            if (result == DialogResult.Yes)
                            {
                                // 重新打开标签页
                                string tabName = newConfig.Name;
                                ETLogManager.Info(this, $"用户选择重新打开标签页以应用代理设置: {tabName}");
                                string newTabName = _tabManager.ReopenTabWithConfig(tabName);
                                if (!string.IsNullOrEmpty(newTabName))
                                {
                                    ETLogManager.Info(this, $"已重新打开标签页: {tabName} -> {newTabName}");
                                }
                                else
                                {
                                    ETLogManager.Error(this, $"重新打开标签页失败: {tabName}");
                                    MessageBox.Show($"重新打开标签页失败，请手动关闭并重新打开标签页以应用代理设置。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                            else
                            {
                                ETLogManager.Info(this, $"用户选择稍后重新打开标签页: {newConfig.Name}");
                                MessageBox.Show(
                                    $"要应用新的代理设置，您需要稍后手动关闭并重新打开标签页 {newConfig.Name}。",
                                    "提示",
                                    MessageBoxButtons.OK,
                                    MessageBoxIcon.Information);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"编辑标签页设置失败: {ex.Message}");
                MessageBox.Show($"编辑标签页设置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 查看会话状态菜单项点击事件
        /// </summary>
        private void ViewSessionStatusMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取当前选中的标签页
                if (tabControl1.SelectedTab == null)
                    return;

                string tabName = tabControl1.SelectedTab.Name;

                // 获取会话维持器
                Dictionary<string, WebBrowserSessionKeeper> sessionKeepers = _sessionManager.GetSessionKeepers();
                if (sessionKeepers.TryGetValue(tabName, out WebBrowserSessionKeeper keeper))
                {
                    // 获取定时器状态
                    string timerStatus = keeper.GetTimerStatus();

                    // 获取标签页配置
                    WebBrowserTabConfig config = null;
                    if (_tabManager.TabDataDictionary.TryGetValue(tabName, out WebBrowserTabManager.TabData tabData))
                    {
                        config = tabData.Config;
                    }

                    // 构建状态信息
                    StringBuilder statusBuilder = new StringBuilder();
                    statusBuilder.AppendLine($"标签页: {tabName}");
                    statusBuilder.AppendLine($"刷新状态: {(keeper.IsRunning ? "运行中" : "已停止")}");
                    statusBuilder.AppendLine($"最后刷新时间: {keeper.LastRefreshTime:yyyy-MM-dd HH:mm:ss}");
                    statusBuilder.AppendLine($"最后刷新结果: {(keeper.LastRefreshSuccess ? "成功" : "失败")}");
                    statusBuilder.AppendLine($"定时器状态: {timerStatus}");

                    if (config != null)
                    {
                        statusBuilder.AppendLine($"配置启用状态: {(config.EnableHttpClientRefresh ? "已启用" : "未启用")}");
                        statusBuilder.AppendLine($"配置刷新间隔: {config.HttpClientRefreshInterval} 秒");
                        statusBuilder.AppendLine($"配置刷新URL: {config.GetActualRefreshUrl()}");
                    }

                    // 显示状态信息
                    MessageBox.Show(statusBuilder.ToString(), "会话状态", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 添加日志记录
                    ETLogManager.Info(this, $"已查看会话状态: {tabName}");

                    // 更新状态栏
                    if (config != null)
                    {
                        WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, config);
                    }
                }
                else
                {
                    // 如果找不到会话维持器，可能是未启用
                    if (_tabManager.TabDataDictionary.TryGetValue(tabName, out WebBrowserTabManager.TabData tabData) &&
                        tabData.Config != null)
                    {
                        string message = $"标签页 {tabName} 的HttpClient会话维持未启用。\n" +
                                        $"启用状态: {tabData.Config.EnableHttpClientRefresh}\n" +
                                        $"刷新间隔: {tabData.Config.HttpClientRefreshInterval} 秒";

                        MessageBox.Show(message, "会话状态", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // 更新状态栏
                        WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, tabData.Config);
                    }
                    else
                    {
                        MessageBox.Show($"无法获取标签页 {tabName} 的会话状态", "会话状态", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"查看会话状态失败: {ex.Message}");
                MessageBox.Show($"查看会话状态失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除标签页菜单项点击事件
        /// </summary>
        private void 删除网站ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 如果没有标签页，提示用户
            if (tabControl1.TabPages.Count == 0)
            {
                MessageBox.Show("当前没有标签页可以删除", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 获取当前标签页配置
                WebBrowserTabConfig config = _tabManager.GetCurrentTabConfig();
                if (config == null)
                {
                    MessageBox.Show("无法获取当前标签页配置信息", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 获取当前标签页数据
                WebBrowserTabManager.TabData currentTabData = _tabManager.GetCurrentTabData();

                // 如果是副本标签页，仅关闭不删除配置
                if (currentTabData != null && currentTabData.IsClone)
                {
                    // 关闭当前标签页
                    _tabManager.CloseCurrentTab();

                    ETLogManager.Info(this, $"已关闭副本标签页: {config.Name}");
                    return;
                }

                // 弹出确认框
                string message = $"确定要删除标签页 \"{config.Name}\" 吗？\n这将同时删除该标签页的配置信息。";
                DialogResult result = MessageBox.Show(message, "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    string tabName = config.Name;
                    string sectionId = config.SectionId;

                    // 关闭当前标签页
                    _tabManager.CloseCurrentTab();

                    // 从配置文件中删除对应段
                    bool deleteResult = _configManager.DeleteTabConfig(sectionId);

                    if (deleteResult)
                    {
                        ETLogManager.Info(this, $"已删除标签页配置: {tabName}, SectionId: {sectionId}");

                        // 刷新标签页下拉菜单
                        InitializeTabsDropDownMenu();

                        //MessageBox.Show($"标签页 \"{tabName}\" 已成功删除", "删除成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        ETLogManager.Error(this, $"删除标签页配置失败: {tabName}, SectionId: {sectionId}");
                        MessageBox.Show($"删除标签页 \"{tabName}\" 的配置信息失败", "删除失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"删除标签页失败: {ex.Message}");
                MessageBox.Show($"删除标签页失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 管理Cookies菜单项点击事件
        /// </summary>
        private void ManageCookiesMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取当前标签页的WebView2控件
                WebView2 currentWebView = _tabManager.GetCurrentWebView();
                if (currentWebView == null)
                {
                    MessageBox.Show("当前没有活动的标签页", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 获取当前标签页的配置
                WebBrowserTabConfig currentConfig = _tabManager.GetCurrentTabConfig();
                if (currentConfig == null)
                {
                    MessageBox.Show("无法获取当前标签页配置", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 更新标头管理器的配置
                _cookieManager.HeadersPath = currentConfig.CookiePath; // 兼容使用CookiePath作为HeadersPath
                _cookieManager.CurrentUrl = currentConfig.Url;

                // 创建并显示Cookie管理窗体
                using (CookieManagerForm cookieManagerForm = new CookieManagerForm(_cookieManager, currentWebView, this))
                {
                    cookieManagerForm.ShowDialog();

                    // 如果路径有更新，更新TabConfig
                    if (!string.IsNullOrEmpty(_cookieManager.HeadersPath) &&
                        _cookieManager.HeadersPath != currentConfig.CookiePath)
                    {
                        currentConfig.CookiePath = _cookieManager.HeadersPath; // 兼容保存到CookiePath
                        _configManager.SaveTabConfig(currentConfig);
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"打开Cookie管理窗口失败: {ex.Message}");
                MessageBox.Show($"打开Cookie管理窗口失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 事件处理方法

        #region 上下文菜单

        /// <summary>
        /// TabControl1右键菜单事件
        /// </summary>
        private void TabControl1_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right && tabControl1.TabPages.Count > 0)
            {
                // 使用预定义的tabContextMenu，而不是创建新的
                if (tabContextMenu != null)
                {
                    // 获取当前标签页信息，以便菜单项能够访问
                    string currentTabName = null;
                    bool isCloneTab = false;

                    if (tabControl1.SelectedTab != null)
                    {
                        currentTabName = tabControl1.SelectedTab.Name;

                        // 检查当前标签页是否为副本
                        if (_tabManager.TabDataDictionary.TryGetValue(currentTabName, out WebBrowserTabManager.TabData tabData))
                        {
                            isCloneTab = tabData.IsClone;
                            ETLogManager.Debug(this, $"右键菜单: 标签页 {tabData.Config.Name} (SectionId: {currentTabName}) {(isCloneTab ? "是副本" : "非副本")}");
                        }
                    }

                    // 确保所有事件处理程序已绑定 注：这些事件处理程序应该已经在Designer中绑定， 但如果有些没有绑定，可以在这里检查并绑定

                    // 动态启用/禁用菜单项
                    foreach (ToolStripItem item in tabContextMenu.Items)
                    {
                        if (item is ToolStripMenuItem menuItem)
                        {
                            // 基本规则：标签页不为空时菜单项启用
                            bool shouldEnable = !string.IsNullOrEmpty(currentTabName);

                            // 特殊规则：如果标签页是副本，禁用特定菜单项
                            if (isCloneTab)
                            {
                                // 副本标签页禁用"复制标签页"功能
                                if (menuItem == 复制标签页ToolStripMenuItem)
                                {
                                    shouldEnable = false;
                                }

                                // 副本标签页禁用"退出登录"功能
                                if (menuItem == 退出登录ToolStripMenuItem)
                                {
                                    shouldEnable = false;
                                }

                                // 副本标签页禁用"编辑标签页设置"功能
                                if (menuItem == editTabSettingsMenuItem)
                                {
                                    shouldEnable = false;
                                }

                                // 副本标签页禁用"删除网站"功能
                                if (menuItem == 删除网站ToolStripMenuItem)
                                {
                                    shouldEnable = false;
                                }
                            }

                            // 设置菜单项启用状态
                            menuItem.Enabled = shouldEnable;
                        }
                    }

                    // 显示菜单
                    tabContextMenu.Show(tabControl1, e.Location);
                }
                else
                {
                    // 备用方案：如果tabContextMenu为null，则创建临时菜单 注意：正常情况下不应该进入这个分支
                    ETLogManager.Warning(this, "tabContextMenu为空，创建临时右键菜单");

                    ContextMenuStrip menuStrip = new ContextMenuStrip();

                    // 添加关闭标签页菜单项
                    ToolStripMenuItem closeMenuItem = new ToolStripMenuItem("关闭标签页");
                    closeMenuItem.Click += CloseTabMenuItem_Click;
                    menuStrip.Items.Add(closeMenuItem);

                    // 添加编辑标签页设置菜单项
                    ToolStripMenuItem editSettingsMenuItem = new ToolStripMenuItem("编辑标签页设置");
                    editSettingsMenuItem.Click += EditTabSettingsMenuItem_Click;
                    menuStrip.Items.Add(editSettingsMenuItem);

                    // 添加查看会话状态菜单项
                    ToolStripMenuItem viewSessionStatusMenuItem = new ToolStripMenuItem("查看会话状态");
                    viewSessionStatusMenuItem.Click += ViewSessionStatusMenuItem_Click;
                    menuStrip.Items.Add(viewSessionStatusMenuItem);

                    // 显示右键菜单
                    menuStrip.Show(tabControl1, e.Location);
                }
            }
        }

        #endregion 上下文菜单

        #region 公共方法

        /// <summary>
        /// 彻底关闭浏览器窗体，释放所有资源
        /// </summary>
        public void DoRealClose()
        {
            try
            {
                ETLogManager.Info(this, "开始彻底关闭WebBrowser窗体...");

                _isRealClosing = true;

                // 停止所有会话刷新（包括WebView2和HttpClient）
                _sessionManager.StopAllRefreshers();

                // 保存标签页配置（使用线程安全的方法）
                try
                {
                    List<WebBrowserTabConfig> configs = _tabManager.GetAllTabConfigsSafely();
                    foreach (WebBrowserTabConfig config in configs)
                    {
                        _configManager.SaveTabConfig(config);
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Warning(this, $"保存标签页配置时出现警告: {ex.Message}");
                }

                // 执行资源释放前先安全解绑事件
                try
                {
                    foreach (KeyValuePair<string, WebBrowserTabManager.TabData> pair in _tabManager.TabDataDictionary)
                    {
                        if (pair.Value.WebView?.CoreWebView2 != null)
                        {
                            try
                            {
                                // 停止所有导航
                                pair.Value.WebView.CoreWebView2.Stop();

                                // 解除事件绑定，使用try-catch分别处理每个事件解绑
                                try { pair.Value.WebView.CoreWebView2.SourceChanged -= _tabManager.WebView_SourceChanged; } catch { }
                                try { pair.Value.WebView.CoreWebView2.NavigationStarting -= _tabManager.WebView_NavigationStarting; } catch { }
                                try { pair.Value.WebView.CoreWebView2.NavigationCompleted -= _tabManager.WebView_NavigationCompleted; } catch { }

                                if (pair.Value.WebResourceRequestedHandler != null)
                                {
                                    try { pair.Value.WebView.CoreWebView2.WebResourceRequested -= pair.Value.WebResourceRequestedHandler; } catch { }
                                }
                            }
                            catch (Exception ex)
                            {
                                ETLogManager.Warning(this, $"解绑事件时出现异常: {pair.Key}, {ex.Message}");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Warning(this, $"事件解绑过程出现异常: {ex.Message}");
                }

                // 清理所有标签页资源
                _tabManager.CleanupAllTabs();

                // 释放资源管理器管理的所有资源
                _resourceManager?.Dispose();

                // 执行内存优化和清理
                MemoryOptimizer.ForceGarbageCollection(this, true);

                UpdateMainMenuStatus(false);

                ETLogManager.Info(this, "WebBrowser窗体已彻底关闭，所有资源已释放");
                Close();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"彻底关闭WebBrowser窗体时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用标签页配置
        /// </summary>
        /// <param name="config">标签页配置</param>
        public void ApplyTabConfig(WebBrowserTabConfig config)
        {
            if (config == null)
            {
                ETLogManager.Error(this, "应用标签页配置失败：配置对象为空");
                return;
            }

            try
            {
                // 确保SectionId存在
                if (string.IsNullOrEmpty(config.SectionId))
                {
                    config.GenerateNewSectionId();
                    ETLogManager.Info(this, $"为配置生成新的SectionId: {config.SectionId}");
                }

                // 保存配置
                _configManager.SaveTabConfig(config);
                ETLogManager.Info(this, $"已保存标签页配置: {config.Name}, SectionId: {config.SectionId}");

                // 查找当前标签页或匹配的标签页来更新
                if (_tabManager.TabDataDictionary.TryGetValue(config.SectionId, out WebBrowserTabManager.TabData tabData))
                {
                    // 如果找到匹配的SectionId，更新该标签页配置
                    _tabManager.UpdateCurrentTabConfig(config);
                    ETLogManager.Info(this, $"通过SectionId匹配更新标签页配置: {config.Name}, SectionId: {config.SectionId}");

                    // 如果当前显示的就是这个标签页，更新状态栏
                    if (tabControl1.SelectedTab != null && tabControl1.SelectedTab.Name == config.SectionId)
                    {
                        WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, config);
                    }

                    return;
                }

                // 查找名称匹配的标签页
                foreach (KeyValuePair<string, WebBrowserTabManager.TabData> pair in _tabManager.TabDataDictionary)
                {
                    if (pair.Value.Config.Name == config.Name)
                    {
                        // 更新配置的SectionId以匹配现有标签页
                        config.SectionId = pair.Key;
                        _tabManager.UpdateCurrentTabConfig(config);
                        ETLogManager.Info(this, $"通过名称匹配更新标签页配置: {config.Name}, SectionId: {config.SectionId}");

                        // 如果当前显示的就是这个标签页，更新状态栏
                        if (tabControl1.SelectedTab != null && tabControl1.SelectedTab.Name == pair.Key)
                        {
                            WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, config);
                        }

                        return;
                    }
                }

                // 如果找不到匹配项，创建新标签页
                ETLogManager.Info(this, $"未找到匹配的标签页，创建新标签页: {config.Name}, SectionId: {config.SectionId}");
                _tabManager.CreateNewTab(config);

                // 更新状态栏
                WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, config);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"应用标签页配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据标签页名称刷新特定标签页
        /// </summary>
        /// <param name="identifier">要刷新的标签页名称或SectionId</param>
        public void RefreshTabByName(string identifier)
        {
            if (string.IsNullOrEmpty(identifier))
            {
                ETLogManager.Error(this, "刷新标签页失败：标识符为空");
                return;
            }

            try
            {
                // 先尝试作为SectionId刷新
                if (_tabManager.TabDataDictionary.ContainsKey(identifier))
                {
                    bool success = _tabManager.RefreshTabBySectionId(identifier);
                    if (success)
                    {
                        ETLogManager.Info(this, $"已刷新标签页 SectionId={identifier}");
                        return;
                    }
                }

                // 如果作为SectionId未找到，尝试查找名称匹配的标签页
                foreach (KeyValuePair<string, WebBrowserTabManager.TabData> pair in _tabManager.TabDataDictionary)
                {
                    if (pair.Value.Config.Name == identifier)
                    {
                        bool success = _tabManager.RefreshTabBySectionId(pair.Key);
                        if (success)
                        {
                            ETLogManager.Info(this, $"已刷新标签页 {identifier}, SectionId={pair.Key}");
                            return;
                        }
                    }
                }

                ETLogManager.Error(this, $"刷新标签页失败：未找到标签页 {identifier}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"刷新标签页异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化标签页右键菜单
        /// </summary>
        private void InitializeTabContextMenu()
        {
            // 不再创建新菜单，而是使用Designer中定义的tabContextMenu 确保所有需要的事件处理器都已经绑定
            foreach (ToolStripItem item in tabContextMenu.Items)
            {
                if (item is ToolStripMenuItem menuItem)
                {
                    // 根据菜单项名称绑定对应的事件处理程序
                    if (menuItem == editTabSettingsMenuItem)
                    {
                        SafeRegisterMenuEvent(menuItem, EditTabSettingsMenuItem_Click);
                    }
                    else if (menuItem == 新建标签页ToolStripMenuItem)
                    {
                        SafeRegisterMenuEvent(menuItem, 新建网站ToolStripMenuItem_Click);
                    }
                    else if (menuItem == closeTabMenuItem)
                    {
                        SafeRegisterMenuEvent(menuItem, CloseTabMenuItem_Click);
                    }
                    else if (menuItem == 删除网站ToolStripMenuItem)
                    {
                        SafeRegisterMenuEvent(menuItem, 删除网站ToolStripMenuItem_Click);
                    }
                    else if (menuItem == viewSessionStatusMenuItem)
                    {
                        SafeRegisterMenuEvent(menuItem, ViewSessionStatusMenuItem_Click);
                    }
                    // 添加Cookie管理菜单项事件绑定
                    else if (menuItem.Text == "Cookie管理")
                    {
                        SafeRegisterMenuEvent(menuItem, ManageCookiesMenuItem_Click);
                    }
                    // 添加复制标签页菜单项事件绑定
                    else if (menuItem == 复制标签页ToolStripMenuItem)
                    {
                        SafeRegisterMenuEvent(menuItem, CloneTabMenuItem_Click);
                    }
                    // 添加退出登录菜单项事件绑定
                    else if (menuItem == 退出登录ToolStripMenuItem)
                    {
                        SafeRegisterMenuEvent(menuItem, LogoutTabMenuItem_Click);
                    }
                }
            }

            // 设置右键菜单
            tabControl1.ContextMenuStrip = tabContextMenu;
        }

        #endregion 公共方法

        private void ClearCacheButton_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取缓存目录路径
                string webViewCacheFolder = Path.Combine(
                    Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location),
                    WebBrowserConstants.WEBVIEW_CACHE_FOLDER);

                // 检查缓存目录是否存在
                if (!Directory.Exists(webViewCacheFolder))
                {
                    ETLogManager.Info(this, $"缓存目录不存在: {webViewCacheFolder}");
                    MessageBox.Show("浏览器缓存目录不存在，无需清理", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 显示确认对话框
                DialogResult result = MessageBox.Show(
                    "清理浏览器缓存将关闭所有标签页，您确定要继续吗？\n\n注意：清理缓存后，您需要重新登录所有网站。",
                    "确认清理缓存",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result != DialogResult.Yes)
                {
                    return;
                }

                try
                {
                    // 显示等待光标
                    Cursor = Cursors.WaitCursor;
                    statusLabel.Text = "正在清理缓存...";
                    Application.DoEvents();

                    // 关闭所有标签页
                    _tabManager.CleanupAllTabs();

                    // 清空TabControl
                    tabControl1.TabPages.Clear();

                    // 记录清理开始
                    ETLogManager.Info(this, $"开始清理WebView2缓存: {webViewCacheFolder}");

                    int deletedFolders = 0;
                    int failedFolders = 0;
                    List<string> failedFolderNames = new List<string>();

                    // 获取所有子文件夹
                    DirectoryInfo rootDir = new DirectoryInfo(webViewCacheFolder);
                    DirectoryInfo[] subDirs = rootDir.GetDirectories();

                    // 使用同步对象
                    object syncLock = new object();

                    // 使用并行处理提高性能
                    System.Threading.Tasks.Parallel.ForEach(subDirs, (dir) =>
                    {
                        try
                        {
                            // 递归删除文件夹及其内容
                            dir.Delete(true);

                            lock (syncLock)
                            {
                                deletedFolders++;
                                ETLogManager.Info(this, $"已删除缓存文件夹: {dir.Name}");
                            }
                        }
                        catch (Exception ex)
                        {
                            lock (syncLock)
                            {
                                failedFolders++;
                                failedFolderNames.Add(dir.Name);
                                ETLogManager.Error(this, $"删除缓存文件夹失败: {dir.Name}, 错误: {ex.Message}");
                            }
                        }
                    });

                    // 创建结果消息
                    StringBuilder resultMessage = new StringBuilder();
                    resultMessage.AppendLine($"清理完成，共处理 {subDirs.Length} 个缓存目录");
                    resultMessage.AppendLine($"成功删除: {deletedFolders} 个");

                    if (failedFolders > 0)
                    {
                        resultMessage.AppendLine($"删除失败: {failedFolders} 个");
                        resultMessage.AppendLine($"失败文件夹: {string.Join(", ", failedFolderNames)}");
                    }

                    // 输出日志
                    ETLogManager.Info(this, resultMessage.ToString());

                    // 显示结果消息
                    MessageBox.Show(
                        resultMessage.ToString(),
                        "清理完成",
                        MessageBoxButtons.OK,
                        failedFolders > 0 ? MessageBoxIcon.Warning : MessageBoxIcon.Information);

                    // 刷新标签页下拉菜单
                    InitializeTabsDropDownMenu();
                }
                finally
                {
                    // 恢复光标
                    Cursor = Cursors.Default;
                    statusLabel.Text = string.Empty;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"清理浏览器缓存失败: {ex.Message}");
                MessageBox.Show($"清理浏览器缓存失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 工具栏大小变化事件处理
        /// </summary>
        private void ToolStrip1_SizeChanged(object sender, EventArgs e)
        {
            // 调用窗体大小变化事件处理方法，复用代码
            WebBrowser_SizeChanged(sender, e);
        }

        /// <summary>
        /// 复制标签页菜单项点击事件
        /// </summary>
        private void CloneTabMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info(this, "开始复制标签页操作");

                // 如果没有标签页，给出提示
                if (tabControl1.TabPages.Count == 0)
                {
                    ETLogManager.Warning(this, "复制标签页失败: 当前没有标签页");
                    MessageBox.Show("当前没有标签页可以操作", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                ETLogManager.Info(this, $"当前标签页数量: {tabControl1.TabPages.Count}");

                // 获取当前标签页数据
                WebBrowserTabManager.TabData currentTabData = _tabManager.GetCurrentTabData();
                if (currentTabData == null)
                {
                    ETLogManager.Error(this, "复制标签页失败: 无法获取当前标签页数据");
                    MessageBox.Show("无法获取当前标签页信息", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                ETLogManager.Info(this, $"当前标签页信息: Name={currentTabData.Config?.Name}, SectionId={currentTabData.Config?.SectionId}, IsClone={currentTabData.IsClone}");

                // 检查当前标签页是否为副本
                if (currentTabData.IsClone)
                {
                    ETLogManager.Warning(this, "复制标签页失败: 不能从副本标签页创建副本");
                    MessageBox.Show("不能从副本标签页创建副本", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 显示等待光标
                Cursor = Cursors.WaitCursor;
                statusLabel.Text = "正在创建标签页副本...";
                Application.DoEvents();

                try
                {
                    // 调用同步方法，避免死锁和线程问题
                    string newTabSectionId = _tabManager.CloneTab(currentTabData.Config.SectionId);

                    if (string.IsNullOrEmpty(newTabSectionId))
                    {
                        ETLogManager.Error(this, "创建标签页副本失败: CloneTab返回空值");
                        MessageBox.Show("创建标签页副本失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    else
                    {
                        ETLogManager.Info(this, $"已成功创建标签页副本: {newTabSectionId}");
                        statusLabel.Text = "标签页副本创建成功";
                    }
                }
                finally
                {
                    // 恢复光标
                    Cursor = Cursors.Default;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"创建标签页副本失败: {ex.Message}");
                MessageBox.Show($"创建标签页副本失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// 退出登录菜单项点击事件
        /// </summary>
        private void LogoutTabMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 如果没有标签页，给出提示
                if (tabControl1.TabPages.Count == 0)
                {
                    MessageBox.Show("当前没有标签页可以操作", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 获取当前标签页数据
                WebBrowserTabManager.TabData currentTabData = _tabManager.GetCurrentTabData();
                if (currentTabData == null)
                {
                    MessageBox.Show("无法获取当前标签页信息", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 检查当前标签页是否为副本 - 即使在菜单中已禁用，也做双重检查
                if (currentTabData.IsClone)
                {
                    MessageBox.Show("副本标签页不支持退出登录功能，请使用原始标签页", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 获取当前URL和标签页配置
                string currentUrl = currentTabData.LastUrl;
                if (string.IsNullOrEmpty(currentUrl))
                {
                    MessageBox.Show("无法获取当前标签页的URL", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 获取标签页配置
                WebBrowserTabConfig config = currentTabData.Config;
                if (config == null)
                {
                    MessageBox.Show("无法获取当前标签页配置", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 确认操作
                DialogResult result = MessageBox.Show(
                    $"确定要退出 {config.Name} 的登录状态吗？\n这将清除所有Cookie并重新加载页面。",
                    "确认退出登录",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                {
                    return;
                }

                // 显示等待光标
                Cursor = Cursors.WaitCursor;
                statusLabel.Text = "正在退出登录...";
                Application.DoEvents();

                try
                {
                    // 关闭所有关联的副本标签页
                    _tabManager.CloseAllCloneTabsOfParent(config.SectionId);

                    // 获取WebView2控件
                    if (currentTabData.WebView?.CoreWebView2 != null)
                    {
                        // 确保在UI线程上执行WebView2操作
                        if (currentTabData.WebView.InvokeRequired)
                        {
                            currentTabData.WebView.Invoke(new Action(() =>
                            {
                                try
                                {
                                    // 清除所有Cookie
                                    currentTabData.WebView.CoreWebView2.CookieManager.DeleteAllCookies();
                                    ETLogManager.Info(this, $"已清除标签页 {config.Name} 的所有Cookie");

                                    // 导航到原始URL或首页URL
                                    string targetUrl = !string.IsNullOrEmpty(config.Url) ? config.Url : "about:blank";
                                    currentTabData.WebView.Source = new Uri(targetUrl);
                                    ETLogManager.Info(this, $"标签页 {config.Name} 已退出登录并重新加载页面");
                                }
                                catch (Exception ex)
                                {
                                    ETLogManager.Error(this, $"在UI线程上清除Cookie和导航失败: {ex.Message}");
                                }
                            }));
                        }
                        else
                        {
                            try
                            {
                                // 清除所有Cookie
                                currentTabData.WebView.CoreWebView2.CookieManager.DeleteAllCookies();
                                ETLogManager.Info(this, $"已清除标签页 {config.Name} 的所有Cookie");

                                // 导航到原始URL或首页URL
                                string targetUrl = !string.IsNullOrEmpty(config.Url) ? config.Url : "about:blank";
                                currentTabData.WebView.Source = new Uri(targetUrl);
                                ETLogManager.Info(this, $"标签页 {config.Name} 已退出登录并重新加载页面");
                            }
                            catch (Exception ex)
                            {
                                ETLogManager.Error(this, $"清除Cookie和导航失败: {ex.Message}");
                            }
                        }

                        // 更新会话日志
                        _sessionManager.AddSessionLog(config.SectionId, $"已退出登录并清除所有Cookie");

                        // 确保在UI线程上更新状态栏
                        if (this.InvokeRequired)
                        {
                            this.Invoke(new Action(() => statusLabel.Text = "已退出登录并清除所有Cookie"));
                        }
                        else
                        {
                            statusLabel.Text = "已退出登录并清除所有Cookie";
                        }
                    }
                    else
                    {
                        ETLogManager.Error(this, $"标签页 {config.Name} 的WebView2未初始化，无法清除Cookie");
                        MessageBox.Show("WebView2未初始化，无法清除Cookie", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                finally
                {
                    // 恢复光标
                    Cursor = Cursors.Default;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"退出登录失败: {ex.Message}");
                MessageBox.Show($"退出登录失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// 复制Cookie配置文件到剪贴板按钮点击事件
        /// </summary>
        private async void CopyCookiesConfigButton_Click(object sender, EventArgs e)
        {
            try
            {
                // 显示正在处理的状态
                statusLabel.Text = "正在获取并复制Cookie...";
                Cursor = Cursors.WaitCursor;

                // 获取当前标签页的WebView2控件
                WebView2 currentWebView = _tabManager.GetCurrentWebView();
                if (currentWebView == null)
                {
                    MessageBox.Show("当前没有活动的标签页", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 获取当前标签页的配置
                WebBrowserTabConfig currentConfig = _tabManager.GetCurrentTabConfig();
                if (currentConfig == null)
                {
                    MessageBox.Show("无法获取当前标签页配置", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 更新标头管理器的配置
                _cookieManager.HeadersPath = currentConfig.CookiePath; // 兼容使用CookiePath作为HeadersPath
                _cookieManager.CurrentUrl = currentWebView.Source?.ToString() ?? currentConfig.Url;

                try
                {
                    // 使用通用模块直接获取新格式的登录信息
                    var options = new ETWebBrowserJsonFormatter.HeadersOptions
                    {
                        Source = "WebBrowser",
                        RequestType = "Page"
                    };

                    // 直接获取新格式的JSON数据 - 移除ConfigureAwait(false)以保持在UI线程上下文
                    string json = await ETWebBrowserJsonFormatter.CreateLoginInfoJsonFromWebViewAsync(
                        currentWebView,
                        null, // 不传递额外标头，让通用模块自动处理
                        options);

                    // 解析JSON以验证数据和获取统计信息
                    if (string.IsNullOrEmpty(json) || !ETWebBrowserJsonFormatter.IsStandardFormat(json))
                    {
                        statusLabel.Text = "当前页面没有登录信息或获取失败";
                        ETLogManager.Warning(this, "当前页面没有登录信息或获取失败");
                        return;
                    }

                    var loginInfo = ETWebBrowserJsonFormatter.ParseLoginInfoJson(json);
                    if (loginInfo.Cookies == null || loginInfo.Cookies.Count == 0)
                    {
                        statusLabel.Text = "当前页面没有Cookie数据";
                        ETLogManager.Warning(this, "当前页面没有Cookie数据");
                        return;
                    }

                    // 确保缓存目录存在
                    string webViewCacheFolder = Path.Combine(
                        Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location),
                        WebBrowserConstants.WEBVIEW_CACHE_FOLDER);
                    Directory.CreateDirectory(webViewCacheFolder);

                    // 生成临时文件路径
                    string tempFilePath = Path.Combine(webViewCacheFolder, "cookies.dat");

                    // 写入文件
                    File.WriteAllText(tempFilePath, json);

                    // 验证文件是否有内容
                    FileInfo fileInfo = new FileInfo(tempFilePath);
                    if (fileInfo.Length == 0)
                    {
                        statusLabel.Text = "生成的Cookie文件为空，复制失败";
                        ETLogManager.Error(this, "生成的Cookie文件为空，复制失败");
                        return;
                    }

                    // 复制文件到剪贴板
                    ETFile.FileCopyToClipboard(tempFilePath);

                    // 显示成功消息
                    statusLabel.Text = $"登录信息配置文件已复制到剪贴板 ({loginInfo.Cookies.Count}个Cookie, {loginInfo.Headers.Count}个标头)";
                    ETLogManager.Info(this, $"登录信息配置文件已复制到剪贴板 ({loginInfo.Cookies.Count}个Cookie, {loginInfo.Headers.Count}个标头)");
                }
                catch (Exception ex)
                {
                    statusLabel.Text = $"复制登录信息配置文件失败: {ex.Message}";
                    ETLogManager.Error(this, $"复制登录信息配置文件失败: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                statusLabel.Text = $"复制Cookie配置文件失败: {ex.Message}";
                ETLogManager.Error(this, $"复制Cookie配置文件失败: {ex.Message}");
            }
            finally
            {
                // 恢复光标 - 确保在UI线程上执行
                if (InvokeRequired)
                {
                    Invoke(new Action(() => Cursor = Cursors.Default));
                }
                else
                {
                    Cursor = Cursors.Default;
                }
            }
        }

        /// <summary>
        /// 粘贴Cookie并更新网址按钮点击事件
        /// </summary>
        private async void PasteCookiesButton_Click(object sender, EventArgs e)
        {
            try
            {
                // 显示正在处理的状态
                statusLabel.Text = "正在从剪贴板粘贴Cookie...";
                Cursor = Cursors.WaitCursor;

                // 获取当前标签页的WebView2控件
                WebView2 currentWebView = _tabManager.GetCurrentWebView();
                if (currentWebView == null)
                {
                    MessageBox.Show("当前没有活动的标签页", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 获取当前标签页的配置
                WebBrowserTabConfig currentConfig = _tabManager.GetCurrentTabConfig();
                if (currentConfig == null)
                {
                    MessageBox.Show("无法获取当前标签页配置", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 更新标头管理器的配置
                _cookieManager.HeadersPath = currentConfig.CookiePath; // 兼容使用CookiePath作为HeadersPath
                _cookieManager.CurrentUrl = currentWebView.Source?.ToString() ?? currentConfig.Url;

                try
                {
                    // 检查剪贴板内容
                    string clipboardText = string.Empty;
                    string tempFile = string.Empty;

                    // 优先检查剪贴板是否有文件
                    if (Clipboard.ContainsFileDropList())
                    {
                        StringCollection filePaths = Clipboard.GetFileDropList();
                        foreach (string filePath in filePaths)
                        {
                            if (File.Exists(filePath) && Path.GetExtension(filePath).Equals(".dat", StringComparison.OrdinalIgnoreCase))
                            {
                                // 读取.dat文件内容
                                clipboardText = File.ReadAllText(filePath);
                                break;
                            }
                        }
                    }

                    // 如果没有文件或文件读取失败，尝试获取文本
                    if (string.IsNullOrEmpty(clipboardText) && Clipboard.ContainsText())
                    {
                        clipboardText = Clipboard.GetText();
                    }

                    if (string.IsNullOrEmpty(clipboardText))
                    {
                        statusLabel.Text = "剪贴板中没有有效的Cookie数据";
                        ETLogManager.Warning(this, "剪贴板中没有有效的Cookie数据");
                        return;
                    }

                    // 清理并解析JSON
                    try
                    {
                        // 尝试清理JSON字符串
                        clipboardText = CleanJsonString(clipboardText);

                        // 反序列化为Cookie数据
                        CookieData cookieData = JsonConvert.DeserializeObject<CookieData>(clipboardText);
                        if (cookieData == null || cookieData.Cookies == null || cookieData.Cookies.Count == 0)
                        {
                            statusLabel.Text = "剪贴板中的数据不是有效的Cookie格式";
                            ETLogManager.Warning(this, "剪贴板中的数据不是有效的Cookie格式");
                            return;
                        }

                        // 确保URL存在
                        string url = cookieData.Url;
                        if (string.IsNullOrEmpty(url))
                        {
                            // 如果JSON中没有URL，则使用当前标签页的URL
                            url = currentWebView.Source?.ToString() ?? currentConfig.Url;
                            cookieData.Url = url;
                        }

                        // 设置Cookie到WebView2 - 移除ConfigureAwait(false)以保持在UI线程上下文
                        await _cookieManager.SetCookiesToWebView2Async(currentWebView, cookieData);

                        // 等待短暂延迟确保Cookie设置生效 - 移除ConfigureAwait(false)以保持在UI线程上下文
                        await Task.Delay(200);

                        // 跳转到URL
                        if (!string.IsNullOrEmpty(url))
                        {
                            // 确保在UI线程上导航到URL
                            if (currentWebView.InvokeRequired)
                            {
                                currentWebView.Invoke(new Action(() =>
                                {
                                    if (currentWebView.CoreWebView2 != null)
                                    {
                                        currentWebView.CoreWebView2.Navigate(url);
                                        ETLogManager.Info(this, $"手动导航到URL: {url}");
                                    }
                                }));
                            }
                            else
                            {
                                if (currentWebView.CoreWebView2 != null)
                                {
                                    currentWebView.CoreWebView2.Navigate(url);
                                    ETLogManager.Info(this, $"手动导航到URL: {url}");
                                }
                            }
                        }

                        // 显示成功消息
                        statusLabel.Text = $"已从剪贴板粘贴{cookieData.Cookies.Count}个Cookie并导航到URL";
                        ETLogManager.Info(this, $"已从剪贴板粘贴{cookieData.Cookies.Count}个Cookie并导航到URL");
                    }
                    catch (JsonException ex)
                    {
                        statusLabel.Text = $"解析Cookie数据失败: {ex.Message}";
                        ETLogManager.Error(this, $"解析Cookie数据失败: {ex.Message}");
                    }
                }
                catch (Exception ex)
                {
                    statusLabel.Text = $"粘贴Cookie失败: {ex.Message}";
                    ETLogManager.Error(this, $"粘贴Cookie失败: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                statusLabel.Text = $"粘贴Cookie失败: {ex.Message}";
                ETLogManager.Error(this, $"粘贴Cookie失败: {ex.Message}");
            }
            finally
            {
                // 恢复光标 - 确保在UI线程上执行
                if (InvokeRequired)
                {
                    Invoke(new Action(() => Cursor = Cursors.Default));
                }
                else
                {
                    Cursor = Cursors.Default;
                }
            }
        }

        /// <summary>
        /// 清理JSON字符串，移除可能导致解析错误的额外字符
        /// </summary>
        private string CleanJsonString(string json)
        {
            try
            {
                // 尝试验证是否为有效的JSON
                JsonConvert.DeserializeObject(json);
                return json;
            }
            catch (JsonException)
            {
                // 如果解析失败，尝试清理字符串
                try
                {
                    // 找到最后一个大括号
                    int lastBrace = json.LastIndexOf('}');
                    if (lastBrace > 0 && lastBrace < json.Length - 1)
                    {
                        // 只保留到最后一个大括号的内容
                        return json.Substring(0, lastBrace + 1);
                    }
                }
                catch
                {
                    // 如果清理过程出错，返回原始字符串
                }
                return json;
            }
        }


    }
}