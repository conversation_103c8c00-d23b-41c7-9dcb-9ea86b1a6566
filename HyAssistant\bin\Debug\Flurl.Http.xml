<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Flurl.Http</name>
    </assembly>
    <members>
        <member name="T:Flurl.Http.Configuration.DefaultJsonSerializer">
            <summary>
            ISerializer implementation based on System.Text.Json.
            Default serializer used in calls to GetJsonAsync, PostJsonAsync, etc.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.DefaultJsonSerializer.#ctor(System.Text.Json.JsonSerializerOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.Configuration.DefaultJsonSerializer"/> class.
            </summary>
            <param name="options">Options to control (de)serialization behavior.</param>
        </member>
        <member name="M:Flurl.Http.Configuration.DefaultJsonSerializer.Serialize(System.Object)">
            <summary>
            Serializes the specified object to a JSON string.
            </summary>
            <param name="obj">The object to serialize.</param>
        </member>
        <member name="M:Flurl.Http.Configuration.DefaultJsonSerializer.Deserialize``1(System.String)">
            <summary>
            Deserializes the specified JSON string to an object of type T.
            </summary>
            <param name="s">The JSON string to deserialize.</param>
        </member>
        <member name="M:Flurl.Http.Configuration.DefaultJsonSerializer.Deserialize``1(System.IO.Stream)">
            <summary>
            Deserializes the specified stream to an object of type T.
            </summary>
            <param name="stream">The stream to deserialize.</param>
        </member>
        <member name="T:Flurl.Http.Configuration.DefaultUrlEncodedSerializer">
            <summary>
            ISerializer implementation that converts an object representing name/value pairs to a URL-encoded string.
            Default serializer used in calls to PostUrlEncodedAsync, etc. 
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.DefaultUrlEncodedSerializer.Serialize(System.Object)">
            <summary>
            Serializes the specified object to a URL-encoded string.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.DefaultUrlEncodedSerializer.Deserialize``1(System.String)">
            <summary>
            Deserializing a URL-encoded string is not supported.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.DefaultUrlEncodedSerializer.Deserialize``1(System.IO.Stream)">
            <summary>
            Deserializing a URL-encoded string is not supported.
            </summary>
        </member>
        <member name="T:Flurl.Http.Configuration.IFlurlClientBuilder">
            <summary>
            A builder for configuring IFlurlClient instances.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.IFlurlClientBuilder.ConfigureHttpClient(System.Action{System.Net.Http.HttpClient})">
            <summary>
            Configure the HttpClient wrapped by this IFlurlClient.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.IFlurlClientBuilder.ConfigureInnerHandler(System.Action{System.Net.Http.HttpClientHandler})">
            <summary>
            Configure the inner-most HttpMessageHandler (an instance of HttpClientHandler) associated with this IFlurlClient.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.IFlurlClientBuilder.AddMiddleware(System.Func{System.Net.Http.DelegatingHandler})">
            <summary>
            Add a provided DelegatingHandler to the IFlurlClient.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.IFlurlClientBuilder.Build">
            <summary>
            Builds an instance of IFlurlClient based on configurations specified.
            </summary>
        </member>
        <member name="T:Flurl.Http.Configuration.FlurlClientBuilder">
            <summary>
            Default implementation of IFlurlClientBuilder.
            </summary>
        </member>
        <member name="P:Flurl.Http.Configuration.FlurlClientBuilder.Settings">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.Configuration.FlurlClientBuilder.EventHandlers">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.Configuration.FlurlClientBuilder.Headers">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlClientBuilder.#ctor(System.String)">
            <summary>
            Creates a new FlurlClientBuilder.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlClientBuilder.AddMiddleware(System.Func{System.Net.Http.DelegatingHandler})">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlClientBuilder.ConfigureHttpClient(System.Action{System.Net.Http.HttpClient})">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlClientBuilder.ConfigureInnerHandler(System.Action{System.Net.Http.HttpClientHandler})">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlClientBuilder.Build">
            <inheritdoc />
        </member>
        <member name="T:Flurl.Http.Configuration.IFlurlClientCache">
            <summary>
            Interface for a cache of IFlurlClient instances.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.IFlurlClientCache.Add(System.String,System.String,System.Action{Flurl.Http.Configuration.IFlurlClientBuilder})">
            <summary>
            Adds a new IFlurlClient to this cache. Call once per client at startup to register and configure a named client.
            </summary>
            <param name="name">Name of the IFlurlClient. Serves as a cache key. Subsequent calls to Get will return this client.</param>
            <param name="baseUrl">Optional. The base URL associated with the new client.</param>
            <param name="configure">Optional. Configure the builder associated with the added client.</param>
            <returns>This IFlurlCache.</returns>
        </member>
        <member name="M:Flurl.Http.Configuration.IFlurlClientCache.Get(System.String)">
            <summary>
            Gets a preconfigured named IFlurlClient.
            </summary>
            <param name="name">The client name.</param>
            <returns>The cached IFlurlClient.</returns>
        </member>
        <member name="M:Flurl.Http.Configuration.IFlurlClientCache.GetOrAdd(System.String,System.String,System.Action{Flurl.Http.Configuration.IFlurlClientBuilder})">
            <summary>
            Gets a named IFlurlClient, creating and (optionally) configuring one if it doesn't exist or has been disposed.
            </summary>
            <param name="name">The client name.</param>
            <param name="baseUrl">The base URL associated with the new client, if it doesn't exist.</param>
            <param name="configure">Configure the builder associated with the new client, if it doesn't exist.</param>
            <returns>The cached IFlurlClient.</returns>
        </member>
        <member name="M:Flurl.Http.Configuration.IFlurlClientCache.WithDefaults(System.Action{Flurl.Http.Configuration.IFlurlClientBuilder})">
            <summary>
            Adds initialization logic that gets executed for every new IFlurlClient added this cache.
            Good place for things like default settings. Executes before client-specific builder logic.
            Call at startup (or whenever the cache is first created); clients already cached will NOT have this logic applied.
            </summary>
            <returns>This IFlurlCache.</returns>
        </member>
        <member name="M:Flurl.Http.Configuration.IFlurlClientCache.Remove(System.String)">
            <summary>
            Removes a named client from this cache.
            </summary>
            <returns>This IFlurlCache.</returns>
        </member>
        <member name="M:Flurl.Http.Configuration.IFlurlClientCache.Clear">
            <summary>
            Disposes and removes all cached IFlurlClient instances.
            </summary>
            <returns>This IFlurlCache.</returns>
        </member>
        <member name="T:Flurl.Http.Configuration.FlurlClientCache">
            <summary>
            Default implementation of IFlurlClientCache.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlClientCache.Add(System.String,System.String,System.Action{Flurl.Http.Configuration.IFlurlClientBuilder})">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlClientCache.Get(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlClientCache.GetOrAdd(System.String,System.String,System.Action{Flurl.Http.Configuration.IFlurlClientBuilder})">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlClientCache.WithDefaults(System.Action{Flurl.Http.Configuration.IFlurlClientBuilder})">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlClientCache.Remove(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlClientCache.Clear">
            <inheritdoc />
        </member>
        <member name="T:Flurl.Http.Configuration.IFlurlClientFactory">
            <summary>
            Interface for helper methods used to construct IFlurlClient instances.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.IFlurlClientFactory.CreateHttpClient(System.Net.Http.HttpMessageHandler)">
            <summary>
            Creates and configures a new HttpClient as needed when a new IFlurlClient instance is created.
            Implementors should NOT attempt to cache or reuse HttpClient instances here - their lifetime is
            bound one-to-one with an IFlurlClient, whose caching and reuse is managed by IFlurlClientCache.
            </summary>
            <param name="handler">The HttpMessageHandler passed to the constructor of the HttpClient.</param>
        </member>
        <member name="M:Flurl.Http.Configuration.IFlurlClientFactory.CreateInnerHandler">
            <summary>
            Creates and configures a new HttpMessageHandler as needed when a new IFlurlClient instance is created.
            The default implementation creates an instance of SocketsHttpHandler for platforms that support it,
            otherwise HttpClientHandler.
            </summary>
        </member>
        <member name="T:Flurl.Http.Configuration.FlurlClientFactoryExtensions">
            <summary>
            Extension methods on IFlurlClientFactory
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlClientFactoryExtensions.CreateHttpClient(Flurl.Http.Configuration.IFlurlClientFactory)">
            <summary>
            Creates an HttpClient with the HttpMessageHandler returned from this factory's CreateInnerHandler method.
            </summary>
        </member>
        <member name="T:Flurl.Http.Configuration.DefaultFlurlClientFactory">
            <summary>
            Default implementation of IFlurlClientFactory, used to build and cache IFlurlClient instances.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.DefaultFlurlClientFactory.CreateHttpClient(System.Net.Http.HttpMessageHandler)">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.Configuration.DefaultFlurlClientFactory.CreateInnerHandler">
            <summary>
            Creates and configures a new HttpMessageHandler as needed when a new IFlurlClient instance is created.
            </summary>
        </member>
        <member name="T:Flurl.Http.Configuration.FlurlHttpSettings">
            <summary>
            A set of properties that affect Flurl.Http behavior
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlHttpSettings.#ctor">
            <summary>
            Creates a new FlurlHttpSettings object.
            </summary>
        </member>
        <member name="P:Flurl.Http.Configuration.FlurlHttpSettings.Parent">
            <summary>
            Gets or sets the default values to fall back on when values are not explicitly set on this instance.
            </summary>
        </member>
        <member name="P:Flurl.Http.Configuration.FlurlHttpSettings.Timeout">
            <summary>
            Gets or sets the HTTP request timeout.
            </summary>
        </member>
        <member name="P:Flurl.Http.Configuration.FlurlHttpSettings.HttpVersion">
            <summary>
            Gets or sets the HTTP version to be used. Default is "1.1".
            </summary>
        </member>
        <member name="P:Flurl.Http.Configuration.FlurlHttpSettings.AllowedHttpStatusRange">
            <summary>
            Gets or sets a pattern representing a range of HTTP status codes which (in addition to 2xx) will NOT result in Flurl.Http throwing an Exception.
            Examples: "3xx", "100,300,600", "100-299,6xx", "*" (allow everything)
            2xx will never throw regardless of this setting.
            </summary>
        </member>
        <member name="P:Flurl.Http.Configuration.FlurlHttpSettings.JsonSerializer">
            <summary>
            Gets or sets object used to serialize and deserialize JSON. Default implementation uses Newtonsoft Json.NET.
            </summary>
        </member>
        <member name="P:Flurl.Http.Configuration.FlurlHttpSettings.UrlEncodedSerializer">
            <summary>
            Gets or sets object used to serialize URL-encoded data. (Deserialization not supported in default implementation.)
            </summary>
        </member>
        <member name="P:Flurl.Http.Configuration.FlurlHttpSettings.Redirects">
            <summary>
            Gets object whose properties describe how Flurl.Http should handle redirect (3xx) responses.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlHttpSettings.ResetDefaults">
            <summary>
            Resets all overridden settings to their default values. For example, on a FlurlRequest,
            all settings are reset to FlurlClient-level settings.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlHttpSettings.Get``1(System.String)">
            <summary>
            Gets a settings value from this instance if explicitly set, otherwise from the default settings that back this instance.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.FlurlHttpSettings.Set``1(``0,System.String)">
            <summary>
            Sets a settings value for this instance.
            </summary>
        </member>
        <member name="T:Flurl.Http.Configuration.ISerializer">
            <summary>
            Contract for serializing and deserializing objects.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.ISerializer.Serialize(System.Object)">
            <summary>
            Serializes an object to a string representation.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.ISerializer.Deserialize``1(System.String)">
            <summary>
            Deserializes an object from a string representation.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.ISerializer.Deserialize``1(System.IO.Stream)">
            <summary>
            Deserializes an object from a stream representation.
            </summary>
        </member>
        <member name="T:Flurl.Http.Configuration.RedirectSettings">
            <summary>
            A set of properties that affect Flurl.Http behavior specific to auto-redirecting.
            </summary>
        </member>
        <member name="M:Flurl.Http.Configuration.RedirectSettings.#ctor(Flurl.Http.Configuration.FlurlHttpSettings)">
            <summary>
            Creates a new instance of RedirectSettings.
            </summary>
        </member>
        <member name="P:Flurl.Http.Configuration.RedirectSettings.Enabled">
            <summary>
            If false, all of Flurl's mechanisms for handling redirects, including raising the OnRedirect event,
            are disabled entirely. This could also impact cookie functionality. Default is true. If you don't
            need Flurl's redirect or cookie functionality, or you are providing an HttpClient whose HttpClientHandler
            is providing these services, then it is safe to set this to false.
            </summary>
        </member>
        <member name="P:Flurl.Http.Configuration.RedirectSettings.AllowSecureToInsecure">
            <summary>
            If true, redirecting from HTTPS to HTTP is allowed. Default is false, as this behavior is considered
            insecure.
            </summary>
        </member>
        <member name="P:Flurl.Http.Configuration.RedirectSettings.ForwardHeaders">
            <summary>
            If true, request-level headers sent in the original request are forwarded in the redirect, with the
            exception of Authorization (use ForwardAuthorizationHeader) and Cookie (use a CookieJar). Also, any
            headers set on FlurlClient are automatically sent with all requests, including redirects. Default is true.
            </summary>
        </member>
        <member name="P:Flurl.Http.Configuration.RedirectSettings.ForwardAuthorizationHeader">
            <summary>
            If true, any Authorization header sent in the original request is forwarded in the redirect.
            Default is false, as this behavior is considered insecure.
            </summary>
        </member>
        <member name="P:Flurl.Http.Configuration.RedirectSettings.MaxAutoRedirects">
            <summary>
            Maximum number of redirects that Flurl will automatically follow in a single request. Default is 10.
            </summary>
        </member>
        <member name="T:Flurl.Http.Content.CapturedJsonContent">
            <summary>
            Provides HTTP content based on a serialized JSON object, with the JSON string captured to a property
            so it can be read without affecting the read-once content stream.
            </summary>
        </member>
        <member name="M:Flurl.Http.Content.CapturedJsonContent.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.Content.CapturedJsonContent"/> class.
            </summary>
            <param name="json">The json.</param>
        </member>
        <member name="T:Flurl.Http.Content.CapturedMultipartContent">
            <summary>
            Provides HTTP content for a multipart/form-data request.
            </summary>
        </member>
        <member name="P:Flurl.Http.Content.CapturedMultipartContent.Parts">
            <summary>
            Gets an array of HttpContent objects that make up the parts of the multipart request.
            </summary>
        </member>
        <member name="M:Flurl.Http.Content.CapturedMultipartContent.#ctor(Flurl.Http.Configuration.FlurlHttpSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.Content.CapturedMultipartContent"/> class.
            </summary>
            <param name="settings">The FlurlHttpSettings used to serialize each content part.</param>
        </member>
        <member name="M:Flurl.Http.Content.CapturedMultipartContent.#ctor(System.String,Flurl.Http.Configuration.FlurlHttpSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.Content.CapturedMultipartContent"/> class.
            </summary>
            <param name="subtype">The subtype of the multipart content.</param>
            <param name="settings">The FlurlHttpSettings used to serialize each content part.</param>
        </member>
        <member name="M:Flurl.Http.Content.CapturedMultipartContent.#ctor(System.String,System.String,Flurl.Http.Configuration.FlurlHttpSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.Content.CapturedMultipartContent"/> class.
            </summary>
            <param name="subtype">The subtype of the multipart content.</param>
            <param name="boundary">The boundary string for the multipart content.</param>
            <param name="settings">The FlurlHttpSettings used to serialize each content part.</param>
        </member>
        <member name="M:Flurl.Http.Content.CapturedMultipartContent.Add(System.String,System.Net.Http.HttpContent)">
            <summary>
            Add a content part to the multipart request.
            </summary>
            <param name="name">The control name of the part.</param>
            <param name="content">The HttpContent of the part.</param>
            <returns>This CapturedMultipartContent instance (supports method chaining).</returns>
        </member>
        <member name="M:Flurl.Http.Content.CapturedMultipartContent.AddString(System.String,System.String,System.String)">
            <summary>
            Add a simple string part to the multipart request.
            </summary>
            <param name="name">The name of the part.</param>
            <param name="value">The string value of the part.</param>
            <param name="contentType">The value of the Content-Type header for this part. If null (the default), header will be excluded, which complies with the HTML 5 standard.</param>
            <returns>This CapturedMultipartContent instance (supports method chaining).</returns>
        </member>
        <member name="M:Flurl.Http.Content.CapturedMultipartContent.AddStringParts(System.Object,System.String)">
            <summary>
            Add multiple string parts to the multipart request by parsing an object's properties into control name/content pairs.
            </summary>
            <param name="data">The object (typically anonymous) whose properties are parsed into control name/content pairs.</param>
            <param name="contentType">The value of the Content-Type header for this part. If null, header will be excluded, which complies with the HTML 5 standard.</param>
            <returns>This CapturedMultipartContent instance (supports method chaining).</returns>
        </member>
        <member name="M:Flurl.Http.Content.CapturedMultipartContent.AddJson(System.String,System.Object)">
            <summary>
            Add a JSON-serialized part to the multipart request.
            </summary>
            <param name="name">The control name of the part.</param>
            <param name="data">The content of the part, which will be serialized to JSON.</param>
            <returns>This CapturedMultipartContent instance (supports method chaining).</returns>
        </member>
        <member name="M:Flurl.Http.Content.CapturedMultipartContent.AddUrlEncoded(System.String,System.Object)">
            <summary>
            Add a URL-encoded part to the multipart request.
            </summary>
            <param name="name">The control name of the part.</param>
            <param name="data">The content of the part, whose properties will be parsed and serialized to URL-encoded format.</param>
            <returns>This CapturedMultipartContent instance (supports method chaining).</returns>
        </member>
        <member name="M:Flurl.Http.Content.CapturedMultipartContent.AddFile(System.String,System.IO.Stream,System.String,System.String,System.Int32)">
            <summary>
            Adds a file to the multipart request from a stream.
            </summary>
            <param name="name">The control name of the part.</param>
            <param name="stream">The file stream to send.</param>
            <param name="fileName">The filename, added to the Content-Disposition header of the part.</param>
            <param name="contentType">The content type of the file.</param>
            <param name="bufferSize">The buffer size of the stream upload in bytes. Defaults to 4096.</param>
            <returns>This CapturedMultipartContent instance (supports method chaining).</returns>
        </member>
        <member name="M:Flurl.Http.Content.CapturedMultipartContent.AddFile(System.String,System.String,System.String,System.Int32,System.String)">
            <summary>
            Adds a file to the multipart request from a local path.
            </summary>
            <param name="name">The control name of the part.</param>
            <param name="path">The local path to the file.</param>
            <param name="contentType">The content type of the file.</param>
            <param name="bufferSize">The buffer size of the stream upload in bytes. Defaults to 4096.</param>
            <param name="fileName">The filename, added to the Content-Disposition header of the part. Defaults to local file name.</param>
            <returns>This CapturedMultipartContent instance (supports method chaining).</returns>
        </member>
        <member name="T:Flurl.Http.Content.CapturedStringContent">
            <summary>
            Provides HTTP content based on a string, with the string itself captured to a property
            so it can be read without affecting the read-once content stream.
            </summary>
        </member>
        <member name="P:Flurl.Http.Content.CapturedStringContent.Content">
            <summary>
            The content body captured as a string. Can be read multiple times (unlike the content stream).
            </summary>
        </member>
        <member name="M:Flurl.Http.Content.CapturedStringContent.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Flurl.Http.Content.CapturedStringContent"/> with a Content-Type header of text/plain; charset=UTF-8
            </summary>
            <param name="content">The content.</param>
        </member>
        <member name="M:Flurl.Http.Content.CapturedStringContent.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.Content.CapturedStringContent"/> class.
            </summary>
            <param name="content">The content.</param>
            <param name="contentType">Value of the Content-Type header. To exclude the header, set to null explicitly.</param>
        </member>
        <member name="T:Flurl.Http.Content.CapturedUrlEncodedContent">
            <summary>
            Provides HTTP content based on an object serialized to URL-encoded name-value pairs.
            Useful in simulating an HTML form POST. Serialized content is captured to Content property
            so it can be read without affecting the read-once content stream.
            </summary>
        </member>
        <member name="M:Flurl.Http.Content.CapturedUrlEncodedContent.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.Content.CapturedUrlEncodedContent"/> class.
            </summary>
            <param name="data">Content represented as a (typically anonymous) object, which will be parsed into name/value pairs.</param>
        </member>
        <member name="T:Flurl.Http.Content.FileContent">
            <summary>
            Represents HTTP content based on a local file. Typically used with PostMultipartAsync for uploading files.
            </summary>
        </member>
        <member name="P:Flurl.Http.Content.FileContent.Path">
            <summary>
            The local file path.
            </summary>
        </member>
        <member name="M:Flurl.Http.Content.FileContent.#ctor(System.String,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.Content.FileContent"/> class.
            </summary>
            <param name="path">The local file path.</param>
            <param name="bufferSize">The buffer size of the stream upload in bytes. Defaults to 4096.</param>
        </member>
        <member name="M:Flurl.Http.Content.FileContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
            <summary>
            Serializes to stream asynchronous.
            </summary>
            <param name="stream">The stream.</param>
            <param name="context">The context.</param>
            <returns></returns>
        </member>
        <member name="M:Flurl.Http.Content.FileContent.TryComputeLength(System.Int64@)">
            <summary>
            Tries the length of the compute.
            </summary>
            <param name="length">The length.</param>
            <returns></returns>
        </member>
        <member name="T:Flurl.Http.CookieCutter">
            <summary>
            Utility and extension methods for parsing and validating cookies.
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieCutter.ParseRequestHeader(System.String)">
            <summary>
            Parses a Cookie request header to name-value pairs.
            </summary>
            <param name="headerValue">Value of the Cookie request header.</param>
            <returns></returns>
        </member>
        <member name="M:Flurl.Http.CookieCutter.ParseResponseHeader(System.String,System.String,System.Nullable{System.DateTimeOffset})">
            <summary>
            Parses a Set-Cookie response header to a FlurlCookie.
            </summary>
            <param name="headerValue">Value of the Set-Cookie header.</param>
            <param name="url">The URL that sent the response.</param>
            <param name="dateReceived">Date/time that original Set-Cookie header was received. Defaults to current date/time. Important for Max-Age to be enforced correctly.</param>
            <returns></returns>
        </member>
        <member name="M:Flurl.Http.CookieCutter.GetPairs(System.String)">
            <summary>
            Parses list of semicolon-delimited "name=value" pairs.
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieCutter.BuildRequestHeader(System.Collections.Generic.IEnumerable{System.ValueTuple{System.String,System.String}})">
            <summary>
            Creates a Cookie request header value from a list of cookie name-value pairs.
            </summary>
            <returns>A header value if cookie values are present, otherwise null.</returns>
        </member>
        <member name="M:Flurl.Http.CookieCutter.BuildResponseHeader(Flurl.Http.FlurlCookie)">
            <summary>
            Creates a Set-Cookie response header value from a FlurlCookie.
            </summary>
            <returns>A header value if cookie is non-null, otherwise null.</returns>
        </member>
        <member name="M:Flurl.Http.CookieCutter.IsValid(Flurl.Http.FlurlCookie,System.String@)">
            <summary>
            True if this cookie passes well-accepted rules for the Set-Cookie header. If false, provides a descriptive reason.
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieCutter.IsExpired(Flurl.Http.FlurlCookie,System.String@)">
            <summary>
            True if this cookie is expired. If true, provides a descriptive reason (Expires or Max-Age).
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieCutter.ShouldSendTo(Flurl.Http.FlurlCookie,Flurl.Url,System.String@)">
            <summary>
            True if this cookie should be sent in a request to the given URL. If false, provides a descriptive reason.
            </summary>
        </member>
        <member name="T:Flurl.Http.CookieExtensions">
            <summary>
            Fluent extension methods for working with HTTP cookies.
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieExtensions.WithCookie(Flurl.Http.IFlurlRequest,System.String,System.Object)">
            <summary>
            Adds or updates a name-value pair in this request's Cookie header.
            To automatically maintain a cookie "session", consider using a CookieJar or CookieSession instead.
            </summary>
            <param name="request">The IFlurlRequest.</param>
            <param name="name">The cookie name.</param>
            <param name="value">The cookie value.</param>
            <returns>This IFlurlClient instance.</returns>
        </member>
        <member name="M:Flurl.Http.CookieExtensions.WithCookies(Flurl.Http.IFlurlRequest,System.Object)">
            <summary>
            Adds or updates name-value pairs in this request's Cookie header, based on property names/values
            of the provided object, or keys/values if object is a dictionary.
            To automatically maintain a cookie "session", consider using a CookieJar or CookieSession instead.
            </summary>
            <param name="request">The IFlurlRequest.</param>
            <param name="values">Names/values of HTTP cookies to set. Typically an anonymous object or IDictionary.</param>
            <returns>This IFlurlClient.</returns>
        </member>
        <member name="M:Flurl.Http.CookieExtensions.WithCookies(Flurl.Http.IFlurlRequest,Flurl.Http.CookieJar)">
            <summary>
            Sets the CookieJar associated with this request, which will be updated with any Set-Cookie headers present
            in the response and is suitable for reuse in subsequent requests.
            </summary>
            <param name="request">The IFlurlRequest.</param>
            <param name="cookieJar">The CookieJar.</param>
            <returns>This IFlurlClient instance.</returns>
        </member>
        <member name="M:Flurl.Http.CookieExtensions.WithCookies(Flurl.Http.IFlurlRequest,Flurl.Http.CookieJar@)">
            <summary>
            Creates a new CookieJar and associates it with this request, which will be updated with any Set-Cookie
            headers present in the response and is suitable for reuse in subsequent requests.
            </summary>
            <param name="request">The IFlurlRequest.</param>
            <param name="cookieJar">The created CookieJar, which can be reused in subsequent requests.</param>
            <returns>This IFlurlClient instance.</returns>
        </member>
        <member name="T:Flurl.Http.CookieJar">
            <summary>
            A collection of FlurlCookies that can be attached to one or more FlurlRequests, either explicitly via WithCookies
            or implicitly via a CookieSession. Stores cookies received via Set-Cookie response headers.
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieJar.AddOrReplace(System.String,System.Object,System.String,System.Nullable{System.DateTimeOffset})">
            <summary>
            Adds a cookie to the jar or replaces one with the same Name/Domain/Path.
            Throws InvalidCookieException if cookie is invalid.
            </summary>
            <param name="name">Name of the cookie.</param>
            <param name="value">Value of the cookie.</param>
            <param name="originUrl">URL of request that sent the original Set-Cookie header.</param>
            <param name="dateReceived">Date/time that original Set-Cookie header was received. Defaults to current date/time. Important for Max-Age to be enforced correctly.</param>
        </member>
        <member name="M:Flurl.Http.CookieJar.AddOrReplace(Flurl.Http.FlurlCookie)">
            <summary>
            Adds a cookie to the jar or replaces one with the same Name/Domain/Path.
            Throws InvalidCookieException if cookie is invalid.
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieJar.TryAddOrReplace(Flurl.Http.FlurlCookie,System.String@)">
            <summary>
            Adds a cookie to the jar or updates if one with the same Name/Domain/Path already exists,
            but only if it is valid and not expired.
            </summary>
            <returns>true if cookie is valid and was added or updated. If false, provides descriptive reason.</returns>
        </member>
        <member name="M:Flurl.Http.CookieJar.Remove(System.Func{Flurl.Http.FlurlCookie,System.Boolean})">
            <summary>
            Removes all cookies matching the given predicate.
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieJar.Clear">
            <summary>
            Removes all cookies from this CookieJar
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieJar.WriteTo(System.IO.TextWriter)">
            <summary>
            Writes this CookieJar to a TextWriter. Useful for persisting to a file.
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieJar.LoadFrom(System.IO.TextReader)">
            <summary>
            Instantiates a CookieJar that was previously persisted using WriteTo.
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieJar.ToString">
            <summary>
            Returns a string representing this CookieJar.
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieJar.LoadFromString(System.String)">
            <summary>
            Instantiates a CookieJar that was previously persisted using ToString.
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieJar.GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:Flurl.Http.CookieJar.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="P:Flurl.Http.CookieJar.Count">
            <inheritdoc/>
        </member>
        <member name="T:Flurl.Http.InvalidCookieException">
            <summary>
            Exception thrown when attempting to add or update an invalid FlurlCookie to a CookieJar.
            </summary>
        </member>
        <member name="M:Flurl.Http.InvalidCookieException.#ctor(System.String)">
            <summary>
            Creates a new InvalidCookieException.
            </summary>
        </member>
        <member name="T:Flurl.Http.CookieSession">
            <summary>
            A context where multiple requests use a common CookieJar.
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieSession.#ctor(System.String)">
            <summary>
            Creates a new CookieSession where all requests are made off the same base URL.
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieSession.#ctor(Flurl.Http.IFlurlClient)">
            <summary>
            Creates a new CookieSession where all requests are made using the provided IFlurlClient
            </summary>
        </member>
        <member name="P:Flurl.Http.CookieSession.Cookies">
            <summary>
            The CookieJar used by all requests sent with this CookieSession.
            </summary>
        </member>
        <member name="M:Flurl.Http.CookieSession.Request(System.Object[])">
            <summary>
            Creates a new IFlurlRequest with this session's CookieJar that can be further built and sent fluently.
            </summary>
            <param name="urlSegments">The URL or URL segments for the request.</param>
        </member>
        <member name="M:Flurl.Http.CookieSession.Dispose">
            <summary>
            Not necessary to call. IDisposable is implemented mainly for the syntactic sugar of using statements.
            </summary>
        </member>
        <member name="T:Flurl.Http.DownloadExtensions">
            <summary>
            Fluent extension methods for downloading a file.
            </summary>
        </member>
        <member name="M:Flurl.Http.DownloadExtensions.DownloadFileAsync(Flurl.Http.IFlurlRequest,System.String,System.String,System.Int32,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Asynchronously downloads a file at the specified URL.
            </summary>
            <param name="request">The flurl request.</param>
            <param name="localFolderPath">Path of local folder where file is to be downloaded.</param>
            <param name="localFileName">Name of local file. If not specified, the source filename (from Content-Dispostion header, or last segment of the URL) is used.</param>
            <param name="bufferSize">Buffer size in bytes. Default is 4096.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the local path of the downloaded file.</returns>
        </member>
        <member name="M:Flurl.Http.FileUtil.MakeValidName(System.String)">
            <summary>
            Replaces invalid path characters with underscores.
            </summary>
        </member>
        <member name="T:Flurl.Http.FlurlCall">
            <summary>
            Encapsulates request, response, and other details associated with an HTTP call. Useful for diagnostics and available in
            global event handlers and FlurlHttpException.Call.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCall.Client">
            <summary>
            The IFlurlClient used to make this call.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCall.Request">
            <summary>
            The IFlurlRequest associated with this call.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCall.HttpRequestMessage">
            <summary>
            The raw HttpRequestMessage associated with this call.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCall.RequestBody">
            <summary>
            Captured request body. Available ONLY if HttpRequestMessage.Content is a Flurl.Http.Content.CapturedStringContent.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCall.Response">
            <summary>
            The IFlurlResponse associated with this call if the call completed, otherwise null.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCall.Redirect">
            <summary>
            If this call has a 3xx response and Location header, contains information about how to handle the redirect.
            Otherwise null.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCall.HttpResponseMessage">
            <summary>
            The raw HttpResponseMessage associated with the call if the call completed, otherwise null.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCall.Exception">
            <summary>
            Exception that occurred while sending the HttpRequestMessage.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCall.ExceptionHandled">
            <summary>
            User code should set this to true inside global event handlers (OnError, etc) to indicate
            that the exception was handled and should not be propagated further.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCall.StartedUtc">
            <summary>
            DateTime the moment the request was sent.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCall.EndedUtc">
            <summary>
            DateTime the moment a response was received.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCall.Duration">
            <summary>
            Total duration of the call if it completed, otherwise null.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCall.Completed">
            <summary>
            True if a response was received, regardless of whether it is an error status.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCall.Succeeded">
            <summary>
            True if response was received with any success status or a match with AllowedHttpStatusRange setting.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlCall.ToString">
            <summary>
            Returns the verb and absolute URI associated with this call.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Flurl.Http.FlurlRedirect">
            <summary>
            An object containing information about if/how an automatic redirect request will be created and sent.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlRedirect.Url">
            <summary>
            The URL to redirect to, from the response's Location header.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlRedirect.Count">
            <summary>
            The number of auto-redirects that have already occurred since the original call, plus 1 for this one.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlRedirect.Follow">
            <summary>
            If true, Flurl will automatically send a redirect request. Set to false to prevent auto-redirect.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlRedirect.ChangeVerbToGet">
            <summary>
            If true, the redirect request will use the GET verb and will not forward the original request body.
            Otherwise, the original verb and body will be preserved in the redirect.
            </summary>
        </member>
        <member name="T:Flurl.Http.IFlurlClient">
            <summary>
            Interface defining FlurlClient's contract (useful for mocking and DI)
            </summary>
        </member>
        <member name="P:Flurl.Http.IFlurlClient.HttpClient">
            <summary>
            Gets the HttpClient that this IFlurlClient wraps.
            </summary>
        </member>
        <member name="P:Flurl.Http.IFlurlClient.BaseUrl">
            <summary>
            Gets or sets the base URL used for all calls made with this client.
            </summary>
        </member>
        <member name="M:Flurl.Http.IFlurlClient.Request(System.Object[])">
            <summary>
            Creates a new IFlurlRequest that can be further built and sent fluently.
            </summary>
            <param name="urlSegments">The URL or URL segments for the request. If BaseUrl is defined, it is assumed that these are path segments off that base.</param>
            <returns>A new IFlurlRequest</returns>
        </member>
        <member name="P:Flurl.Http.IFlurlClient.IsDisposed">
            <summary>
            Gets a value indicating whether this instance (and its underlying HttpClient) has been disposed.
            </summary>
        </member>
        <member name="M:Flurl.Http.IFlurlClient.SendAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Asynchronously sends an HTTP request.
            </summary>
            <param name="request">The IFlurlRequest to send.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="T:Flurl.Http.FlurlClient">
            <summary>
            A reusable object for making HTTP calls.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlClient.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Flurl.Http.FlurlClient"/>.
            </summary>
            <param name="baseUrl">The base URL associated with this client.</param>
        </member>
        <member name="M:Flurl.Http.FlurlClient.#ctor(System.Net.Http.HttpClient,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Flurl.Http.FlurlClient"/>, wrapping an existing HttpClient.
            Generally, you should let Flurl create and manage HttpClient instances for you, but you might, for
            example, have an HttpClient instance that was created by a 3rd-party library and you want to use
            Flurl to build and send calls with it. Be aware that if the HttpClient has an underlying
            HttpMessageHandler that processes cookies and automatic redirects (as is the case by default),
            Flurl's re-implementation of those features may not work properly.
            </summary>
            <param name="httpClient">The instantiated HttpClient instance.</param>
            <param name="baseUrl">Optional. The base URL associated with this client.</param>
        </member>
        <member name="P:Flurl.Http.FlurlClient.BaseUrl">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlClient.Settings">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlClient.EventHandlers">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlClient.Headers">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlClient.HttpClient">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.FlurlClient.Request(System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.FlurlClient.SendAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlClient.IsDisposed">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.FlurlClient.Dispose">
            <summary>
            Disposes the underlying HttpClient and HttpMessageHandler.
            </summary>
        </member>
        <member name="T:Flurl.Http.SameSite">
            <summary>
            Corresponds to the possible values of the SameSite attribute of the Set-Cookie header.
            </summary>
        </member>
        <member name="F:Flurl.Http.SameSite.Strict">
            <summary>
            Indicates a browser should only send cookie for same-site requests.
            </summary>
        </member>
        <member name="F:Flurl.Http.SameSite.Lax">
            <summary>
            Indicates a browser should send cookie for cross-site requests only with top-level navigation. 
            </summary>
        </member>
        <member name="F:Flurl.Http.SameSite.None">
            <summary>
            Indicates a browser should send cookie for same-site and cross-site requests.
            </summary>
        </member>
        <member name="T:Flurl.Http.FlurlCookie">
            <summary>
            Represents an HTTP cookie. Closely matches Set-Cookie response header.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlCookie.#ctor(System.String,System.String,System.String,System.Nullable{System.DateTimeOffset})">
            <summary>
            Creates a new FlurlCookie.
            </summary>
            <param name="name">Name of the cookie.</param>
            <param name="value">Value of the cookie.</param>
            <param name="originUrl">URL of request that sent the original Set-Cookie header.</param>
            <param name="dateReceived">Date/time that original Set-Cookie header was received. Defaults to current date/time. Important for Max-Age to be enforced correctly.</param>
        </member>
        <member name="P:Flurl.Http.FlurlCookie.OriginUrl">
            <summary>
            The URL that originally sent the Set-Cookie response header. If adding to a CookieJar, this is required unless
            both Domain AND Path are specified.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCookie.DateReceived">
            <summary>
            Date and time the cookie was received. Defaults to date/time this FlurlCookie was created.
            Important for Max-Age to be enforced correctly.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCookie.Name">
            <summary>
            The cookie name.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCookie.Value">
            <summary>
            The cookie value.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCookie.Expires">
            <summary>
            Corresponds to the Expires attribute of the Set-Cookie header.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCookie.MaxAge">
            <summary>
            Corresponds to the Max-Age attribute of the Set-Cookie header.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCookie.Domain">
            <summary>
            Corresponds to the Domain attribute of the Set-Cookie header.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCookie.Path">
            <summary>
            Corresponds to the Path attribute of the Set-Cookie header.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCookie.Secure">
            <summary>
            Corresponds to the Secure attribute of the Set-Cookie header.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCookie.HttpOnly">
            <summary>
            Corresponds to the HttpOnly attribute of the Set-Cookie header.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlCookie.SameSite">
            <summary>
            Corresponds to the SameSite attribute of the Set-Cookie header.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlCookie.GetKey">
            <summary>
            Generates a key based on cookie Name, Domain, and Path (using OriginalUrl in the absence of Domain/Path).
            Used by CookieJar to determine whether to add a cookie or update an existing one.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlCookie.WriteTo(System.IO.TextWriter)">
            <summary>
            Writes this cookie to a TextWriter. Useful for persisting to a file.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlCookie.LoadFrom(System.IO.TextReader)">
            <summary>
            Instantiates a FlurlCookie that was previously persisted using WriteTo.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlCookie.ToString">
            <summary>
            Returns a string representing this FlurlCookie.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlCookie.LoadFromString(System.String)">
            <summary>
            Instantiates a FlurlCookie that was previously persisted using ToString.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlCookie.Lock">
            <summary>
            Makes this cookie immutable. Call when added to a jar.
            </summary>
        </member>
        <member name="T:Flurl.Http.FlurlEventType">
            <summary>
            Types of events raised by Flurl over the course of a call that can be handled via event handlers.
            </summary>
        </member>
        <member name="F:Flurl.Http.FlurlEventType.BeforeCall">
            <summary>
            Fired immediately before an HTTP request is sent.
            </summary>
        </member>
        <member name="F:Flurl.Http.FlurlEventType.AfterCall">
            <summary>
            Fired immediately after an HTTP response is received.
            </summary>
        </member>
        <member name="F:Flurl.Http.FlurlEventType.OnError">
            <summary>
            Fired when an HTTP error response is received, just before AfterCall is fired. Error
            responses include any status in 4xx or 5xx range by default, configurable via AllowHttpStatus.
            You can inspect call.Exception for details, and optionally set call.ExceptionHandled to
            true to prevent the exception from bubbling up after the handler exits.
            </summary>
        </member>
        <member name="F:Flurl.Http.FlurlEventType.OnRedirect">
            <summary>
            Fired when any 3xx response with a Location header is received, just before AfterCall is fired
            and before the subsequent (redirected) request is sent. You can inspect/manipulate the
            call.Redirect object to determine what will happen next. An auto-redirect will only happen if
            call.Redirect.Follow is true upon exiting the callback.
            </summary>
        </member>
        <member name="T:Flurl.Http.IFlurlEventHandler">
            <summary>
            Defines a handler for Flurl events such as BeforeCall, AfterCall, and OnError
            </summary>
        </member>
        <member name="M:Flurl.Http.IFlurlEventHandler.Handle(Flurl.Http.FlurlEventType,Flurl.Http.FlurlCall)">
            <summary>
            Action to take when a Flurl event fires. Prefer HandleAsync if async calls need to be made.
            </summary>
        </member>
        <member name="M:Flurl.Http.IFlurlEventHandler.HandleAsync(Flurl.Http.FlurlEventType,Flurl.Http.FlurlCall)">
            <summary>
            Asynchronous action to take when a Flurl event fires.
            </summary>
        </member>
        <member name="T:Flurl.Http.FlurlEventHandler">
            <summary>
            Default implementation of IFlurlEventHandler. Typically, you should override Handle or HandleAsync.
            Both are noops by default.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlEventHandler.Handle(Flurl.Http.FlurlEventType,Flurl.Http.FlurlCall)">
            <summary>
            Override to define an action to take when a Flurl event fires. Prefer HandleAsync if async calls need to be made.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlEventHandler.HandleAsync(Flurl.Http.FlurlEventType,Flurl.Http.FlurlCall)">
            <summary>
            Override to define an asynchronous action to take when a Flurl event fires.
            </summary>
        </member>
        <member name="T:Flurl.Http.FlurlHttp">
            <summary>
            A static object for configuring Flurl for "clientless" usage. Provides a default IFlurlClientCache instance primarily
            for clientless support, but can be used directly, as an alternative to a DI-managed singleton cache.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlHttp.Clients">
            <summary>
            A global collection of cached IFlurlClient instances.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlHttp.ConfigureClientForUrl(System.String)">
            <summary>
            Gets a builder for configuring the IFlurlClient that would be selected for calling the given URL when the clientless pattern is used.
            Note that if you've overridden the caching strategy to vary clients by request properties other than Url, you should instead use
            FlurlHttp.Clients.Add(name) to ensure you are configuring the correct client.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlHttp.GetClientForRequest(Flurl.Http.IFlurlRequest)">
            <summary>
            Gets or creates the IFlurlClient that would be selected for sending the given IFlurlRequest when the clientless pattern is used.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlHttp.UseClientCachingStrategy(System.Func{Flurl.Http.IFlurlRequest,System.String})">
            <summary>
            Sets a global caching strategy for getting or creating an IFlurlClient instance when the clientless pattern is used, e.g. url.GetAsync.
            </summary>
            <param name="buildClientName">A delegate that returns a cache key used to store and retrieve a client instance based on properties of the request.</param>
        </member>
        <member name="M:Flurl.Http.FlurlHttp.UseClientPerHostStrategy">
            <summary>
            Sets a global caching strategy of one IFlurlClient per scheme/host/port combination when the clientless pattern is used,
            e.g. url.GetAsync. This is the default strategy, so you shouldn't need to call this except to revert a previous call to
            UseClientCachingStrategy, which would be rare.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlHttp.BuildClientNameByHost(Flurl.Http.IFlurlRequest)">
            <summary>
            Builds a cache key consisting of URL scheme, host, and port. This is the default client caching strategy.
            </summary>
        </member>
        <member name="T:Flurl.Http.FlurlHttpException">
            <summary>
            An exception that is thrown when an HTTP call made by Flurl.Http fails, including when the response
            indicates an unsuccessful HTTP status code.
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlHttpException.Call">
            <summary>
            An object containing details about the failed HTTP call
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlHttpException.#ctor(Flurl.Http.FlurlCall,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.FlurlHttpException"/> class.
            </summary>
            <param name="call">The call.</param>
            <param name="message">The message.</param>
            <param name="inner">The inner.</param>
        </member>
        <member name="M:Flurl.Http.FlurlHttpException.#ctor(Flurl.Http.FlurlCall,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.FlurlHttpException"/> class.
            </summary>
            <param name="call">The call.</param>
            <param name="inner">The inner.</param>
        </member>
        <member name="M:Flurl.Http.FlurlHttpException.#ctor(Flurl.Http.FlurlCall)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.FlurlHttpException"/> class.
            </summary>
            <param name="call">The call.</param>
        </member>
        <member name="P:Flurl.Http.FlurlHttpException.StatusCode">
            <summary>
            Gets the HTTP status code of the response if a response was received, otherwise null.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlHttpException.GetResponseStringAsync">
            <summary>
            Gets the response body of the failed call.
            </summary>
            <returns>A task whose result is the string contents of the response body.</returns>
        </member>
        <member name="M:Flurl.Http.FlurlHttpException.GetResponseJsonAsync``1">
            <summary>
            Deserializes the JSON response body to an object of the given type.
            </summary>
            <typeparam name="T">A type whose structure matches the expected JSON response.</typeparam>
            <returns>A task whose result is an object containing data in the response body.</returns>
        </member>
        <member name="T:Flurl.Http.FlurlHttpTimeoutException">
            <summary>
            An exception that is thrown when an HTTP call made by Flurl.Http times out.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlHttpTimeoutException.#ctor(Flurl.Http.FlurlCall,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.FlurlHttpTimeoutException"/> class.
            </summary>
            <param name="call">Details of the HTTP call that caused the exception.</param>
            <param name="inner">The inner exception.</param>
        </member>
        <member name="T:Flurl.Http.FlurlParsingException">
            <summary>
            An exception that is thrown when an HTTP response could not be parsed to a particular format.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlParsingException.#ctor(Flurl.Http.FlurlCall,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.FlurlParsingException"/> class.
            </summary>
            <param name="call">Details of the HTTP call that caused the exception.</param>
            <param name="expectedFormat">The format that could not be parsed to, i.e. JSON.</param>
            <param name="inner">The inner exception.</param>
        </member>
        <member name="P:Flurl.Http.FlurlParsingException.ExpectedFormat">
            <summary>
            The format that could not be parsed to, i.e. JSON.
            </summary>
        </member>
        <member name="T:Flurl.Http.FlurlConfigurationException">
            <summary>
            An exception that is thrown when Flurl.Http has been misconfigured.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlConfigurationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.FlurlConfigurationException"/> class.
            </summary>
        </member>
        <member name="T:Flurl.Http.IFlurlRequest">
            <summary>
            Represents an HTTP request. Can be created explicitly via new FlurlRequest(), fluently via Url.Request(),
            or implicitly when a call is made via methods like Url.GetAsync().
            </summary>
        </member>
        <member name="P:Flurl.Http.IFlurlRequest.Client">
            <summary>
            Gets or sets the IFlurlClient to use when sending the request.
            </summary>
        </member>
        <member name="P:Flurl.Http.IFlurlRequest.Verb">
            <summary>
            Gets or sets the HTTP method of the request. Normally you don't need to set this explicitly; it will be set
            when you call the sending method, such as GetAsync, PostAsync, etc.
            </summary>
        </member>
        <member name="P:Flurl.Http.IFlurlRequest.Url">
            <summary>
            Gets or sets the URL to be called.
            </summary>
        </member>
        <member name="P:Flurl.Http.IFlurlRequest.Content">
            <summary>
            The body content of this request.
            </summary>
        </member>
        <member name="P:Flurl.Http.IFlurlRequest.Cookies">
            <summary>
            Gets Name/Value pairs parsed from the Cookie request header.
            </summary>
        </member>
        <member name="P:Flurl.Http.IFlurlRequest.CookieJar">
            <summary>
            Gets or sets the collection of HTTP cookies that can be shared between multiple requests. When set, values that
            should be sent with this request (based on Domain, Path, and other rules) are immediately copied to the Cookie
            request header, and any Set-Cookie headers received in the response will be written to the CookieJar.
            </summary>
        </member>
        <member name="P:Flurl.Http.IFlurlRequest.RedirectedFrom">
            <summary>
            The FlurlCall that received a 3xx response and automatically triggered this request.
            </summary>
        </member>
        <member name="M:Flurl.Http.IFlurlRequest.EnsureClient">
            <summary>
            If Client property is null, selects (or creates) a FlurlClient from the global FlurlHttp.Clients cache. Called
            automatically just before a request is sent, so in most cases there is no need to call explicitly.
            </summary>
        </member>
        <member name="M:Flurl.Http.IFlurlRequest.SendAsync(System.Net.Http.HttpMethod,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Asynchronously sends the HTTP request. Mainly used to implement higher-level extension methods (GetJsonAsync, etc).
            </summary>
            <param name="verb">The HTTP method used to make the request.</param>
            <param name="content">Contents of the request body.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="T:Flurl.Http.FlurlRequest">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.FlurlRequest.#ctor(Flurl.Url)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.FlurlRequest"/> class.
            </summary>
            <param name="url">The URL to call with this FlurlRequest instance.</param>
        </member>
        <member name="M:Flurl.Http.FlurlRequest.#ctor(Flurl.Http.IFlurlClient,System.Object[])">
            <summary>
            Used internally by FlurlClient.Request
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlRequest.#ctor(System.String,System.Object[])">
            <summary>
            Used internally by FlurlClient.Request and CookieSession.Request
            </summary>
        </member>
        <member name="P:Flurl.Http.FlurlRequest.Settings">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlRequest.EventHandlers">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlRequest.Client">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlRequest.Verb">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlRequest.Url">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlRequest.Content">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlRequest.RedirectedFrom">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlRequest.Headers">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlRequest.Cookies">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlRequest.CookieJar">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.FlurlRequest.EnsureClient">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.FlurlRequest.SendAsync(System.Net.Http.HttpMethod,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.FlurlRequest.SyncBaseUrl(Flurl.Http.IFlurlClient,Flurl.Http.IFlurlRequest)">
            <summary>
            Prepends client.BaseUrl to this.Url, but only if this.Url isn't already a valid, absolute URL.
            </summary>
        </member>
        <member name="T:Flurl.Http.IFlurlResponse">
            <summary>
            Represents an HTTP response.
            </summary>
        </member>
        <member name="P:Flurl.Http.IFlurlResponse.Headers">
            <summary>
            Gets the collection of response headers received.
            </summary>
        </member>
        <member name="P:Flurl.Http.IFlurlResponse.Cookies">
            <summary>
            Gets the collection of HTTP cookies received in this response via Set-Cookie headers.
            </summary>
        </member>
        <member name="P:Flurl.Http.IFlurlResponse.ResponseMessage">
            <summary>
            Gets the raw HttpResponseMessage that this IFlurlResponse wraps.
            </summary>
        </member>
        <member name="P:Flurl.Http.IFlurlResponse.StatusCode">
            <summary>
            Gets the HTTP status code of the response.
            </summary>
        </member>
        <member name="M:Flurl.Http.IFlurlResponse.GetJsonAsync``1">
            <summary>
            Deserializes JSON-formatted HTTP response body to object of type T.
            </summary>
            <typeparam name="T">A type whose structure matches the expected JSON response.</typeparam>
            <returns>A Task whose result is an object containing data in the response body.</returns>
            <example>x = await url.PostAsync(data).GetJson&lt;T&gt;()</example>
            <exception cref="T:Flurl.Http.FlurlHttpException">Condition.</exception>
        </member>
        <member name="M:Flurl.Http.IFlurlResponse.GetStringAsync">
            <summary>
            Returns HTTP response body as a string.
            </summary>
            <returns>A Task whose result is the response body as a string.</returns>
            <example>s = await url.PostAsync(data).GetString()</example>
        </member>
        <member name="M:Flurl.Http.IFlurlResponse.GetStreamAsync">
            <summary>
            Returns HTTP response body as a stream.
            </summary>
            <returns>A Task whose result is the response body as a stream.</returns>
            <example>stream = await url.PostAsync(data).GetStream()</example>
        </member>
        <member name="M:Flurl.Http.IFlurlResponse.GetBytesAsync">
            <summary>
            Returns HTTP response body as a byte array.
            </summary>
            <returns>A Task whose result is the response body as a byte array.</returns>
            <example>bytes = await url.PostAsync(data).GetBytes()</example>
        </member>
        <member name="T:Flurl.Http.FlurlResponse">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlResponse.Headers">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlResponse.Cookies">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlResponse.ResponseMessage">
            <inheritdoc />
        </member>
        <member name="P:Flurl.Http.FlurlResponse.StatusCode">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.FlurlResponse.#ctor(Flurl.Http.FlurlCall,Flurl.Http.CookieJar)">
            <summary>
            Creates a new FlurlResponse that wraps the give HttpResponseMessage.
            </summary>
        </member>
        <member name="M:Flurl.Http.FlurlResponse.GetJsonAsync``1">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.FlurlResponse.GetStringAsync">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.FlurlResponse.GetStreamAsync">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.FlurlResponse.GetBytesAsync">
            <inheritdoc />
        </member>
        <member name="M:Flurl.Http.FlurlResponse.Dispose">
            <summary>
            Disposes the underlying HttpResponseMessage.
            </summary>
        </member>
        <member name="T:Flurl.Http.GeneratedExtensions">
            <summary>
            Fluent extension methods on String, Url, Uri, and IFlurlRequest.
            </summary>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendJsonAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpMethod,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendStringAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpMethod,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendUrlEncodedAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpMethod,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="body">An object representing the request body, which will be serialized to a URL-encoded string.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous GET request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetJsonAsync``1(Flurl.Http.IFlurlRequest,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous GET request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the JSON response body deserialized to an object of type T.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetStringAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous GET request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the response body as a string.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetStreamAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous GET request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the response body as a Stream.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetBytesAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous GET request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the response body as a byte array.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous POST request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostJsonAsync(Flurl.Http.IFlurlRequest,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous POST request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostStringAsync(Flurl.Http.IFlurlRequest,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous POST request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostUrlEncodedAsync(Flurl.Http.IFlurlRequest,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous POST request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="body">An object representing the request body, which will be serialized to a URL-encoded string.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.HeadAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous HEAD request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PutAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous PUT request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PutJsonAsync(Flurl.Http.IFlurlRequest,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous PUT request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PutStringAsync(Flurl.Http.IFlurlRequest,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous PUT request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.DeleteAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous DELETE request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PatchAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous PATCH request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PatchJsonAsync(Flurl.Http.IFlurlRequest,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous PATCH request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PatchStringAsync(Flurl.Http.IFlurlRequest,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous PATCH request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OptionsAsync(Flurl.Http.IFlurlRequest,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous OPTIONS request.
            </summary>
            <param name="request">This IFlurlRequest</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendAsync(Flurl.Url,System.Net.Http.HttpMethod,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendJsonAsync(Flurl.Url,System.Net.Http.HttpMethod,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendStringAsync(Flurl.Url,System.Net.Http.HttpMethod,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendUrlEncodedAsync(Flurl.Url,System.Net.Http.HttpMethod,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="body">An object representing the request body, which will be serialized to a URL-encoded string.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetAsync(Flurl.Url,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetJsonAsync``1(Flurl.Url,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the JSON response body deserialized to an object of type T.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetStringAsync(Flurl.Url,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the response body as a string.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetStreamAsync(Flurl.Url,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the response body as a Stream.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetBytesAsync(Flurl.Url,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the response body as a byte array.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostAsync(Flurl.Url,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous POST request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostJsonAsync(Flurl.Url,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous POST request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostStringAsync(Flurl.Url,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous POST request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostUrlEncodedAsync(Flurl.Url,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous POST request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="body">An object representing the request body, which will be serialized to a URL-encoded string.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.HeadAsync(Flurl.Url,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous HEAD request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PutAsync(Flurl.Url,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PUT request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PutJsonAsync(Flurl.Url,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PUT request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PutStringAsync(Flurl.Url,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PUT request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.DeleteAsync(Flurl.Url,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous DELETE request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PatchAsync(Flurl.Url,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PATCH request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PatchJsonAsync(Flurl.Url,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PATCH request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PatchStringAsync(Flurl.Url,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PATCH request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OptionsAsync(Flurl.Url,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous OPTIONS request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.DownloadFileAsync(Flurl.Url,System.String,System.String,System.Int32,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a new FlurlRequest and asynchronously downloads a file.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="localFolderPath">Path of local folder where file is to be downloaded.</param>
            <param name="localFileName">Name of local file. If not specified, the source filename (last segment of the URL) is used.</param>
            <param name="bufferSize">Buffer size in bytes. Default is 4096.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the local path of the downloaded file.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostMultipartAsync(Flurl.Url,System.Action{Flurl.Http.Content.CapturedMultipartContent},System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous multipart/form-data POST request.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="buildContent">A delegate for building the content parts.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithHeader(Flurl.Url,System.String,System.Object)">
            <summary>
            Creates a new FlurlRequest and sets a request header.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="name">The header name.</param>
            <param name="value">The header value.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithHeaders(Flurl.Url,System.Object,System.Boolean)">
            <summary>
            Creates a new FlurlRequest and sets request headers based on property names/values of the provided object, or keys/values if object is a dictionary, to be sent.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="headers">Names/values of HTTP headers to set. Typically an anonymous object or IDictionary.</param>
            <param name="replaceUnderscoreWithHyphen">If true, underscores in property names will be replaced by hyphens. Default is true.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithBasicAuth(Flurl.Url,System.String,System.String)">
            <summary>
            Creates a new FlurlRequest and sets the Authorization header according to Basic Authentication protocol.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="username">Username of authenticating user.</param>
            <param name="password">Password of authenticating user.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithOAuthBearerToken(Flurl.Url,System.String)">
            <summary>
            Creates a new FlurlRequest and sets the Authorization header with a bearer token according to OAuth 2.0 specification.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="token">The acquired oAuth bearer token.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithCookie(Flurl.Url,System.String,System.Object)">
            <summary>
            Creates a new FlurlRequest and adds a name-value pair to its Cookie header. To automatically maintain a cookie "session", consider using a CookieJar or CookieSession instead.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="name">The cookie name.</param>
            <param name="value">The cookie value.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithCookies(Flurl.Url,System.Object)">
            <summary>
            Creates a new FlurlRequest and adds name-value pairs to its Cookie header based on property names/values of the provided object, or keys/values if object is a dictionary. To automatically maintain a cookie "session", consider using a CookieJar or CookieSession instead.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="values">Names/values of HTTP cookies to set. Typically an anonymous object or IDictionary.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithCookies(Flurl.Url,Flurl.Http.CookieJar)">
            <summary>
            Creates a new FlurlRequest and sets the CookieJar associated with this request, which will be updated with any Set-Cookie headers present in the response and is suitable for reuse in subsequent requests.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="cookieJar">The CookieJar.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithCookies(Flurl.Url,Flurl.Http.CookieJar@)">
            <summary>
            Creates a new FlurlRequest and associates it with a new CookieJar, which will be updated with any Set-Cookie headers present in the response and is suitable for reuse in subsequent requests.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="cookieJar">The created CookieJar, which can be reused in subsequent requests.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithSettings(Flurl.Url,System.Action{Flurl.Http.Configuration.FlurlHttpSettings})">
            <summary>
            Creates a new FlurlRequest and allows changing its Settings inline.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="action">A delegate defining the Settings changes.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithTimeout(Flurl.Url,System.TimeSpan)">
            <summary>
            Creates a new FlurlRequest and sets the request timeout.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="timespan">Time to wait before the request times out.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithTimeout(Flurl.Url,System.Int32)">
            <summary>
            Creates a new FlurlRequest and sets the request timeout.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="seconds">Seconds to wait before the request times out.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AllowHttpStatus(Flurl.Url,System.String)">
            <summary>
            Creates a new FlurlRequest and adds a pattern representing an HTTP status code or range of codes which (in addition to 2xx) will NOT result in a FlurlHttpException being thrown.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="pattern">Examples: "3xx", "100,300,600", "100-299,6xx"</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AllowHttpStatus(Flurl.Url,System.Int32[])">
            <summary>
            Creates a new FlurlRequest and adds one or more response status codes which (in addition to 2xx) will NOT result in a FlurlHttpException being thrown.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="statusCodes">One or more response status codes that, when received, will not cause an exception to be thrown.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AllowAnyHttpStatus(Flurl.Url)">
            <summary>
            Creates a new FlurlRequest and configures it to allow any returned HTTP status without throwing a FlurlHttpException.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithAutoRedirect(Flurl.Url,System.Boolean)">
            <summary>
            Creates a new FlurlRequest and configures whether redirects are automatically followed.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="enabled">true if Flurl should automatically send a new request to the redirect URL, false if it should not.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.BeforeCall(Flurl.Url,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Creates a new FlurlRequest and adds a new BeforeCall event handler.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="action">Action to perform when the BeforeCall event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.BeforeCall(Flurl.Url,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Creates a new FlurlRequest and adds a new asynchronous BeforeCall event handler.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="action">Async action to perform when the BeforeCall event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AfterCall(Flurl.Url,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Creates a new FlurlRequest and adds a new AfterCall event handler.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="action">Action to perform when the AfterCall event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AfterCall(Flurl.Url,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Creates a new FlurlRequest and adds a new asynchronous AfterCall event handler.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="action">Async action to perform when the AfterCall event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OnError(Flurl.Url,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Creates a new FlurlRequest and adds a new OnError event handler.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="action">Action to perform when the OnError event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OnError(Flurl.Url,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Creates a new FlurlRequest and adds a new asynchronous OnError event handler.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="action">Async action to perform when the OnError event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OnRedirect(Flurl.Url,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Creates a new FlurlRequest and adds a new OnRedirect event handler.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="action">Action to perform when the OnRedirect event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OnRedirect(Flurl.Url,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Creates a new FlurlRequest and adds a new asynchronous OnRedirect event handler.
            </summary>
            <param name="url">This Flurl.Url.</param>
            <param name="action">Async action to perform when the OnRedirect event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendAsync(System.String,System.Net.Http.HttpMethod,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous request.
            </summary>
            <param name="url">This URL.</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendJsonAsync(System.String,System.Net.Http.HttpMethod,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous request.
            </summary>
            <param name="url">This URL.</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendStringAsync(System.String,System.Net.Http.HttpMethod,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous request.
            </summary>
            <param name="url">This URL.</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendUrlEncodedAsync(System.String,System.Net.Http.HttpMethod,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous request.
            </summary>
            <param name="url">This URL.</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="body">An object representing the request body, which will be serialized to a URL-encoded string.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="url">This URL.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetJsonAsync``1(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="url">This URL.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the JSON response body deserialized to an object of type T.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetStringAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="url">This URL.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the response body as a string.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetStreamAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="url">This URL.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the response body as a Stream.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetBytesAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="url">This URL.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the response body as a byte array.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostAsync(System.String,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous POST request.
            </summary>
            <param name="url">This URL.</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostJsonAsync(System.String,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous POST request.
            </summary>
            <param name="url">This URL.</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostStringAsync(System.String,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous POST request.
            </summary>
            <param name="url">This URL.</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostUrlEncodedAsync(System.String,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous POST request.
            </summary>
            <param name="url">This URL.</param>
            <param name="body">An object representing the request body, which will be serialized to a URL-encoded string.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.HeadAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous HEAD request.
            </summary>
            <param name="url">This URL.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PutAsync(System.String,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PUT request.
            </summary>
            <param name="url">This URL.</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PutJsonAsync(System.String,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PUT request.
            </summary>
            <param name="url">This URL.</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PutStringAsync(System.String,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PUT request.
            </summary>
            <param name="url">This URL.</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.DeleteAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous DELETE request.
            </summary>
            <param name="url">This URL.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PatchAsync(System.String,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PATCH request.
            </summary>
            <param name="url">This URL.</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PatchJsonAsync(System.String,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PATCH request.
            </summary>
            <param name="url">This URL.</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PatchStringAsync(System.String,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PATCH request.
            </summary>
            <param name="url">This URL.</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OptionsAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous OPTIONS request.
            </summary>
            <param name="url">This URL.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.DownloadFileAsync(System.String,System.String,System.String,System.Int32,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a new FlurlRequest and asynchronously downloads a file.
            </summary>
            <param name="url">This URL.</param>
            <param name="localFolderPath">Path of local folder where file is to be downloaded.</param>
            <param name="localFileName">Name of local file. If not specified, the source filename (last segment of the URL) is used.</param>
            <param name="bufferSize">Buffer size in bytes. Default is 4096.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the local path of the downloaded file.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostMultipartAsync(System.String,System.Action{Flurl.Http.Content.CapturedMultipartContent},System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous multipart/form-data POST request.
            </summary>
            <param name="url">This URL.</param>
            <param name="buildContent">A delegate for building the content parts.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithHeader(System.String,System.String,System.Object)">
            <summary>
            Creates a new FlurlRequest and sets a request header.
            </summary>
            <param name="url">This URL.</param>
            <param name="name">The header name.</param>
            <param name="value">The header value.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithHeaders(System.String,System.Object,System.Boolean)">
            <summary>
            Creates a new FlurlRequest and sets request headers based on property names/values of the provided object, or keys/values if object is a dictionary, to be sent.
            </summary>
            <param name="url">This URL.</param>
            <param name="headers">Names/values of HTTP headers to set. Typically an anonymous object or IDictionary.</param>
            <param name="replaceUnderscoreWithHyphen">If true, underscores in property names will be replaced by hyphens. Default is true.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithBasicAuth(System.String,System.String,System.String)">
            <summary>
            Creates a new FlurlRequest and sets the Authorization header according to Basic Authentication protocol.
            </summary>
            <param name="url">This URL.</param>
            <param name="username">Username of authenticating user.</param>
            <param name="password">Password of authenticating user.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithOAuthBearerToken(System.String,System.String)">
            <summary>
            Creates a new FlurlRequest and sets the Authorization header with a bearer token according to OAuth 2.0 specification.
            </summary>
            <param name="url">This URL.</param>
            <param name="token">The acquired oAuth bearer token.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithCookie(System.String,System.String,System.Object)">
            <summary>
            Creates a new FlurlRequest and adds a name-value pair to its Cookie header. To automatically maintain a cookie "session", consider using a CookieJar or CookieSession instead.
            </summary>
            <param name="url">This URL.</param>
            <param name="name">The cookie name.</param>
            <param name="value">The cookie value.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithCookies(System.String,System.Object)">
            <summary>
            Creates a new FlurlRequest and adds name-value pairs to its Cookie header based on property names/values of the provided object, or keys/values if object is a dictionary. To automatically maintain a cookie "session", consider using a CookieJar or CookieSession instead.
            </summary>
            <param name="url">This URL.</param>
            <param name="values">Names/values of HTTP cookies to set. Typically an anonymous object or IDictionary.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithCookies(System.String,Flurl.Http.CookieJar)">
            <summary>
            Creates a new FlurlRequest and sets the CookieJar associated with this request, which will be updated with any Set-Cookie headers present in the response and is suitable for reuse in subsequent requests.
            </summary>
            <param name="url">This URL.</param>
            <param name="cookieJar">The CookieJar.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithCookies(System.String,Flurl.Http.CookieJar@)">
            <summary>
            Creates a new FlurlRequest and associates it with a new CookieJar, which will be updated with any Set-Cookie headers present in the response and is suitable for reuse in subsequent requests.
            </summary>
            <param name="url">This URL.</param>
            <param name="cookieJar">The created CookieJar, which can be reused in subsequent requests.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithSettings(System.String,System.Action{Flurl.Http.Configuration.FlurlHttpSettings})">
            <summary>
            Creates a new FlurlRequest and allows changing its Settings inline.
            </summary>
            <param name="url">This URL.</param>
            <param name="action">A delegate defining the Settings changes.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithTimeout(System.String,System.TimeSpan)">
            <summary>
            Creates a new FlurlRequest and sets the request timeout.
            </summary>
            <param name="url">This URL.</param>
            <param name="timespan">Time to wait before the request times out.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithTimeout(System.String,System.Int32)">
            <summary>
            Creates a new FlurlRequest and sets the request timeout.
            </summary>
            <param name="url">This URL.</param>
            <param name="seconds">Seconds to wait before the request times out.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AllowHttpStatus(System.String,System.String)">
            <summary>
            Creates a new FlurlRequest and adds a pattern representing an HTTP status code or range of codes which (in addition to 2xx) will NOT result in a FlurlHttpException being thrown.
            </summary>
            <param name="url">This URL.</param>
            <param name="pattern">Examples: "3xx", "100,300,600", "100-299,6xx"</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AllowHttpStatus(System.String,System.Int32[])">
            <summary>
            Creates a new FlurlRequest and adds one or more response status codes which (in addition to 2xx) will NOT result in a FlurlHttpException being thrown.
            </summary>
            <param name="url">This URL.</param>
            <param name="statusCodes">One or more response status codes that, when received, will not cause an exception to be thrown.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AllowAnyHttpStatus(System.String)">
            <summary>
            Creates a new FlurlRequest and configures it to allow any returned HTTP status without throwing a FlurlHttpException.
            </summary>
            <param name="url">This URL.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithAutoRedirect(System.String,System.Boolean)">
            <summary>
            Creates a new FlurlRequest and configures whether redirects are automatically followed.
            </summary>
            <param name="url">This URL.</param>
            <param name="enabled">true if Flurl should automatically send a new request to the redirect URL, false if it should not.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.BeforeCall(System.String,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Creates a new FlurlRequest and adds a new BeforeCall event handler.
            </summary>
            <param name="url">This URL.</param>
            <param name="action">Action to perform when the BeforeCall event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.BeforeCall(System.String,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Creates a new FlurlRequest and adds a new asynchronous BeforeCall event handler.
            </summary>
            <param name="url">This URL.</param>
            <param name="action">Async action to perform when the BeforeCall event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AfterCall(System.String,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Creates a new FlurlRequest and adds a new AfterCall event handler.
            </summary>
            <param name="url">This URL.</param>
            <param name="action">Action to perform when the AfterCall event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AfterCall(System.String,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Creates a new FlurlRequest and adds a new asynchronous AfterCall event handler.
            </summary>
            <param name="url">This URL.</param>
            <param name="action">Async action to perform when the AfterCall event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OnError(System.String,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Creates a new FlurlRequest and adds a new OnError event handler.
            </summary>
            <param name="url">This URL.</param>
            <param name="action">Action to perform when the OnError event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OnError(System.String,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Creates a new FlurlRequest and adds a new asynchronous OnError event handler.
            </summary>
            <param name="url">This URL.</param>
            <param name="action">Async action to perform when the OnError event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OnRedirect(System.String,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Creates a new FlurlRequest and adds a new OnRedirect event handler.
            </summary>
            <param name="url">This URL.</param>
            <param name="action">Action to perform when the OnRedirect event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OnRedirect(System.String,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Creates a new FlurlRequest and adds a new asynchronous OnRedirect event handler.
            </summary>
            <param name="url">This URL.</param>
            <param name="action">Async action to perform when the OnRedirect event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendAsync(System.Uri,System.Net.Http.HttpMethod,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendJsonAsync(System.Uri,System.Net.Http.HttpMethod,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendStringAsync(System.Uri,System.Net.Http.HttpMethod,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.SendUrlEncodedAsync(System.Uri,System.Net.Http.HttpMethod,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="verb">The HTTP verb used to make the request.</param>
            <param name="body">An object representing the request body, which will be serialized to a URL-encoded string.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetJsonAsync``1(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the JSON response body deserialized to an object of type T.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetStringAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the response body as a string.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetStreamAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the response body as a Stream.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.GetBytesAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous GET request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the response body as a byte array.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostAsync(System.Uri,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous POST request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostJsonAsync(System.Uri,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous POST request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostStringAsync(System.Uri,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous POST request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostUrlEncodedAsync(System.Uri,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous POST request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="body">An object representing the request body, which will be serialized to a URL-encoded string.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.HeadAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous HEAD request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PutAsync(System.Uri,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PUT request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PutJsonAsync(System.Uri,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PUT request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PutStringAsync(System.Uri,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PUT request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.DeleteAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous DELETE request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PatchAsync(System.Uri,System.Net.Http.HttpContent,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PATCH request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="content">The request body content.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PatchJsonAsync(System.Uri,System.Object,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PATCH request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="body">An object representing the request body, which will be serialized to JSON.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PatchStringAsync(System.Uri,System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous PATCH request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="body">The request body.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OptionsAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous OPTIONS request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.DownloadFileAsync(System.Uri,System.String,System.String,System.Int32,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a new FlurlRequest and asynchronously downloads a file.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="localFolderPath">Path of local folder where file is to be downloaded.</param>
            <param name="localFileName">Name of local file. If not specified, the source filename (last segment of the URL) is used.</param>
            <param name="bufferSize">Buffer size in bytes. Default is 4096.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the local path of the downloaded file.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.PostMultipartAsync(System.Uri,System.Action{Flurl.Http.Content.CapturedMultipartContent},System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates a FlurlRequest and sends an asynchronous multipart/form-data POST request.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="buildContent">A delegate for building the content parts.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithHeader(System.Uri,System.String,System.Object)">
            <summary>
            Creates a new FlurlRequest and sets a request header.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="name">The header name.</param>
            <param name="value">The header value.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithHeaders(System.Uri,System.Object,System.Boolean)">
            <summary>
            Creates a new FlurlRequest and sets request headers based on property names/values of the provided object, or keys/values if object is a dictionary, to be sent.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="headers">Names/values of HTTP headers to set. Typically an anonymous object or IDictionary.</param>
            <param name="replaceUnderscoreWithHyphen">If true, underscores in property names will be replaced by hyphens. Default is true.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithBasicAuth(System.Uri,System.String,System.String)">
            <summary>
            Creates a new FlurlRequest and sets the Authorization header according to Basic Authentication protocol.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="username">Username of authenticating user.</param>
            <param name="password">Password of authenticating user.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithOAuthBearerToken(System.Uri,System.String)">
            <summary>
            Creates a new FlurlRequest and sets the Authorization header with a bearer token according to OAuth 2.0 specification.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="token">The acquired oAuth bearer token.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithCookie(System.Uri,System.String,System.Object)">
            <summary>
            Creates a new FlurlRequest and adds a name-value pair to its Cookie header. To automatically maintain a cookie "session", consider using a CookieJar or CookieSession instead.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="name">The cookie name.</param>
            <param name="value">The cookie value.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithCookies(System.Uri,System.Object)">
            <summary>
            Creates a new FlurlRequest and adds name-value pairs to its Cookie header based on property names/values of the provided object, or keys/values if object is a dictionary. To automatically maintain a cookie "session", consider using a CookieJar or CookieSession instead.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="values">Names/values of HTTP cookies to set. Typically an anonymous object or IDictionary.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithCookies(System.Uri,Flurl.Http.CookieJar)">
            <summary>
            Creates a new FlurlRequest and sets the CookieJar associated with this request, which will be updated with any Set-Cookie headers present in the response and is suitable for reuse in subsequent requests.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="cookieJar">The CookieJar.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithCookies(System.Uri,Flurl.Http.CookieJar@)">
            <summary>
            Creates a new FlurlRequest and associates it with a new CookieJar, which will be updated with any Set-Cookie headers present in the response and is suitable for reuse in subsequent requests.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="cookieJar">The created CookieJar, which can be reused in subsequent requests.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithSettings(System.Uri,System.Action{Flurl.Http.Configuration.FlurlHttpSettings})">
            <summary>
            Creates a new FlurlRequest and allows changing its Settings inline.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="action">A delegate defining the Settings changes.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithTimeout(System.Uri,System.TimeSpan)">
            <summary>
            Creates a new FlurlRequest and sets the request timeout.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="timespan">Time to wait before the request times out.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithTimeout(System.Uri,System.Int32)">
            <summary>
            Creates a new FlurlRequest and sets the request timeout.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="seconds">Seconds to wait before the request times out.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AllowHttpStatus(System.Uri,System.String)">
            <summary>
            Creates a new FlurlRequest and adds a pattern representing an HTTP status code or range of codes which (in addition to 2xx) will NOT result in a FlurlHttpException being thrown.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="pattern">Examples: "3xx", "100,300,600", "100-299,6xx"</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AllowHttpStatus(System.Uri,System.Int32[])">
            <summary>
            Creates a new FlurlRequest and adds one or more response status codes which (in addition to 2xx) will NOT result in a FlurlHttpException being thrown.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="statusCodes">One or more response status codes that, when received, will not cause an exception to be thrown.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AllowAnyHttpStatus(System.Uri)">
            <summary>
            Creates a new FlurlRequest and configures it to allow any returned HTTP status without throwing a FlurlHttpException.
            </summary>
            <param name="uri">This System.Uri.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.WithAutoRedirect(System.Uri,System.Boolean)">
            <summary>
            Creates a new FlurlRequest and configures whether redirects are automatically followed.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="enabled">true if Flurl should automatically send a new request to the redirect URL, false if it should not.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.BeforeCall(System.Uri,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Creates a new FlurlRequest and adds a new BeforeCall event handler.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="action">Action to perform when the BeforeCall event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.BeforeCall(System.Uri,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Creates a new FlurlRequest and adds a new asynchronous BeforeCall event handler.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="action">Async action to perform when the BeforeCall event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AfterCall(System.Uri,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Creates a new FlurlRequest and adds a new AfterCall event handler.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="action">Action to perform when the AfterCall event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.AfterCall(System.Uri,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Creates a new FlurlRequest and adds a new asynchronous AfterCall event handler.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="action">Async action to perform when the AfterCall event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OnError(System.Uri,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Creates a new FlurlRequest and adds a new OnError event handler.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="action">Action to perform when the OnError event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OnError(System.Uri,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Creates a new FlurlRequest and adds a new asynchronous OnError event handler.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="action">Async action to perform when the OnError event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OnRedirect(System.Uri,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Creates a new FlurlRequest and adds a new OnRedirect event handler.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="action">Action to perform when the OnRedirect event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="M:Flurl.Http.GeneratedExtensions.OnRedirect(System.Uri,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Creates a new FlurlRequest and adds a new asynchronous OnRedirect event handler.
            </summary>
            <param name="uri">This System.Uri.</param>
            <param name="action">Async action to perform when the OnRedirect event is raised.</param>
            <returns>A new IFlurlRequest.</returns>
        </member>
        <member name="T:Flurl.Http.HttpMessageExtensions">
            <summary>
            Extension methods off HttpRequestMessage and HttpResponseMessage.
            </summary>
        </member>
        <member name="M:Flurl.Http.HttpMessageExtensions.SetHeader(System.Net.Http.HttpRequestMessage,System.String,System.Object,System.Boolean)">
            <summary>
            Set a header on this HttpRequestMessage (default), or its Content property if it's a known content-level header.
            No validation. Overwrites any existing value(s) for the header. 
            </summary>
            <param name="request">The HttpRequestMessage.</param>
            <param name="name">The header name.</param>
            <param name="value">The header value.</param>
            <param name="createContentIfNecessary">If it's a content-level header and there is no content, this determines whether to create an empty HttpContent or just ignore the header.</param>
        </member>
        <member name="M:Flurl.Http.HttpMessageExtensions.SetHeader(System.Net.Http.HttpResponseMessage,System.String,System.Object,System.Boolean)">
            <summary>
            Set a header on this HttpResponseMessage (default), or its Content property if it's a known content-level header.
            No validation. Overwrites any existing value(s) for the header. 
            </summary>
            <param name="response">The HttpResponseMessage.</param>
            <param name="name">The header name.</param>
            <param name="value">The header value.</param>
            <param name="createContentIfNecessary">If it's a content-level header and there is no content, this determines whether to create an empty HttpContent or just ignore the header.</param>
        </member>
        <member name="T:Flurl.Http.HttpMessageExtensions.HttpMessage">
            <summary>
            Wrapper class for treating HttpRequestMessage and HttpResponseMessage uniformly. (Unfortunately they don't have a common interface.)
            </summary>
        </member>
        <member name="T:Flurl.Http.HttpStatusRangeParser">
            <summary>
            The status range parser class.
            </summary>
        </member>
        <member name="M:Flurl.Http.HttpStatusRangeParser.IsMatch(System.String,System.Net.HttpStatusCode)">
            <summary>
            Determines whether the specified pattern is match.
            </summary>
            <param name="pattern">The pattern.</param>
            <param name="value">The value.</param>
            <exception cref="T:System.ArgumentException">pattern is invalid.</exception>
        </member>
        <member name="M:Flurl.Http.HttpStatusRangeParser.IsMatch(System.String,System.Int32)">
            <summary>
            Determines whether the specified pattern is match.
            </summary>
            <param name="pattern">The pattern.</param>
            <param name="value">The value.</param>
            <exception cref="T:System.ArgumentException"><paramref name="pattern"/> is invalid.</exception>
        </member>
        <member name="T:Flurl.Http.IEventHandlerContainer">
            <summary>
            A common interface for Flurl.Http objects that contain event handlers.
            </summary>
        </member>
        <member name="P:Flurl.Http.IEventHandlerContainer.EventHandlers">
            <summary>
            A collection of Flurl event handlers.
            </summary>
        </member>
        <member name="T:Flurl.Http.EventHandlerContainerExtensions">
            <summary>
            Fluent extension methods for tweaking FlurlHttpSettings
            </summary>
        </member>
        <member name="M:Flurl.Http.EventHandlerContainerExtensions.BeforeCall``1(``0,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Adds an event handler that is invoked when a BeforeCall event is fired.
            </summary>
            <returns>This event handler container.</returns>
        </member>
        <member name="M:Flurl.Http.EventHandlerContainerExtensions.BeforeCall``1(``0,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Adds an asynchronous event handler that is invoked when a BeforeCall event is fired.
            </summary>
            <returns>This event handler container.</returns>
        </member>
        <member name="M:Flurl.Http.EventHandlerContainerExtensions.AfterCall``1(``0,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Adds an event handler that is invoked when an AfterCall event is fired.
            </summary>
            <returns>This event handler container.</returns>
        </member>
        <member name="M:Flurl.Http.EventHandlerContainerExtensions.AfterCall``1(``0,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Adds an asynchronous event handler that is invoked when an AfterCall event is fired.
            </summary>
            <returns>This event handler container.</returns>
        </member>
        <member name="M:Flurl.Http.EventHandlerContainerExtensions.OnError``1(``0,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Adds an event handler that is invoked when an OnError event is fired.
            </summary>
            <returns>This event handler container.</returns>
        </member>
        <member name="M:Flurl.Http.EventHandlerContainerExtensions.OnError``1(``0,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Adds an asynchronous event handler that is invoked when an OnError event is fired.
            </summary>
            <returns>This event handler container.</returns>
        </member>
        <member name="M:Flurl.Http.EventHandlerContainerExtensions.OnRedirect``1(``0,System.Action{Flurl.Http.FlurlCall})">
            <summary>
            Adds an event handler that is invoked when an OnRedirect event is fired.
            </summary>
            <returns>This event handler container.</returns>
        </member>
        <member name="M:Flurl.Http.EventHandlerContainerExtensions.OnRedirect``1(``0,System.Func{Flurl.Http.FlurlCall,System.Threading.Tasks.Task})">
            <summary>
            Adds an asynchronous event handler that is invoked when an OnRedirect event is fired.
            </summary>
            <returns>This event handler container.</returns>
        </member>
        <member name="T:Flurl.Http.IHeadersContainer">
            <summary>
            A common interface for Flurl.Http objects that contain a collection of request headers.
            </summary>
        </member>
        <member name="P:Flurl.Http.IHeadersContainer.Headers">
            <summary>
            A collection of request headers.
            </summary>
        </member>
        <member name="T:Flurl.Http.HeaderExtensions">
            <summary>
            Fluent extension methods for working with HTTP request headers.
            </summary>
        </member>
        <member name="M:Flurl.Http.HeaderExtensions.WithHeader``1(``0,System.String,System.Object)">
            <summary>
            Sets an HTTP header associated with this request or client.
            </summary>
            <param name="obj">Object containing request headers.</param>
            <param name="name">HTTP header name.</param>
            <param name="value">HTTP header value.</param>
            <returns>This headers container.</returns>
        </member>
        <member name="M:Flurl.Http.HeaderExtensions.WithHeaders``1(``0,System.Object,System.Boolean)">
            <summary>
            Sets HTTP headers based on property names/values of the provided object, or keys/values if object is a dictionary, associated with this request or client.
            </summary>
            <param name="obj">Object containing request headers.</param>
            <param name="headers">Names/values of HTTP headers to set. Typically an anonymous object or IDictionary.</param>
            <param name="replaceUnderscoreWithHyphen">If true, underscores in property names will be replaced by hyphens. Default is true.</param>
            <returns>This headers container.</returns>
        </member>
        <member name="M:Flurl.Http.HeaderExtensions.WithBasicAuth``1(``0,System.String,System.String)">
            <summary>
            Sets HTTP authorization header according to Basic Authentication protocol associated with this request or client.
            </summary>
            <param name="obj">Object containing request headers.</param>
            <param name="username">Username of authenticating user.</param>
            <param name="password">Password of authenticating user.</param>
            <returns>This headers container.</returns>
        </member>
        <member name="M:Flurl.Http.HeaderExtensions.WithOAuthBearerToken``1(``0,System.String)">
            <summary>
            Sets HTTP authorization header with acquired bearer token according to OAuth 2.0 specification associated with this request or client.
            </summary>
            <param name="obj">Object containing request headers.</param>
            <param name="token">The acquired bearer token to pass.</param>
            <returns>This headers container.</returns>
        </member>
        <member name="T:Flurl.Http.ISettingsContainer">
            <summary>
            A common interface for Flurl.Http objects that are configurable via a Settings property.
            </summary>
        </member>
        <member name="P:Flurl.Http.ISettingsContainer.Settings">
            <summary>
            A collection request settings.
            </summary>
        </member>
        <member name="T:Flurl.Http.SettingsExtensions">
            <summary>
            Fluent extension methods for tweaking FlurlHttpSettings
            </summary>
        </member>
        <member name="M:Flurl.Http.SettingsExtensions.WithSettings``1(``0,System.Action{Flurl.Http.Configuration.FlurlHttpSettings})">
            <summary>
            Change FlurlHttpSettings for this request, client, or test context.
            </summary>
            <param name="obj">Object containing settings.</param>
            <param name="action">Action defining the settings changes.</param>
            <returns>This settings container.</returns>
        </member>
        <member name="M:Flurl.Http.SettingsExtensions.WithTimeout``1(``0,System.TimeSpan)">
            <summary>
            Sets the timeout for this request, client, or test context.
            </summary>
            <param name="obj">Object containing settings.</param>
            <param name="timespan">Time to wait before the request times out.</param>
            <returns>This settings container.</returns>
        </member>
        <member name="M:Flurl.Http.SettingsExtensions.WithTimeout``1(``0,System.Int32)">
            <summary>
            Sets the timeout for this request, client, or test context.
            </summary>
            <param name="obj">Object containing settings.</param>
            <param name="seconds">Seconds to wait before the request times out.</param>
            <returns>This settings container.</returns>
        </member>
        <member name="M:Flurl.Http.SettingsExtensions.AllowHttpStatus``1(``0,System.String)">
            <summary>
            Adds a pattern representing an HTTP status code or range of codes which (in addition to 2xx) will NOT result in a FlurlHttpException being thrown.
            </summary>
            <param name="obj">Object containing settings.</param>
            <param name="pattern">Examples: "3xx", "100,300,600", "100-299,6xx"</param>
            <returns>This settings container.</returns>
        </member>
        <member name="M:Flurl.Http.SettingsExtensions.AllowHttpStatus``1(``0,System.Int32[])">
            <summary>
            Adds one or more response status codes which (in addition to 2xx) will NOT result in a FlurlHttpException being thrown.
            </summary>
            <param name="obj">Object containing settings.</param>
            <param name="statusCodes">One or more response status codes that, when received, will not cause an exception to be thrown.</param>
            <returns>This settings container.</returns>
        </member>
        <member name="M:Flurl.Http.SettingsExtensions.AllowAnyHttpStatus``1(``0)">
            <summary>
            Prevents a FlurlHttpException from being thrown on any completed response, regardless of the HTTP status code.
            </summary>
            <param name="obj">Object containing settings.</param>
            <returns>This settings container.</returns>
        </member>
        <member name="M:Flurl.Http.SettingsExtensions.WithAutoRedirect``1(``0,System.Boolean)">
            <summary>
            Configures whether redirects are automatically followed.
            </summary>
            <param name="obj">Object containing settings.</param>
            <param name="enabled">true if Flurl should automatically send a new request to the redirect URL, false if it should not.</param>
            <returns>This settings container.</returns>
        </member>
        <member name="T:Flurl.Http.MultipartExtensions">
            <summary>
            Fluent extension methods for sending multipart/form-data requests.
            </summary>
        </member>
        <member name="M:Flurl.Http.MultipartExtensions.PostMultipartAsync(Flurl.Http.IFlurlRequest,System.Action{Flurl.Http.Content.CapturedMultipartContent},System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Sends an asynchronous multipart/form-data POST request.
            </summary>
            <param name="buildContent">A delegate for building the content parts.</param>
            <param name="request">The IFlurlRequest.</param>
            <param name="completionOption">The HttpCompletionOption used in the request. Optional.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A Task whose result is the received IFlurlResponse.</returns>
        </member>
        <member name="T:Flurl.Http.ResponseExtensions">
            <summary>
            ReceiveXXX extension methods off Task&lt;IFlurlResponse&gt; that allow chaining off methods like SendAsync
            without the need for nested awaits.
            </summary>
        </member>
        <member name="M:Flurl.Http.ResponseExtensions.ReceiveJson``1(System.Threading.Tasks.Task{Flurl.Http.IFlurlResponse})">
            <summary>
            Deserializes JSON-formatted HTTP response body to object of type T. Intended to chain off an async HTTP.
            </summary>
            <typeparam name="T">A type whose structure matches the expected JSON response.</typeparam>
            <returns>A Task whose result is an object containing data in the response body.</returns>
            <example>x = await url.PostAsync(data).ReceiveJson&lt;T&gt;()</example>
            <exception cref="T:Flurl.Http.FlurlHttpException">Condition.</exception>
        </member>
        <member name="M:Flurl.Http.ResponseExtensions.ReceiveString(System.Threading.Tasks.Task{Flurl.Http.IFlurlResponse})">
            <summary>
            Returns HTTP response body as a string. Intended to chain off an async call.
            </summary>
            <returns>A Task whose result is the response body as a string.</returns>
            <example>s = await url.PostAsync(data).ReceiveString()</example>
        </member>
        <member name="M:Flurl.Http.ResponseExtensions.ReceiveStream(System.Threading.Tasks.Task{Flurl.Http.IFlurlResponse})">
            <summary>
            Returns HTTP response body as a stream. Intended to chain off an async call.
            </summary>
            <returns>A Task whose result is the response body as a stream.</returns>
            <example>stream = await url.PostAsync(data).ReceiveStream()</example>
        </member>
        <member name="M:Flurl.Http.ResponseExtensions.ReceiveBytes(System.Threading.Tasks.Task{Flurl.Http.IFlurlResponse})">
            <summary>
            Returns HTTP response body as a byte array. Intended to chain off an async call.
            </summary>
            <returns>A Task whose result is the response body as a byte array.</returns>
            <example>bytes = await url.PostAsync(data).ReceiveBytes()</example>
        </member>
        <member name="T:Flurl.Http.Testing.FilteredHttpTestSetup">
            <summary>
            Represents a set of request conditions and fake responses for faking HTTP calls in tests.
            Usually created fluently via HttpTest.ForCallsTo, rather than instantiated directly.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.#ctor(Flurl.Http.Configuration.FlurlHttpSettings,System.String[])">
            <summary>
            Constructs a new instance of FilteredHttpTestSetup.
            </summary>
            <param name="settings">FlurlHttpSettings used in fake calls.</param>
            <param name="urlPatterns">URL(s) or URL pattern(s) that this HttpTestSetup applies to. Can contain * wildcard.</param>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.IsMatch(Flurl.Http.FlurlCall)">
            <summary>
            Returns true if the given FlurlCall matches one of the URL patterns and all other criteria defined for this HttpTestSetup.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.With(System.Func{Flurl.Http.FlurlCall,System.Boolean})">
            <summary>
            Defines a condition for which this HttpTestSetup applies.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.Without(System.Func{Flurl.Http.FlurlCall,System.Boolean})">
            <summary>
            Defines a condition for which this HttpTestSetup does NOT apply.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.WithVerb(System.Net.Http.HttpMethod[])">
            <summary>
            Defines one or more HTTP verbs, any of which a call must match in order for this HttpTestSetup to apply.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.WithVerb(System.String[])">
            <summary>
            Defines one or more HTTP verbs, any of which a call must match in order for this HttpTestSetup to apply.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.WithQueryParam(System.String,System.Object)">
            <summary>
            Defines a query parameter and (optionally) its value that a call must contain in order for this HttpTestSetup to apply.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.WithoutQueryParam(System.String,System.Object)">
            <summary>
            Defines a query parameter and (optionally) its value that a call must NOT contain in order for this HttpTestSetup to apply.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.WithQueryParams(System.String[])">
            <summary>
            Defines query parameter names, ALL of which a call must contain in order for this HttpTestSetup to apply.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.WithoutQueryParams(System.String[])">
            <summary>
            Defines query parameter names, NONE of which a call must contain in order for this HttpTestSetup to apply.
            If no names are provided, call must not contain any query parameters.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.WithQueryParams(System.Object)">
            <summary>
            Defines query parameters, ALL of which a call must contain in order for this HttpTestSetup to apply.
            </summary>
            <param name="values">Object (usually anonymous) or dictionary that is parsed to name/value query parameters to check for. Values may contain * wildcard.</param>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.WithoutQueryParams(System.Object)">
            <summary>
            Defines query parameters, NONE of which a call must contain in order for this HttpTestSetup to apply.
            </summary>
            <param name="values">Object (usually anonymous) or dictionary that is parsed to name/value query parameters to check for. Values may contain * wildcard.</param>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.WithAnyQueryParam(System.String[])">
            <summary>
            Defines query parameter names, ANY of which a call must contain in order for this HttpTestSetup to apply.
            If no names are provided, call must contain at least one query parameter with any name.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.WithHeader(System.String,System.String)">
            <summary>
            Defines a request header and (optionally) its value that a call must contain in order for this HttpTestSetup to apply.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.WithoutHeader(System.String,System.String)">
            <summary>
            Defines a request header and (optionally) its value that a call must NOT contain in order for this HttpTestSetup to apply.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.WithRequestBody(System.String)">
            <summary>
            Defines a request body that must exist in order for this HttpTestSetup to apply.
            The * wildcard can be used.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.FilteredHttpTestSetup.WithRequestJson(System.Object)">
            <summary>
            Defines an object that, when serialized to JSON, must match the request body in order for this HttpTestSetup to apply.
            </summary>
        </member>
        <member name="T:Flurl.Http.Testing.HttpCallAssertion">
            <summary>
            Provides fluent helpers for asserting against fake HTTP calls. Usually created fluently
            via HttpTest.ShouldHaveCalled or HttpTest.ShouldNotHaveCalled, rather than instantiated directly.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.#ctor(Flurl.Http.Testing.HttpTest,System.Boolean)">
            <summary>
            Constructs a new instance of HttpCallAssertion.
            </summary>
            <param name="test">The HttpTest containing calls being asserted.</param>
            <param name="negate">If true, assertions pass when calls matching criteria were NOT made.</param>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.Times(System.Int32)">
            <summary>
            Assert whether calls matching specified criteria were made a specific number of times. (When not specified,
            assertions verify whether any calls matching criteria were made.)
            </summary>
            <param name="expectedCount">Exact number of expected calls</param>
            <exception cref="T:System.ArgumentException"><paramref name="expectedCount"/> must be greater than or equal to 0.</exception>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.With(System.Func{Flurl.Http.FlurlCall,System.Boolean},System.String)">
            <summary>
            Asserts whether calls were made matching the given predicate function.
            </summary>
            <param name="match">Predicate (usually a lambda expression) that tests a FlurlCall and returns a bool.</param>
            <param name="descrip">A description of what is being asserted.</param>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.Without(System.Func{Flurl.Http.FlurlCall,System.Boolean},System.String)">
            <summary>
            Asserts whether calls were made that do NOT match the given predicate function.
            </summary>
            <param name="match">Predicate (usually a lambda expression) that tests a FlurlCall and returns a bool.</param>
            <param name="descrip">A description of what is being asserted.</param>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithUrlPattern(System.String)">
            <summary>
            Asserts whether calls were made matching given URL or URL pattern.
            </summary>
            <param name="urlPattern">Can contain * wildcard.</param>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithVerb(System.Net.Http.HttpMethod[])">
            <summary>
            Asserts whether calls were made with any of the given HTTP verbs.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithVerb(System.String[])">
            <summary>
            Asserts whether calls were made with any of the given HTTP verbs.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithRequestBody(System.String)">
            <summary>
            Asserts whether calls were made containing given request body. body may contain * wildcard.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithRequestJson(System.Object)">
            <summary>
            Asserts whether calls were made containing given JSON-encoded request body. body may contain * wildcard.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithRequestUrlEncoded(System.Object)">
            <summary>
            Asserts whether calls were made containing given URL-encoded request body. body may contain * wildcard.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithQueryParam(System.String,System.Object)">
            <summary>
            Asserts whether calls were made containing the given query parameter name and (optionally) value. value may contain * wildcard.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithoutQueryParam(System.String,System.Object)">
            <summary>
            Asserts whether calls were made NOT containing the given query parameter and (optionally) value. value may contain * wildcard.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithQueryParams(System.String[])">
            <summary>
            Asserts whether calls were made containing ALL the given query parameters (regardless of their values).
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithoutQueryParams(System.String[])">
            <summary>
            Asserts whether calls were made NOT containing any of the given query parameters.
            If no names are provided, asserts no calls were made with any query parameters.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithAnyQueryParam(System.String[])">
            <summary>
            Asserts whether calls were made containing ANY the given query parameters (regardless of their values).
            If no names are provided, asserts that calls were made containing at least one query parameter with any name.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithQueryParams(System.Object)">
            <summary>
            Asserts whether calls were made containing all of the given query parameter values.
            </summary>
            <param name="values">Object (usually anonymous) or dictionary that is parsed to name/value query parameters to check for. Values may contain * wildcard.</param>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithoutQueryParams(System.Object)">
            <summary>
            Asserts whether calls were made NOT containing any of the given query parameter values.
            </summary>
            <param name="values">Object (usually anonymous) or dictionary that is parsed to name/value query parameters to check for. Values may contain * wildcard.</param>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithHeader(System.String,System.Object)">
            <summary>
            Asserts whether calls were made containing the given header name and (optionally) value. value may contain * wildcard.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithoutHeader(System.String,System.Object)">
            <summary>
            Asserts whether calls were made NOT containing the given header and (optionally) value. value may contain * wildcard.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithHeaders(System.String[])">
            <summary>
            Asserts whether calls were made containing ALL the given headers (regardless of their values).
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithoutHeaders(System.String[])">
            <summary>
            Asserts whether calls were made NOT containing any of the given headers.
            If no names are provided, asserts no calls were made with any headers.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithAnyHeader(System.String[])">
            <summary>
            Asserts whether calls were made containing ANY the given headers (regardless of their values).
            If no names are provided, asserts that calls were made containing at least one header with any name.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithHeaders(System.Object)">
            <summary>
            Asserts whether calls were made containing all of the given header values.
            </summary>
            <param name="values">Object (usually anonymous) or dictionary that is parsed to name/value headers to check for. Values may contain * wildcard.</param>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithoutHeaders(System.Object)">
            <summary>
            Asserts whether calls were made NOT containing any of the given header values.
            </summary>
            <param name="values">Object (usually anonymous) or dictionary that is parsed to name/value headers to check for. Values may contain * wildcard.</param>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithCookie(System.String,System.Object)">
            <summary>
            Asserts whether calls were made containing the given cookie name and (optionally) value. value may contain * wildcard.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithoutCookie(System.String,System.Object)">
            <summary>
            Asserts whether calls were made NOT containing the given cookie and (optionally) value. value may contain * wildcard.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithCookies(System.String[])">
            <summary>
            Asserts whether calls were made containing ALL the given cookies (regardless of their values).
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithoutCookies(System.String[])">
            <summary>
            Asserts whether calls were made NOT containing any of the given cookies.
            If no names are provided, asserts no calls were made with any cookies.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithAnyCookie(System.String[])">
            <summary>
            Asserts whether calls were made containing ANY the given cookies (regardless of their values).
            If no names are provided, asserts that calls were made containing at least one cookie with any name.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithCookies(System.Object)">
            <summary>
            Asserts whether calls were made containing all of the given cookie values.
            </summary>
            <param name="values">Object (usually anonymous) or dictionary that is parsed to name/value cookies to check for. Values may contain * wildcard.</param>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithoutCookies(System.Object)">
            <summary>
            Asserts whether calls were made NOT containing any of the given cookie values.
            </summary>
            <param name="values">Object (usually anonymous) or dictionary that is parsed to name/value cookies to check for. Values may contain * wildcard.</param>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithContentType(System.String)">
            <summary>
            Asserts whether calls were made with a request body of the given content (MIME) type.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithOAuthBearerToken(System.String)">
            <summary>
            Asserts whether an Authorization header was set with the given Bearer token, or any Bearer token if excluded.
            Token can contain * wildcard.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpCallAssertion.WithBasicAuth(System.String,System.String)">
            <summary>
            Asserts whether the Authorization header was set with Basic auth and (optionally) the given credentials.
            Username and password can contain * wildcard.
            </summary>
        </member>
        <member name="T:Flurl.Http.Testing.HttpTest">
            <summary>
            An object whose existence puts Flurl.Http into test mode where actual HTTP calls are faked. Provides a response
            queue, call log, and assertion helpers for use in Arrange/Act/Assert style tests.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTest.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.Testing.HttpTest"/> class.
            </summary>
            <exception cref="T:System.Exception">A delegate callback throws an exception.</exception>
        </member>
        <member name="P:Flurl.Http.Testing.HttpTest.Current">
            <summary>
            Gets the current HttpTest from the logical (async) call context
            </summary>
        </member>
        <member name="P:Flurl.Http.Testing.HttpTest.CallLog">
            <summary>
            List of all (fake) HTTP calls made since this HttpTest was created.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTest.ForCallsTo(System.String[])">
            <summary>
            Fluently creates and returns a new request-specific test setup. 
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTest.ShouldHaveCalled(System.String)">
            <summary>
            Asserts whether matching URL was called, throwing HttpCallAssertException if it wasn't.
            </summary>
            <param name="urlPattern">URL that should have been called. Can include * wildcard character.</param>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTest.ShouldNotHaveCalled(System.String)">
            <summary>
            Asserts whether matching URL was NOT called, throwing HttpCallAssertException if it was.
            </summary>
            <param name="urlPattern">URL that should not have been called. Can include * wildcard character.</param>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTest.ShouldHaveMadeACall">
            <summary>
            Asserts whether any HTTP call was made, throwing HttpCallAssertException if none were.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTest.ShouldNotHaveMadeACall">
            <summary>
            Asserts whether no HTTP calls were made, throwing HttpCallAssertException if any were.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTest.Dispose">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
        </member>
        <member name="T:Flurl.Http.Testing.HttpTestException">
            <summary>
            An exception thrown by HttpTest's assertion methods to indicate that the assertion failed.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTestException.#ctor(System.Collections.Generic.IList{System.String},System.Nullable{System.Int32},System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Flurl.Http.Testing.HttpTestException"/> class.
            </summary>
            <param name="conditions">The expected call conditions.</param>
            <param name="expectedCalls">The expected number of calls.</param>
            <param name="actualCalls">The actual number calls.</param>
        </member>
        <member name="T:Flurl.Http.Testing.HttpTestSetup">
            <summary>
            Abstract base class class for HttpTest and FilteredHttpTestSetup. Provides fluent methods for building queue of fake responses.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTestSetup.#ctor(Flurl.Http.Configuration.FlurlHttpSettings)">
            <summary>
            Constructs a new instance of HttpTestSetup.
            </summary>
            <param name="settings">FlurlHttpSettings used in fake calls.</param>
        </member>
        <member name="P:Flurl.Http.Testing.HttpTestSetup.Settings">
            <summary>
            The FlurlHttpSettings used in fake calls.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTestSetup.RespondWith(System.String,System.Int32,System.Object,System.Object,System.Boolean)">
            <summary>
            Adds a fake HTTP response to the response queue.
            </summary>
            <param name="body">The simulated response body string.</param>
            <param name="status">The simulated HTTP status. Default is 200.</param>
            <param name="headers">The simulated response headers (optional).</param>
            <param name="cookies">The simulated response cookies (optional).</param>
            <param name="replaceUnderscoreWithHyphen">If true, underscores in property names of headers will be replaced by hyphens. Default is true.</param>
            <returns>The current HttpTest object (so more responses can be chained).</returns>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTestSetup.RespondWithJson(System.Object,System.Int32,System.Object,System.Object,System.Boolean)">
            <summary>
            Adds a fake HTTP response to the response queue with the given data serialized to JSON as the content body.
            </summary>
            <param name="body">The object to be JSON-serialized and used as the simulated response body.</param>
            <param name="status">The simulated HTTP status. Default is 200.</param>
            <param name="headers">The simulated response headers (optional).</param>
            <param name="cookies">The simulated response cookies (optional).</param>
            <param name="replaceUnderscoreWithHyphen">If true, underscores in property names of headers will be replaced by hyphens. Default is true.</param>
            <returns>The current HttpTest object (so more responses can be chained).</returns>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTestSetup.RespondWith(System.Func{System.Net.Http.HttpContent},System.Int32,System.Object,System.Object,System.Boolean)">
            <summary>
            Adds a fake HTTP response to the response queue.
            </summary>
            <param name="buildContent">A function that builds the simulated response body content. Optional.</param>
            <param name="status">The simulated HTTP status. Optional. Default is 200.</param>
            <param name="headers">The simulated response headers. Optional.</param>
            <param name="cookies">The simulated response cookies. Optional.</param>
            <param name="replaceUnderscoreWithHyphen">If true, underscores in property names of headers will be replaced by hyphens. Default is true.</param>
            <returns>The current HttpTest object (so more responses can be chained).</returns>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTestSetup.SimulateTimeout">
            <summary>
            Adds a simulated timeout response to the response queue.
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTestSetup.SimulateException(System.Exception)">
            <summary>
            Adds the throwing of an exception to the response queue.
            </summary>
            <param name="exception">The exception to throw when the call is simulated.</param>
        </member>
        <member name="M:Flurl.Http.Testing.HttpTestSetup.AllowRealHttp">
            <summary>
            Do NOT fake requests for this setup. Typically called on a filtered setup, i.e. HttpTest.ForCallsTo(urlPattern).AllowRealHttp();
            </summary>
        </member>
        <member name="T:Flurl.Http.Testing.Util">
            <summary>
            Utility methods used by both HttpTestSetup and HttpTestAssertion
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.Util.HasQueryParam(Flurl.Http.FlurlCall,System.String,System.Object)">
            <summary>
            null value means just check for existence by name
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.Util.HasHeader(Flurl.Http.FlurlCall,System.String,System.Object)">
            <summary>
            null value means just check for existence by name
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.Util.HasCookie(Flurl.Http.FlurlCall,System.String,System.Object)">
            <summary>
            null value means just check for existence by name
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.Util.MatchesUrlPattern(System.String,System.String)">
            <summary>
            same as MatchesPattern, but doesn't require trailing * to ignore query string
            </summary>
        </member>
        <member name="M:Flurl.Http.Testing.Util.MatchesPattern(System.String,System.String)">
            <summary>
            match simple patterns with * wildcard
            </summary>
        </member>
        <member name="T:Flurl.Http.UrlBuilderExtensions">
            <summary>
            URL builder extension methods on FlurlRequest
            </summary>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.AppendPathSegment(Flurl.Http.IFlurlRequest,System.Object,System.Boolean)">
            <summary>
            Appends a segment to the URL path, ensuring there is one and only one '/' character as a seperator.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="segment">The segment to append</param>
            <param name="fullyEncode">If true, URL-encodes reserved characters such as '/', '+', and '%'. Otherwise, only encodes strictly illegal characters (including '%' but only when not followed by 2 hex characters).</param>
            <returns>This IFlurlRequest</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="segment"/> is <see langword="null" />.</exception>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.AppendPathSegments(Flurl.Http.IFlurlRequest,System.Object[])">
            <summary>
            Appends multiple segments to the URL path, ensuring there is one and only one '/' character as a seperator.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="segments">The segments to append</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.AppendPathSegments(Flurl.Http.IFlurlRequest,System.Collections.Generic.IEnumerable{System.Object})">
            <summary>
            Appends multiple segments to the URL path, ensuring there is one and only one '/' character as a seperator.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="segments">The segments to append</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.SetQueryParam(Flurl.Http.IFlurlRequest,System.String,System.Object,Flurl.NullValueHandling)">
            <summary>
            Adds a parameter to the URL query, overwriting the value if name exists.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="name">Name of query parameter</param>
            <param name="value">Value of query parameter</param>
            <param name="nullValueHandling">Indicates how to handle null values. Defaults to Remove (any existing)</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.SetQueryParam(Flurl.Http.IFlurlRequest,System.String,System.String,System.Boolean,Flurl.NullValueHandling)">
            <summary>
            Adds a parameter to the URL query, overwriting the value if name exists.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="name">Name of query parameter</param>
            <param name="value">Value of query parameter</param>
            <param name="isEncoded">Set to true to indicate the value is already URL-encoded</param>
            <param name="nullValueHandling">Indicates how to handle null values. Defaults to Remove (any existing)</param>
            <returns>This IFlurlRequest</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is <see langword="null" />.</exception>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.SetQueryParam(Flurl.Http.IFlurlRequest,System.String)">
            <summary>
            Adds a parameter without a value to the URL query, removing any existing value.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="name">Name of query parameter</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.SetQueryParams(Flurl.Http.IFlurlRequest,System.Object,Flurl.NullValueHandling)">
            <summary>
            Parses values (usually an anonymous object or dictionary) into name/value pairs and adds them to the URL query, overwriting any that already exist.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="values">Typically an anonymous object, ie: new { x = 1, y = 2 }</param>
            <param name="nullValueHandling">Indicates how to handle null values. Defaults to Remove (any existing)</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.SetQueryParams(Flurl.Http.IFlurlRequest,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds multiple parameters without values to the URL query.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="names">Names of query parameters.</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.SetQueryParams(Flurl.Http.IFlurlRequest,System.String[])">
            <summary>
            Adds multiple parameters without values to the URL query.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="names">Names of query parameters</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.AppendQueryParam(Flurl.Http.IFlurlRequest,System.String,System.Object,Flurl.NullValueHandling)">
            <summary>
            Adds a parameter to the URL query.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="name">Name of query parameter</param>
            <param name="value">Value of query parameter</param>
            <param name="nullValueHandling">Indicates how to handle null values. Defaults to Remove (any existing)</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.AppendQueryParam(Flurl.Http.IFlurlRequest,System.String,System.String,System.Boolean,Flurl.NullValueHandling)">
            <summary>
            Adds a parameter to the URL query.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="name">Name of query parameter</param>
            <param name="value">Value of query parameter</param>
            <param name="isEncoded">Set to true to indicate the value is already URL-encoded</param>
            <param name="nullValueHandling">Indicates how to handle null values. Defaults to Remove (any existing)</param>
            <returns>This IFlurlRequest</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is <see langword="null" />.</exception>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.AppendQueryParam(Flurl.Http.IFlurlRequest,System.String)">
            <summary>
            Adds a parameter without a value to the URL query.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="name">Name of query parameter</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.AppendQueryParam(Flurl.Http.IFlurlRequest,System.Object,Flurl.NullValueHandling)">
            <summary>
            Parses values (usually an anonymous object or dictionary) into name/value pairs and adds them to the URL query, overwriting any that already exist.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="values">Typically an anonymous object, ie: new { x = 1, y = 2 }</param>
            <param name="nullValueHandling">Indicates how to handle null values. Defaults to Remove (any existing)</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.AppendQueryParam(Flurl.Http.IFlurlRequest,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds multiple parameters without values to the URL query.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="names">Names of query parameters.</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.AppendQueryParam(Flurl.Http.IFlurlRequest,System.String[])">
            <summary>
            Adds multiple parameters without values to the URL query.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="names">Names of query parameters</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.RemoveQueryParam(Flurl.Http.IFlurlRequest,System.String)">
            <summary>
            Removes a name/value pair from the URL query by name.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="name">Query string parameter name to remove</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.RemoveQueryParams(Flurl.Http.IFlurlRequest,System.String[])">
            <summary>
            Removes multiple name/value pairs from the URL query by name.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="names">Query string parameter names to remove</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.RemoveQueryParams(Flurl.Http.IFlurlRequest,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Removes multiple name/value pairs from the URL query by name.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="names">Query string parameter names to remove</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.SetFragment(Flurl.Http.IFlurlRequest,System.String)">
            <summary>
            Set the URL fragment fluently.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <param name="fragment">The part of the URL afer #</param>
            <returns>This IFlurlRequest</returns>
        </member>
        <member name="M:Flurl.Http.UrlBuilderExtensions.RemoveFragment(Flurl.Http.IFlurlRequest)">
            <summary>
            Removes the URL fragment including the #.
            </summary>
            <param name="request">The IFlurlRequest associated with the URL</param>
            <returns>This IFlurlRequest</returns>
        </member>
    </members>
</doc>
