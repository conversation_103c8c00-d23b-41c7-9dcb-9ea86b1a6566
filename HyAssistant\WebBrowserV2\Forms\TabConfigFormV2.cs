using ET;
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant.WebBrowserV2.Forms
{
    /// <summary>
    /// 标签页配置窗体V2版本
    /// 优化重点：异步编程安全、配置验证、日志统一
    /// </summary>
    [System.ComponentModel.DesignerCategory("Form")]
    public partial class TabConfigFormV2 : Form
    {
        #region 字段和属性

        /// <summary>
        /// 获取修改后的配置
        /// </summary>
        public WebBrowserTabConfig Config { get; private set; }

        /// <summary>
        /// 是否已确认
        /// </summary>
        public bool Confirmed { get; private set; } = false;

        /// <summary>
        /// 是否是新建模式
        /// </summary>
        private bool _isCreateMode = false;

        /// <summary>
        /// 配置的原始section ID，用于在重命名时保持原section
        /// </summary>
        private string _originalSectionId = null;

        /// <summary>
        /// 用于记录的对象
        /// </summary>
        private readonly object _logSource;

        /// <summary>
        /// 异步操作取消令牌源
        /// </summary>
        private readonly CancellationTokenSource _cancellationTokenSource;

        /// <summary>
        /// 是否已释放
        /// </summary>
        private bool _disposed = false;

        #endregion 字段和属性

        #region 构造方法

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config">标签页配置</param>
        public TabConfigFormV2(WebBrowserTabConfig config) : this(config, false)
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config">标签页配置</param>
        /// <param name="isCreateMode">是否为新建模式</param>
        public TabConfigFormV2(WebBrowserTabConfig config, bool isCreateMode)
        {
            InitializeComponent();

            _logSource = this;
            _cancellationTokenSource = new CancellationTokenSource();

            // 设置窗体模式
            _isCreateMode = isCreateMode;

            // 克隆配置对象，避免直接修改原始对象
            Config = CloneConfig(config);

            // 记录原始配置的section ID
            if (!_isCreateMode && config != null)
            {
                // 获取配置的SectionId
                _originalSectionId = config.SectionId;

                // 如果SectionId为空，生成一个新的UUID
                if (string.IsNullOrEmpty(_originalSectionId))
                {
                    _originalSectionId = Config.GenerateNewSectionId();
                    ETLogManager.Info(_logSource, $"为已有配置生成新的SectionId: {_originalSectionId}");
                }
            }

            // 如果是新建模式，需要生成新的section ID
            if (_isCreateMode)
            {
                // 生成一个基于GUID的唯一标识
                _originalSectionId = Config.GenerateNewSectionId();
                ETLogManager.Info(_logSource, $"为新建配置生成SectionId: {_originalSectionId}");
            }

            // 如果是新建模式，可能需要调整名称以避免重复
            if (_isCreateMode)
            {
                Text = "新建标签页";
                btnOK.Text = "创建";

                // 修改窗体标题以反映创建模式
                if (!string.IsNullOrEmpty(Config.Name))
                {
                    Text = $"新建标签页 - 基于 {Config.Name}";
                    // 添加"副本"到名称
                    Config.Name = $"{Config.Name} - 副本";
                }
            }
            else
            {
                // 设置窗体标题
                Text = $"标签页配置 - {Config.Name}";
            }

            // 设置窗体图标
            try
            {
                Icon = System.Drawing.Icon.ExtractAssociatedIcon(Application.ExecutablePath);
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(_logSource, $"设置窗体图标失败: {ex.Message}");
            }

            // 加载配置到控件
            LoadConfigToControls();

            ETLogManager.Info(_logSource, $"TabConfigFormV2初始化完成，模式: {(_isCreateMode ? "新建" : "编辑")}");
        }

        /// <summary>
        /// 释放自定义资源
        /// </summary>
        private void DisposeCustomResources()
        {
            if (!_disposed)
            {
                try
                {
                    // 取消所有异步操作
                    _cancellationTokenSource?.Cancel();
                    _cancellationTokenSource?.Dispose();

                    ETLogManager.Info(_logSource, "TabConfigFormV2自定义资源释放完成");
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, $"释放TabConfigFormV2自定义资源失败: {ex.Message}");
                }

                _disposed = true;
            }
        }

        #endregion 构造方法

        #region 配置操作方法

        /// <summary>
        /// 克隆配置对象
        /// </summary>
        /// <param name="original">原始配置</param>
        /// <returns>克隆的配置</returns>
        private WebBrowserTabConfig CloneConfig(WebBrowserTabConfig original)
        {
            if (original == null)
            {
                return new WebBrowserTabConfig
                {
                    Name = "新标签页",
                    Url = "https://www.baidu.com",
                    HomeUrl = "https://www.baidu.com",
                    UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    WebView2RefreshInterval = 300,
                    CookiePath = "",
                    ProxyServer = "",
                    ProxyPort = 8080,
                    EnableWebView2Refresh = false
                };
            }

            return new WebBrowserTabConfig
            {
                SectionId = original.SectionId,
                Name = original.Name,
                Url = original.Url,
                HomeUrl = original.HomeUrl,
                UserAgent = original.UserAgent,
                WebView2RefreshInterval = original.WebView2RefreshInterval,
                HttpClientRefreshInterval = original.HttpClientRefreshInterval,
                EnableWebView2Refresh = original.EnableWebView2Refresh,
                EnableHttpClientRefresh = original.EnableHttpClientRefresh,
                HttpClientRefreshUrlOrKey = original.HttpClientRefreshUrlOrKey,
                CookiePath = original.CookiePath,
                ProxyServer = original.ProxyServer,
                ProxyPort = original.ProxyPort,
                ProxyUsername = original.ProxyUsername,
                ProxyPassword = original.ProxyPassword,
                ExtraHeaders = original.ExtraHeaders,
                AutoShow = original.AutoShow
            };
        }

        /// <summary>
        /// 加载配置到控件
        /// </summary>
        private void LoadConfigToControls()
        {
            try
            {
                if (Config == null)
                    return;

                // 基本设置
                txtTabName.Text = Config.Name ?? "";
                txtUrl.Text = Config.Url ?? "";
                txtHomeUrl.Text = Config.HomeUrl ?? "";

                // 刷新设置
                chkEnableWebView2Refresh.Checked = Config.EnableWebView2Refresh;
                numWebView2Interval.Value = Math.Max(5, Math.Min(86400, Config.WebView2RefreshInterval));
                chkEnableHttpClientRefresh.Checked = Config.EnableHttpClientRefresh;
                numHttpClientInterval.Value = Math.Max(5, Math.Min(86400, Config.HttpClientRefreshInterval));
                txtHttpClientUrl.Text = Config.HttpClientRefreshUrlOrKey ?? "";

                // Cookie设置
                txtCookiePath.Text = Config.CookiePath ?? "";

                // 代理设置
                txtProxyServer.Text = Config.ProxyServer ?? "";
                numProxyPort.Value = Math.Max(1, Math.Min(65535, Config.ProxyPort));
                txtProxyUsername.Text = Config.ProxyUsername ?? "";
                txtProxyPassword.Text = Config.ProxyPassword ?? "";

                // 浏览器设置
                txtUserAgent.Text = Config.UserAgent ?? "";
                txtExtraHeaders.Text = Config.ExtraHeaders ?? "";

                ETLogManager.Debug(_logSource, "配置加载到控件完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"加载配置到控件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从控件保存配置
        /// </summary>
        private void SaveConfigFromControls()
        {
            try
            {
                if (Config == null)
                    Config = new WebBrowserTabConfig();

                // 基本设置
                Config.Name = txtTabName.Text?.Trim() ?? "";
                Config.Url = txtUrl.Text?.Trim() ?? "";
                Config.HomeUrl = txtHomeUrl.Text?.Trim() ?? "";

                // 刷新设置
                Config.EnableWebView2Refresh = chkEnableWebView2Refresh.Checked;
                Config.WebView2RefreshInterval = (int)numWebView2Interval.Value;
                Config.EnableHttpClientRefresh = chkEnableHttpClientRefresh.Checked;
                Config.HttpClientRefreshInterval = (int)numHttpClientInterval.Value;
                Config.HttpClientRefreshUrlOrKey = txtHttpClientUrl.Text?.Trim() ?? "";

                // Cookie设置
                Config.CookiePath = txtCookiePath.Text?.Trim() ?? "";

                // 代理设置
                Config.ProxyServer = txtProxyServer.Text?.Trim() ?? "";
                Config.ProxyPort = (int)numProxyPort.Value;
                Config.ProxyUsername = txtProxyUsername.Text?.Trim() ?? "";
                Config.ProxyPassword = txtProxyPassword.Text?.Trim() ?? "";

                // 浏览器设置
                Config.UserAgent = txtUserAgent.Text?.Trim() ?? "";
                Config.ExtraHeaders = txtExtraHeaders.Text?.Trim() ?? "";

                // 确保SectionId不为空
                if (string.IsNullOrEmpty(Config.SectionId))
                {
                    Config.SectionId = _originalSectionId ?? Config.GenerateNewSectionId();
                }

                ETLogManager.Debug(_logSource, "从控件保存配置完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"从控件保存配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果</returns>
        private bool ValidateConfig()
        {
            try
            {
                if (_disposed || _cancellationTokenSource.Token.IsCancellationRequested)
                    return false;

                // 验证名称
                if (string.IsNullOrWhiteSpace(txtTabName.Text))
                {
                    MessageBox.Show("标签页名称不能为空", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtTabName.Focus();
                    return false;
                }

                // 验证URL
                if (string.IsNullOrWhiteSpace(txtUrl.Text))
                {
                    MessageBox.Show("URL不能为空", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUrl.Focus();
                    return false;
                }

                // 验证URL格式
                if (!Uri.TryCreate(txtUrl.Text.Trim(), UriKind.Absolute, out Uri uri))
                {
                    MessageBox.Show("URL格式不正确", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUrl.Focus();
                    return false;
                }

                // 验证代理设置（如果有代理服务器设置）
                if (!string.IsNullOrWhiteSpace(txtProxyServer.Text))
                {
                    if (numProxyPort.Value <= 0)
                    {
                        MessageBox.Show("设置代理服务器时，代理端口必须大于0", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        numProxyPort.Focus();
                        return false;
                    }
                }

                ETLogManager.Debug(_logSource, "配置验证通过");
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"配置验证异常: {ex.Message}");
                return false;
            }
        }

        #endregion 配置操作方法

        #region 事件处理方法

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                if (_disposed)
                    return;

                // 验证配置
                if (!ValidateConfig())
                    return;

                // 保存配置
                SaveConfigFromControls();

                // 设置确认状态
                Confirmed = true;

                ETLogManager.Info(_logSource, $"配置确认完成: {Config.Name}");

                // 关闭窗体
                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"确认配置失败: {ex.Message}");
                MessageBox.Show($"保存配置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                Confirmed = false;
                ETLogManager.Info(_logSource, "用户取消配置");
                DialogResult = DialogResult.Cancel;
                Close();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"取消配置失败: {ex.Message}");
            }
        }

        #endregion 事件处理方法
    }
}
