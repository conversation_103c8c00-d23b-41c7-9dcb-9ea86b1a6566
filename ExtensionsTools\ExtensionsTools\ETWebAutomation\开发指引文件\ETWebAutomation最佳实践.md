# 🏆 ETWebAutomation 最佳实践指南

## 📋 概述

本指南汇总了ETWebAutomation模块开发和使用过程中的最佳实践，包括代码规范、性能优化、安全考虑、错误处理、测试策略等方面的经验和建议。

## 🎯 代码规范最佳实践

### 1. 命名规范

```csharp
// ✅ 推荐的命名方式
public class ETWebUserManager
{
    private readonly ETWebApiClient _apiClient;
    private readonly ETWebSessionManager _sessionManager;
    
    public async Task<ETWebApiResponse<UserInfo>> GetUserInfoAsync(string userId)
    {
        // 方法名清晰表达功能和异步特性
    }
    
    public event EventHandler<UserLoginEventArgs> UserLoginCompleted;
    // 事件名称明确表达触发时机
}

// ❌ 避免的命名方式
public class Manager // 名称过于泛化
{
    private ETWebApiClient client; // 缺少访问修饰符和下划线前缀
    
    public void DoSomething() // 方法名不明确
    {
    }
}
```

### 2. 异步编程规范

```csharp
// ✅ 正确的异步模式
public async Task<ETWebUploadResult> UploadFileAsync(string filePath)
{
    try
    {
        // 使用ConfigureAwait(false)避免死锁
        var result = await _apiClient.PostAsync(endpoint, data).ConfigureAwait(false);
        
        // 正确处理异步操作结果
        return ProcessUploadResult(result);
    }
    catch (OperationCanceledException)
    {
        // 正确处理取消操作
        ETLogManager.Info("文件上传被取消");
        throw;
    }
    catch (Exception ex)
    {
        // 记录异常并重新抛出
        ETLogManager.Error($"文件上传失败: {ex.Message}", ex);
        throw;
    }
}

// ❌ 避免的异步反模式
public ETWebUploadResult UploadFile(string filePath)
{
    // 不要在同步方法中阻塞异步调用
    return UploadFileAsync(filePath).Result; // 可能导致死锁
}
```

### 3. 资源管理规范

```csharp
// ✅ 正确的资源管理
public class ETWebFileProcessor : IDisposable
{
    private readonly FileStream _fileStream;
    private readonly HttpClient _httpClient;
    private bool _disposed = false;
    
    public ETWebFileProcessor(string filePath)
    {
        _fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
        _httpClient = new HttpClient();
    }
    
    public async Task ProcessAsync()
    {
        using (var reader = new StreamReader(_fileStream))
        {
            // 使用using确保资源正确释放
            var content = await reader.ReadToEndAsync();
            await ProcessContent(content);
        }
    }
    
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
    
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _fileStream?.Dispose();
            _httpClient?.Dispose();
            _disposed = true;
        }
    }
}
```

## ⚡ 性能优化最佳实践

### 1. 异步并发控制

```csharp
// ✅ 使用SemaphoreSlim控制并发
public class ETWebBatchUploader
{
    private readonly SemaphoreSlim _concurrencyLimiter;
    private readonly int _maxConcurrency;
    
    public ETWebBatchUploader(int maxConcurrency = 3)
    {
        _maxConcurrency = maxConcurrency;
        _concurrencyLimiter = new SemaphoreSlim(maxConcurrency, maxConcurrency);
    }
    
    public async Task<List<ETWebUploadResult>> UploadFilesAsync(List<string> filePaths)
    {
        var uploadTasks = filePaths.Select(async filePath =>
        {
            await _concurrencyLimiter.WaitAsync();
            try
            {
                return await UploadSingleFileAsync(filePath);
            }
            finally
            {
                _concurrencyLimiter.Release();
            }
        });
        
        return (await Task.WhenAll(uploadTasks)).ToList();
    }
}
```

### 2. 内存优化

```csharp
// ✅ 大文件流式处理
public async Task<bool> ProcessLargeFileAsync(string filePath)
{
    const int bufferSize = 8192; // 8KB缓冲区
    
    using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
    using (var reader = new StreamReader(fileStream, bufferSize: bufferSize))
    {
        string line;
        while ((line = await reader.ReadLineAsync()) != null)
        {
            // 逐行处理，避免一次性加载整个文件到内存
            await ProcessLineAsync(line);
            
            // 定期检查内存使用情况
            if (GC.GetTotalMemory(false) > 100 * 1024 * 1024) // 100MB
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }
        }
    }
    
    return true;
}
```

### 3. 缓存策略

```csharp
// ✅ 智能缓存实现
public class ETWebDataCache
{
    private readonly MemoryCache _cache;
    private readonly TimeSpan _defaultExpiry = TimeSpan.FromMinutes(30);
    
    public ETWebDataCache()
    {
        _cache = new MemoryCache(new MemoryCacheOptions
        {
            SizeLimit = 1000, // 最多缓存1000个项目
            CompactionPercentage = 0.25 // 内存不足时清理25%的项目
        });
    }
    
    public async Task<T> GetOrCreateAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null)
    {
        if (_cache.TryGetValue(key, out T cachedValue))
        {
            ETLogManager.Debug($"缓存命中: {key}");
            return cachedValue;
        }
        
        ETLogManager.Debug($"缓存未命中，创建新值: {key}");
        var value = await factory();
        
        var cacheOptions = new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = expiry ?? _defaultExpiry,
            Size = 1,
            Priority = CacheItemPriority.Normal
        };
        
        _cache.Set(key, value, cacheOptions);
        return value;
    }
}
```

## 🔒 安全最佳实践

### 1. 敏感数据保护

```csharp
// ✅ 安全的密码处理
public class ETWebSecureLoginInfo
{
    private SecureString _password;
    
    public string Username { get; set; }
    
    public void SetPassword(string password)
    {
        _password?.Dispose();
        _password = new SecureString();
        
        foreach (char c in password)
        {
            _password.AppendChar(c);
        }
        
        _password.MakeReadOnly();
        
        // 清除原始密码字符串
        password = null;
    }
    
    public string GetPassword()
    {
        if (_password == null)
            return null;
        
        IntPtr ptr = Marshal.SecureStringToBSTR(_password);
        try
        {
            return Marshal.PtrToStringBSTR(ptr);
        }
        finally
        {
            Marshal.ZeroFreeBSTR(ptr);
        }
    }
    
    public void Dispose()
    {
        _password?.Dispose();
        _password = null;
    }
}
```

### 2. 输入验证

```csharp
// ✅ 严格的输入验证
public class ETWebInputValidator
{
    public static bool ValidateUsername(string username)
    {
        if (string.IsNullOrWhiteSpace(username))
            return false;
        
        // 长度检查
        if (username.Length < 3 || username.Length > 50)
            return false;
        
        // 字符检查：只允许字母、数字、下划线
        if (!Regex.IsMatch(username, @"^[a-zA-Z0-9_]+$"))
            return false;
        
        // 防止SQL注入关键词
        var sqlKeywords = new[] { "SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "EXEC" };
        var upperUsername = username.ToUpperInvariant();
        
        return !sqlKeywords.Any(keyword => upperUsername.Contains(keyword));
    }
    
    public static bool ValidateFilePath(string filePath)
    {
        if (string.IsNullOrWhiteSpace(filePath))
            return false;
        
        try
        {
            // 检查路径格式
            var fullPath = Path.GetFullPath(filePath);
            
            // 检查是否包含危险字符
            var invalidChars = Path.GetInvalidPathChars();
            if (filePath.Any(c => invalidChars.Contains(c)))
                return false;
            
            // 检查是否尝试访问系统目录
            var systemDirs = new[] { "Windows", "System32", "Program Files" };
            return !systemDirs.Any(dir => fullPath.Contains(dir, StringComparison.OrdinalIgnoreCase));
        }
        catch
        {
            return false;
        }
    }
}
```

### 3. 加密传输

```csharp
// ✅ 安全的数据传输
public class ETWebSecureApiClient
{
    private readonly HttpClient _httpClient;
    
    public ETWebSecureApiClient()
    {
        var handler = new HttpClientHandler()
        {
            ServerCertificateCustomValidationCallback = ValidateServerCertificate
        };
        
        _httpClient = new HttpClient(handler);
        
        // 设置安全头
        _httpClient.DefaultRequestHeaders.Add("X-Content-Type-Options", "nosniff");
        _httpClient.DefaultRequestHeaders.Add("X-Frame-Options", "DENY");
        _httpClient.DefaultRequestHeaders.Add("X-XSS-Protection", "1; mode=block");
    }
    
    private bool ValidateServerCertificate(HttpRequestMessage request, 
        X509Certificate2 certificate, X509Chain chain, SslPolicyErrors errors)
    {
        // 在生产环境中实施严格的证书验证
        if (errors == SslPolicyErrors.None)
            return true;
        
        ETLogManager.Warning($"SSL证书验证失败: {errors}");
        
        // 在开发环境中可以放宽验证，但要记录警告
        #if DEBUG
        return true;
        #else
        return false;
        #endif
    }
}
```

## 🐛 错误处理最佳实践

### 1. 分层异常处理

```csharp
// ✅ 分层异常处理策略
public class ETWebExceptionHandler
{
    public static async Task<T> ExecuteWithRetryAsync<T>(
        Func<Task<T>> operation, 
        int maxRetries = 3,
        TimeSpan? delay = null)
    {
        var currentDelay = delay ?? TimeSpan.FromSeconds(1);
        
        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex) when (IsRetryableException(ex) && attempt < maxRetries)
            {
                ETLogManager.Warning($"操作失败，第 {attempt} 次重试: {ex.Message}");
                
                await Task.Delay(currentDelay);
                currentDelay = TimeSpan.FromMilliseconds(currentDelay.TotalMilliseconds * 2); // 指数退避
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"操作最终失败，已重试 {attempt - 1} 次: {ex.Message}", ex);
                throw;
            }
        }
        
        throw new InvalidOperationException("不应该到达这里");
    }
    
    private static bool IsRetryableException(Exception ex)
    {
        return ex is HttpRequestException ||
               ex is TaskCanceledException ||
               ex is SocketException ||
               (ex is WebException webEx && IsRetryableWebException(webEx));
    }
    
    private static bool IsRetryableWebException(WebException ex)
    {
        return ex.Status == WebExceptionStatus.Timeout ||
               ex.Status == WebExceptionStatus.ConnectFailure ||
               ex.Status == WebExceptionStatus.ReceiveFailure;
    }
}
```

### 2. 自定义异常类型

```csharp
// ✅ 明确的异常类型定义
public class ETWebAuthenticationException : Exception
{
    public string Username { get; }
    public DateTime AttemptTime { get; }
    
    public ETWebAuthenticationException(string username, string message) 
        : base(message)
    {
        Username = username;
        AttemptTime = DateTime.Now;
    }
    
    public ETWebAuthenticationException(string username, string message, Exception innerException) 
        : base(message, innerException)
    {
        Username = username;
        AttemptTime = DateTime.Now;
    }
}

public class ETWebFileUploadException : Exception
{
    public string FilePath { get; }
    public long FileSize { get; }
    public string UploadId { get; }
    
    public ETWebFileUploadException(string filePath, long fileSize, string uploadId, string message) 
        : base(message)
    {
        FilePath = filePath;
        FileSize = fileSize;
        UploadId = uploadId;
    }
}
```

## 📊 监控和日志最佳实践

### 1. 结构化日志

```csharp
// ✅ 结构化日志记录
public class ETWebStructuredLogger
{
    public static void LogUserAction(string action, string username, Dictionary<string, object> properties = null)
    {
        var logData = new
        {
            Timestamp = DateTime.UtcNow,
            Action = action,
            Username = username,
            Properties = properties ?? new Dictionary<string, object>(),
            SessionId = GetCurrentSessionId(),
            ClientIP = GetClientIP()
        };
        
        ETLogManager.Info($"用户操作: {ETWebJsonHelper.ToJson(logData)}");
    }
    
    public static void LogPerformanceMetric(string operation, TimeSpan duration, Dictionary<string, object> metrics = null)
    {
        var logData = new
        {
            Timestamp = DateTime.UtcNow,
            Operation = operation,
            Duration = duration.TotalMilliseconds,
            Metrics = metrics ?? new Dictionary<string, object>()
        };
        
        if (duration.TotalSeconds > 5) // 超过5秒记录为警告
        {
            ETLogManager.Warning($"性能警告: {ETWebJsonHelper.ToJson(logData)}");
        }
        else
        {
            ETLogManager.Debug($"性能指标: {ETWebJsonHelper.ToJson(logData)}");
        }
    }
}
```

### 2. 健康检查

```csharp
// ✅ 系统健康检查
public class ETWebHealthChecker
{
    public async Task<HealthCheckResult> CheckSystemHealthAsync()
    {
        var checks = new List<Task<HealthCheckItem>>
        {
            CheckDatabaseConnectionAsync(),
            CheckApiEndpointAsync(),
            CheckDiskSpaceAsync(),
            CheckMemoryUsageAsync()
        };
        
        var results = await Task.WhenAll(checks);
        
        var overallStatus = results.All(r => r.IsHealthy) ? HealthStatus.Healthy :
                           results.Any(r => r.Status == HealthStatus.Critical) ? HealthStatus.Critical :
                           HealthStatus.Warning;
        
        return new HealthCheckResult
        {
            Status = overallStatus,
            CheckTime = DateTime.UtcNow,
            Details = results.ToList()
        };
    }
    
    private async Task<HealthCheckItem> CheckApiEndpointAsync()
    {
        try
        {
            var stopwatch = Stopwatch.StartNew();
            var response = await _httpClient.GetAsync("/api/health");
            stopwatch.Stop();
            
            return new HealthCheckItem
            {
                Name = "API端点",
                IsHealthy = response.IsSuccessStatusCode,
                Status = response.IsSuccessStatusCode ? HealthStatus.Healthy : HealthStatus.Critical,
                ResponseTime = stopwatch.Elapsed,
                Message = response.IsSuccessStatusCode ? "API响应正常" : $"API响应异常: {response.StatusCode}"
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckItem
            {
                Name = "API端点",
                IsHealthy = false,
                Status = HealthStatus.Critical,
                Message = $"API连接失败: {ex.Message}"
            };
        }
    }
}
```

## 🧪 测试最佳实践

### 1. 单元测试

```csharp
// ✅ 全面的单元测试
[TestClass]
public class ETWebLoginInfoTests
{
    [TestMethod]
    public void Validate_ValidLoginInfo_ReturnsTrue()
    {
        // Arrange
        var loginInfo = new ETWebLoginInfo
        {
            Username = "testuser",
            Password = "password123",
            LoginUrl = "https://test.com/login"
        };
        
        // Act
        var result = loginInfo.Validate();
        
        // Assert
        Assert.IsTrue(result);
    }
    
    [TestMethod]
    [DataRow("", "password123", "https://test.com/login")] // 空用户名
    [DataRow("ab", "password123", "https://test.com/login")] // 用户名过短
    [DataRow("testuser", "", "https://test.com/login")] // 空密码
    [DataRow("testuser", "123", "https://test.com/login")] // 密码过短
    [DataRow("testuser", "password123", "")] // 空URL
    [DataRow("testuser", "password123", "invalid-url")] // 无效URL
    public void Validate_InvalidLoginInfo_ReturnsFalse(string username, string password, string loginUrl)
    {
        // Arrange
        var loginInfo = new ETWebLoginInfo
        {
            Username = username,
            Password = password,
            LoginUrl = loginUrl
        };
        
        // Act
        var result = loginInfo.Validate();
        
        // Assert
        Assert.IsFalse(result);
    }
}
```

### 2. 集成测试

```csharp
// ✅ 集成测试示例
[TestClass]
public class ETWebClientIntegrationTests
{
    private ETWebClient _client;
    private TestConfiguration _config;
    
    [TestInitialize]
    public void Setup()
    {
        _config = TestConfiguration.Load();
        _client = new ETWebClient();
    }
    
    [TestCleanup]
    public void Cleanup()
    {
        _client?.Dispose();
    }
    
    [TestMethod]
    public async Task LoginAsync_ValidCredentials_ReturnsSuccess()
    {
        // Arrange
        var loginInfo = new ETWebLoginInfo
        {
            Username = _config.TestUsername,
            Password = _config.TestPassword,
            LoginUrl = _config.TestLoginUrl
        };
        
        // Act
        var result = await _client.LoginAsync(loginInfo);
        
        // Assert
        Assert.IsTrue(result.IsSuccess);
        Assert.IsNotNull(result.SessionId);
        Assert.IsTrue(_client.IsLoggedIn);
    }
}
```

---

**📅 文档版本**: v1.0  
**🔄 最后更新**: 2024年12月  
**👨‍💻 维护团队**: ETWebAutomation开发组
