# 🚀 ETWebClient 主客户端执行流程详解

## 📋 概述

ETWebClient是ETWebAutomation系统的核心入口类，负责统一管理和协调所有子模块，提供简洁的API接口供外部调用。

## 🏗️ 类结构图

```mermaid
classDiagram
    class ETWebClient {
        +string BaseUrl
        +bool IsLoggedIn
        +ETWebLoginInfo LoginInfo
        +ETWebSessionManager SessionManager
        +ETWebApiClient ApiClient
        +ETWebFileUploader FileUploader
        +ETWebSimulationBrowser SimulationBrowser
        
        +LoginAsync(username, password) Task~ETWebLoginInfo~
        +GetApiDataAsync~T~(endpoint) Task~ETWebApiResponse~T~~
        +PostApiDataAsync~T~(endpoint, data) Task~ETWebApiResponse~T~~
        +UploadFileAsync(endpoint, filePath) Task~ETWebUploadResult~
        +SetConfig(section, key, value) void
        +Dispose() void
    }
    
    ETWebClient --> ETWebLoginBrowser
    ETWebClient --> ETWebApiClient
    ETWebClient --> ETWebSessionManager
    ETWebClient --> ETWebFileUploader
    ETWebClient --> ETWebSimulationBrowser
    ETWebClient --> ETWebConfigHelper
```

## 🔄 初始化流程

```mermaid
sequenceDiagram
    participant User as 用户代码
    participant Client as ETWebClient
    participant Config as ETWebConfigHelper
    participant Login as ETWebLoginBrowser
    participant Api as ETWebApiClient
    participant Session as ETWebSessionManager
    participant Upload as ETWebFileUploader
    participant Sim as ETWebSimulationBrowser
    
    User->>Client: new ETWebClient(baseUrl)
    Client->>Config: 初始化配置管理器
    Config-->>Client: 配置加载完成
    
    Client->>Login: 创建登录浏览器实例
    Client->>Api: 创建API客户端实例
    Client->>Session: 创建会话管理器实例
    Client->>Upload: 创建文件上传器实例
    Client->>Sim: 创建模拟浏览器实例
    
    Login-->>Client: 初始化完成
    Api-->>Client: 初始化完成
    Session-->>Client: 初始化完成
    Upload-->>Client: 初始化完成
    Sim-->>Client: 初始化完成
    
    Client-->>User: ETWebClient实例就绪
```

## 🔐 登录流程详解

```mermaid
flowchart TD
    A[调用LoginAsync] --> B[验证参数有效性]
    B --> C{参数是否有效}
    C -->|无效| D[返回参数错误]
    C -->|有效| E[创建ETWebLoginBrowser实例]
    E --> F[设置登录参数]
    F --> G[调用AutoLoginAsync]
    G --> H[等待登录完成]
    H --> I{登录是否成功}
    I -->|失败| J[记录错误日志]
    I -->|成功| K[保存登录信息]
    K --> L[启动会话管理]
    L --> M[配置API客户端认证]
    M --> N[更新客户端状态]
    N --> O[返回登录结果]
    J --> P[清理资源]
    P --> Q[返回失败结果]
    
    style A fill:#e1f5fe
    style O fill:#c8e6c9
    style Q fill:#ffcdd2
```

### 登录方法实现逻辑

```csharp
public async Task<ETWebLoginInfo> LoginAsync(string username, string password)
{
    try
    {
        // 1. 参数验证
        if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            throw new ArgumentException("用户名和密码不能为空");

        // 2. 创建登录浏览器
        using (var loginBrowser = new ETWebLoginBrowser())
        {
            // 3. 执行自动登录
            var loginResult = await loginBrowser.AutoLoginAsync(username, password, BaseUrl);
            
            if (loginResult.IsSuccess)
            {
                // 4. 保存登录信息
                LoginInfo = loginResult;
                IsLoggedIn = true;
                
                // 5. 启动会话管理
                SessionManager.StartMonitoring(LoginInfo);
                
                // 6. 配置API客户端
                ApiClient.SetAuthenticationInfo(LoginInfo);
                
                // 7. 记录成功日志
                ETLogManager.Info($"用户 {username} 登录成功");
                
                return LoginInfo;
            }
            else
            {
                // 8. 处理登录失败
                ETLogManager.Error($"用户 {username} 登录失败: {loginResult.ErrorMessage}");
                throw new ETException($"登录失败: {loginResult.ErrorMessage}");
            }
        }
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"登录过程发生异常: {ex.Message}", ex);
        throw;
    }
}
```

## 🌐 API调用流程

```mermaid
sequenceDiagram
    participant User as 用户代码
    participant Client as ETWebClient
    participant Session as ETWebSessionManager
    participant Api as ETWebApiClient
    participant Retry as ETWebRetryHelper
    participant Log as ETLogManager
    
    User->>Client: GetApiDataAsync<T>(endpoint)
    Client->>Client: 检查登录状态
    
    alt 未登录
        Client-->>User: 抛出未登录异常
    else 已登录
        Client->>Session: 检查会话有效性
        Session-->>Client: 会话状态
        
        alt 会话无效
            Client->>Session: 触发自动重登
            Session-->>Client: 重登结果
        end
        
        Client->>Api: 发送API请求
        Api->>Retry: 执行带重试的请求
        
        loop 重试循环
            Retry->>Api: 发送HTTP请求
            Api-->>Retry: 响应结果
            
            alt 请求成功
                Retry-->>Api: 返回成功响应
            else 需要重试
                Retry->>Log: 记录重试日志
                Retry->>Retry: 等待重试延迟
            end
        end
        
        Api-->>Client: API响应结果
        Client->>Log: 记录API调用日志
        Client-->>User: 返回响应数据
    end
```

### API调用方法实现

```csharp
public async Task<ETWebApiResponse<T>> GetApiDataAsync<T>(string endpoint, 
    Dictionary<string, object> queryParams = null)
{
    // 1. 检查登录状态
    if (!IsLoggedIn || LoginInfo == null)
        throw new InvalidOperationException("请先登录");

    // 2. 检查会话有效性
    if (!SessionManager.IsSessionValid())
    {
        var reloginResult = await SessionManager.TriggerAutoReloginAsync();
        if (!reloginResult)
            throw new InvalidOperationException("会话已过期且自动重登失败");
    }

    try
    {
        // 3. 发送API请求
        var response = await ApiClient.GetAsync<T>(endpoint, queryParams);
        
        // 4. 记录API调用日志
        ETLogManager.Info($"API调用成功: GET {endpoint}");
        
        return response;
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"API调用失败: GET {endpoint}, 错误: {ex.Message}", ex);
        throw;
    }
}
```

## 📤 文件上传流程

```mermaid
flowchart TD
    A[调用UploadFileAsync] --> B[验证文件路径]
    B --> C{文件是否存在}
    C -->|不存在| D[返回文件不存在错误]
    C -->|存在| E[检查登录状态]
    E --> F{是否已登录}
    F -->|未登录| G[返回未登录错误]
    F -->|已登录| H[验证文件大小和类型]
    H --> I{文件是否有效}
    I -->|无效| J[返回文件验证错误]
    I -->|有效| K[调用FileUploader.UploadFileAsync]
    K --> L[监控上传进度]
    L --> M{上传是否成功}
    M -->|失败| N[记录上传失败日志]
    M -->|成功| O[记录上传成功日志]
    O --> P[返回上传结果]
    N --> Q[返回失败结果]
    
    style A fill:#e1f5fe
    style P fill:#c8e6c9
    style D fill:#ffcdd2
    style G fill:#ffcdd2
    style J fill:#ffcdd2
    style Q fill:#ffcdd2
```

## 🔧 配置管理流程

```mermaid
graph TD
    A[调用SetConfig] --> B[验证配置参数]
    B --> C[调用ETWebConfigHelper.SetConfig]
    C --> D[写入INI配置文件]
    D --> E[触发配置变更事件]
    E --> F[通知相关模块]
    F --> G[更新模块配置]
    G --> H[记录配置变更日志]
    
    I[调用GetConfig] --> J[调用ETWebConfigHelper.GetConfig]
    J --> K[从INI文件读取]
    K --> L[返回配置值]
    
    style A fill:#e1f5fe
    style I fill:#e1f5fe
    style H fill:#c8e6c9
    style L fill:#c8e6c9
```

## 🔄 资源释放流程

```mermaid
sequenceDiagram
    participant User as 用户代码
    participant Client as ETWebClient
    participant Session as ETWebSessionManager
    participant Login as ETWebLoginBrowser
    participant Api as ETWebApiClient
    participant Upload as ETWebFileUploader
    participant Sim as ETWebSimulationBrowser
    
    User->>Client: Dispose()
    Client->>Session: StopMonitoring()
    Session-->>Client: 会话监控已停止
    
    Client->>Login: Dispose()
    Login-->>Client: 登录浏览器已释放
    
    Client->>Api: Dispose()
    Api-->>Client: API客户端已释放
    
    Client->>Upload: Dispose()
    Upload-->>Client: 文件上传器已释放
    
    Client->>Sim: Dispose()
    Sim-->>Client: 模拟浏览器已释放
    
    Client->>Client: 清理内部资源
    Client-->>User: 资源释放完成
```

## 📊 性能监控集成

```mermaid
graph LR
    A[ETWebClient操作] --> B[ETWebPerformanceHelper]
    B --> C[记录操作耗时]
    B --> D[监控内存使用]
    B --> E[统计API调用次数]
    B --> F[记录错误率]
    
    C --> G[性能报告]
    D --> G
    E --> G
    F --> G
    
    G --> H[日志输出]
    G --> I[性能警告]
    G --> J[资源优化建议]
```

## 🛡️ 异常处理策略

### 异常分类处理

```csharp
public class ETWebClientExceptionHandler
{
    public static void HandleException(Exception ex, string operation)
    {
        switch (ex)
        {
            case ArgumentException argEx:
                ETLogManager.Warning($"参数错误 - {operation}: {argEx.Message}");
                break;
                
            case InvalidOperationException opEx:
                ETLogManager.Error($"操作无效 - {operation}: {opEx.Message}");
                break;
                
            case HttpRequestException httpEx:
                ETLogManager.Error($"网络请求失败 - {operation}: {httpEx.Message}");
                break;
                
            case TimeoutException timeoutEx:
                ETLogManager.Error($"操作超时 - {operation}: {timeoutEx.Message}");
                break;
                
            case ETException etEx:
                ETLogManager.Error($"系统异常 - {operation}: {etEx.Message}", etEx);
                break;
                
            default:
                ETLogManager.Fatal($"未知异常 - {operation}: {ex.Message}", ex);
                break;
        }
    }
}
```

## 📈 使用最佳实践

### 1. 正确的初始化方式
```csharp
// 推荐方式
using (var client = new ETWebClient("https://oa.company.com"))
{
    var loginResult = await client.LoginAsync("username", "password");
    if (loginResult.IsSuccess)
    {
        // 执行业务操作
        var data = await client.GetApiDataAsync<UserInfo>("/api/user/info");
    }
} // 自动释放资源
```

### 2. 异常处理最佳实践
```csharp
try
{
    var result = await client.GetApiDataAsync<DataModel>("/api/data");
    // 处理成功结果
}
catch (InvalidOperationException ex) when (ex.Message.Contains("未登录"))
{
    // 处理未登录异常
    await client.LoginAsync(username, password);
    // 重试操作
}
catch (HttpRequestException ex)
{
    // 处理网络异常
    ETLogManager.Error($"网络请求失败: {ex.Message}");
}
```

### 3. 配置管理最佳实践
```csharp
// 在应用启动时配置
client.SetConfig("API", "Timeout", "30");
client.SetConfig("Upload", "MaxFileSize", "100");
client.SetConfig("Session", "HeartbeatInterval", "300");
```

---

**📅 文档版本**: v1.0  
**🔄 最后更新**: 2024年12月  
**👨‍💻 维护团队**: ETWebAutomation开发组
