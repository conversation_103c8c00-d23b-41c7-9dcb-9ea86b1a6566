using System;
using System.IO;
using System.Threading.Tasks;
using Flurl;
using Flurl.Http;
using ET.ETWebAutomation.Models;
using ET;

namespace ET.ETWebAutomation
{
    /// <summary>
    /// 简化的文件上传处理器，专注于基本的单文件上传功能
    /// </summary>
    public class ETWebFileUploader : IDisposable
    {
        #region 私有字段

        private readonly ETWebApiClient _apiClient;
        private bool _disposed = false; // 资源释放状态标志

        #endregion 私有字段

        #region 公共属性

        /// <summary>
        /// 最大文件大小（MB）
        /// </summary>
        public int MaxFileSize { get; set; } = 100;

        /// <summary>
        /// 允许的文件扩展名
        /// </summary>
        public string[] AllowedExtensions { get; set; } =
        {
            ".jpg", ".jpeg", ".png", ".gif", ".bmp",
            ".pdf", ".doc", ".docx", ".xls", ".xlsx",
            ".ppt", ".pptx", ".txt", ".zip", ".rar"
        };

        /// <summary>
        /// 上传重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        #endregion 公共属性

        #region 构造函数

        /// <summary>
        /// 初始化文件上传器
        /// </summary>
        /// <param name="apiClient">API客户端</param>
        public ETWebFileUploader(ETWebApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            ETLogManager.Info("ETOAFileUploader", "简化文件上传器初始化完成");
        }

        #endregion 构造函数

        #region 私有方法

        /// <summary>
        /// 验证文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        private (bool IsValid, string ErrorMessage) ValidateFile(string filePath)
        {
            if (!File.Exists(filePath))
            {
                return (false, "文件不存在");
            }

            var fileInfo = new FileInfo(filePath);

            // 检查文件大小
            if (fileInfo.Length > MaxFileSize * 1024 * 1024)
            {
                return (false, $"文件大小超过限制 ({MaxFileSize}MB)");
            }

            // 检查文件扩展名
            var extension = fileInfo.Extension.ToLower();
            if (AllowedExtensions != null && AllowedExtensions.Length > 0)
            {
                bool isAllowed = false;
                foreach (var allowedExt in AllowedExtensions)
                {
                    if (extension == allowedExt.ToLower())
                    {
                        isAllowed = true;
                        break;
                    }
                }

                if (!isAllowed)
                {
                    return (false, $"不支持的文件类型: {extension}");
                }
            }

            return (true, null);
        }

        /// <summary>
        /// 创建上传结果
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="success">是否成功</param>
        /// <param name="message">消息</param>
        /// <param name="fileId">文件ID</param>
        /// <returns>上传结果</returns>
        private ETWebUploadResult CreateUploadResult(string filePath, bool success, string message, string fileId = null)
        {
            return new ETWebUploadResult
            {
                FileName = Path.GetFileName(filePath),
                FilePath = filePath,
                IsSuccess = success,
                Message = message,
                FileId = fileId,
                UploadTime = DateTime.Now
            };
        }

        #endregion 私有方法

        #region 公共方法

        /// <summary>
        /// 上传单个文件（简化版本）
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formData">表单数据</param>
        /// <returns>上传结果</returns>
        public async Task<ETWebUploadResult> UploadFileAsync(string endpoint, string filePath, object formData = null)
        {
            return await UploadFileWithRetryAsync(endpoint, filePath, formData, RetryCount);
        }

        /// <summary>
        /// 上传单个文件（带重试）
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formData">表单数据</param>
        /// <param name="retryCount">重试次数</param>
        /// <returns>上传结果</returns>
        public async Task<ETWebUploadResult> UploadFileWithRetryAsync(string endpoint, string filePath, object formData = null, int retryCount = 3)
        {
            ETWebUploadResult lastResult = null;

            for (int attempt = 0; attempt <= retryCount; attempt++)
            {
                try
                {
                    // 验证文件
                    var validation = ValidateFile(filePath);
                    if (!validation.IsValid)
                    {
                        return CreateUploadResult(filePath, false, validation.ErrorMessage);
                    }

                    var fileName = Path.GetFileName(filePath);
                    ETLogManager.Info("ETOAFileUploader", $"开始上传文件: {fileName} (尝试 {attempt + 1}/{retryCount + 1})");

                    // 直接上传文件
                    lastResult = await UploadFileDirectAsync(endpoint, filePath, formData);

                    if (lastResult.IsSuccess)
                    {
                        ETLogManager.Info("ETOAFileUploader", $"文件上传成功: {fileName}");
                        return lastResult;
                    }

                    if (attempt < retryCount)
                    {
                        var delay = (int)Math.Pow(2, attempt) * 1000; // 指数退避
                        ETLogManager.Warning("ETOAFileUploader", $"上传失败，{delay}ms后重试: {lastResult.Message}");
                        await Task.Delay(delay);
                    }
                }
                catch (Exception ex)
                {
                    var errorMsg = $"上传异常 (尝试 {attempt + 1}/{retryCount + 1}): {ex.Message}";
                    ETLogManager.Error("ETOAFileUploader", errorMsg);

                    lastResult = CreateUploadResult(filePath, false, errorMsg);

                    if (attempt < retryCount)
                    {
                        var delay = (int)Math.Pow(2, attempt) * 1000;
                        await Task.Delay(delay);
                    }
                }
            }

            return lastResult ?? CreateUploadResult(filePath, false, "上传失败：未知错误");
        }

        /// <summary>
        /// 直接上传文件（简化版本）
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formData">表单数据</param>
        /// <returns>上传结果</returns>
        private async Task<ETWebUploadResult> UploadFileDirectAsync(string endpoint, string filePath, object formData = null)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);
                var fileBytes = File.ReadAllBytes(filePath);

                // 创建多部分表单数据
                var request = _apiClient.BaseUrl
                    .AppendPathSegment(endpoint)
                    .PostMultipartAsync(mp =>
                    {
                        mp.AddFile("file", new MemoryStream(fileBytes), fileName);

                        // 添加表单数据
                        if (formData != null)
                        {
                            var properties = formData.GetType().GetProperties();
                            foreach (var prop in properties)
                            {
                                var value = prop.GetValue(formData);
                                if (value != null)
                                {
                                    mp.AddString(prop.Name, value.ToString());
                                }
                            }
                        }
                    });

                var response = await request;
                var result = await response.GetJsonAsync<dynamic>();

                return CreateUploadResult(filePath, true, "上传成功", result?.fileId?.ToString());
            }
            catch (Exception ex)
            {
                return CreateUploadResult(filePath, false, $"上传失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    _disposed = true;
                    ETLogManager.Info("ETOAFileUploader", "简化文件上传器资源释放完成");
                }
                catch (Exception ex)
                {
                    ETLogManager.Error("ETOAFileUploader", "释放文件上传器资源时发生异常", ex);
                }
            }
        }

        /// <summary>
        /// 检查对象是否已释放
        /// </summary>
        private void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(ETWebFileUploader));
            }
        }

        #endregion 公共方法
    }
}