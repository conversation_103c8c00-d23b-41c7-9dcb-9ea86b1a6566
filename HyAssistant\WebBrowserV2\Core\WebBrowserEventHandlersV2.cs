using ET;
using Microsoft.Web.WebView2.WinForms;
using Microsoft.Web.WebView2.Core;
using System;
using System.Drawing;
using System.IO;
using System.Reflection;
using System.Windows.Forms;
using HyAssistant.WebBrowserV2.Utils;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Threading;
using System.Text;
using System.Runtime.InteropServices;
using System.Diagnostics;
using Newtonsoft.Json;

namespace HyAssistant.WebBrowserV2.Core
{
    /// <summary>
    /// WebBrowserV2事件处理器 - partial class
    /// </summary>
    /// <remarks>
    /// V2版本优化：
    /// 1. 异步事件处理安全性提升
    /// 2. 统一使用ETLogManager记录日志
    /// 3. 增强异常处理机制
    /// 4. 优化UI响应性能
    /// </remarks>
    public partial class WebBrowserV2
    {
        #region 窗体事件处理

        /// <summary>
        /// V2版本优化: 窗体大小改变事件处理
        /// </summary>
        private void WebBrowserV2_SizeChanged(object sender, EventArgs e)
        {
            try
            {
                if (urlTextBox != null && toolStrip1 != null)
                {
                    // 计算工具栏中除地址栏外所有控件的总宽度
                    int otherControlsWidth = 0;
                    foreach (ToolStripItem item in toolStrip1.Items)
                    {
                        if (item != urlTextBox)
                        {
                            otherControlsWidth += item.Width;
                        }
                    }

                    // 添加边距
                    otherControlsWidth += WebBrowserConstantsV2.TOOLBAR_MARGIN;

                    // 计算地址栏应占用的宽度
                    int availableWidth = toolStrip1.Width - otherControlsWidth;

                    // 确保宽度至少为最小宽度
                    int newWidth = Math.Max(WebBrowserConstantsV2.MIN_ADDRESS_BAR_WIDTH, availableWidth);

                    // 设置地址栏宽度
                    if (urlTextBox.Width != newWidth)
                    {
                        urlTextBox.Width = newWidth;
                    }
                }
            }
            catch (Exception ex)
            {
                // V2版本优化: 使用增强的异常处理
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "调整地址栏宽度");
            }
        }

        /// <summary>
        /// V2版本优化: 窗体加载事件
        /// </summary>
        private void WebBrowserV2_Load(object sender, EventArgs e)
        {
            try
            {
                _loggingHelperV2.LogInfo("WebBrowserV2窗体开始加载");

                // 初始调整地址栏宽度 在窗体完全加载后执行，确保所有控件已正确布局
                BeginInvoke(new Action(() => WebBrowserV2_SizeChanged(this, EventArgs.Empty)));

                // V2版本优化: 异步安全的配置初始化
                InitializeConfigurationSafelyV2();

                // 移除默认标签页并加载配置的标签页
                LoadConfiguredTabsSafelyV2();

                _loggingHelperV2.LogInfo("WebBrowserV2窗体加载完成");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "WebBrowserV2窗体加载", true, true);
            }
        }

        /// <summary>
        /// V2版本优化: 窗体关闭前事件处理
        /// </summary>
        private void WebBrowserV2_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // 如果是真正关闭，直接返回
                if (_isRealClosing)
                    return;

                _loggingHelperV2.LogInfo("WebBrowserV2窗体开始关闭处理");

                // V2版本优化: 使用增强的窗体关闭处理器 注意：FormClosingHandlerV2没有HandleFormClosingSafely方法，使用事件处理机制
                bool shouldCancel = false;

                if (shouldCancel)
                {
                    e.Cancel = true;
                    _loggingHelperV2.LogInfo("WebBrowserV2窗体关闭被取消");
                }
                else
                {
                    _loggingHelperV2.LogInfo("WebBrowserV2窗体关闭处理完成");
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "WebBrowserV2窗体关闭处理", false, true);
                // 关闭处理异常时，允许窗体关闭
                e.Cancel = false;
            }
        }

        /// <summary>
        /// V2版本优化: 窗体关闭后事件处理
        /// </summary>
        private void WebBrowserV2_FormClosed(object sender, FormClosedEventArgs e)
        {
            try
            {
                if (_isRealClosing)
                {
                    _loggingHelperV2.LogInfo("WebBrowserV2开始执行真正关闭清理");

                    // V2版本优化: 异步安全的资源清理
                    CleanupResourcesSafelyV2();

                    // 触发真正关闭事件
                    RealClose?.Invoke(this, EventArgs.Empty);

                    _loggingHelperV2.LogInfo("WebBrowserV2真正关闭清理完成");
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "WebBrowserV2窗体关闭后处理", false, false);
            }
        }

        #endregion 窗体事件处理

        #region 定时器事件处理

        // 注意：MouseMonitorTimer_Tick方法已移至WebBrowserUIOperations.cs中实现

        #endregion 定时器事件处理

        #region 工具栏事件处理

        /// <summary>
        /// V2版本优化: 工具栏大小变化事件处理
        /// </summary>
        private void ToolStrip1_SizeChanged(object sender, EventArgs e)
        {
            // 调用窗体大小变化事件处理方法，复用代码
            WebBrowserV2_SizeChanged(sender, e);
        }

        /// <summary>
        /// V2版本优化: 后退按钮点击事件
        /// </summary>
        private void BackButton_Click(object sender, EventArgs e)
        {
            try
            {
                _loggingHelperV2.LogInfo("执行后退操作");
                string currentSectionId = GetCurrentTabSectionId();
                if (!string.IsNullOrEmpty(currentSectionId))
                {
                    _tabManagerV2.GoBackSafely(currentSectionId);
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "后退操作");
            }
        }

        /// <summary>
        /// V2版本优化: 前进按钮点击事件
        /// </summary>
        private void ForwardButton_Click(object sender, EventArgs e)
        {
            try
            {
                _loggingHelperV2.LogInfo("执行前进操作");
                string currentSectionId = GetCurrentTabSectionId();
                if (!string.IsNullOrEmpty(currentSectionId))
                {
                    _tabManagerV2.GoForwardSafely(currentSectionId);
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "前进操作");
            }
        }

        /// <summary>
        /// V2版本优化: 刷新按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, EventArgs e)
        {
            try
            {
                _loggingHelperV2.LogInfo("执行刷新操作");
                string currentSectionId = GetCurrentTabSectionId();
                if (!string.IsNullOrEmpty(currentSectionId))
                {
                    _tabManagerV2.RefreshPageSafely(currentSectionId);
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "刷新操作");
            }
        }

        /// <summary>
        /// V2版本优化: 停止按钮点击事件
        /// </summary>
        private void StopButton_Click(object sender, EventArgs e)
        {
            try
            {
                _loggingHelperV2.LogInfo("执行停止操作");
                string currentSectionId = GetCurrentTabSectionId();
                if (!string.IsNullOrEmpty(currentSectionId))
                {
                    _tabManagerV2.StopLoadingSafely(currentSectionId);
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "停止操作");
            }
        }

        /// <summary>
        /// V2版本优化: 主页按钮点击事件
        /// </summary>
        private void HomeButton_Click(object sender, EventArgs e)
        {
            try
            {
                _loggingHelperV2.LogInfo("执行回到主页操作");
                // 注意：WebBrowserTabManagerV2没有GoHomeSafely方法，使用导航到主页URL
                string currentSectionId = GetCurrentTabSectionId();
                if (!string.IsNullOrEmpty(currentSectionId))
                {
                    // 这里需要获取主页URL，暂时使用空字符串占位
                    string homeUrl = GetHomeUrl();
                    if (!string.IsNullOrEmpty(homeUrl))
                    {
                        _tabManagerV2.NavigateToUrlSafely(currentSectionId, homeUrl);
                    }
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "回到主页操作");
            }
        }

        /// <summary>
        /// V2版本优化: 完全退出按钮点击事件
        /// </summary>
        private void CompleteExitButton_Click(object sender, EventArgs e)
        {
            try
            {
                _loggingHelperV2.LogInfo("用户点击完全退出按钮");

                // 显示确认对话框
                DialogResult result = MessageBox.Show(
                    "确定要完全退出浏览器吗？这将停止所有后台会话维持。",
                    "确认退出",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _loggingHelperV2.LogInfo("用户确认完全退出");
                    _isRealClosing = true;
                    Close();
                }
                else
                {
                    _loggingHelperV2.LogInfo("用户取消完全退出");
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "完全退出操作");
            }
        }

        #endregion 工具栏事件处理

        #region 全局异常处理事件

        /// <summary>
        /// V2版本新增: 应用程序域未处理异常事件
        /// </summary>
        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            WebBrowserExceptionHandlerV2.HandleUnhandledException(sender, e);
        }

        /// <summary>
        /// V2版本新增: UI线程未处理异常事件
        /// </summary>
        private void OnThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            WebBrowserExceptionHandlerV2.HandleThreadException(sender, e);
        }

        #endregion 全局异常处理事件

        #region Cookie和缓存操作事件处理

        /// <summary>
        /// V2版本优化: 传送标签页按钮点击事件 - 复制Cookie配置文件到剪贴板
        /// </summary>
        private async void CopyCookiesConfigButton_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info(this, "传送标签页按钮被点击");

                // 显示正在处理的状态
                UpdateStatusSafely("正在获取并复制Cookie配置...");
                SetCursorSafely(Cursors.WaitCursor);

                // 获取当前标签页的WebView2控件
                WebView2 currentWebView = _tabManagerV2.GetCurrentWebView();
                if (currentWebView == null)
                {
                    ShowInfoMessage("当前没有活动的标签页");
                    return;
                }

                // 检查CoreWebView2是否已初始化
                if (currentWebView.CoreWebView2 == null)
                {
                    ShowWarningMessage("当前标签页尚未完全加载，请稍后再试");
                    return;
                }

                // 获取当前标签页的配置
                WebBrowserTabConfig currentConfig = _tabManagerV2.GetCurrentTabConfig();
                if (currentConfig == null)
                {
                    ShowWarningMessage("无法获取当前标签页配置");
                    return;
                }

                // 使用V2版本优化的实现逻辑，确保兼容性
                await GenerateAndCopyConfigurationAsync(currentWebView, currentConfig);

                ETLogManager.Info(this, "传送标签页操作完成");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "传送标签页", true);
                UpdateStatusSafely($"传送标签页失败: {ex.Message}");
            }
            finally
            {
                SetCursorSafely(Cursors.Default);
            }
        }

        /// <summary>
        /// V2版本优化: 粘贴标签页按钮点击事件 - 从剪贴板粘贴Cookie并更新
        /// </summary>
        private async void PasteCookiesButton_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info(this, "粘贴标签页按钮被点击");

                // 显示正在处理的状态
                UpdateStatusSafely("正在从剪贴板粘贴Cookie...");
                SetCursorSafely(Cursors.WaitCursor);

                // 获取当前标签页的WebView2控件
                WebView2 currentWebView = _tabManagerV2.GetCurrentWebView();
                if (currentWebView == null)
                {
                    ShowInfoMessage("当前没有活动的标签页");
                    return;
                }

                // 检查CoreWebView2是否已初始化
                if (currentWebView.CoreWebView2 == null)
                {
                    ShowWarningMessage("当前标签页尚未完全加载，请稍后再试");
                    return;
                }

                // 检查剪贴板内容
                if (!Clipboard.ContainsText())
                {
                    ShowInfoMessage("剪贴板中没有文本数据");
                    return;
                }

                // 使用V2版本优化的实现逻辑，确保兼容性
                await ProcessClipboardDataAsync(currentWebView);

                ETLogManager.Info(this, "粘贴标签页操作完成");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "粘贴标签页", true);
                UpdateStatusSafely($"粘贴标签页失败: {ex.Message}");
            }
            finally
            {
                SetCursorSafely(Cursors.Default);
            }
        }

        /// <summary>
        /// V2版本优化: 清理缓存按钮点击事件 - 清理WebView2缓存
        /// </summary>
        private void ClearCacheButton_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info(this, "清理缓存按钮被点击");

                // 获取缓存目录路径
                string webViewCacheFolder = Path.Combine(
                    Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location),
                    WebBrowserConstants.WEBVIEW_CACHE_FOLDER);

                // 检查缓存目录是否存在
                if (!Directory.Exists(webViewCacheFolder))
                {
                    ETLogManager.Info(this, $"缓存目录不存在: {webViewCacheFolder}");
                    ShowInfoMessage("浏览器缓存目录不存在，无需清理");
                    return;
                }

                // 显示确认对话框
                DialogResult result = ShowConfirmDialog(
                    "清理浏览器缓存将关闭所有标签页，您确定要继续吗？\n\n注意：清理缓存后，您需要重新登录所有网站。",
                    "确认清理缓存");

                if (result != DialogResult.Yes)
                {
                    return;
                }

                // 执行清理操作
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await ExecuteCacheClearingAsync(webViewCacheFolder);
                    }
                    catch (Exception ex)
                    {
                        WebBrowserExceptionHandlerV2.HandleAsyncException(this, ex, "清理缓存异步操作");
                    }
                });

                ETLogManager.Info(this, "清理缓存操作已启动");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "清理缓存", true);
            }
        }

        #endregion Cookie和缓存操作事件处理

        #region 地址栏和导航事件处理

        /// <summary>
        /// V2版本优化: 地址栏键盘事件
        /// </summary>
        private void UrlTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (e.KeyCode == Keys.Enter)
                {
                    _loggingHelperV2.LogInfo($"用户在地址栏输入URL: {urlTextBox.Text}");

                    // V2版本优化: 异步安全的导航操作
                    NavigateToUrlSafely(urlTextBox.Text);
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "地址栏导航");
            }
        }

        #endregion 地址栏和导航事件处理

        #region 标签页事件处理

        /// <summary>
        /// V2版本优化: TabControl选项卡切换事件
        /// </summary>
        private void TabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                _loggingHelperV2.LogInfo("标签页切换事件触发");

                // V2版本优化: 委托给TabManagerV2处理
                // 注意：WebBrowserTabManagerV2没有TabControl_SelectedIndexChangedSafely方法 需要手动处理标签页切换逻辑
                HandleTabSelectionChanged(sender, e);

                // 更新状态栏刷新状态信息
                UpdateStatusBarSafely();
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "标签页切换");
            }
        }

        /// <summary>
        /// V2版本优化: TabControl右键菜单事件
        /// </summary>
        private void TabControl1_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                if (e.Button == MouseButtons.Right && tabControl1.TabPages.Count > 0)
                {
                    _loggingHelperV2.LogInfo("显示标签页右键菜单");

                    // 使用预定义的tabContextMenu
                    if (tabContextMenu != null)
                    {
                        // V2版本优化: 安全的菜单显示
                        ShowTabContextMenuSafelyV2(e.Location);
                    }
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "显示标签页右键菜单");
            }
        }

        #endregion 标签页事件处理

        #region 状态栏和会话日志事件处理

        /// <summary>
        /// V2版本优化: StatusStrip鼠标进入事件
        /// </summary>
        private void StatusStrip1_MouseEnter(object sender, EventArgs e)
        {
            try
            {
                // 显示会话日志
                if (sessionLogTextBox != null && !sessionLogTextBox.IsDisposed &&
                    statusStrip1 != null && !statusStrip1.IsDisposed)
                {
                    ShowSessionLogSafelyV2();
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "显示会话日志");
            }
        }

        /// <summary>
        /// V2版本优化: StatusStrip鼠标离开事件
        /// </summary>
        private void StatusStrip1_MouseLeave(object sender, EventArgs e)
        {
            try
            {
                // 隐藏会话日志
                if (sessionLogTextBox != null)
                {
                    HideSessionLogSafelyV2();
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "隐藏会话日志");
            }
        }

        /// <summary>
        /// V2版本优化: SessionLogTextBox鼠标离开事件
        /// </summary>
        private void SessionLogTextBox_MouseLeave(object sender, EventArgs e)
        {
            try
            {
                // 隐藏会话日志
                if (sessionLogTextBox != null)
                {
                    HideSessionLogWithDelayCheckV2();
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "会话日志鼠标离开处理");
            }
        }

        #endregion 状态栏和会话日志事件处理

        #region 辅助方法

        /// <summary>
        /// V2版本新增: 异步安全的导航操作（已移除重复定义，使用下方的优化版本）
        /// </summary>
        // 注释：此方法已被下方的优化版本替代，避免CS0121二义性错误

        /// <summary>
        /// V2版本新增: 安全更新状态栏
        /// </summary>
        private void UpdateStatusBarSafely()
        {
            try
            {
                if (tabControl1.SelectedTab != null)
                {
                    string sectionId = tabControl1.SelectedTab.Name;
                    // 使用TabManager获取标签页数据
                    var tabData = _tabManager.GetTabDataBySectionId(sectionId);
                    if (tabData != null)
                    {
                        // V2版本优化: 使用UIHelper安全更新状态栏
                        WebBrowserUIHelperV2.UpdateRefreshStatusLabelsSafely(statusStrip1, tabData.Config);
                    }
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "更新状态栏", false, false);
            }
        }

        /// <summary>
        /// V2版本新增: 获取当前标签页的SectionId（已移除重复定义，使用下方的优化版本）
        /// </summary>
        // 注释：此方法已被下方的优化版本替代，避免CS0121二义性错误

        /// <summary>
        /// V2版本新增: 获取主页URL（已移除重复定义，使用下方的优化版本）
        /// </summary>
        // 注释：此方法已被下方的优化版本替代，避免CS0121二义性错误

        /// <summary>
        /// V2版本新增: 处理标签页选择变更
        /// </summary>
        private void HandleTabSelectionChanged(object sender, EventArgs e)
        {
            try
            {
                // 这里应该实现标签页切换的具体逻辑
                _loggingHelperV2.LogInfo("处理标签页选择变更");
                UpdateStatusBarSafely();
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "处理标签页选择变更");
            }
        }

        #endregion 辅助方法

        #region V2版本新增方法占位符

        /// <summary>
        /// V2版本新增: 安全初始化配置
        /// </summary>
        private void InitializeConfigurationSafelyV2()
        {
            try
            {
                _loggingHelperV2.LogInfo("开始初始化配置");

                // 异步初始化配置管理器
                _ = Task.Run(async () =>
                {
                    try
                    {
                        bool success = await _configManagerV2.InitializeAsync();
                        if (success)
                        {
                            ETLogManager.Info(this, "配置管理器初始化成功");

                            // 在UI线程上标记菜单需要刷新
                            BeginInvoke(new Action(() => _tabMenuManagerV2?.MarkForRefresh()));
                        }
                        else
                        {
                            ETLogManager.Warning(this, "配置管理器初始化失败");
                        }
                    }
                    catch (Exception ex)
                    {
                        WebBrowserExceptionHandlerV2.HandleAsyncException(this, ex, "异步初始化配置");
                    }
                });
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "初始化配置");
            }
        }

        /// <summary>
        /// V2版本新增: 安全加载配置的标签页
        /// </summary>
        private void LoadConfiguredTabsSafelyV2()
        {
            try
            {
                _loggingHelperV2.LogInfo("开始加载配置的标签页");

                // 移除默认标签页
                tabControl1.TabPages.Clear();

                // 注册所有事件处理程序，确保事件只注册一次
                RegisterMenuEventHandlers();

                // 初始化标签页右键菜单
                InitializeTabContextMenuV2();

                // 导航按钮状态默认为禁用，直到标签页创建并初始化完成
                WebBrowserUIHelper.UpdateNavigationButtonState(toolStrip1, false);

                // 标签页下拉菜单始终可用
                tabsDropDownButton.Enabled = true;

                // 设置默认窗口标题
                Text = "浏览器V2";

                // 初始化状态栏信息
                WebBrowserUIHelper.UpdateRefreshStatusLabels(statusStrip1, null);

                // 初始化标签页下拉菜单
                InitializeTabsDropDownMenu();

                _loggingHelperV2.LogInfo("配置的标签页加载完成");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "加载配置的标签页");
            }
        }

        /// <summary>
        /// V2版本新增: 安全清理资源
        /// </summary>
        private void CleanupResourcesSafelyV2()
        {
            try
            {
                _loggingHelperV2.LogInfo("开始清理资源");
                // 这里应该实现资源清理逻辑
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "清理资源");
            }
        }

        /// <summary>
        /// V2版本新增: 初始化标签页右键菜单
        /// </summary>
        private void InitializeTabContextMenuV2()
        {
            try
            {
                _loggingHelperV2.LogInfo("初始化标签页右键菜单");
                // 使用Designer中定义的tabContextMenu，确保菜单项正确绑定
                if (tabContextMenu != null)
                {
                    ETLogManager.Info(this, "标签页右键菜单初始化完成");
                }
                else
                {
                    ETLogManager.Warning(this, "标签页右键菜单未找到，请检查Designer.cs");
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "初始化标签页右键菜单");
            }
        }

        /// <summary>
        /// V2版本新增: 安全显示标签页右键菜单
        /// </summary>
        private void ShowTabContextMenuSafelyV2(Point location)
        {
            try
            {
                _loggingHelperV2.LogInfo("显示标签页右键菜单");
                // 这里应该实现显示右键菜单逻辑
                if (tabContextMenu != null)
                {
                    tabContextMenu.Show(tabControl1, location);
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "显示标签页右键菜单");
            }
        }

        /// <summary>
        /// V2版本新增: 安全显示会话日志
        /// </summary>
        private void ShowSessionLogSafelyV2()
        {
            try
            {
                _loggingHelperV2.LogInfo("显示会话日志");
                // 这里应该实现显示会话日志逻辑
                if (sessionLogTextBox != null && !sessionLogTextBox.IsDisposed)
                {
                    sessionLogTextBox.Visible = true;
                    sessionLogTextBox.BringToFront();
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "显示会话日志");
            }
        }

        /// <summary>
        /// V2版本新增: 安全隐藏会话日志
        /// </summary>
        private void HideSessionLogSafelyV2()
        {
            try
            {
                _loggingHelperV2.LogInfo("隐藏会话日志");
                // 这里应该实现隐藏会话日志逻辑
                if (sessionLogTextBox != null && !sessionLogTextBox.IsDisposed)
                {
                    sessionLogTextBox.Visible = false;
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "隐藏会话日志");
            }
        }

        /// <summary>
        /// V2版本新增: 延迟检查隐藏会话日志
        /// </summary>
        private void HideSessionLogWithDelayCheckV2()
        {
            try
            {
                _loggingHelperV2.LogInfo("延迟检查隐藏会话日志");
                // 这里应该实现延迟检查隐藏会话日志逻辑
                if (_mouseMonitorTimer != null)
                {
                    _mouseMonitorTimer.Start();
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, "延迟检查隐藏会话日志");
            }
        }

        /// <summary>
        /// V2版本优化：清理JSON字符串
        /// </summary>
        /// <param name="json">原始JSON字符串</param>
        /// <returns>清理后的JSON字符串</returns>
        private string CleanJsonStringV2(string json)
        {
            if (string.IsNullOrEmpty(json))
                return json;

            // 移除BOM标记
            if (json.StartsWith("\uFEFF"))
                json = json.Substring(1);

            // 移除首尾空白字符
            json = json.Trim();

            // 移除可能的尾部逗号和其他无效字符
            while (json.EndsWith(",") || json.EndsWith(";") || json.EndsWith("\0"))
            {
                json = json.Substring(0, json.Length - 1).Trim();
            }

            return json;
        }

        /// <summary>
        /// V2版本优化：解析登录信息数据
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>登录信息数据</returns>
        private Task<ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.LoginInfoData> ParseLoginInfoDataAsync(string json)
        {
            try
            {
                // 首先尝试解析为标准格式
                var loginInfoData = ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.ParseLoginInfoJson(json);
                if (loginInfoData?.Cookies != null && loginInfoData.Cookies.Count > 0)
                {
                    ETLogManager.Info(this, $"V2优化：成功解析标准格式，Cookie数量: {loginInfoData.Cookies.Count}");
                    return Task.FromResult(loginInfoData);
                }

                // 尝试解析为其他格式并转换
                var dynamicData = JsonConvert.DeserializeObject<dynamic>(json);
                if (dynamicData != null)
                {
                    var convertedData = new ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.LoginInfoData();

                    // 提取URL
                    if (dynamicData.url != null)
                        convertedData.Url = dynamicData.url.ToString();
                    else if (dynamicData.Url != null)
                        convertedData.Url = dynamicData.Url.ToString();

                    // 提取Headers
                    if (dynamicData.headers != null)
                    {
                        var headers = JsonConvert.DeserializeObject<Dictionary<string, object>>(dynamicData.headers.ToString());
                        convertedData.Headers = headers ?? new Dictionary<string, object>();
                    }
                    else if (dynamicData.Headers != null)
                    {
                        var headers = JsonConvert.DeserializeObject<Dictionary<string, object>>(dynamicData.Headers.ToString());
                        convertedData.Headers = headers ?? new Dictionary<string, object>();
                    }

                    // 提取Cookies
                    if (dynamicData.cookies != null)
                    {
                        var cookies = JsonConvert.DeserializeObject<List<ET.ETLoginWebBrowser.CookieItem>>(dynamicData.cookies.ToString());
                        convertedData.Cookies = cookies ?? new List<ET.ETLoginWebBrowser.CookieItem>();
                    }
                    else if (dynamicData.Cookies != null)
                    {
                        var cookies = JsonConvert.DeserializeObject<List<ET.ETLoginWebBrowser.CookieItem>>(dynamicData.Cookies.ToString());
                        convertedData.Cookies = cookies ?? new List<ET.ETLoginWebBrowser.CookieItem>();
                    }

                    if (convertedData.Cookies?.Count > 0)
                    {
                        ETLogManager.Info(this, $"V2优化：成功转换其他格式，Cookie数量: {convertedData.Cookies.Count}");
                        return Task.FromResult(convertedData);
                    }
                }

                return Task.FromResult<ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.LoginInfoData>(null);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：解析登录信息数据异常: {ex.Message}");
                return Task.FromResult<ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.LoginInfoData>(null);
            }
        }

        /// <summary>
        /// V2版本优化：设置Cookie到WebView2
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="loginInfoData">登录信息数据</param>
        private Task<bool> SetCookiesToWebViewAsync(WebView2 webView, ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.LoginInfoData loginInfoData)
        {
            try
            {
                if (loginInfoData?.Cookies == null || loginInfoData.Cookies.Count == 0)
                {
                    ETLogManager.Warning(this, "V2优化：没有Cookie数据需要设置");
                    return Task.FromResult(false);
                }

                ETLogManager.Info(this, $"V2优化：开始设置{loginInfoData.Cookies.Count}个Cookie到WebView2");

                foreach (var cookieItem in loginInfoData.Cookies)
                {
                    try
                    {
                        var cookie = webView.CoreWebView2.CookieManager.CreateCookie(
                            cookieItem.Name,
                            cookieItem.Value,
                            cookieItem.Domain,
                            cookieItem.Path);

                        cookie.IsSecure = cookieItem.Secure;
                        cookie.IsHttpOnly = cookieItem.HttpOnly;

                        if (cookieItem.Expires > DateTime.MinValue)
                        {
                            // 直接设置DateTime，WebView2会自动处理
                            cookie.Expires = cookieItem.Expires.ToUniversalTime();
                        }

                        // 设置SameSite属性
                        if (!string.IsNullOrEmpty(cookieItem.SameSite))
                        {
                            switch (cookieItem.SameSite.ToLower())
                            {
                                case "strict":
                                    cookie.SameSite = CoreWebView2CookieSameSiteKind.Strict;
                                    break;

                                case "lax":
                                    cookie.SameSite = CoreWebView2CookieSameSiteKind.Lax;
                                    break;

                                case "none":
                                default:
                                    cookie.SameSite = CoreWebView2CookieSameSiteKind.None;
                                    break;
                            }
                        }

                        webView.CoreWebView2.CookieManager.AddOrUpdateCookie(cookie);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(this, $"V2优化：设置Cookie失败: {cookieItem.Name}, {ex.Message}");
                    }
                }

                ETLogManager.Info(this, "V2优化：Cookie设置完成");
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：设置Cookie到WebView2失败: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// V2版本优化：导航到指定URL（已移除重复定义，使用WebBrowserCookieOperations.cs中的版本）
        /// </summary>
        // 注释：此方法已在WebBrowserCookieOperations.cs中定义，避免CS0121二义性错误

        /// <summary>
        /// V2版本优化：更新地址栏显示（已移除重复定义，使用WebBrowserCookieOperations.cs中的版本）
        /// </summary>
        // 注释：此方法已在WebBrowserCookieOperations.cs中定义，避免CS0121二义性错误

        /// <summary>
        /// V2版本优化：更新地址栏（UI线程）
        /// </summary>
        /// <param name="url">URL地址</param>
        private void UpdateAddressBar(string url)
        {
            try
            {
                // 直接使用类成员变量，避免控件查找类型转换错误 var urlTextBox = this.Controls.Find("urlTextBox",
                // true).FirstOrDefault() as ToolStripTextBox;
                if (this.urlTextBox != null)
                {
                    this.urlTextBox.Text = url;
                    ETLogManager.Debug(this, $"V2优化：地址栏已更新: {url}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：更新地址栏控件失败: {ex.Message}");
            }
        }

        #endregion V2版本新增方法占位符

        #region 缓存清理功能

        /// <summary>
        /// V2版本优化：安全关闭所有标签页（已移除重复定义，使用WebBrowserCacheOperations.cs中的版本）
        /// </summary>
        // 注释：此方法已在WebBrowserCacheOperations.cs中定义，避免CS0121二义性错误

        /// <summary>
        /// 内部关闭所有标签页的实现
        /// </summary>
        private async Task CloseAllTabsInternal()
        {
            try
            {
                // 获取所有标签页配置
                var allConfigs = _tabManagerV2.GetAllTabConfigsSafely();
                foreach (var config in allConfigs)
                {
                    await _tabManagerV2.CloseTabAsync(config.SectionId);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"关闭所有标签页内部实现失败: {ex.Message}");
            }
        }

        /// <summary>
        /// V2版本优化：安全清空TabControl
        /// </summary>
        private void ClearTabControlSafely()
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        var tabControl = this.Controls.Find("tabControl1", true).FirstOrDefault() as TabControl;
                        if (tabControl != null)
                        {
                            tabControl.TabPages.Clear();
                            ETLogManager.Info(this, "V2优化：TabControl已清空");
                        }
                    }));
                }
                else
                {
                    var tabControl = this.Controls.Find("tabControl1", true).FirstOrDefault() as TabControl;
                    if (tabControl != null)
                    {
                        tabControl.TabPages.Clear();
                        ETLogManager.Info(this, "V2优化：TabControl已清空");
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：清空TabControl失败: {ex.Message}");
            }
        }

        /// <summary>
        /// V2版本优化：清理WebView2缓存
        /// </summary>
        /// <param name="cacheFolder">缓存目录</param>
        /// <returns>清理结果</returns>
        private async Task<CacheClearingResult> ClearWebView2CacheAsync(string cacheFolder)
        {
            var result = new CacheClearingResult();

            try
            {
                ETLogManager.Info(this, $"V2优化：开始清理缓存目录: {cacheFolder}");

                if (!Directory.Exists(cacheFolder))
                {
                    result.Success = true;
                    result.Message = "缓存目录不存在，无需清理";
                    return result;
                }

                // 获取所有文件和目录
                var files = Directory.GetFiles(cacheFolder, "*", SearchOption.AllDirectories);
                var directories = Directory.GetDirectories(cacheFolder, "*", SearchOption.AllDirectories);

                result.TotalFiles = files.Length;
                result.TotalDirectories = directories.Length;

                // 并行删除文件
                int deletedFiles = 0;
                int failedFiles = 0;
                await Task.Run(() =>
                {
                    Parallel.ForEach(files, file =>
                    {
                        try
                        {
                            File.SetAttributes(file, FileAttributes.Normal);
                            File.Delete(file);
                            Interlocked.Increment(ref deletedFiles);
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Warning(this, $"V2优化：删除文件失败: {file}, {ex.Message}");
                            Interlocked.Increment(ref failedFiles);
                        }
                    });
                });

                result.DeletedFiles = deletedFiles;
                result.FailedFiles = failedFiles;

                // 删除目录（从最深层开始）
                var sortedDirectories = directories.OrderByDescending(d => d.Length).ToArray();
                foreach (var directory in sortedDirectories)
                {
                    try
                    {
                        if (Directory.Exists(directory))
                        {
                            Directory.Delete(directory, false);
                            result.DeletedDirectories++;
                        }
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Warning(this, $"V2优化：删除目录失败: {directory}, {ex.Message}");
                        result.FailedDirectories++;
                    }
                }

                // 尝试删除根目录
                try
                {
                    if (Directory.Exists(cacheFolder) && !Directory.EnumerateFileSystemEntries(cacheFolder).Any())
                    {
                        Directory.Delete(cacheFolder);
                        ETLogManager.Info(this, "V2优化：根缓存目录已删除");
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Warning(this, $"V2优化：删除根缓存目录失败: {ex.Message}");
                }

                result.Success = true;
                result.Message = $"清理完成：删除{result.DeletedFiles}个文件，{result.DeletedDirectories}个目录";

                ETLogManager.Info(this, $"V2优化：{result.Message}");
                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"清理失败: {ex.Message}";
                ETLogManager.Error(this, $"V2优化：清理WebView2缓存失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// V2版本优化：显示清理结果
        /// </summary>
        /// <param name="result">清理结果</param>
        private void ShowClearingResult(CacheClearingResult result)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => ShowClearingResultInternal(result)));
                }
                else
                {
                    ShowClearingResultInternal(result);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：显示清理结果失败: {ex.Message}");
            }
        }

        /// <summary>
        /// V2版本优化：显示清理结果（内部方法）
        /// </summary>
        /// <param name="result">清理结果</param>
        private void ShowClearingResultInternal(CacheClearingResult result)
        {
            string title = result.Success ? "清理完成" : "清理失败";
            MessageBoxIcon icon = result.Success ? MessageBoxIcon.Information : MessageBoxIcon.Warning;

            string detailMessage = result.Success ?
                $"{result.Message}\n\n详细统计：\n" +
                $"• 文件：{result.DeletedFiles}/{result.TotalFiles} 删除成功\n" +
                $"• 目录：{result.DeletedDirectories}/{result.TotalDirectories} 删除成功\n" +
                $"• 失败：{result.FailedFiles + result.FailedDirectories} 项" :
                result.Message;

            MessageBox.Show(detailMessage, title, MessageBoxButtons.OK, icon);
            UpdateStatusSafely(result.Message);
        }

        /// <summary>
        /// V2版本优化：刷新菜单状态（已移除重复定义，使用WebBrowserCacheOperations.cs中的版本）
        /// </summary>
        // 注释：此方法已在WebBrowserCacheOperations.cs中定义，避免CS0121二义性错误

        #endregion 缓存清理功能

        #region 数据模型类

        /// <summary>
        /// 缓存清理结果
        /// </summary>
        private class CacheClearingResult
        {
            /// <summary>
            /// 是否成功
            /// </summary>
            public bool Success { get; set; }

            /// <summary>
            /// 结果消息
            /// </summary>
            public string Message { get; set; } = string.Empty;

            /// <summary>
            /// 总文件数
            /// </summary>
            public int TotalFiles { get; set; }

            /// <summary>
            /// 总目录数
            /// </summary>
            public int TotalDirectories { get; set; }

            /// <summary>
            /// 已删除文件数
            /// </summary>
            public int DeletedFiles { get; set; }

            /// <summary>
            /// 已删除目录数
            /// </summary>
            public int DeletedDirectories { get; set; }

            /// <summary>
            /// 失败文件数
            /// </summary>
            public int FailedFiles { get; set; }

            /// <summary>
            /// 失败目录数
            /// </summary>
            public int FailedDirectories { get; set; }
        }

        /// <summary>
        /// 性能监控令牌
        /// </summary>
        private class PerformanceToken
        {
            /// <summary>
            /// 操作名称
            /// </summary>
            public string Operation { get; }

            /// <summary>
            /// 开始时间
            /// </summary>
            public DateTime StartTime { get; }

            /// <summary>
            /// 秒表
            /// </summary>
            private readonly Stopwatch _stopwatch;

            /// <summary>
            /// 构造函数
            /// </summary>
            /// <param name="operation">操作名称</param>
            public PerformanceToken(string operation)
            {
                Operation = operation;
                StartTime = DateTime.Now;
                _stopwatch = Stopwatch.StartNew();
            }

            /// <summary>
            /// 停止计时
            /// </summary>
            /// <returns>耗时</returns>
            public TimeSpan Stop()
            {
                _stopwatch.Stop();
                return _stopwatch.Elapsed;
            }
        }

        /// <summary>
        /// 性能指标
        /// </summary>
        private class PerformanceMetrics
        {
            /// <summary>
            /// 操作名称
            /// </summary>
            public string Operation { get; }

            /// <summary>
            /// 总操作次数
            /// </summary>
            public int TotalOperations { get; private set; }

            /// <summary>
            /// 成功操作次数
            /// </summary>
            public int SuccessfulOperations { get; private set; }

            /// <summary>
            /// 成功率
            /// </summary>
            public double SuccessRate => TotalOperations > 0 ? (double)SuccessfulOperations / TotalOperations : 0;

            /// <summary>
            /// 总耗时
            /// </summary>
            public TimeSpan TotalTime { get; private set; }

            /// <summary>
            /// 平均耗时（毫秒）
            /// </summary>
            public double AverageTime => TotalOperations > 0 ? TotalTime.TotalMilliseconds / TotalOperations : 0;

            /// <summary>
            /// 最大耗时（毫秒）
            /// </summary>
            public double MaxTime { get; private set; }

            /// <summary>
            /// 最小耗时（毫秒）
            /// </summary>
            public double MinTime { get; private set; } = double.MaxValue;

            /// <summary>
            /// 构造函数
            /// </summary>
            /// <param name="operation">操作名称</param>
            public PerformanceMetrics(string operation)
            {
                Operation = operation;
            }

            /// <summary>
            /// 开始操作
            /// </summary>
            public void StartOperation()
            {
                // 预留方法，用于未来扩展
            }

            /// <summary>
            /// 结束操作
            /// </summary>
            /// <param name="elapsed">耗时</param>
            /// <param name="success">是否成功</param>
            public void EndOperation(TimeSpan elapsed, bool success)
            {
                TotalOperations++;
                if (success)
                {
                    SuccessfulOperations++;
                }

                TotalTime = TotalTime.Add(elapsed);

                double elapsedMs = elapsed.TotalMilliseconds;
                if (elapsedMs > MaxTime)
                {
                    MaxTime = elapsedMs;
                }

                if (elapsedMs < MinTime)
                {
                    MinTime = elapsedMs;
                }
            }
        }

        /// <summary>
        /// 缓存的Cookie数据
        /// </summary>
        private class CachedCookieData
        {
            /// <summary>
            /// Cookie数据
            /// </summary>
            public ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.LoginInfoData Data { get; set; }

            /// <summary>
            /// 缓存时间
            /// </summary>
            public DateTime CacheTime { get; set; }
        }

        #endregion 数据模型类

        #region 导航功能辅助方法

        /// <summary>
        /// V2版本优化：安全导航到指定URL
        /// </summary>
        /// <param name="url">目标URL</param>
        private void NavigateToUrlSafely(string url)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(url))
                {
                    ETLogManager.Warning(this, "V2优化：导航URL为空");
                    return;
                }

                // 确保URL格式正确
                url = NormalizeUrl(url);

                ETLogManager.Info(this, $"V2优化：开始导航到URL: {url}");

                string currentSectionId = GetCurrentTabSectionId();
                if (!string.IsNullOrEmpty(currentSectionId))
                {
                    _tabManagerV2.NavigateToUrlSafely(currentSectionId, url);

                    // 更新地址栏显示
                    UpdateAddressBarSafely(url);

                    ETLogManager.Info(this, $"V2优化：导航命令已发送: {url}");
                }
                else
                {
                    ETLogManager.Warning(this, "V2优化：无法获取当前标签页ID，导航失败");
                    ShowWarningMessage("当前没有活动的标签页");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：导航到URL失败: {url}, {ex.Message}");
                ShowErrorMessage($"导航失败: {ex.Message}");
            }
        }

        /// <summary>
        /// V2版本优化：规范化URL格式
        /// </summary>
        /// <param name="url">原始URL</param>
        /// <returns>规范化后的URL</returns>
        private string NormalizeUrl(string url)
        {
            try
            {
                url = url.Trim();

                // 如果不包含协议，默认添加https://
                if (!url.StartsWith("http://", StringComparison.OrdinalIgnoreCase) &&
                    !url.StartsWith("https://", StringComparison.OrdinalIgnoreCase) &&
                    !url.StartsWith("file://", StringComparison.OrdinalIgnoreCase) &&
                    !url.StartsWith("about:", StringComparison.OrdinalIgnoreCase))
                {
                    url = "https://" + url;
                }

                // 验证URL格式
                if (Uri.TryCreate(url, UriKind.Absolute, out Uri validUri))
                {
                    return validUri.ToString();
                }

                ETLogManager.Warning(this, $"V2优化：URL格式无效: {url}");
                return url; // 返回原始URL，让WebView2处理
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：规范化URL失败: {url}, {ex.Message}");
                return url;
            }
        }

        /// <summary>
        /// V2版本优化：获取主页URL
        /// </summary>
        /// <returns>主页URL</returns>
        private string GetHomeUrl()
        {
            try
            {
                // 尝试从当前标签页配置获取主页URL
                string currentSectionId = GetCurrentTabSectionId();
                if (!string.IsNullOrEmpty(currentSectionId))
                {
                    var currentConfig = _tabManagerV2.GetTabConfig(currentSectionId);
                    if (currentConfig != null && !string.IsNullOrEmpty(currentConfig.Url))
                    {
                        ETLogManager.Info(this, $"V2优化：使用当前标签页配置URL作为主页: {currentConfig.Url}");
                        return currentConfig.Url;
                    }
                }

                // 默认主页URL
                string defaultHomeUrl = "https://www.baidu.com";
                ETLogManager.Info(this, $"V2优化：使用默认主页URL: {defaultHomeUrl}");
                return defaultHomeUrl;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：获取主页URL失败: {ex.Message}");
                return "https://www.baidu.com";
            }
        }

        /// <summary>
        /// V2版本优化：获取当前标签页SectionId
        /// </summary>
        /// <returns>当前标签页的SectionId</returns>
        private string GetCurrentTabSectionId()
        {
            try
            {
                if (tabControl1?.SelectedTab?.Tag is string sectionId)
                {
                    return sectionId;
                }

                ETLogManager.Warning(this, "V2优化：无法获取当前标签页SectionId");
                return string.Empty;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：获取当前标签页SectionId失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// V2版本优化：检查导航功能可用性
        /// </summary>
        /// <returns>导航功能是否可用</returns>
        private bool IsNavigationAvailable()
        {
            try
            {
                string currentSectionId = GetCurrentTabSectionId();
                if (string.IsNullOrEmpty(currentSectionId))
                {
                    return false;
                }

                var currentWebView = _tabManagerV2.GetCurrentWebView();
                return currentWebView?.CoreWebView2 != null;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：检查导航功能可用性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// V2版本优化：更新导航按钮状态
        /// </summary>
        private void UpdateNavigationButtonsState()
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(UpdateNavigationButtonsStateInternal));
                }
                else
                {
                    UpdateNavigationButtonsStateInternal();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：更新导航按钮状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// V2版本优化：更新导航按钮状态（内部方法）
        /// </summary>
        private void UpdateNavigationButtonsStateInternal()
        {
            try
            {
                bool navigationAvailable = IsNavigationAvailable();

                // 直接使用类成员变量，避免控件查找类型转换错误 var backButton = this.Controls.Find("backButton",
                // true).FirstOrDefault() as ToolStripButton; var forwardButton =
                // this.Controls.Find("forwardButton", true).FirstOrDefault() as ToolStripButton;
                // var refreshButton = this.Controls.Find("refreshButton", true).FirstOrDefault() as
                // ToolStripButton; var stopButton = this.Controls.Find("stopButton",
                // true).FirstOrDefault() as ToolStripButton; var homeButton =
                // this.Controls.Find("homeButton", true).FirstOrDefault() as ToolStripButton;

                if (this.backButton != null) this.backButton.Enabled = navigationAvailable;
                if (this.forwardButton != null) this.forwardButton.Enabled = navigationAvailable;
                if (this.refreshButton != null) this.refreshButton.Enabled = navigationAvailable;
                if (this.stopButton != null) this.stopButton.Enabled = navigationAvailable;
                if (this.homeButton != null) this.homeButton.Enabled = navigationAvailable;

                ETLogManager.Debug(this, $"V2优化：导航按钮状态已更新，可用性: {navigationAvailable}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：更新导航按钮状态内部方法失败: {ex.Message}");
            }
        }

        #endregion 导航功能辅助方法

        #region 异常处理机制完善

        /// <summary>
        /// V2版本优化：增强的异常处理包装器
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="action">要执行的操作</param>
        /// <param name="showToUser">是否显示给用户</param>
        /// <param name="isCritical">是否为严重错误</param>
        /// <returns>操作是否成功</returns>
        private bool SafeExecute(string operation, Action action, bool showToUser = false, bool isCritical = false)
        {
            try
            {
                action?.Invoke();
                return true;
            }
            catch (ObjectDisposedException ex)
            {
                ETLogManager.Warning(this, $"V2优化：{operation}失败，对象已释放: {ex.Message}");
                return false;
            }
            catch (InvalidOperationException ex)
            {
                ETLogManager.Warning(this, $"V2优化：{operation}失败，操作无效: {ex.Message}");
                if (showToUser)
                {
                    ShowWarningMessage($"{operation}失败：当前状态不允许此操作");
                }
                return false;
            }
            catch (UnauthorizedAccessException ex)
            {
                ETLogManager.Error(this, $"V2优化：{operation}失败，权限不足: {ex.Message}");
                if (showToUser)
                {
                    ShowErrorMessage($"{operation}失败：权限不足");
                }
                return false;
            }
            catch (TimeoutException ex)
            {
                ETLogManager.Error(this, $"V2优化：{operation}超时: {ex.Message}");
                if (showToUser)
                {
                    ShowWarningMessage($"{operation}超时，请稍后重试");
                }
                return false;
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, operation, showToUser, isCritical);
                return false;
            }
        }

        /// <summary>
        /// V2版本优化：异步操作的异常处理包装器
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="asyncAction">要执行的异步操作</param>
        /// <param name="showToUser">是否显示给用户</param>
        /// <param name="context">操作上下文</param>
        /// <returns>操作是否成功</returns>
        private async Task<bool> SafeExecuteAsync(string operation, Func<Task> asyncAction, bool showToUser = false, string context = null)
        {
            try
            {
                if (asyncAction != null)
                {
                    await asyncAction();
                }
                return true;
            }
            catch (TaskCanceledException ex)
            {
                ETLogManager.Warning(this, $"V2优化：{operation}被取消: {ex.Message}");
                return false;
            }
            catch (ObjectDisposedException ex)
            {
                ETLogManager.Warning(this, $"V2优化：{operation}失败，对象已释放: {ex.Message}");
                return false;
            }
            catch (InvalidOperationException ex)
            {
                ETLogManager.Warning(this, $"V2优化：{operation}失败，操作无效: {ex.Message}");
                if (showToUser)
                {
                    ShowWarningMessage($"{operation}失败：当前状态不允许此操作");
                }
                return false;
            }
            catch (TimeoutException ex)
            {
                ETLogManager.Error(this, $"V2优化：{operation}超时: {ex.Message}");
                if (showToUser)
                {
                    ShowWarningMessage($"{operation}超时，请稍后重试");
                }
                return false;
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleAsyncException(this, ex, operation, showToUser, context);
                return false;
            }
        }

        /// <summary>
        /// V2版本优化：带返回值的安全执行
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="operation">操作名称</param>
        /// <param name="func">要执行的函数</param>
        /// <param name="defaultValue">默认返回值</param>
        /// <param name="showToUser">是否显示给用户</param>
        /// <returns>执行结果或默认值</returns>
        private T SafeExecute<T>(string operation, Func<T> func, T defaultValue = default(T), bool showToUser = false)
        {
            try
            {
                return func != null ? func() : defaultValue;
            }
            catch (ObjectDisposedException ex)
            {
                ETLogManager.Warning(this, $"V2优化：{operation}失败，对象已释放: {ex.Message}");
                return defaultValue;
            }
            catch (InvalidOperationException ex)
            {
                ETLogManager.Warning(this, $"V2优化：{operation}失败，操作无效: {ex.Message}");
                if (showToUser)
                {
                    ShowWarningMessage($"{operation}失败：当前状态不允许此操作");
                }
                return defaultValue;
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, operation, showToUser, false);
                return defaultValue;
            }
        }

        /// <summary>
        /// V2版本优化：WebView2操作的专用异常处理
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="webViewAction">WebView2操作</param>
        /// <param name="showToUser">是否显示给用户</param>
        /// <returns>操作是否成功</returns>
        private bool SafeExecuteWebView2(string operation, Action<WebView2> webViewAction, bool showToUser = false)
        {
            try
            {
                var currentWebView = _tabManagerV2?.GetCurrentWebView();
                if (currentWebView == null)
                {
                    ETLogManager.Warning(this, $"V2优化：{operation}失败，当前没有WebView2控件");
                    if (showToUser)
                    {
                        ShowWarningMessage("当前没有活动的标签页");
                    }
                    return false;
                }

                if (currentWebView.CoreWebView2 == null)
                {
                    ETLogManager.Warning(this, $"V2优化：{operation}失败，WebView2未初始化");
                    if (showToUser)
                    {
                        ShowWarningMessage("当前标签页尚未完全加载，请稍后再试");
                    }
                    return false;
                }

                webViewAction?.Invoke(currentWebView);
                return true;
            }
            catch (COMException ex)
            {
                WebBrowserExceptionHandlerV2.HandleWebView2Exception(this, ex, operation, showToUser);
                return false;
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, $"WebView2操作[{operation}]", showToUser, false);
                return false;
            }
        }

        /// <summary>
        /// V2版本优化：线程安全的UI操作异常处理
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="uiAction">UI操作</param>
        /// <param name="showToUser">是否显示给用户</param>
        /// <returns>操作是否成功</returns>
        private bool SafeExecuteUI(string operation, Action uiAction, bool showToUser = false)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => SafeExecuteUIInternal(operation, uiAction, showToUser)));
                }
                else
                {
                    SafeExecuteUIInternal(operation, uiAction, showToUser);
                }
                return true;
            }
            catch (ObjectDisposedException ex)
            {
                ETLogManager.Warning(this, $"V2优化：UI操作[{operation}]失败，窗体已释放: {ex.Message}");
                return false;
            }
            catch (InvalidOperationException ex)
            {
                ETLogManager.Warning(this, $"V2优化：UI操作[{operation}]失败，跨线程调用: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, $"UI操作[{operation}]");
                return false;
            }
        }

        /// <summary>
        /// V2版本优化：UI操作内部实现
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="uiAction">UI操作</param>
        /// <param name="showToUser">是否显示给用户</param>
        private void SafeExecuteUIInternal(string operation, Action uiAction, bool showToUser)
        {
            try
            {
                uiAction?.Invoke();
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleUIException(this, ex, operation);
            }
        }

        #endregion 异常处理机制完善

        #region UI状态管理优化

        /// <summary>
        /// V2版本优化：安全设置光标样式（已移除重复定义，使用WebBrowserUIOperations.cs中的版本）
        /// </summary>
        // 注释：此方法已在WebBrowserUIOperations.cs中定义，避免CS0121二义性错误

        /// <summary>
        /// V2版本优化：安全更新状态栏（已移除重复定义，使用WebBrowserUIOperations.cs中的版本）
        /// </summary>
        // 注释：此方法已在WebBrowserUIOperations.cs中定义，避免CS0121二义性错误

        /// <summary>
        /// V2版本优化：安全显示信息消息（已移除重复定义，使用WebBrowserUIOperations.cs中的版本）
        /// </summary>
        // 注释：此方法已在WebBrowserUIOperations.cs中定义，避免CS0121二义性错误

        /// <summary>
        /// V2版本优化：安全显示警告消息（已移除重复定义，使用WebBrowserUIOperations.cs中的版本）
        /// </summary>
        // 注释：此方法已在WebBrowserUIOperations.cs中定义，避免CS0121二义性错误

        /// <summary>
        /// V2版本优化：安全显示错误消息（已移除重复定义，使用WebBrowserUIOperations.cs中的版本）
        /// </summary>
        // 注释：此方法已在WebBrowserUIOperations.cs中定义，避免CS0121二义性错误

        /// <summary>
        /// V2版本优化：安全显示确认对话框（已移除重复定义，使用WebBrowserUIOperations.cs中的版本）
        /// </summary>
        // 注释：此方法已在WebBrowserUIOperations.cs中定义，避免CS0121二义性错误

        /// <summary>
        /// V2版本优化：安全更新按钮状态
        /// </summary>
        /// <param name="buttonName">按钮名称</param>
        /// <param name="enabled">是否启用</param>
        /// <param name="text">按钮文本（可选）</param>
        private void UpdateButtonStateSafely(string buttonName, bool enabled, string text = null)
        {
            SafeExecuteUI($"更新按钮状态[{buttonName}]", () =>
            {
                try
                {
                    // 通过ToolStrip查找按钮，避免类型转换错误
                    ToolStripButton button = null;
                    if (toolStrip1 != null)
                    {
                        button = toolStrip1.Items.OfType<ToolStripButton>().FirstOrDefault(b => b.Name == buttonName);
                    }
                    if (button != null)
                    {
                        button.Enabled = enabled;
                        if (!string.IsNullOrEmpty(text))
                        {
                            button.Text = text;
                        }
                        ETLogManager.Debug(this, $"V2优化：按钮[{buttonName}]状态已更新，启用: {enabled}");
                    }
                    else
                    {
                        // 尝试查找普通按钮
                        var normalButton = this.Controls.Find(buttonName, true).FirstOrDefault() as Button;
                        if (normalButton != null)
                        {
                            normalButton.Enabled = enabled;
                            if (!string.IsNullOrEmpty(text))
                            {
                                normalButton.Text = text;
                            }
                            ETLogManager.Debug(this, $"V2优化：普通按钮[{buttonName}]状态已更新，启用: {enabled}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(this, $"V2优化：更新按钮[{buttonName}]状态失败: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// V2版本优化：批量更新按钮状态
        /// </summary>
        /// <param name="buttonStates">按钮状态字典</param>
        private void UpdateButtonStatesBatch(Dictionary<string, bool> buttonStates)
        {
            if (buttonStates == null || buttonStates.Count == 0)
                return;

            SafeExecuteUI("批量更新按钮状态", () =>
            {
                foreach (var kvp in buttonStates)
                {
                    try
                    {
                        UpdateButtonStateSafely(kvp.Key, kvp.Value);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(this, $"V2优化：批量更新按钮[{kvp.Key}]状态失败: {ex.Message}");
                    }
                }
                ETLogManager.Info(this, $"V2优化：批量更新了{buttonStates.Count}个按钮状态");
            });
        }

        /// <summary>
        /// V2版本优化：安全更新菜单项状态
        /// </summary>
        /// <param name="menuItemName">菜单项名称</param>
        /// <param name="enabled">是否启用</param>
        /// <param name="visible">是否可见</param>
        private void UpdateMenuItemStateSafely(string menuItemName, bool enabled, bool? visible = null)
        {
            SafeExecuteUI($"更新菜单项状态[{menuItemName}]", () =>
            {
                try
                {
                    // 通过ContextMenuStrip查找菜单项，避免类型转换错误
                    ToolStripMenuItem menuItem = null;
                    if (tabContextMenu != null)
                    {
                        menuItem = FindMenuItemRecursively(tabContextMenu.Items, menuItemName);
                    }
                    if (menuItem != null)
                    {
                        menuItem.Enabled = enabled;
                        if (visible.HasValue)
                        {
                            menuItem.Visible = visible.Value;
                        }
                        ETLogManager.Debug(this, $"V2优化：菜单项[{menuItemName}]状态已更新，启用: {enabled}");
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(this, $"V2优化：更新菜单项[{menuItemName}]状态失败: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// V2版本优化：安全刷新整个UI状态
        /// </summary>
        private void RefreshUIStateSafely()
        {
            SafeExecuteUI("刷新UI状态", () =>
            {
                try
                {
                    // 更新导航按钮状态
                    UpdateNavigationButtonsState();

                    // 更新功能按钮状态
                    bool hasActiveTab = !string.IsNullOrEmpty(GetCurrentTabSectionId());
                    var buttonStates = new Dictionary<string, bool>
                    {
                        ["copyCookiesConfigButton"] = hasActiveTab,
                        ["pasteCookiesButton"] = hasActiveTab,
                        ["clearCacheButton"] = true, // 清理缓存始终可用
                    };
                    UpdateButtonStatesBatch(buttonStates);

                    // 刷新标签页菜单状态
                    _tabMenuManagerV2?.MarkForRefresh();

                    ETLogManager.Info(this, "V2优化：UI状态刷新完成");
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(this, $"V2优化：刷新UI状态失败: {ex.Message}");
                }
            });
        }

        #endregion UI状态管理优化

        #region 线程安全性增强

        /// <summary>
        /// V2版本优化：线程安全的WebView2操作锁
        /// </summary>
        private readonly object _webViewOperationLock = new object();

        /// <summary>
        /// V2版本优化：线程安全的UI操作锁
        /// </summary>
        private readonly object _uiOperationLock = new object();

        /// <summary>
        /// V2版本优化：线程安全的配置操作锁
        /// </summary>
        private readonly object _configOperationLock = new object();

        /// <summary>
        /// V2版本优化：线程安全的WebView2操作
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="webViewAction">WebView2操作</param>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <returns>操作是否成功</returns>
        private bool ThreadSafeWebView2Operation(string operation, Action<WebView2> webViewAction, int timeout = 5000)
        {
            try
            {
                bool lockTaken = false;
                try
                {
                    Monitor.TryEnter(_webViewOperationLock, timeout, ref lockTaken);
                    if (!lockTaken)
                    {
                        ETLogManager.Warning(this, $"V2优化：WebView2操作[{operation}]获取锁超时");
                        return false;
                    }

                    return SafeExecuteWebView2(operation, webViewAction, false);
                }
                finally
                {
                    if (lockTaken)
                    {
                        Monitor.Exit(_webViewOperationLock);
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：线程安全WebView2操作[{operation}]失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// V2版本优化：线程安全的UI操作
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="uiAction">UI操作</param>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <returns>操作是否成功</returns>
        private bool ThreadSafeUIOperation(string operation, Action uiAction, int timeout = 3000)
        {
            try
            {
                bool lockTaken = false;
                try
                {
                    Monitor.TryEnter(_uiOperationLock, timeout, ref lockTaken);
                    if (!lockTaken)
                    {
                        ETLogManager.Warning(this, $"V2优化：UI操作[{operation}]获取锁超时");
                        return false;
                    }

                    return SafeExecuteUI(operation, uiAction, false);
                }
                finally
                {
                    if (lockTaken)
                    {
                        Monitor.Exit(_uiOperationLock);
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：线程安全UI操作[{operation}]失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// V2版本优化：线程安全的配置操作
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="configAction">配置操作</param>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <returns>操作是否成功</returns>
        private bool ThreadSafeConfigOperation(string operation, Action configAction, int timeout = 2000)
        {
            try
            {
                bool lockTaken = false;
                try
                {
                    Monitor.TryEnter(_configOperationLock, timeout, ref lockTaken);
                    if (!lockTaken)
                    {
                        ETLogManager.Warning(this, $"V2优化：配置操作[{operation}]获取锁超时");
                        return false;
                    }

                    return SafeExecute(operation, configAction, false);
                }
                finally
                {
                    if (lockTaken)
                    {
                        Monitor.Exit(_configOperationLock);
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：线程安全配置操作[{operation}]失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// V2版本优化：异步操作的线程安全包装
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="asyncAction">异步操作</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作是否成功</returns>
        private async Task<bool> ThreadSafeAsyncOperation(string operation, Func<CancellationToken, Task> asyncAction, CancellationToken cancellationToken = default)
        {
            try
            {
                using (var semaphore = new SemaphoreSlim(1, 1))
                {
                    await semaphore.WaitAsync(5000, cancellationToken);

                    if (cancellationToken.IsCancellationRequested)
                    {
                        ETLogManager.Warning(this, $"V2优化：异步操作[{operation}]被取消");
                        return false;
                    }

                    await asyncAction(cancellationToken);
                    return true;
                }
            }
            catch (OperationCanceledException)
            {
                ETLogManager.Warning(this, $"V2优化：异步操作[{operation}]被取消");
                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：线程安全异步操作[{operation}]失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// V2版本优化：检查当前线程是否为UI线程
        /// </summary>
        /// <returns>是否为UI线程</returns>
        private bool IsUIThread()
        {
            try
            {
                return !InvokeRequired;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：检查UI线程失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// V2版本优化：强制在UI线程执行操作
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="uiAction">UI操作</param>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <returns>操作是否成功</returns>
        private bool ForceUIThreadOperation(string operation, Action uiAction, int timeout = 5000)
        {
            try
            {
                if (IsUIThread())
                {
                    return SafeExecute(operation, uiAction, false);
                }
                else
                {
                    bool completed = false;
                    Exception operationException = null;

                    var resetEvent = new ManualResetEventSlim(false);

                    Invoke(new Action(() =>
                    {
                        try
                        {
                            SafeExecute(operation, uiAction, false);
                            completed = true;
                        }
                        catch (Exception ex)
                        {
                            operationException = ex;
                        }
                        finally
                        {
                            resetEvent.Set();
                        }
                    }));

                    bool waitResult = resetEvent.Wait(timeout);

                    if (!waitResult)
                    {
                        ETLogManager.Warning(this, $"V2优化：强制UI线程操作[{operation}]超时");
                        return false;
                    }

                    if (operationException != null)
                    {
                        ETLogManager.Error(this, $"V2优化：强制UI线程操作[{operation}]异常: {operationException.Message}");
                        return false;
                    }

                    return completed;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：强制UI线程操作[{operation}]失败: {ex.Message}");
                return false;
            }
        }

        #endregion 线程安全性增强

        #region 性能监控添加

        /// <summary>
        /// V2版本优化：性能监控数据
        /// </summary>
        private readonly Dictionary<string, PerformanceMetrics> _performanceMetrics = new Dictionary<string, PerformanceMetrics>();

        /// <summary>
        /// V2版本优化：性能监控锁
        /// </summary>
        private readonly object _performanceLock = new object();

        /// <summary>
        /// V2版本优化：开始性能监控
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <returns>性能监控令牌</returns>
        private PerformanceToken StartPerformanceMonitoring(string operation)
        {
            try
            {
                var token = new PerformanceToken(operation);

                lock (_performanceLock)
                {
                    if (!_performanceMetrics.ContainsKey(operation))
                    {
                        _performanceMetrics[operation] = new PerformanceMetrics(operation);
                    }
                    _performanceMetrics[operation].StartOperation();
                }

                ETLogManager.Debug(this, $"V2优化：开始监控操作[{operation}]");
                return token;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：开始性能监控失败: {ex.Message}");
                return new PerformanceToken(operation); // 返回空令牌
            }
        }

        /// <summary>
        /// V2版本优化：结束性能监控
        /// </summary>
        /// <param name="token">性能监控令牌</param>
        /// <param name="success">操作是否成功</param>
        private void EndPerformanceMonitoring(PerformanceToken token, bool success = true)
        {
            try
            {
                if (token == null || string.IsNullOrEmpty(token.Operation))
                    return;

                var elapsed = token.Stop();

                lock (_performanceLock)
                {
                    if (_performanceMetrics.ContainsKey(token.Operation))
                    {
                        _performanceMetrics[token.Operation].EndOperation(elapsed, success);

                        var metrics = _performanceMetrics[token.Operation];
                        ETLogManager.Debug(this, $"V2优化：操作[{token.Operation}]完成，耗时: {elapsed.TotalMilliseconds:F2}ms，成功: {success}");

                        // 如果操作时间过长，记录警告
                        if (elapsed.TotalMilliseconds > 3000)
                        {
                            ETLogManager.Warning(this, $"V2优化：操作[{token.Operation}]耗时过长: {elapsed.TotalMilliseconds:F2}ms");
                        }

                        // 定期输出性能统计
                        if (metrics.TotalOperations % 10 == 0)
                        {
                            LogPerformanceStatistics(token.Operation, metrics);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：结束性能监控失败: {ex.Message}");
            }
        }

        /// <summary>
        /// V2版本优化：记录性能统计信息
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="metrics">性能指标</param>
        private void LogPerformanceStatistics(string operation, PerformanceMetrics metrics)
        {
            try
            {
                var stats = $"V2优化：性能统计[{operation}] - " +
                           $"总次数: {metrics.TotalOperations}, " +
                           $"成功率: {metrics.SuccessRate:P2}, " +
                           $"平均耗时: {metrics.AverageTime:F2}ms, " +
                           $"最大耗时: {metrics.MaxTime:F2}ms, " +
                           $"最小耗时: {metrics.MinTime:F2}ms";

                ETLogManager.Info(this, stats);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：记录性能统计失败: {ex.Message}");
            }
        }

        /// <summary>
        /// V2版本优化：获取所有性能统计
        /// </summary>
        /// <returns>性能统计字符串</returns>
        private string GetAllPerformanceStatistics()
        {
            try
            {
                lock (_performanceLock)
                {
                    if (_performanceMetrics.Count == 0)
                    {
                        return "V2优化：暂无性能统计数据";
                    }

                    var sb = new StringBuilder();
                    sb.AppendLine("V2优化：WebBrowserV2性能统计报告");
                    sb.AppendLine("=" + new string('=', 50));

                    foreach (var kvp in _performanceMetrics.OrderByDescending(x => x.Value.TotalOperations))
                    {
                        var metrics = kvp.Value;
                        sb.AppendLine($"操作: {kvp.Key}");
                        sb.AppendLine($"  总次数: {metrics.TotalOperations}");
                        sb.AppendLine($"  成功次数: {metrics.SuccessfulOperations}");
                        sb.AppendLine($"  成功率: {metrics.SuccessRate:P2}");
                        sb.AppendLine($"  平均耗时: {metrics.AverageTime:F2}ms");
                        sb.AppendLine($"  最大耗时: {metrics.MaxTime:F2}ms");
                        sb.AppendLine($"  最小耗时: {metrics.MinTime:F2}ms");
                        sb.AppendLine();
                    }

                    return sb.ToString();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：获取性能统计失败: {ex.Message}");
                return $"获取性能统计失败: {ex.Message}";
            }
        }

        /// <summary>
        /// V2版本优化：清理性能统计数据
        /// </summary>
        private void ClearPerformanceStatistics()
        {
            try
            {
                lock (_performanceLock)
                {
                    int count = _performanceMetrics.Count;
                    _performanceMetrics.Clear();
                    ETLogManager.Info(this, $"V2优化：已清理{count}项性能统计数据");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：清理性能统计失败: {ex.Message}");
            }
        }

        /// <summary>
        /// V2版本优化：带性能监控的操作执行
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="action">要执行的操作</param>
        /// <param name="showToUser">是否显示给用户</param>
        /// <returns>操作是否成功</returns>
        private bool ExecuteWithPerformanceMonitoring(string operation, Action action, bool showToUser = false)
        {
            var token = StartPerformanceMonitoring(operation);
            try
            {
                bool success = SafeExecute(operation, action, showToUser);
                EndPerformanceMonitoring(token, success);
                return success;
            }
            catch (Exception ex)
            {
                EndPerformanceMonitoring(token, false);
                ETLogManager.Error(this, $"V2优化：带性能监控的操作[{operation}]失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// V2版本优化：带性能监控的异步操作执行
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="asyncAction">要执行的异步操作</param>
        /// <param name="showToUser">是否显示给用户</param>
        /// <returns>操作是否成功</returns>
        private async Task<bool> ExecuteWithPerformanceMonitoringAsync(string operation, Func<Task> asyncAction, bool showToUser = false)
        {
            var token = StartPerformanceMonitoring(operation);
            try
            {
                bool success = await SafeExecuteAsync(operation, asyncAction, showToUser);
                EndPerformanceMonitoring(token, success);
                return success;
            }
            catch (Exception ex)
            {
                EndPerformanceMonitoring(token, false);
                ETLogManager.Error(this, $"V2优化：带性能监控的异步操作[{operation}]失败: {ex.Message}");
                return false;
            }
        }

        #endregion 性能监控添加

        #region 性能优化增强

        /// <summary>
        /// V2版本优化：Cookie数据缓存
        /// </summary>
        private readonly Dictionary<string, CachedCookieData> _cookieDataCache = new Dictionary<string, CachedCookieData>();

        /// <summary>
        /// V2版本优化：缓存操作锁
        /// </summary>
        private readonly object _cacheLock = new object();

        /// <summary>
        /// V2版本优化：缓存过期时间（分钟）
        /// </summary>
        private const int CACHE_EXPIRY_MINUTES = 10;

        /// <summary>
        /// V2版本优化：获取缓存的Cookie数据
        /// </summary>
        /// <param name="cacheKey">缓存键</param>
        /// <returns>缓存的Cookie数据</returns>
        private ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.LoginInfoData GetCachedCookieData(string cacheKey)
        {
            try
            {
                lock (_cacheLock)
                {
                    if (_cookieDataCache.TryGetValue(cacheKey, out CachedCookieData cachedData))
                    {
                        if (DateTime.Now - cachedData.CacheTime < TimeSpan.FromMinutes(CACHE_EXPIRY_MINUTES))
                        {
                            ETLogManager.Debug(this, $"V2优化：使用缓存的Cookie数据: {cacheKey}");
                            return cachedData.Data;
                        }
                        else
                        {
                            // 缓存过期，移除
                            _cookieDataCache.Remove(cacheKey);
                            ETLogManager.Debug(this, $"V2优化：缓存过期，移除: {cacheKey}");
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：获取缓存Cookie数据失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// V2版本优化：设置Cookie数据缓存
        /// </summary>
        /// <param name="cacheKey">缓存键</param>
        /// <param name="data">Cookie数据</param>
        private void SetCachedCookieData(string cacheKey, ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.LoginInfoData data)
        {
            try
            {
                if (data == null || string.IsNullOrEmpty(cacheKey))
                    return;

                lock (_cacheLock)
                {
                    _cookieDataCache[cacheKey] = new CachedCookieData
                    {
                        Data = data,
                        CacheTime = DateTime.Now
                    };

                    // 清理过期缓存
                    CleanExpiredCache();

                    ETLogManager.Debug(this, $"V2优化：缓存Cookie数据: {cacheKey}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：设置Cookie数据缓存失败: {ex.Message}");
            }
        }

        /// <summary>
        /// V2版本优化：清理过期缓存
        /// </summary>
        private void CleanExpiredCache()
        {
            try
            {
                var expiredKeys = _cookieDataCache
                    .Where(kvp => DateTime.Now - kvp.Value.CacheTime > TimeSpan.FromMinutes(CACHE_EXPIRY_MINUTES))
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredKeys)
                {
                    _cookieDataCache.Remove(key);
                }

                if (expiredKeys.Count > 0)
                {
                    ETLogManager.Debug(this, $"V2优化：清理了{expiredKeys.Count}个过期缓存项");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：清理过期缓存失败: {ex.Message}");
            }
        }

        /// <summary>
        /// V2版本优化：批量操作优化
        /// </summary>
        /// <param name="operations">操作列表</param>
        /// <param name="batchSize">批次大小</param>
        /// <returns>操作是否成功</returns>
        private async Task<bool> ExecuteBatchOperationsAsync(List<Func<Task<bool>>> operations, int batchSize = 5)
        {
            try
            {
                if (operations == null || operations.Count == 0)
                    return true;

                ETLogManager.Info(this, $"V2优化：开始批量执行{operations.Count}个操作，批次大小: {batchSize}");

                int totalBatches = (int)Math.Ceiling((double)operations.Count / batchSize);
                int successCount = 0;

                for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++)
                {
                    var batchOperations = operations
                        .Skip(batchIndex * batchSize)
                        .Take(batchSize)
                        .ToList();

                    var batchTasks = batchOperations.Select(op => op()).ToArray();
                    var batchResults = await Task.WhenAll(batchTasks);

                    successCount += batchResults.Count(r => r);

                    ETLogManager.Debug(this, $"V2优化：批次{batchIndex + 1}/{totalBatches}完成，成功: {batchResults.Count(r => r)}/{batchResults.Length}");

                    // 批次间短暂延迟，避免资源争用
                    if (batchIndex < totalBatches - 1)
                    {
                        await Task.Delay(50);
                    }
                }

                bool allSuccess = successCount == operations.Count;
                ETLogManager.Info(this, $"V2优化：批量操作完成，成功: {successCount}/{operations.Count}");

                return allSuccess;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：批量操作执行失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// V2版本优化：内存优化的JSON处理
        /// </summary>
        /// <param name="jsonString">JSON字符串</param>
        /// <returns>处理结果</returns>
        private ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.LoginInfoData ProcessJsonWithMemoryOptimization(string jsonString)
        {
            try
            {
                if (string.IsNullOrEmpty(jsonString))
                    return null;

                // 生成缓存键
                string cacheKey = GenerateCacheKey(jsonString);

                // 尝试从缓存获取
                var cachedData = GetCachedCookieData(cacheKey);
                if (cachedData != null)
                {
                    return cachedData;
                }

                // 解析JSON
                var loginInfoData = ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.ParseLoginInfoJson(jsonString);

                // 缓存结果
                if (loginInfoData != null)
                {
                    SetCachedCookieData(cacheKey, loginInfoData);
                }

                return loginInfoData;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：内存优化JSON处理失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// V2版本优化：生成缓存键
        /// </summary>
        /// <param name="content">内容</param>
        /// <returns>缓存键</returns>
        private string GenerateCacheKey(string content)
        {
            try
            {
                if (string.IsNullOrEmpty(content))
                    return string.Empty;

                // 使用内容的哈希值作为缓存键
                using (var sha256 = System.Security.Cryptography.SHA256.Create())
                {
                    byte[] hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(content));
                    return Convert.ToBase64String(hashBytes).Substring(0, 16); // 取前16个字符
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：生成缓存键失败: {ex.Message}");
                return Guid.NewGuid().ToString("N").Substring(0, 16);
            }
        }

        /// <summary>
        /// V2版本优化：清理所有缓存
        /// </summary>
        private void ClearAllCache()
        {
            try
            {
                lock (_cacheLock)
                {
                    int cacheCount = _cookieDataCache.Count;
                    _cookieDataCache.Clear();
                    ETLogManager.Info(this, $"V2优化：清理了{cacheCount}个缓存项");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"V2优化：清理所有缓存失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 递归查找菜单项的辅助方法
        /// </summary>
        /// <param name="items">菜单项集合</param>
        /// <param name="menuItemName">要查找的菜单项名称</param>
        /// <returns>找到的菜单项，如果未找到则返回null</returns>
        private ToolStripMenuItem FindMenuItemRecursively(ToolStripItemCollection items, string menuItemName)
        {
            foreach (ToolStripItem item in items)
            {
                if (item is ToolStripMenuItem menuItem)
                {
                    if (menuItem.Name == menuItemName)
                    {
                        return menuItem;
                    }

                    // 递归查找子菜单项
                    var subMenuItem = FindMenuItemRecursively(menuItem.DropDownItems, menuItemName);
                    if (subMenuItem != null)
                    {
                        return subMenuItem;
                    }
                }
            }
            return null;
        }

        #endregion 性能优化增强
    }
}