using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using ET.ETAIv2.Models;
using ET.ETAIv2.Constants;
using ET.ETAIv2.Interfaces;
using ET.ETAIv2.Exceptions;

namespace ET.ETAIv2.Utils
{
    /// <summary>
    /// 文件验证辅助类
    /// </summary>
    public class FileValidationHelper
    {
        private readonly IAILogger _logger;

        public FileValidationHelper(IAILogger logger = null)
        {
            _logger = logger ?? new AILogger();
        }

        /// <summary>
        /// 验证文件是否有效
        /// </summary>
        public FileValidationResult ValidateFile(string filePath)
        {
            var result = new FileValidationResult
            {
                FilePath = filePath,
                IsValid = false,
                ValidationErrors = new List<string>()
            };

            try
            {
                // 检查文件路径
                if (string.IsNullOrEmpty(filePath))
                {
                    result.ValidationErrors.Add("文件路径为空");
                    return result;
                }

                // 检查文件是否存在
                if (!File.Exists(filePath))
                {
                    result.ValidationErrors.Add("文件不存在");
                    return result;
                }

                var fileInfo = new FileInfo(filePath);

                // 检查文件大小
                if (fileInfo.Length == 0)
                {
                    result.ValidationErrors.Add("文件为空");
                    return result;
                }

                if (fileInfo.Length > AIConstants.MaxFileSize)
                {
                    result.ValidationErrors.Add($"文件大小超过限制 ({fileInfo.Length} bytes > {AIConstants.MaxFileSize} bytes)");
                    return result;
                }

                // 检查文件扩展名
                var extension = fileInfo.Extension?.ToLower();
                if (string.IsNullOrEmpty(extension))
                {
                    result.ValidationErrors.Add("文件没有扩展名");
                    return result;
                }

                if (!AIConstants.SupportedFileExtensions.Contains(extension))
                {
                    result.ValidationErrors.Add($"不支持的文件类型: {extension}");
                    return result;
                }

                // 检查文件访问权限
                if (!CanReadFile(filePath))
                {
                    result.ValidationErrors.Add("文件无法读取，可能是权限问题");
                    return result;
                }

                // 检查文件内容完整性
                if (!ValidateFileIntegrity(filePath, extension))
                {
                    result.ValidationErrors.Add("文件内容可能已损坏");
                    return result;
                }

                // 所有验证通过
                result.IsValid = true;
                result.FileSize = fileInfo.Length;
                result.FileType = extension;
                result.MimeType = GetMimeType(extension);

                _logger.LogInfo($"文件验证通过: {filePath}");
            }
            catch (Exception ex)
            {
                result.ValidationErrors.Add($"验证过程中发生错误: {ex.Message}");
                _logger.LogError($"文件验证失败: {filePath}", ex);
            }

            return result;
        }

        /// <summary>
        /// 批量验证文件
        /// </summary>
        public List<FileValidationResult> ValidateFiles(List<string> filePaths)
        {
            var results = new List<FileValidationResult>();

            if (filePaths == null || !filePaths.Any())
                return results;

            _logger.LogInfo($"开始批量验证文件，共 {filePaths.Count} 个文件");

            foreach (var filePath in filePaths)
            {
                var result = ValidateFile(filePath);
                results.Add(result);
            }

            var validCount = results.Count(r => r.IsValid);
            _logger.LogInfo($"批量文件验证完成，有效文件: {validCount}/{filePaths.Count}");

            return results;
        }

        /// <summary>
        /// 检查文件是否可读
        /// </summary>
        private bool CanReadFile(string filePath)
        {
            try
            {
                using (var stream = File.OpenRead(filePath))
                {
                    return stream.CanRead;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证文件完整性
        /// </summary>
        private bool ValidateFileIntegrity(string filePath, string extension)
        {
            try
            {
                switch (extension)
                {
                    case ".txt":
                    case ".md":
                    case ".json":
                    case ".csv":
                    case ".xml":
                    case ".html":
                        return ValidateTextFile(filePath);
                    
                    case ".pdf":
                        return ValidatePdfFile(filePath);
                    
                    case ".docx":
                        return ValidateDocxFile(filePath);
                    
                    case ".doc":
                        return ValidateDocFile(filePath);
                    
                    case ".xlsx":
                    case ".xls":
                        return ValidateExcelFile(filePath);
                    
                    default:
                        // 对于其他类型，只检查是否能读取
                        return CanReadFile(filePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"文件完整性验证失败 {filePath}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证文本文件
        /// </summary>
        private bool ValidateTextFile(string filePath)
        {
            try
            {
                var content = File.ReadAllText(filePath);
                return !string.IsNullOrEmpty(content.Trim());
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证PDF文件
        /// </summary>
        private bool ValidatePdfFile(string filePath)
        {
            try
            {
                // 检查PDF文件头
                using (var stream = File.OpenRead(filePath))
                {
                    var buffer = new byte[4];
                    stream.Read(buffer, 0, 4);
                    var header = System.Text.Encoding.ASCII.GetString(buffer);
                    return header == "%PDF";
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证DOCX文件
        /// </summary>
        private bool ValidateDocxFile(string filePath)
        {
            try
            {
                // DOCX文件实际上是ZIP文件
                using (var stream = File.OpenRead(filePath))
                {
                    var buffer = new byte[2];
                    stream.Read(buffer, 0, 2);
                    // ZIP文件头：PK
                    return buffer[0] == 0x50 && buffer[1] == 0x4B;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证DOC文件
        /// </summary>
        private bool ValidateDocFile(string filePath)
        {
            try
            {
                // DOC文件头检查
                using (var stream = File.OpenRead(filePath))
                {
                    var buffer = new byte[8];
                    stream.Read(buffer, 0, 8);
                    // DOC文件的OLE头
                    return buffer[0] == 0xD0 && buffer[1] == 0xCF && 
                           buffer[2] == 0x11 && buffer[3] == 0xE0;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证Excel文件
        /// </summary>
        private bool ValidateExcelFile(string filePath)
        {
            try
            {
                var extension = Path.GetExtension(filePath)?.ToLower();
                
                if (extension == ".xlsx")
                {
                    // XLSX文件是ZIP格式
                    return ValidateDocxFile(filePath);
                }
                else if (extension == ".xls")
                {
                    // XLS文件是OLE格式
                    return ValidateDocFile(filePath);
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取MIME类型
        /// </summary>
        private string GetMimeType(string extension)
        {
            return FileConstants.MimeTypes.TryGetValue(extension, out var mimeType) 
                ? mimeType 
                : "application/octet-stream";
        }

        /// <summary>
        /// 生成文件摘要信息
        /// </summary>
        public FileSummary GenerateFileSummary(string filePath)
        {
            var summary = new FileSummary
            {
                FilePath = filePath,
                FileName = Path.GetFileName(filePath)
            };

            try
            {
                if (File.Exists(filePath))
                {
                    var fileInfo = new FileInfo(filePath);
                    summary.FileSize = fileInfo.Length;
                    summary.FileType = fileInfo.Extension?.ToLower();
                    summary.CreatedAt = fileInfo.CreationTime;
                    summary.ModifiedAt = fileInfo.LastWriteTime;
                    summary.MimeType = GetMimeType(summary.FileType);
                    
                    // 生成简单的文件描述
                    summary.Description = GenerateFileDescription(fileInfo);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"生成文件摘要失败: {filePath}", ex);
                summary.Description = $"无法读取文件信息: {ex.Message}";
            }

            return summary;
        }

        /// <summary>
        /// 生成文件描述
        /// </summary>
        private string GenerateFileDescription(FileInfo fileInfo)
        {
            var sizeDescription = GetSizeDescription(fileInfo.Length);
            var typeDescription = GetTypeDescription(fileInfo.Extension?.ToLower());
            
            return $"{typeDescription}，大小: {sizeDescription}，修改时间: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}";
        }

        /// <summary>
        /// 获取大小描述
        /// </summary>
        private string GetSizeDescription(long bytes)
        {
            if (bytes < 1024)
                return $"{bytes} bytes";
            
            if (bytes < 1024 * 1024)
                return $"{bytes / 1024.0:F1} KB";
            
            return $"{bytes / (1024.0 * 1024.0):F1} MB";
        }

        /// <summary>
        /// 获取类型描述
        /// </summary>
        private string GetTypeDescription(string extension)
        {
            return extension switch
            {
                ".pdf" => "PDF文档",
                ".txt" => "文本文件",
                ".docx" => "Word文档",
                ".doc" => "Word文档(旧版)",
                ".xlsx" => "Excel工作簿",
                ".xls" => "Excel工作簿(旧版)",
                ".md" => "Markdown文档",
                ".html" => "HTML文档",
                ".json" => "JSON数据文件",
                ".csv" => "CSV数据文件",
                ".xml" => "XML文档",
                ".rtf" => "RTF文档",
                _ => "未知类型文件"
            };
        }
    }

    /// <summary>
    /// 文件验证结果
    /// </summary>
    public class FileValidationResult
    {
        public string FilePath { get; set; }
        public bool IsValid { get; set; }
        public List<string> ValidationErrors { get; set; }
        public long FileSize { get; set; }
        public string FileType { get; set; }
        public string MimeType { get; set; }

        public FileValidationResult()
        {
            ValidationErrors = new List<string>();
        }
    }

    /// <summary>
    /// 文件摘要信息
    /// </summary>
    public class FileSummary
    {
        public string FilePath { get; set; }
        public string FileName { get; set; }
        public long FileSize { get; set; }
        public string FileType { get; set; }
        public string MimeType { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ModifiedAt { get; set; }
        public string Description { get; set; }
    }
}
