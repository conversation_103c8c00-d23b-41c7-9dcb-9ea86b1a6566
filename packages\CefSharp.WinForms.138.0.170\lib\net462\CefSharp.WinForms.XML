<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CefSharp.WinForms</name>
    </assembly>
    <members>
        <member name="T:CefSharp.WinForms.CefSettings">
            <summary>
            Initialization settings. Many of these and other settings can also configured
            using command-line switches.
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.CefSettings.#ctor">
            <summary>
            Intialize with default values
            </summary>
        </member>
        <member name="T:CefSharp.WinForms.ChromiumWebBrowser">
            <summary>
            ChromiumWebBrowser is the WinForms web browser control
            </summary>
            <seealso cref="T:System.Windows.Forms.Control" />
            <seealso cref="T:CefSharp.WinForms.IWinFormsWebBrowser" />
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.managedCefBrowserAdapter">
            <summary>
            The managed cef browser adapter
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.parentFormMessageInterceptor">
            <summary>
            The parent form message interceptor
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.designMode">
            <summary>
            A flag that indicates whether or not the designer is active
            NOTE: DesignMode becomes false by the time we get to the destructor/dispose so it gets stored here
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.initialized">
            <summary>
            A flag that indicates whether or not <see cref="M:CefSharp.WinForms.ChromiumWebBrowser.InitializeFieldsAndCefIfRequired"/> has been called.
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.browserCreated">
            <summary>
            Has the underlying Cef Browser been created (slightly different to initialized in that
            the browser is initialized in an async fashion)
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.initialAddressLoaded">
            <summary>
            A flag indicating if the <see cref="P:CefSharp.WinForms.ChromiumWebBrowser.Address"/> was used when calling CreateBrowser
            If false and <see cref="P:CefSharp.WinForms.ChromiumWebBrowser.Address"/> contains a non empty string Load will be called
            on the main frame
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.removeExNoActivateStyle">
            <summary>
            If true the the WS_EX_NOACTIVATE style will be removed so that future mouse clicks
            inside the browser correctly activate and focus the window.
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.browserSettings">
            <summary>
            Browser initialization settings
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.requestContext">
            <summary>
            The request context (we deliberately use a private variable so we can throw an exception if
            user attempts to set after browser created)
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.parkingControl">
            <summary>
            Parking control used to temporarily host the CefBrowser instance
            when <see cref="P:System.Windows.Forms.Control.RecreatingHandle"/> is <c>true</c>.
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.initialFocus">
            <summary>
            This flag is set when the browser gets focus before the underlying CEF browser
            has been initialized.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.IsDisposed">
            <summary>
            Gets a value indicating whether this instance is disposed.
            </summary>
            <value><see langword="true" /> if this instance is disposed; otherwise, <see langword="false" />.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.BrowserSettings">
            <summary>
            Gets or sets the browser settings.
            </summary>
            <value>The browser settings.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.ActivateBrowserOnCreation">
            <summary>
            Activates browser upon creation, the default value is false. Prior to version 73
            the default behaviour was to activate browser on creation (Equivalent of setting this property to true).
            To restore this behaviour set this value to true immediately after you create the <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> instance.
            https://github.com/chromiumembedded/cef/issues/1856
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.RequestContext">
            <summary>
            Gets or sets the request context.
            </summary>
            <value>The request context.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.IsLoading">
            <summary>
            A flag that indicates whether the control is currently loading one or more web pages (true) or not (false).
            </summary>
            <value><c>true</c> if this instance is loading; otherwise, <c>false</c>.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.TooltipText">
            <summary>
            The text that will be displayed as a ToolTip
            </summary>
            <value>The tooltip text.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.Address">
            <summary>
            The address (URL) which the browser control is currently displaying.
            Will automatically be updated as the user navigates to another page (e.g. by clicking on a link).
            </summary>
            <value>The address.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.AddressChanged">
            <summary>
            Occurs when the browser address changed.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.TitleChanged">
            <summary>
            Occurs when the browser title changed.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.CanGoForward">
            <summary>
            A flag that indicates whether the state of the control currently supports the GoForward action (true) or not (false).
            </summary>
            <value><c>true</c> if this instance can go forward; otherwise, <c>false</c>.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.CanGoBack">
            <summary>
            A flag that indicates whether the state of the control current supports the GoBack action (true) or not (false).
            </summary>
            <value><c>true</c> if this instance can go back; otherwise, <c>false</c>.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.IsBrowserInitialized">
            <summary>
            A flag that indicates whether the WebBrowser is initialized (true) or not (false).
            </summary>
            <value><c>true</c> if this instance is browser initialized; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.UseParentFormMessageInterceptor">
            <summary>
            ParentFormMessageInterceptor hooks the Form handle and forwards
            the move/active messages to the browser, the default is true
            and should only be required when using <see cref="P:CefSharp.CefSettingsBase.MultiThreadedMessageLoop"/>
            set to true.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.ParkControlOnHandleDestroyed">
            <summary>
            By default when <see cref="M:System.Windows.Forms.Control.OnHandleDestroyed(System.EventArgs)"/> is called
            the underlying Browser Hwnd is only parked (moved to a temp parent) 
            when <see cref="P:System.Windows.Forms.Control.RecreatingHandle"/> is <c>true</c>, there are a few other
            cases where parking of the control is desired, you can force parking by setting
            this property to <c>true</c>.
            </summary>
            <remarks>
            You may wish to set this property to <c>true</c> when using the browser in conjunction
            with https://github.com/dockpanelsuite/dockpanelsuite
            </remarks>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.#cctor">
            <summary>
            Initializes static members of the <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> class.
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.OnApplicationExit(System.Object,System.EventArgs)">
            <summary>
            Handles the <see cref="E:ApplicationExit" /> event.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.UnregisterShutdownHandler">
            <summary>
            To control how <see cref="M:CefSharp.Cef.Shutdown"/> is called, this method will
            unsubscribe from <see cref="E:System.Windows.Forms.Application.ApplicationExit"/>,
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.#ctor">
            <summary>
            <strong>Important!!!</strong>
            This constructor exists as the WinForms designer requires a parameterless constructor, if you are instantiating
            an instance of this class in code then use the <see cref="M:CefSharp.WinForms.ChromiumWebBrowser.#ctor(System.String,CefSharp.IRequestContext)"/>
            constructor overload instead. Using this constructor in code is unsupported and you may experience <see cref="T:System.NullReferenceException"/>'s
            when attempting to access some of the properties immediately after instantiation. 
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.#ctor(CefSharp.Web.HtmlString,CefSharp.IRequestContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> class.
            **Important** - When using this constructor the <see cref="P:System.Windows.Forms.Control.Dock"/> property
            will default to <see cref="F:System.Windows.Forms.DockStyle.Fill"/>.
            </summary>
            <param name="html">html string to be initially loaded in the browser.</param>
            <param name="requestContext">(Optional) Request context that will be used for this browser instance, if null the Global
            Request Context will be used.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.#ctor(System.String,CefSharp.IRequestContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> class.
            **Important** - When using this constructor the <see cref="P:System.Windows.Forms.Control.Dock"/> property
            will default to <see cref="F:System.Windows.Forms.DockStyle.Fill"/>.
            </summary>
            <param name="address">The address.</param>
            <param name="requestContext">(Optional) Request context that will be used for this browser instance, if null the Global
            Request Context will be used.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.InitializeFieldsAndCefIfRequired">
            <summary>
            Required for designer support - this method cannot be inlined as the designer
            will attempt to load libcef.dll and will subsequently throw an exception.
            TODO: Still not happy with this method name, need something better
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.Dispose(System.Boolean)">
            <summary>
            If not in design mode; Releases unmanaged and - optionally - managed resources for the <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/>
            </summary>
            <param name="disposing"><see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.InternalDispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources for the <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/>
            </summary>
            <param name="disposing"><see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
            <remarks>
            This method cannot be inlined as the designer will attempt to load libcef.dll and will subsequently throw an exception.
            </remarks>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.Load(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CaptureScreenshotAsync(CefSharp.DevTools.Page.CaptureScreenshotFormat,System.Nullable{System.Int32},CefSharp.DevTools.Page.Viewport,System.Boolean,System.Boolean)">
            <summary>
            Capture page screenshot.
            </summary>
            <param name="format">Image compression format (defaults to png).</param>
            <param name="quality">Compression quality from range [0..100] (jpeg only).</param>
            <param name="viewPort">Capture the screenshot of a given region only.</param>
            <param name="fromSurface">Capture the screenshot from the surface, rather than the view. Defaults to true.</param>
            <param name="captureBeyondViewport">Capture the screenshot beyond the viewport. Defaults to false.</param>
            <returns>A task that can be awaited to obtain the screenshot as a byte[].</returns>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.JavascriptObjectRepository">
            <summary>
            The javascript object repository, one repository per ChromiumWebBrowser instance.
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.IsParentInDesignMode(System.Windows.Forms.Control)">
            <summary>
             Indicates if one of the Ancestors of this control is sited
             and that site in DesignMode.
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.OnHandleCreated(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.HandleCreated" /> event.
            </summary>
            <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CreateBrowserWindowInfo(System.IntPtr)">
             <summary>
             Override this method to handle creation of WindowInfo. This method can be used to customise aspects of
             browser creation including configuration of settings such as <see cref="P:CefSharp.IWindowInfo.ExStyle"/>.
             Window Activation is disabled by default, you can re-enable it by overriding and removing the
             WS_EX_NOACTIVATE style from <see cref="P:CefSharp.IWindowInfo.ExStyle"/>.
             </summary>
             <param name="handle">Window handle for the Control</param>
             <returns>Window Info</returns>
             <example>
             To re-enable Window Activation then remove WS_EX_NOACTIVATE from ExStyle
             <code>
             const uint WS_EX_NOACTIVATE = 0x08000000;
             windowInfo.ExStyle &amp;= ~WS_EX_NOACTIVATE;
            </code>
             </example>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.OnSetBrowserInitialFocus">
            <summary>
            Called from <see cref="M:CefSharp.WinForms.ChromiumWebBrowser.OnAfterBrowserCreated(CefSharp.IBrowser)"/> when we set focus
            to the CefBrowser instance via <see cref="M:CefSharp.IBrowserHost.SetFocus(System.Boolean)"/>.
            Method is only called if the browser got focus via <see cref="M:CefSharp.WinForms.ChromiumWebBrowser.OnGotFocus(System.EventArgs)"/>
            before the call to <see cref="M:CefSharp.WinForms.ChromiumWebBrowser.OnAfterBrowserCreated(CefSharp.IBrowser)"/>.
            Can be overridden to provide custom behaviour.
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#SetAddress(CefSharp.AddressChangedEventArgs)">
            <summary>
            Sets the address.
            </summary>
            <param name="args">The <see cref="T:CefSharp.AddressChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#SetTitle(CefSharp.TitleChangedEventArgs)">
            <summary>
            Sets the title.
            </summary>
            <param name="args">The <see cref="T:CefSharp.TitleChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#SetTooltipText(System.String)">
            <summary>
            Sets the tooltip text.
            </summary>
            <param name="tooltipText">The tooltip text.</param>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.Focused">
            <summary>
            Manually implement Focused because cef does not implement it.
            </summary>
            <value><c>true</c> if focused; otherwise, <c>false</c>.</value>
            <remarks>This is also how the Microsoft's WebBrowserControl implements the Focused property.</remarks>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.ResizeBrowser(System.Int32,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.HideInternal">
            <summary>
            When minimized set the browser window size to 0x0 to reduce resource usage.
            https://github.com/chromiumembedded/cef/blob/c7701b8a6168f105f2c2d6b239ce3958da3e3f13/tests/cefclient/browser/browser_window_std_win.cc#L87
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.ShowInternal">
            <summary>
            Show the browser (called after previous minimised)
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.OnGotFocus(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.GetBrowser">
            <summary>
            Returns the current IBrowser Instance
            </summary>
            <returns>browser instance</returns>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.FromBrowser(CefSharp.IBrowser)">
            <summary>
            Gets the <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> associated with
            a specific <see cref="T:CefSharp.IBrowser"/> instance. 
            </summary>
            <param name="browser">browser</param>
            <returns>returns the assocaited <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> or null if Disposed or no host found.</returns>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.canExecuteJavascriptInMainFrameChildProcessId">
            <summary>
            Used as workaround for issue https://github.com/cefsharp/CefSharp/issues/3021
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.browserInitialized">
            <summary>
            The browser initialized - boolean represented as 0 (false) and 1(true) as we use Interlocker to increment/reset
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.disposeSignaled">
            <summary>
            The value for disposal, if it's 1 (one) then this instance is either disposed
            or in the process of getting disposed
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.browser">
            <summary>
            The browser
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.initialLoadTaskCompletionSource">
            <summary>
            Initial browser load task complection source
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.initialLoadAction">
            <summary>
            Initial browser load action
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.BrowserCore">
            <summary>
            Get access to the core <see cref="T:CefSharp.IBrowser"/> instance.
            Maybe null if the underlying CEF Browser has not yet been
            created or if this control has been disposed. Check
            <see cref="P:CefSharp.IBrowser.IsDisposed"/> before accessing.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.CanExecuteJavascriptInMainFrame">
            <summary>
            A flag that indicates if you can execute javascript in the main frame.
            Flag is set to true in IRenderProcessMessageHandler.OnContextCreated.
            and false in IRenderProcessMessageHandler.OnContextReleased
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.DialogHandler">
            <summary>
            Implement <see cref="T:CefSharp.IDialogHandler" /> and assign to handle dialog events.
            </summary>
            <value>The dialog handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.JsDialogHandler">
            <summary>
            Implement <see cref="T:CefSharp.IJsDialogHandler" /> and assign to handle events related to JavaScript Dialogs.
            </summary>
            <value>The js dialog handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.KeyboardHandler">
            <summary>
            Implement <see cref="T:CefSharp.IKeyboardHandler" /> and assign to handle events related to key press.
            </summary>
            <value>The keyboard handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.RequestHandler">
            <summary>
            Implement <see cref="T:CefSharp.IRequestHandler" /> and assign to handle events related to browser requests.
            </summary>
            <value>The request handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.DownloadHandler">
            <summary>
            Implement <see cref="T:CefSharp.IDownloadHandler" /> and assign to handle events related to downloading files.
            </summary>
            <value>The download handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.LoadHandler">
            <summary>
            Implement <see cref="T:CefSharp.ILoadHandler" /> and assign to handle events related to browser load status.
            </summary>
            <value>The load handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.LifeSpanHandler">
            <summary>
            Implement <see cref="T:CefSharp.ILifeSpanHandler" /> and assign to handle events related to popups.
            </summary>
            <value>The life span handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.DisplayHandler">
            <summary>
            Implement <see cref="T:CefSharp.IDisplayHandler" /> and assign to handle events related to browser display state.
            </summary>
            <value>The display handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.MenuHandler">
            <summary>
            Implement <see cref="T:CefSharp.IContextMenuHandler" /> and assign to handle events related to the browser context menu
            </summary>
            <value>The menu handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.RenderProcessMessageHandler">
            <summary>
            Implement <see cref="T:CefSharp.IRenderProcessMessageHandler" /> and assign to handle messages from the render process.
            </summary>
            <value>The render process message handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.FindHandler">
            <summary>
            Implement <see cref="T:CefSharp.IFindHandler" /> to handle events related to find results.
            </summary>
            <value>The find handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.AudioHandler">
            <summary>
            Implement <see cref="T:CefSharp.IAudioHandler" /> to handle audio events.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.FrameHandler">
            <summary>
            Implement <see cref="T:CefSharp.IFrameHandler" /> to handle frame events.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.PermissionHandler">
            <summary>
            Implement <see cref="T:CefSharp.IPermissionHandler" /> to handle events related to permission requests.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.FocusHandler">
            <summary>
            The <see cref="T:CefSharp.IFocusHandler" /> for this ChromiumWebBrowser.
            </summary>
            <value>The focus handler.</value>
            <remarks>If you need customized focus handling behavior for WinForms, the suggested
            best practice would be to inherit from DefaultFocusHandler and try to avoid
            needing to override the logic in OnGotFocus. The implementation in
            DefaultFocusHandler relies on very detailed behavior of how WinForms and
            Windows interact during window activation.</remarks>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.DragHandler">
            <summary>
            Implement <see cref="T:CefSharp.IDragHandler" /> and assign to handle events related to dragging.
            </summary>
            <value>The drag handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.ResourceRequestHandlerFactory">
            <summary>
            Implement <see cref="T:CefSharp.IResourceRequestHandlerFactory" /> and control the loading of resources
            </summary>
            <value>The resource handler factory.</value>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.LoadError">
            <summary>
            Event handler that will get called when the resource load for a navigation fails or is canceled.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.FrameLoadStart">
            <summary>
            Event handler that will get called when the browser begins loading a frame. Multiple frames may be loading at the same
            time. Sub-frames may start or continue loading after the main frame load has ended. This method may not be called for a
            particular frame if the load request for that frame fails. For notification of overall browser load status use
            OnLoadingStateChange instead.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
            <remarks>Whilst this may seem like a logical place to execute js, it's called before the DOM has been loaded, implement
            <see cref="M:CefSharp.IRenderProcessMessageHandler.OnContextCreated(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame)" /> as it's called when the underlying V8Context is created
            </remarks>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.FrameLoadEnd">
            <summary>
            Event handler that will get called when the browser is done loading a frame. Multiple frames may be loading at the same
            time. Sub-frames may start or continue loading after the main frame load has ended. This method will always be called
            for all frames irrespective of whether the request completes successfully.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.LoadingStateChanged">
            <summary>
            Event handler that will get called when the Loading state has changed.
            This event will be fired twice. Once when loading is initiated either programmatically or
            by user action, and once when loading is terminated due to completion, cancellation of failure.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.ConsoleMessage">
            <summary>
            Event handler for receiving Javascript console messages being sent from web pages.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            (The exception to this is when you're running with settings.MultiThreadedMessageLoop = false, then they'll be the same thread).
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.StatusMessage">
            <summary>
            Event handler for changes to the status message.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang.
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            (The exception to this is when you're running with settings.MultiThreadedMessageLoop = false, then they'll be the same thread).
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.JavascriptMessageReceived">
            <summary>
            Event handler that will get called when the message that originates from CefSharp.PostMessage
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#IChromiumWebBrowserBase#IsBrowserInitialized">
            <summary>
            A flag that indicates whether the WebBrowser is initialized (true) or not (false).
            </summary>
            <value><c>true</c> if this instance is browser initialized; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#OnFrameLoadStart(CefSharp.FrameLoadStartEventArgs)">
            <summary>
            Handles the <see cref="E:FrameLoadStart" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.FrameLoadStartEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#OnFrameLoadEnd(CefSharp.FrameLoadEndEventArgs)">
            <summary>
            Handles the <see cref="E:FrameLoadEnd" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.FrameLoadEndEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#OnConsoleMessage(CefSharp.ConsoleMessageEventArgs)">
            <summary>
            Handles the <see cref="E:ConsoleMessage" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.ConsoleMessageEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#OnStatusMessage(CefSharp.StatusMessageEventArgs)">
            <summary>
            Handles the <see cref="E:StatusMessage" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.StatusMessageEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#OnLoadError(CefSharp.LoadErrorEventArgs)">
            <summary>
            Handles the <see cref="E:LoadError" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.LoadErrorEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#HasParent">
            <summary>
            Gets or sets a value indicating whether this instance has parent.
            </summary>
            <value><c>true</c> if this instance has parent; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#DevToolsContext">
            <summary>
            Used by CefSharp.Puppeteer to associate a single DevToolsContext with a ChromiumWebBrowser instance.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#BrowserAdapter">
            <summary>
            Gets the browser adapter.
            </summary>
            <value>The browser adapter.</value>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#SetLoadingStateChange(CefSharp.LoadingStateChangedEventArgs)">
            <summary>
            Sets the loading state change.
            </summary>
            <param name="args">The <see cref="T:CefSharp.LoadingStateChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.LoadUrl(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.LoadUrlAsync(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.WaitForNavigationAsync(System.Nullable{System.TimeSpan},System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.WaitForInitialLoadAsync">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.TryGetBrowserCoreById(System.Int32,CefSharp.IBrowser@)">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.GetContentSizeAsync">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.OnAfterBrowserCreated(CefSharp.IBrowser)">
            <summary>
            Called after browser created.
            </summary>
            <param name="browser">The browser.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.SetLoadingStateChange(CefSharp.LoadingStateChangedEventArgs)">
            <summary>
            Sets the loading state change.
            </summary>
            <param name="args">The <see cref="T:CefSharp.LoadingStateChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.FreeHandlersExceptLifeSpanAndFocus">
            <summary>
            Sets the handler references to null.
            Where required also calls Dispose().
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.InternalIsBrowserInitialized">
            <summary>
            Check is browser is initialized
            </summary>
            <returns>true if browser is initialized</returns>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.ThrowExceptionIfBrowserNotInitialized">
            <summary>
            Throw exception if browser not initialized.
            </summary>
            <exception cref="T:System.Exception">Thrown when an exception error condition occurs.</exception>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.ThrowExceptionIfDisposed">
            <summary>
            Throw exception if disposed.
            </summary>
            <exception cref="T:System.ObjectDisposedException">Thrown when a supplied object has been disposed.</exception>
        </member>
        <member name="T:CefSharp.WinForms.ChromiumWebBrowserDesigner">
            <summary>
            ChromiumWebBrowser Control Designer
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowserDesigner.OnPaintAdornments(System.Windows.Forms.PaintEventArgs)">
            <summary>
            Receives a call when the control that the designer is managing has painted its surface so the designer can paint any additional adornments on top of the control.
            </summary>
            <param name="pe">args</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowserDesigner.PreFilterProperties(System.Collections.IDictionary)">
            <summary>
            Adjusts the set of properties the component exposes through a TypeDescriptor.
            </summary>
            <param name="properties">properties</param>
        </member>
        <member name="T:CefSharp.WinForms.Experimental.ChromiumRenderWidgetHandleFinder">
            <summary>
            ChromiumWidgetHandleFinder is a helper class used to find the <see cref="F:CefSharp.WinForms.Experimental.ChromiumRenderWidgetHandleFinder.ChromeRenderWidgetHostClassName"/>
            child Hwnd for the browser instance.
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.Experimental.ChromiumRenderWidgetHandleFinder.ChromeRenderWidgetHostClassName">
            <summary>
            Class Name of the Chrome_RenderWidgetHostHWND Child Window
            </summary>
        </member>
        <member name="T:CefSharp.WinForms.Experimental.ChromiumRenderWidgetHandleFinder.EnumWindowProc">
            <summary>
            EnumWindowProc delegate used by <see cref="M:CefSharp.WinForms.Experimental.ChromiumRenderWidgetHandleFinder.EnumChildWindows(System.IntPtr,CefSharp.WinForms.Experimental.ChromiumRenderWidgetHandleFinder.EnumWindowProc,System.IntPtr)"/>
            </summary>
            <param name="hwnd">A handle to a child window of the parent window specified in EnumChildWindows</param>
            <param name="lParam">The application-defined value given in EnumChildWindows</param>
            <returns>To continue enumeration, the callback function must return true; to stop enumeration, it must return false.</returns>
        </member>
        <member name="M:CefSharp.WinForms.Experimental.ChromiumRenderWidgetHandleFinder.TryFindHandle(CefSharp.IWebBrowser,System.IntPtr@)">
            <summary>
            Chromium's message-loop Window isn't created synchronously, so this may not find it.
            If so, you need to wait and try again later.
            </summary>
            <param name="chromiumWebBrowser">ChromiumWebBrowser instance</param>
            <param name="chromerRenderWidgetHostHandle">Handle of the child HWND with the name <see cref="F:CefSharp.WinForms.Experimental.ChromiumRenderWidgetHandleFinder.ChromeRenderWidgetHostClassName"/></param>
            <returns>returns true if the HWND was found otherwise false.</returns>
        </member>
        <member name="M:CefSharp.WinForms.Experimental.ChromiumRenderWidgetHandleFinder.TryFindHandle(CefSharp.IBrowser,System.IntPtr@)">
            <summary>
            Chromium's message-loop Window isn't created synchronously, so this may not find it.
            If so, you need to wait and try again later.
            </summary>
            <param name="browser">IBrowser instance</param>
            <param name="chromerRenderWidgetHostHandle">Handle of the child HWND with the name <see cref="F:CefSharp.WinForms.Experimental.ChromiumRenderWidgetHandleFinder.ChromeRenderWidgetHostClassName"/></param>
            <returns>returns true if the HWND was found otherwise false.</returns>
        </member>
        <member name="M:CefSharp.WinForms.Experimental.ChromiumRenderWidgetHandleFinder.TryFindHandle(System.IntPtr,System.String,System.IntPtr@)">
            <summary>
            Helper function used to find the child HWND with the ClassName matching <paramref name="chromeRenderWidgetHostClassName"/>
            Chromium's message-loop Window isn't created synchronously, so this may not find it.
            If so, you need to wait and try again later.
            In most cases you should use the <see cref="M:CefSharp.WinForms.Experimental.ChromiumRenderWidgetHandleFinder.TryFindHandle(CefSharp.IWebBrowser,System.IntPtr@)"/> overload.
            </summary>
            <param name="chromiumWebBrowserHandle"><see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> control Handle</param>
            <param name="chromeRenderWidgetHostClassName">class name used to match</param>
            <param name="chromerRenderWidgetHostHandle">Handle of the child HWND with the name <see cref="F:CefSharp.WinForms.Experimental.ChromiumRenderWidgetHandleFinder.ChromeRenderWidgetHostClassName"/></param>
            <returns>returns true if the HWND was found otherwise false.</returns>
        </member>
        <member name="T:CefSharp.WinForms.Experimental.ChromiumWidgetNativeWindow">
            <summary>
            Provides a convenient <see cref="T:System.Windows.Forms.NativeWindow"/> implement
            that can be used without having to create your own class
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Experimental.ChromiumWidgetNativeWindow.#ctor(System.Windows.Forms.Control,System.IntPtr)">
            <summary>
            ChromiumWidgetMessageInterceptor constructor
            </summary>
            <param name="control">Control is used to handled the <see cref="E:System.Windows.Forms.Control.HandleDestroyed"/> event so
            we can automatically call <see cref="M:System.Windows.Forms.NativeWindow.ReleaseHandle"/>. If null then you are responsible
            for calling <see cref="M:System.Windows.Forms.NativeWindow.ReleaseHandle"/></param>
            <param name="chromeWidgetHostHandle">Hwnd to intercept messages for.</param>
        </member>
        <member name="M:CefSharp.WinForms.Experimental.ChromiumWidgetNativeWindow.OnWndProc(System.Func{System.Windows.Forms.Message,System.Boolean})">
            <summary>
            Register a Func which is used to intercept <see cref="M:CefSharp.WinForms.Experimental.ChromiumWidgetNativeWindow.WndProc(System.Windows.Forms.Message@)"/>
            calls. <paramref name="wndProcHandler"/> should return true if the message
            was handled, otherwise false.
            </summary>
            <param name="wndProcHandler">Func to be used to intercept messages, null to clear an existing function.</param>
        </member>
        <member name="M:CefSharp.WinForms.Experimental.ChromiumWidgetNativeWindow.WndProc(System.Windows.Forms.Message@)">
            <inheritdoc/>
        </member>
        <member name="T:CefSharp.WinForms.Handler.DisplayHandler">
            <summary>
            A WinForms Specific <see cref="T:CefSharp.IDisplayHandler"/> implementation that simplifies
            the process of hosting a Popup as a Control/Tab. Use this implementation as a base
            for your own custom implementation if you are using <see cref="M:CefSharp.WinForms.Handler.LifeSpanHandler.Create(CefSharp.WinForms.Handler.CreatePopupChromiumHostControl)"/>
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Handler.DisplayHandler.OnAddressChanged(CefSharp.IWebBrowser,CefSharp.AddressChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:CefSharp.WinForms.Handler.DisplayHandler.OnConsoleMessage(CefSharp.IWebBrowser,CefSharp.ConsoleMessageEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:CefSharp.WinForms.Handler.DisplayHandler.OnTitleChanged(CefSharp.IWebBrowser,CefSharp.TitleChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:CefSharp.WinForms.Handler.DisplayHandler.OnStatusMessage(CefSharp.IWebBrowser,CefSharp.StatusMessageEventArgs)">
            <inheritdoc />
        </member>
        <member name="T:CefSharp.WinForms.Handler.OnBeforePopupCreatedDelegate">
            <summary>
            Called <b>before</b>the popup is created, can be used to cancel popup creation if required
            or modify <see cref="T:CefSharp.IBrowserSettings"/>.
            It's important to note that the methods of this interface are called on a CEF UI thread,
            which by default is not the same as your application UI thread.
            </summary>
            <param name="chromiumWebBrowser">the ChromiumWebBrowser control</param>
            <param name="browser">The browser instance that launched this popup.</param>
            <param name="frame">The HTML frame that launched this popup.</param>
            <param name="targetUrl">The URL of the popup content. (This may be empty/null)</param>
            <param name="targetFrameName">The name of the popup. (This may be empty/null)</param>
            <param name="targetDisposition">The value indicates where the user intended to
            open the popup (e.g. current tab, new tab, etc)</param>
            <param name="userGesture">The value will be true if the popup was opened via explicit user gesture
            (e.g. clicking a link) or false if the popup opened automatically (e.g. via the DomContentLoaded event).</param>
            <param name="browserSettings">browser settings, defaults to source browsers</param>
            <returns>To cancel creation of the popup return true otherwise return false.</returns>
        </member>
        <member name="T:CefSharp.WinForms.Handler.OnPopupCreatedDelegate">
            <summary>
            Called when the <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/> has been created.
            When called you must add the control to it's intended parent
            so the <see cref="P:System.Windows.Forms.Control.ClientRectangle"/> can be calculated to set the initial
            size correctly.
            </summary>
            <param name="control">popup host control</param>
            <param name="url">url</param>
        </member>
        <member name="T:CefSharp.WinForms.Handler.OnPopupBrowserCreatedDelegate">
            <summary>
            Called when the <see cref="T:CefSharp.IBrowser"/> instance has been created.
            The <see cref="T:CefSharp.IBrowser"/> reference will be valid until <see cref="T:CefSharp.WinForms.Handler.OnPopupDestroyedDelegate"/> is called
            </summary>
            <param name="control">popup host control, maybe null if Browser is hosted in a native Popup window.
            DevTools by default will be hosted in a native popup window.</param>
            <param name="browser">browser</param>
        </member>
        <member name="T:CefSharp.WinForms.Handler.OnPopupDestroyedDelegate">
            <summary>
            Called when the <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/> is to be removed from it's parent.
            When called you must remove/dispose of the <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/>.
            </summary>
            <param name="control">popup host control</param>
            <param name="browser">browser</param>
        </member>
        <member name="T:CefSharp.WinForms.Handler.CreatePopupChromiumHostControl">
            <summary>
            Called to create a new instance of <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/>. Allows creation of a derived
            implementation of <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/>.
            </summary>
            <returns>A custom instance of <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/>.</returns>
        </member>
        <member name="T:CefSharp.WinForms.Handler.LifeSpanHandler">
            <summary>
            A WinForms Specific <see cref="T:CefSharp.ILifeSpanHandler"/> implementation that simplifies
            the process of hosting a Popup as a Control/Tab.
            This <see cref="T:CefSharp.ILifeSpanHandler"/> implementation returns true in <see cref="M:CefSharp.ILifeSpanHandler.DoClose(CefSharp.IWebBrowser,CefSharp.IBrowser)"/>
            so no WM_CLOSE message is sent, this differs from the default CEF behaviour.
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandler.#ctor(CefSharp.WinForms.Handler.CreatePopupChromiumHostControl)">
            <summary>
            Default constructor
            </summary>
            <param name="chromiumHostControlCreatedDelegate">Optional delegate used to create custom <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/> instances.</param>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandler.DoClose(CefSharp.IWebBrowser,CefSharp.IBrowser)">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandler.OnAfterCreated(CefSharp.IWebBrowser,CefSharp.IBrowser)">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandler.OnBeforeClose(CefSharp.IWebBrowser,CefSharp.IBrowser)">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandler.OnBeforePopup(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,System.String,System.String,CefSharp.WindowOpenDisposition,System.Boolean,CefSharp.IPopupFeatures,CefSharp.IWindowInfo,CefSharp.IBrowserSettings,System.Boolean@,CefSharp.IWebBrowser@)">
            <inheritdoc/>
            <remarks>
            NOTE: DevTools popups DO NOT trigger OnBeforePopup.
            </remarks>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandler.OnBeforePopupCreated(CefSharp.WinForms.Handler.OnBeforePopupCreatedDelegate)">
            <summary>
            The <see cref="T:CefSharp.WinForms.Handler.OnBeforePopupCreatedDelegate"/> will be called <b>before</b> the popup has been created and
            can be used to cancel popup creation if required or modify <see cref="T:CefSharp.IBrowserSettings"/>.
            </summary>
            <param name="onBeforePopupCreated">Action to be invoked before popup is created.</param>
            <returns><see cref="T:CefSharp.WinForms.Handler.LifeSpanHandler"/> instance allowing you to chain method calls together</returns>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandler.OnPopupCreated(CefSharp.WinForms.Handler.OnPopupCreatedDelegate)">
            <summary>
            The <see cref="T:CefSharp.WinForms.Handler.OnPopupCreatedDelegate"/> will be called when the<see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/> has been
            created. When the <see cref="T:CefSharp.WinForms.Handler.OnPopupCreatedDelegate"/> is called you must add the control to it's intended parent
            so the <see cref="P:System.Windows.Forms.Control.ClientRectangle"/> can be calculated to set the initial
            size correctly.
            </summary>
            <param name="onPopupCreated">Action to be invoked when the Popup host has been created and is ready to be attached to it's parent.</param>
            <returns><see cref="T:CefSharp.WinForms.Handler.LifeSpanHandler"/> instance allowing you to chain method calls together</returns>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandler.OnPopupBrowserCreated(CefSharp.WinForms.Handler.OnPopupBrowserCreatedDelegate)">
            <summary>
            The <see cref="T:CefSharp.WinForms.Handler.OnPopupBrowserCreatedDelegate"/> will be called when the<see cref="T:CefSharp.IBrowser"/> has been
            created. The <see cref="T:CefSharp.IBrowser"/> instance is valid until <see cref="M:CefSharp.WinForms.Handler.LifeSpanHandler.OnPopupDestroyed(CefSharp.WinForms.Handler.OnPopupDestroyedDelegate)"/>
            is called. <see cref="T:CefSharp.IBrowser"/> provides low level access to the CEF Browser, you can access frames, view source,
            perform navigation (via frame) etc.
            </summary>
            <param name="onPopupBrowserCreated">Action to be invoked when the <see cref="T:CefSharp.IBrowser"/> has been created.</param>
            <returns><see cref="T:CefSharp.WinForms.Handler.LifeSpanHandler"/> instance allowing you to chain method calls together</returns>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandler.OnPopupDestroyed(CefSharp.WinForms.Handler.OnPopupDestroyedDelegate)">
            <summary>
            The <see cref="T:CefSharp.WinForms.Handler.OnPopupDestroyedDelegate"/> will be called when the <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/> is to be
            removed from it's parent.
            When the <see cref="T:CefSharp.WinForms.Handler.OnPopupDestroyedDelegate"/> is called you must remove/dispose of the <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/>.
            </summary>
            <param name="onPopupDestroyed">Action to be invoked when the Popup is to be destroyed.</param>
            <returns><see cref="T:CefSharp.WinForms.Handler.LifeSpanHandler"/> instance allowing you to chain method calls together</returns>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandler.Create(CefSharp.WinForms.Handler.CreatePopupChromiumHostControl)">
            <summary>
            Create a new instance of the <see cref="T:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder"/>
            which can be used to create a WinForms specific <see cref="T:CefSharp.ILifeSpanHandler"/>
            implementation that simplifies the process of hosting a Popup as a Control/Tab.
            In scnarios where you also need to implement <see cref="T:CefSharp.ILoadHandler"/> then instead
            of implementing directly you will need to inherit from <see cref="T:CefSharp.WinForms.Handler.LoadHandler"/>.
            As it provides base functionality required to make <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/> events work correctly.
            </summary>
            <returns>
            A <see cref="T:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder"/> which can be used to fluently create an <see cref="T:CefSharp.ILifeSpanHandler"/>.
            Call <see cref="M:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder.Build"/> to create the actual instance after you have call
            <see cref="M:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder.OnPopupCreated(CefSharp.WinForms.Handler.OnPopupCreatedDelegate)"/> etc.
            </returns>
        </member>
        <member name="T:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder">
            <summary>
            Fluent <see cref="T:CefSharp.WinForms.Handler.LifeSpanHandler"/> Builder
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder.#ctor(CefSharp.WinForms.Handler.CreatePopupChromiumHostControl)">
            <summary>
            LifeSpanHandlerBuilder
            </summary>
            <param name="chromiumHostControlCreatedDelegate">
            When specified the delegate will be used to create the <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/>
            instance. Allowing users to create their own custom instance that extends <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/>
            </param>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder.OnBeforePopupCreated(CefSharp.WinForms.Handler.OnBeforePopupCreatedDelegate)">
            <summary>
            The <see cref="T:CefSharp.WinForms.Handler.OnBeforePopupCreatedDelegate"/> will be called <b>before</b> the popup has been created and
            can be used to cancel popup creation if required, modify <see cref="T:CefSharp.IBrowserSettings"/> and disable javascript.
            </summary>
            <param name="onBeforePopupCreated">Action to be invoked before popup is created.</param>
            <returns><see cref="T:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder"/> instance allowing you to chain method calls together</returns>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder.OnPopupCreated(CefSharp.WinForms.Handler.OnPopupCreatedDelegate)">
            <summary>
            The <see cref="T:CefSharp.WinForms.Handler.OnPopupCreatedDelegate"/> will be called when the<see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/> has been
            created. When the <see cref="T:CefSharp.WinForms.Handler.OnPopupCreatedDelegate"/> is called you must add the control to it's intended parent
            so the <see cref="P:System.Windows.Forms.Control.ClientRectangle"/> can be calculated to set the initial
            size correctly.
            </summary>
            <param name="onPopupCreated">Action to be invoked when the Popup is to be destroyed.</param>
            <returns><see cref="T:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder"/> instance allowing you to chain method calls together</returns>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder.OnPopupBrowserCreated(CefSharp.WinForms.Handler.OnPopupBrowserCreatedDelegate)">
            <summary>
            The <see cref="T:CefSharp.WinForms.Handler.OnPopupBrowserCreatedDelegate"/> will be called when the<see cref="T:CefSharp.IBrowser"/> has been
            created. The <see cref="T:CefSharp.IBrowser"/> instance is valid until <see cref="M:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder.OnPopupDestroyed(CefSharp.WinForms.Handler.OnPopupDestroyedDelegate)"/>
            is called. <see cref="T:CefSharp.IBrowser"/> provides low level access to the CEF Browser, you can access frames, view source,
            perform navigation (via frame) etc.
            </summary>
            <param name="onPopupBrowserCreated">Action to be invoked when the <see cref="T:CefSharp.IBrowser"/> has been created.</param>
            <returns><see cref="T:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder"/> instance allowing you to chain method calls together</returns>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder.OnPopupDestroyed(CefSharp.WinForms.Handler.OnPopupDestroyedDelegate)">
            <summary>
            The <see cref="T:CefSharp.WinForms.Handler.OnPopupDestroyedDelegate"/> will be called when the <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/> is to be
            removed from it's parent.
            When the <see cref="T:CefSharp.WinForms.Handler.OnPopupDestroyedDelegate"/> is called you must remove/dispose of the <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/>.
            </summary>
            <param name="onPopupDestroyed">Action to be invoked when the Popup is to be destroyed.</param>
            <returns><see cref="T:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder"/> instance allowing you to chain method calls together</returns>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LifeSpanHandlerBuilder.Build">
            <summary>
            Creates an <see cref="T:CefSharp.ILifeSpanHandler"/> implementation
            that can be used to host popups as tabs/controls. The resulting
            <see cref="T:CefSharp.ILifeSpanHandler"/> returns true in <see cref="M:CefSharp.ILifeSpanHandler.DoClose(CefSharp.IWebBrowser,CefSharp.IBrowser)"/>
            so no WM_CLOSE message is sent, this differs from the default CEF behaviour.
            </summary>
            <returns>a <see cref="T:CefSharp.ILifeSpanHandler"/> instance</returns>
        </member>
        <member name="T:CefSharp.WinForms.Handler.LoadHandler">
            <summary>
            A WinForms Specific <see cref="T:CefSharp.ILoadHandler"/> implementation that simplifies
            the process of hosting a Popup as a Control/Tab.Use this implementation as a base
            for your own custom implementation if you are using <see cref="M:CefSharp.WinForms.Handler.LifeSpanHandler.Create(CefSharp.WinForms.Handler.CreatePopupChromiumHostControl)"/>
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Handler.LoadHandler.OnFrameLoadEnd(CefSharp.IWebBrowser,CefSharp.FrameLoadEndEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:CefSharp.WinForms.Handler.LoadHandler.OnFrameLoadStart(CefSharp.IWebBrowser,CefSharp.FrameLoadStartEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:CefSharp.WinForms.Handler.LoadHandler.OnLoadError(CefSharp.IWebBrowser,CefSharp.LoadErrorEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:CefSharp.WinForms.Handler.LoadHandler.OnLoadingStateChange(CefSharp.IWebBrowser,CefSharp.LoadingStateChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="T:CefSharp.WinForms.Handler.PopupCreation">
            <summary>
            Popup Creation options
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.Handler.PopupCreation.Cancel">
            <summary>
            Popup creation is cancled, no further action will occur
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.Handler.PopupCreation.Continue">
            <summary>
            Popup creation will continue as per normal.
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.Handler.PopupCreation.ContinueWithJavascriptDisabled">
            <summary>
            Popup creation will continue with javascript disabled.
            </summary>
        </member>
        <member name="T:CefSharp.WinForms.Host.ChromiumHostControl">
            <summary>
            Chromium Browser Host Control, used for hosting Popups in WinForms
            </summary>
            <seealso cref="T:System.Windows.Forms.Control" />
        </member>
        <member name="P:CefSharp.WinForms.Host.ChromiumHostControl.BrowserCore">
            <summary>
            Get access to the core <see cref="T:CefSharp.IBrowser"/> instance.
            Maybe null if the underlying CEF Browser has not yet been
            created or if this control has been disposed. Check
            <see cref="P:CefSharp.IBrowser.IsDisposed"/> before accessing.
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.Host.ChromiumHostControl.LoadError">
            <summary>
            Event handler that will get called when the resource load for a navigation fails or is canceled.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.Host.ChromiumHostControl.FrameLoadStart">
            <summary>
            Event handler that will get called when the browser begins loading a frame. Multiple frames may be loading at the same
            time. Sub-frames may start or continue loading after the main frame load has ended. This method may not be called for a
            particular frame if the load request for that frame fails. For notification of overall browser load status use
            OnLoadingStateChange instead.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
            <remarks>Whilst this may seem like a logical place to execute js, it's called before the DOM has been loaded, implement
            <see cref="M:CefSharp.IRenderProcessMessageHandler.OnContextCreated(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame)" /> as it's called when the underlying V8Context is created
            </remarks>
        </member>
        <member name="E:CefSharp.WinForms.Host.ChromiumHostControl.FrameLoadEnd">
            <summary>
            Event handler that will get called when the browser is done loading a frame. Multiple frames may be loading at the same
            time. Sub-frames may start or continue loading after the main frame load has ended. This method will always be called
            for all frames irrespective of whether the request completes successfully.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.Host.ChromiumHostControl.LoadingStateChanged">
            <summary>
            Event handler that will get called when the Loading state has changed.
            This event will be fired twice. Once when loading is initiated either programmatically or
            by user action, and once when loading is terminated due to completion, cancellation of failure.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.Host.ChromiumHostControl.ConsoleMessage">
            <summary>
            Event handler for receiving Javascript console messages being sent from web pages.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            (The exception to this is when you're running with settings.MultiThreadedMessageLoop = false, then they'll be the same thread).
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.Host.ChromiumHostControl.StatusMessage">
            <summary>
            Event handler for changes to the status message.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang.
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            (The exception to this is when you're running with settings.MultiThreadedMessageLoop = false, then they'll be the same thread).
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.Host.ChromiumHostControl.AddressChanged">
            <summary>
            Occurs when the browser address changed.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.Host.ChromiumHostControl.TitleChanged">
            <summary>
            Occurs when the browser title changed.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.Host.ChromiumHostControl.IsLoading">
            <summary>
            A flag that indicates whether the control is currently loading one or more web pages (true) or not (false).
            </summary>
            <value><c>true</c> if this instance is loading; otherwise, <c>false</c>.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.WinForms.Host.ChromiumHostControl.Address">
            <summary>
            The address (URL) which the browser control is currently displaying.
            Will automatically be updated as the user navigates to another page (e.g. by clicking on a link).
            </summary>
            <value>The address.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.WinForms.Host.ChromiumHostControl.CanGoForward">
            <summary>
            A flag that indicates whether the state of the control currently supports the GoForward action (true) or not (false).
            </summary>
            <value><c>true</c> if this instance can go forward; otherwise, <c>false</c>.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.WinForms.Host.ChromiumHostControl.CanGoBack">
            <summary>
            A flag that indicates whether the state of the control current supports the GoBack action (true) or not (false).
            </summary>
            <value><c>true</c> if this instance can go back; otherwise, <c>false</c>.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.WinForms.Host.ChromiumHostControl.IsBrowserInitialized">
            <summary>
            A flag that indicates whether the WebBrowser is initialized (true) or not (false).
            </summary>
            <value><c>true</c> if this instance is browser initialized; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControl.OnFrameLoadStart(CefSharp.FrameLoadStartEventArgs)">
            <summary>
            Handles the <see cref="E:FrameLoadStart" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.FrameLoadStartEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControl.OnFrameLoadEnd(CefSharp.FrameLoadEndEventArgs)">
            <summary>
            Handles the <see cref="E:FrameLoadEnd" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.FrameLoadEndEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControl.OnConsoleMessage(CefSharp.ConsoleMessageEventArgs)">
            <summary>
            Handles the <see cref="E:ConsoleMessage" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.ConsoleMessageEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControl.OnStatusMessage(CefSharp.StatusMessageEventArgs)">
            <summary>
            Handles the <see cref="E:StatusMessage" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.StatusMessageEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControl.OnLoadError(CefSharp.LoadErrorEventArgs)">
            <summary>
            Handles the <see cref="E:LoadError" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.LoadErrorEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControl.OnLoadingStateChange(CefSharp.LoadingStateChangedEventArgs)">
            <summary>
            Sets the loading state change.
            </summary>
            <param name="args">The <see cref="T:CefSharp.LoadingStateChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControl.OnTitleChanged(CefSharp.TitleChangedEventArgs)">
            <summary>
            Sets the title.
            </summary>
            <param name="args">The <see cref="T:CefSharp.TitleChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControl.OnAddressChanged(CefSharp.AddressChangedEventArgs)">
            <summary>
            Sets the address.
            </summary>
            <param name="args">The <see cref="T:CefSharp.AddressChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControl.LoadUrl(System.String)">
            <summary>
            Loads the specified <paramref name="url"/> in the Main Frame.
            </summary>
            <param name="url">The URL to be loaded.</param>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControl.LoadUrlAsync(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControl.WaitForNavigationAsync(System.Nullable{System.TimeSpan},System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControl.GetMainFrame">
            <summary>
            Returns the main (top-level) frame for the browser window.
            </summary>
            <returns> the main frame</returns>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControl.Dispose(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControl.FromBrowser(CefSharp.IBrowser)">
            <summary>
            Gets the <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/> associated with
            a specific <see cref="T:CefSharp.IBrowser"/> instance. 
            </summary>
            <param name="browser">browser</param>
            <returns>returns the assocaited <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/> or null if Disposed or no host found.</returns>
        </member>
        <member name="T:CefSharp.WinForms.Host.ChromiumHostControlBase">
            <summary>
            Chromium Browser Host Control, provides base functionality for hosting a
            CefBrowser instance (main browser and popups) in WinForms.
            </summary>
            <seealso cref="T:System.Windows.Forms.Control" />
        </member>
        <member name="P:CefSharp.WinForms.Host.ChromiumHostControlBase.BrowserHwnd">
            <summary>
            IntPtr that represents the CefBrowser Hwnd
            Used for sending messages to the browser
            e.g. resize
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.Host.ChromiumHostControlBase.IsActivating">
            <summary>
            Set to true while handing an activating WM_ACTIVATE message.
            MUST ONLY be cleared by DefaultFocusHandler.
            </summary>
            <value><c>true</c> if this instance is activating; otherwise, <c>false</c>.</value>
        </member>
        <member name="E:CefSharp.WinForms.Host.ChromiumHostControlBase.IsBrowserInitializedChanged">
            <summary>
            Event called after the underlying CEF browser instance has been created. 
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.Host.ChromiumHostControlBase.DefaultSize">
            <summary>
            Gets the default size of the control.
            </summary>
            <value>
            The default <see cref="T:System.Drawing.Size" /> of the control.
            </value>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControlBase.IsInputKey(System.Windows.Forms.Keys)">
            <summary>
            Makes certain keys as Input keys when CefSettings.MultiThreadedMessageLoop = false
            </summary>
            <param name="keyData">key data</param>
            <returns>true for a select list of keys otherwise defers to base.IsInputKey</returns>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControlBase.OnSizeChanged(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.SizeChanged" /> event.
            </summary>
            <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControlBase.OnVisibleChanged(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControlBase.ResizeBrowser(System.Int32,System.Int32)">
            <summary>
            Resizes the browser to the specified <paramref name="width"/> and <paramref name="height"/>.
            If <paramref name="width"/> and <paramref name="height"/> are both 0 then the browser
            will be hidden and resource usage will be minimised.
            </summary>
            <param name="width">width</param>
            <param name="height">height</param>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControlBase.HideInternal">
            <summary>
            When minimized set the browser window size to 0x0 to reduce resource usage.
            https://github.com/chromiumembedded/cef/blob/c7701b8a6168f105f2c2d6b239ce3958da3e3f13/tests/cefclient/browser/browser_window_std_win.cc#L87
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControlBase.ShowInternal">
            <summary>
            Show the browser (called after previous minimised)
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControlBase.Dispose(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControlBase.RaiseIsBrowserInitializedChangedEvent">
            <summary>
            Trigger the <see cref="E:CefSharp.WinForms.Host.ChromiumHostControlBase.IsBrowserInitializedChanged"/> event
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Host.ChromiumHostControlBase.FromBrowser``1(CefSharp.IBrowser)">
            <summary>
            Gets the <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/> or <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> associated with
            a specific <see cref="T:CefSharp.IBrowser"/> instance. 
            </summary>
            <param name="browser">browser</param>
            <returns>returns the assocaited <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/> or <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> or null if Disposed or no host found.</returns>
        </member>
        <member name="T:CefSharp.WinForms.Internals.ControlExtensions">
            <summary>
            ControlExtensions.
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ControlExtensions.InvokeOnUiThreadIfRequired(System.Windows.Forms.Control,System.Action)">
            <summary>
            Executes the Action asynchronously on the UI thread, does not block execution on the calling thread.
            No action will be performed if the control doesn't have a valid handle or the control is Disposed/Disposing.
            </summary>
            <param name="control">the control for which the update is required</param>
            <param name="action">action to be performed on the control</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ControlExtensions.InvokeSyncOnUiThreadIfRequired(System.Windows.Forms.Control,System.Action)">
            <summary>
            Executes the Action sync on the UI thread, blocks execution on the calling thread.
            No action will be performed if the control doesn't have a valid handle or the control is Disposed/Disposing.
            </summary>
            <param name="control">the control for which the update is required</param>
            <param name="action">action to be performed on the control</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ControlExtensions.Activate(System.Windows.Forms.Control)">
            <summary>
            Activates the specified control.
            </summary>
            <param name="control">The control.</param>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ControlExtensions.IsActiveControl(System.Windows.Forms.Control)">
            <summary>
            Returns whether the supplied control is the currently
            active control.
            </summary>
            <param name="control">the control to check</param>
            <returns>true if the control is the currently active control</returns>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ControlExtensions.SelectNextControl(System.Windows.Forms.Control,System.Boolean)">
            <summary>
            Selects the next control.
            </summary>
            <param name="control">The control.</param>
            <param name="next">if set to <c>true</c> [next].</param>
        </member>
        <member name="T:CefSharp.WinForms.Internals.DefaultFocusHandler">
            <summary>
            Default implementation of <see cref="T:CefSharp.IFocusHandler" />
            for the WinForms implementation
            </summary>
            <seealso cref="T:CefSharp.IFocusHandler" />
        </member>
        <member name="M:CefSharp.WinForms.Internals.DefaultFocusHandler.OnGotFocus(CefSharp.IWebBrowser,CefSharp.IBrowser)">
            <summary>
            Called when the browser component has received focus.
            </summary>
            <param name="chromiumWebBrowser">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <remarks>Try to avoid needing to override this logic in a subclass. The implementation in
            DefaultFocusHandler relies on very detailed behavior of how WinForms and
            Windows interact during window activation.</remarks>
        </member>
        <member name="M:CefSharp.WinForms.Internals.DefaultFocusHandler.OnSetFocus(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.CefFocusSource)">
            <summary>
            Called when the browser component is requesting focus.
            </summary>
            <param name="chromiumWebBrowser">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="source">Indicates where the focus request is originating from.</param>
            <returns>Return false to allow the focus to be set or true to cancel setting the focus.</returns>
        </member>
        <member name="M:CefSharp.WinForms.Internals.DefaultFocusHandler.OnTakeFocus(CefSharp.IWebBrowser,CefSharp.IBrowser,System.Boolean)">
            <summary>
            Called when the browser component is about to lose focus.
            For instance, if focus was on the last HTML element and the user pressed the TAB key.
            </summary>
            <param name="chromiumWebBrowser">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="next">Will be true if the browser is giving focus to the next component
            and false if the browser is giving focus to the previous component.</param>
        </member>
        <member name="T:CefSharp.WinForms.Internals.NativeMethods">
            <summary>
            Class NativeMethods.
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.Internals.NativeMethods.WM_MOVE">
            <summary>
            The w m_ move
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.Internals.NativeMethods.WM_MOVING">
            <summary>
            The w m_ moving
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.Internals.NativeMethods.WM_ACTIVATE">
            <summary>
            The w m_ activate
            </summary>
        </member>
        <member name="T:CefSharp.WinForms.Internals.ParentFormMessageInterceptor">
            <summary>
            ParentFormMessageInterceptor - hooks into the parent forms
            message loop to incercept messages like WM_MOVE
            </summary>
            <seealso cref="T:System.Windows.Forms.NativeWindow" />
            <seealso cref="T:System.IDisposable" />
        </member>
        <member name="F:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.isMoving">
            <summary>
            Keep track of whether a move is in progress.
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.movingRectangle">
            <summary>
            Used to determine the coordinates involved in the move
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.previousWindowState">
            <summary>
            Store the previous window state, used to determine if the
            Windows was previously <see cref="F:System.Windows.Forms.FormWindowState.Minimized"/>
            and resume rendering
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.Browser">
            <summary>
            Gets or sets the browser.
            </summary>
            <value>The browser.</value>
        </member>
        <member name="P:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.ParentForm">
            <summary>
            Gets or sets the parent form.
            </summary>
            <value>The parent form.</value>
        </member>
        <member name="E:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.Moving">
            <summary>
            Called when the parent form is moving
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.#ctor(CefSharp.WinForms.Host.ChromiumHostControlBase)">
            <summary>
            Initializes a new instance of the <see cref="T:CefSharp.WinForms.Internals.ParentFormMessageInterceptor"/> class.
            </summary>
            <param name="browser">The browser.</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.RefindParentForm">
            <summary>
            Call to force refinding of the parent Form.
            (i.e. top level window that owns the ChromiumWebBrowserControl)
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.ParentParentChanged(System.Object,System.EventArgs)">
            <summary>
            Adjust the form to listen to if the ChromiumWebBrowserControl's parent changes.
            </summary>
            <param name="sender">The ChromiumWebBrowser whose parent has changed.</param>
            <param name="e">The <see cref="T:System.EventArgs" /> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.OnHandleCreated(System.Object,System.EventArgs)">
            <summary>
            Handles the <see cref="E:HandleCreated" /> event.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.OnHandleDestroyed(System.Object,System.EventArgs)">
            <summary>
            Handles the <see cref="E:HandleDestroyed" /> event.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.WndProc(System.Windows.Forms.Message@)">
            <summary>
            Invokes the default window procedure associated with this window.
            </summary>
            <param name="m">A <see cref="T:System.Windows.Forms.Message" /> that is associated with the current Windows message.</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.OnMoving">
            <summary>
            Called when [moving].
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.OnThreadException(System.Exception)">
            <summary>
            When overridden in a derived class, manages an unhandled thread exception.
            </summary>
            <param name="e">An <see cref="T:System.Exception" /> that specifies the unhandled thread exception.</param>
        </member>
        <member name="T:CefSharp.WinForms.IWinFormsChromiumWebBrowser">
            <summary>
            Winforms Specific Chromium browser implementation, differs from <see cref="T:CefSharp.WinForms.IWinFormsWebBrowser"/> in that
            this interface is implemented by both <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> and <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/>
            where <see cref="T:CefSharp.WinForms.IWinFormsWebBrowser"/> is only implemented by <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/>
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.IWinFormsChromiumWebBrowser.TitleChanged">
            <summary>
            Occurs when the browser title changed.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread. 
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.IWinFormsChromiumWebBrowser.AddressChanged">
            <summary>
            Occurs when the browser address changed.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread. 
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.IWinFormsChromiumWebBrowser.IsBrowserInitializedChanged">
            <summary>
            Event called after the underlying CEF browser instance has been created. 
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.IWinFormsChromiumWebBrowser.CanSelect">
            <inheritdoc cref="P:System.Windows.Forms.Control.CanSelect"/>
        </member>
        <member name="M:CefSharp.WinForms.IWinFormsChromiumWebBrowser.Select">
            <inheritdoc cref="M:System.Windows.Forms.Control.Select"/>
        </member>
        <member name="T:CefSharp.WinForms.IWinFormsWebBrowser">
            <summary>
            WinForms specific implementation, has events the
            <see cref="T:CefSharp.WinForms.ChromiumWebBrowser" /> implementation exposes.
            </summary>
            <seealso cref="T:CefSharp.IWebBrowser" /> and <seealso cref="T:CefSharp.IChromiumWebBrowserBase"/>
        </member>
        <member name="T:CefSharp.WinForms.WebBrowserExtensions">
            <summary>
            Helper extensions for performing common CefSharp related WinForms tasks
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.WebBrowserExtensions.DestroyWindow(CefSharp.IChromiumWebBrowserBase)">
            <summary>
            Manually call https://docs.microsoft.com/en-us/windows/win32/api/winuser/nf-winuser-destroywindow
            passing in the handle returned from <see cref="M:CefSharp.IBrowserHost.GetWindowHandle"/>.
            This method can be used to manually close the underlying CefBrowser instance.
            This will avoid the WM_Close message that CEF sends by default to the top level window.
            (Which closes your application). This method should generally only be used in the WinForms version.
            </summary>
            <param name="chromiumWebBrowser">the <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> or <see cref="T:CefSharp.WinForms.Host.ChromiumHostControl"/> instance.</param>
            <returns>If the function succeeds, the return value is true.</returns>
            <example>
            <code>
            //Invoke on the CEF UI Thread
            Cef.UIThreadTaskFactory.StartNew(() =>
            {
              var closed = chromiumWebBrowser.DestroyWindow();
            });
            </code>
            </example>
        </member>
        <member name="M:CefSharp.WinForms.WebBrowserExtensions.ShowDevToolsDocked(CefSharp.IChromiumWebBrowserBase,System.Windows.Forms.Control,System.String,System.Windows.Forms.DockStyle,System.Int32,System.Int32)">
            <summary>
            Open DevTools using <paramref name="parentControl"/> as the parent control. If inspectElementAtX and/or inspectElementAtY are specified then
            the element at the specified (x,y) location will be inspected.
            For resize/moving to work correctly you will need to use the <see cref="T:CefSharp.WinForms.Handler.LifeSpanHandler"/> implementation.
            (Set <see cref="P:CefSharp.WinForms.ChromiumWebBrowser.LifeSpanHandler"/> to an instance of <see cref="T:CefSharp.WinForms.Handler.LifeSpanHandler"/>)
            </summary>
            <param name="chromiumWebBrowser"><see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> instance</param>
            <param name="parentControl">Control used as the parent for DevTools (a custom control will be added to the <see cref="P:System.Windows.Forms.Control.Controls"/> collection)</param>
            <param name="controlName">Control name</param>
            <param name="dockStyle">Dock Style</param>
            <param name="inspectElementAtX">x coordinate (used for inspectElement)</param>
            <param name="inspectElementAtY">y coordinate (used for inspectElement)</param>
            <returns>Returns the <see cref="T:System.Windows.Forms.Control"/> that hosts the DevTools instance if successful, otherwise returns null on error.</returns>
        </member>
        <member name="M:CefSharp.WinForms.WebBrowserExtensions.ShowDevToolsDocked(CefSharp.IChromiumWebBrowserBase,System.Action{CefSharp.WinForms.Host.ChromiumHostControl},System.String,System.Windows.Forms.DockStyle,System.Int32,System.Int32)">
            <summary>
            Open DevTools using your own Control as the parent. If inspectElementAtX and/or inspectElementAtY are specified then
            the element at the specified (x,y) location will be inspected.
            For resize/moving to work correctly you will need to use the <see cref="T:CefSharp.WinForms.Handler.LifeSpanHandler"/> implementation.
            (Set <see cref="P:CefSharp.WinForms.ChromiumWebBrowser.LifeSpanHandler"/> to an instance of <see cref="T:CefSharp.WinForms.Handler.LifeSpanHandler"/>)
            </summary>
            <param name="chromiumWebBrowser"><see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> instance</param>
            <param name="addParentControl">
            Action that is Invoked when the DevTools Host Control has been created and needs to be added to it's parent.
            It's important the control is added to it's intended parent at this point so the <see cref="P:System.Windows.Forms.Control.ClientRectangle"/>
            can be calculated to set the initial display size.</param>
            <param name="controlName">control name</param>
            <param name="dockStyle">Dock Style</param>
            <param name="inspectElementAtX">x coordinate (used for inspectElement)</param>
            <param name="inspectElementAtY">y coordinate (used for inspectElement)</param>
            <returns>Returns the <see cref="T:System.Windows.Forms.Control"/> that hosts the DevTools instance if successful, otherwise returns null on error.</returns>
        </member>
    </members>
</doc>
