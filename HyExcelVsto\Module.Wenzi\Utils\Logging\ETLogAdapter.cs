using System;
using System.Windows.Forms;
using ET;
using Microsoft.Extensions.Logging;

namespace ZnExcelVsto.Module.Wenzi.Utils.Logging
{
    /// <summary>
    /// ETLogManager适配器，实现Microsoft.Extensions.Logging.ILogger接口
    /// </summary>
    public class ETLogAdapter : Microsoft.Extensions.Logging.ILogger
    {
        readonly string _category;
        readonly TextBox _textBox;

        /// <summary>
        /// 构造函数 - 只使用分类名
        /// </summary>
        /// <param name="category">日志分类名称</param>
        public ETLogAdapter(string category)
        {
            _category = category ?? throw new ArgumentNullException(nameof(category));
            _textBox = null;
        }

        /// <summary>
        /// 构造函数 - 使用分类名和TextBox
        /// </summary>
        /// <param name="textBox">日志输出文本框</param>
        /// <param name="category">日志分类名称</param>
        public ETLogAdapter(TextBox textBox, string category)
        {
            _textBox = textBox;
            _category = category ?? throw new ArgumentNullException(nameof(category));
        }

        /// <summary>
        /// 将此适配器转换为legacy ILogger
        /// </summary>
        public Module.Wenzi.Utils.Logging.ILogger ToLegacyLogger()
        {
            return new LegacyLoggerAdapter(this);
        }

        /// <summary>
        /// 开始一个日志范围（未实现）
        /// </summary>
        public IDisposable BeginScope<TState>(TState state)
        {
            return null; // 简单实现，不支持范围
        }

        /// <summary>
        /// 检查是否应该记录指定级别的日志
        /// </summary>
        public bool IsEnabled(LogLevel logLevel)
        {
            return true; // 始终启用
        }

        /// <summary>
        /// 记录日志
        /// </summary>
        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {
            if (!IsEnabled(logLevel))
                return;

            string message = formatter(state, exception);
            string formattedMessage = $"[{_category}] {message}";

            // 根据日志级别调用对应的ETLogManager方法
            switch (logLevel)
            {
                case LogLevel.Trace:
                case LogLevel.Debug:
                    if (_textBox != null)
                        ETLogManager.LogToTextBox(_textBox, "调试", formattedMessage);
                    else
                        ETLogManager.Debug(formattedMessage);
                    break;
                case LogLevel.Information:
                    if (_textBox != null)
                        ETLogManager.LogToTextBox(_textBox, "信息", formattedMessage);
                    else
                        ETLogManager.Info(formattedMessage);
                    break;
                case LogLevel.Warning:
                    if (_textBox != null)
                        ETLogManager.LogToTextBox(_textBox, "警告", formattedMessage);
                    else
                        ETLogManager.Warning(formattedMessage);
                    break;
                case LogLevel.Error:
                case LogLevel.Critical:
                    if (_textBox != null)
                        ETLogManager.LogToTextBox(_textBox, "错误", formattedMessage);
                    else if (exception != null)
                        ETLogManager.Error(formattedMessage, exception);
                    else
                        ETLogManager.Error(formattedMessage);
                    break;
                default:
                    if (_textBox != null)
                        ETLogManager.LogToTextBox(_textBox, "未知", formattedMessage);
                    else
                        ETLogManager.Info(formattedMessage);
                    break;
            }
        }
    }

    /// <summary>
    /// 传统ILogger接口的适配器，将Microsoft.Extensions.Logging.ILogger转换为传统ILogger
    /// </summary>
    public class LegacyLoggerAdapter : Module.Wenzi.Utils.Logging.ILogger
    {
        readonly Microsoft.Extensions.Logging.ILogger _logger;

        public LegacyLoggerAdapter(Microsoft.Extensions.Logging.ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public void LogDebug(string message)
        {
            _logger.LogDebug(message);
        }

        public void LogInformation(string message)
        {
            _logger.LogInformation(message);
        }

        public void LogWarning(string message)
        {
            _logger.LogWarning(message);
        }

        public void LogError(string message)
        {
            _logger.LogError(message);
        }

        public void LogError(string message, Exception exception)
        {
            _logger.LogError(exception, message);
        }

        public void LogValidation(string fieldName, string value, string status, string message)
        {
            string validationMessage = $"验证字段 '{fieldName}' (值: '{value}') {status}: {message}";
            if (status == "失败")
                _logger.LogWarning(validationMessage);
            else
                _logger.LogInformation(validationMessage);
        }
    }
}