using ET;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// WebBrowser标签页管理器，负责标签页的创建、管理和销毁
    /// </summary>
    public class WebBrowserTabManager : IDisposable
    {
        #region 字段和属性

        /// <summary>
        /// TabControl控件引用
        /// </summary>
        private readonly TabControl _tabControl;

        /// <summary>
        /// 配置管理器
        /// </summary>
        private readonly WebBrowserConfigManager _configManager;

        /// <summary>
        /// Cookie管理器
        /// </summary>
        private readonly WebBrowserCookieManager _cookieManager;

        /// <summary>
        /// 会话管理器
        /// </summary>
        private readonly WebBrowserSessionManager _sessionManager;

        /// <summary>
        /// 用于记录的对象
        /// </summary>
        private readonly object _logSource;

        /// <summary>
        /// 标签页URL变更事件委托
        /// </summary>
        public delegate void TabUrlChangedEventHandler(string tabName, string url);

        /// <summary>
        /// 标签页标题变更事件委托
        /// </summary>
        public delegate void TabTitleChangedEventHandler(string tabName, string title);

        /// <summary>
        /// 标签页切换事件委托
        /// </summary>
        public delegate void TabSwitchedEventHandler(string sectionId);

        /// <summary>
        /// 标签页URL变更事件
        /// </summary>
        public event TabUrlChangedEventHandler TabUrlChanged;

        /// <summary>
        /// 标签页标题变更事件
        /// </summary>
        public event TabTitleChangedEventHandler TabTitleChanged;

        /// <summary>
        /// 标签页切换事件
        /// </summary>
        public event TabSwitchedEventHandler TabSwitched;

        /// <summary>
        /// 标签页数据字典，使用SectionId作为键
        /// </summary>
        public Dictionary<string, TabData> TabDataDictionary { get; } = new Dictionary<string, TabData>();

        /// <summary>
        /// 标签页名称到SectionId的映射，用于兼容旧代码
        /// </summary>
        private Dictionary<string, string> TabNameToSectionId { get; } = new Dictionary<string, string>();

        /// <summary>
        /// 用于保护TabDataDictionary和TabNameToSectionId的并发访问锁
        /// </summary>
        private readonly object _tabDataLock = new object();

        /// <summary>
        /// 当前选中的标签页的SectionId
        /// </summary>
        public string CurrentTabSectionId { get; private set; }

        /// <summary>
        /// 当前选中的标签页名称（兼容旧代码）
        /// </summary>
        public string CurrentTabName
        {
            get
            {
                if (string.IsNullOrEmpty(CurrentTabSectionId) || !TabDataDictionary.TryGetValue(CurrentTabSectionId, out TabData tabData))
                    return string.Empty;

                return tabData.Config.Name;
            }
        }

        // 添加常量字符串，避免重复创建相同字符串
        private const string HTTP_PREFIX = "http://";

        private const string HTTPS_PREFIX = "https://";
        private const string FILE_PREFIX = "file://";
        private const string ABOUT_PREFIX = "about:";
        private const string ABOUT_BLANK = "about:blank";
        private const string USER_AGENT_ARG = "--user-agent=\"{0}\"";
        private const string PROXY_SERVER_ARG = "--proxy-server={0}:{1}";

        /// <summary>
        /// 线程安全地获取所有标签页配置的副本
        /// </summary>
        /// <returns>标签页配置列表</returns>
        public List<WebBrowserTabConfig> GetAllTabConfigsSafely()
        {
            lock (_tabDataLock)
            {
                List<WebBrowserTabConfig> configs = new List<WebBrowserTabConfig>();
                foreach (var pair in TabDataDictionary)
                {
                    configs.Add(pair.Value.Config);
                }
                return configs;
            }
        }

        /// <summary>
        /// 线程安全地检查标签页是否存在
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>是否存在</returns>
        public bool ContainsTabSafely(string sectionId)
        {
            lock (_tabDataLock)
            {
                return TabDataDictionary.ContainsKey(sectionId);
            }
        }

        #endregion 字段和属性

        #region 内部类

        /// <summary>
        /// 标签页数据类
        /// </summary>
        public class TabData
        {
            /// <summary>
            /// 标签页
            /// </summary>
            public TabPage TabPage { get; set; }

            /// <summary>
            /// WebView2控件
            /// </summary>
            public WebView2 WebView { get; set; }

            /// <summary>
            /// 标签页配置
            /// </summary>
            public WebBrowserTabConfig Config { get; set; }

            /// <summary>
            /// 最后访问的URL
            /// </summary>
            public string LastUrl { get; set; }

            /// <summary>
            /// WebView2环境
            /// </summary>
            public CoreWebView2Environment Environment { get; set; }

            /// <summary>
            /// 是否已初始化WebView2
            /// </summary>
            public bool IsWebViewInitialized { get; set; }

            /// <summary>
            /// WebResourceRequested事件处理器引用，用于在清理资源时解除事件绑定
            /// </summary>
            public EventHandler<CoreWebView2WebResourceRequestedEventArgs> WebResourceRequestedHandler { get; set; }

            /// <summary>
            /// 是否为副本标签页
            /// </summary>
            public bool IsClone { get; set; }

            /// <summary>
            /// 父标签页的SectionId
            /// </summary>
            public string ParentSectionId { get; set; }
        }

        #endregion 内部类

        #region 构造方法

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="tabControl">TabControl控件</param>
        /// <param name="configManager">配置管理器</param>
        /// <param name="cookieManager">Cookie管理器</param>
        /// <param name="sessionManager">会话管理器</param>
        /// <param name="logSource">用于记录日志的对象</param>
        public WebBrowserTabManager(TabControl tabControl, WebBrowserConfigManager configManager,
            WebBrowserCookieManager cookieManager, WebBrowserSessionManager sessionManager, object logSource)
        {
            _tabControl = tabControl;
            _configManager = configManager;
            _cookieManager = cookieManager;
            _sessionManager = sessionManager;
            _logSource = logSource ?? this;

            // 注册标签页切换事件
            _tabControl.SelectedIndexChanged += TabControl_SelectedIndexChanged;
        }

        #endregion 构造方法

        #region 标签页管理方法

        /// <summary>
        /// 根据配置创建新标签页
        /// </summary>
        /// <param name="config">标签页配置</param>
        /// <returns>新标签页的SectionId</returns>
        public string CreateNewTab(WebBrowserTabConfig config)
        {
            // 检查配置是否为空
            if (config == null)
            {
                //返回错误提示结束
                MessageBox.Show("标签页配置不能为空！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }

            // 确保名称不为空
            if (string.IsNullOrEmpty(config.Name))
            {
                //返回错误提示，结束
                MessageBox.Show("标签页名称不能为空！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }

            // 确保SectionId不为空
            if (string.IsNullOrEmpty(config.SectionId))
            {
                config.GenerateNewSectionId();
                ETLogManager.Info(_logSource, $"为新标签页生成SectionId: {config.SectionId}");
            }

            // 使用配置创建标签页
            return CreateTabWithConfig(config);
        }

        /// <summary>
        /// 使用配置创建标签页
        /// </summary>
        /// <param name="config">标签页配置</param>
        /// <returns>标签页的SectionId</returns>
        private string CreateTabWithConfig(WebBrowserTabConfig config)
        {
            try
            {
                // 创建新的TabPage，使用配置中的名称
                TabPage tabPage = new TabPage(config.Name)
                {
                    UseVisualStyleBackColor = true,
                    Name = config.SectionId // 使用SectionId作为TabPage的Name属性
                };

                // 将TabPage添加到TabControl
                _tabControl.TabPages.Add(tabPage);

                // 创建WebView2控件，注意不要设置Source属性，避免自动初始化
                WebView2 webView = new WebView2
                {
                    Dock = DockStyle.Fill,
                    DefaultBackgroundColor = System.Drawing.Color.White
                };

                // 创建标签页数据
                TabData tabData = new TabData
                {
                    TabPage = tabPage,
                    WebView = webView,
                    Config = config,
                    LastUrl = config.Url,
                    IsWebViewInitialized = false,
                    IsClone = false, // 确保新标签页标记为非副本
                    ParentSectionId = null // 确保新标签页无父标签页
                };

                // 使用锁保护字典的并发访问
                lock (_tabDataLock)
                {
                    // 添加到字典，使用SectionId作为键
                    TabDataDictionary[config.SectionId] = tabData;

                    // 更新名称到SectionId的映射
                    TabNameToSectionId[config.Name] = config.SectionId;
                }

                // 添加WebView2控件到TabPage
                tabPage.Controls.Add(webView);

                // 确保在UI线程上初始化WebView2环境
                if (_tabControl.InvokeRequired)
                {
                    _tabControl.BeginInvoke(new Action(async () => await InitializeWebView2Async(config.SectionId)));
                }
                else
                {
                    // 直接在UI线程上异步初始化，避免Task.Run导致的线程问题
                    _ = InitializeWebView2Async(config.SectionId);
                }

                // 保存配置到文件（只有非副本标签页才保存）
                if (!tabData.IsClone)
                {
                    _configManager.SaveTabConfig(config);
                }
                else
                {
                    ETLogManager.Debug(_logSource, $"跳过保存副本标签页配置: {config.Name}");
                }

                // 选择新创建的标签页
                _tabControl.SelectedTab = tabPage;
                CurrentTabSectionId = config.SectionId;

                // 触发TabSwitched事件，确保UI更新
                TabSwitched?.Invoke(config.SectionId);

                ETLogManager.Info(_logSource, $"已创建标签页: {config.Name}, SectionId: {config.SectionId}");

                // 启动会话刷新（如果配置中启用）
                if (config.EnableWebView2Refresh && config.WebView2RefreshInterval > 0)
                {
                    _sessionManager.StartTabRefresher(config.SectionId, config.WebView2RefreshInterval);
                }

                // 启动HttpClient会话维持（如果配置中启用）
                if (config.EnableHttpClientRefresh && config.HttpClientRefreshInterval > 0)
                {
                    // 初始化会话维持器，这会在初始化WebView2后异步完成
                    ETLogManager.Info(_logSource, $"标签页 {config.Name} (SectionId: {config.SectionId}) 启用HttpClient会话维持，等待WebView2初始化完成后自动启动");
                }

                return config.SectionId;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"创建标签页失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 关闭当前标签页
        /// </summary>
        public void CloseCurrentTab()
        {
            if (string.IsNullOrEmpty(CurrentTabSectionId))
                return;

            CloseTabBySectionId(CurrentTabSectionId);
        }

        /// <summary>
        /// 根据SectionId关闭标签页
        /// </summary>
        /// <param name="sectionId">标签页的SectionId</param>
        /// <returns>是否成功关闭</returns>
        public bool CloseTabBySectionId(string sectionId)
        {
            if (string.IsNullOrEmpty(sectionId) || !TabDataDictionary.TryGetValue(sectionId, out TabData tabData))
            {
                ETLogManager.Error(_logSource, $"关闭标签页失败: 未找到标签页 SectionId={sectionId}");
                return false;
            }

            try
            {
                string tabName = tabData.Config.Name;
                ETLogManager.Info(_logSource, $"关闭标签页: {tabName}, SectionId: {sectionId}");

                // 在关闭前保存当前配置状态（只有非副本标签页才保存）
                if (!tabData.IsClone && tabData.Config != null)
                {
                    try
                    {
                        _configManager.SaveTabConfig(tabData.Config);
                        ETLogManager.Info(_logSource, $"关闭前已保存标签页配置: {tabData.Config.Name}, SectionId: {sectionId}");
                    }
                    catch (Exception saveEx)
                    {
                        ETLogManager.Error(_logSource, $"关闭前保存配置失败: {saveEx.Message}");
                    }
                }

                // 清理标签页资源
                CleanupTabResources(sectionId);

                // 移除标签页
                if (tabData.TabPage != null)
                {
                    _tabControl.TabPages.Remove(tabData.TabPage);
                }

                // 如果已经清空了所有标签页，重置当前标签页名称
                if (_tabControl.TabPages.Count == 0)
                {
                    CurrentTabSectionId = null;
                    ETLogManager.Info(_logSource, "已关闭所有标签页");
                }

                // 移除名称到SectionId的映射
                if (TabNameToSectionId.ContainsKey(tabName))
                {
                    TabNameToSectionId.Remove(tabName);
                }

                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"关闭标签页失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 清理标签页资源
        /// </summary>
        /// <param name="sectionId">标签页的SectionId</param>
        private void CleanupTabResources(string sectionId)
        {
            if (!TabDataDictionary.TryGetValue(sectionId, out TabData tabData))
                return;

            try
            {
                string tabName = tabData.Config.Name;

                // 停止刷新定时器
                _sessionManager.StopTabRefresh(sectionId);

                // 清理WebView2资源
                if (tabData.WebView != null)
                {
                    try
                    {
                        // 停止所有导航
                        if (tabData.WebView.CoreWebView2 != null)
                        {
                            try
                            {
                                tabData.WebView.CoreWebView2.Stop();
                            }
                            catch (Exception ex)
                            {
                                ETLogManager.Warning(_logSource, $"停止导航失败: {ex.Message}");
                            }
                        }

                        // 安全移除WebView2控件事件，避免在已释放资源上操作
                        try
                        {
                            // 分别处理每个事件的取消订阅，记录具体的失败信息
                            try
                            {
                                tabData.WebView.NavigationStarting -= WebView_NavigationStarting;
                                ETLogManager.Debug(_logSource, "已取消订阅NavigationStarting事件");
                            }
                            catch (Exception ex)
                            {
                                ETLogManager.Warning(_logSource, $"取消订阅NavigationStarting事件失败: {ex.Message}");
                            }

                            try
                            {
                                tabData.WebView.NavigationCompleted -= WebView_NavigationCompleted;
                                ETLogManager.Debug(_logSource, "已取消订阅NavigationCompleted事件");
                            }
                            catch (Exception ex)
                            {
                                ETLogManager.Warning(_logSource, $"取消订阅NavigationCompleted事件失败: {ex.Message}");
                            }

                            try
                            {
                                tabData.WebView.SourceChanged -= WebView_SourceChanged;
                                ETLogManager.Debug(_logSource, "已取消订阅SourceChanged事件");
                            }
                            catch (Exception ex)
                            {
                                ETLogManager.Warning(_logSource, $"取消订阅SourceChanged事件失败: {ex.Message}");
                            }
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Warning(_logSource, $"移除WebView2控件事件失败: {ex.Message}");
                        }

                        // 移除地址更改事件
                        if (tabData.WebView.CoreWebView2 != null)
                        {
                            try
                            {
                                // 清除WebResourceRequested事件
                                if (tabData.WebResourceRequestedHandler != null)
                                {
                                    try
                                    {
                                        tabData.WebView.CoreWebView2.WebResourceRequested -= tabData.WebResourceRequestedHandler;
                                        ETLogManager.Debug(_logSource, "已取消订阅WebResourceRequested事件");
                                    }
                                    catch (Exception ex)
                                    {
                                        ETLogManager.Warning(_logSource, $"取消订阅WebResourceRequested事件失败: {ex.Message}");
                                    }
                                    tabData.WebResourceRequestedHandler = null;
                                }

                                // 清除通用事件，分别处理每个事件
                                try
                                {
                                    tabData.WebView.CoreWebView2.SourceChanged -= WebView_SourceChanged;
                                    ETLogManager.Debug(_logSource, "已取消订阅CoreWebView2.SourceChanged事件");
                                }
                                catch (Exception ex)
                                {
                                    ETLogManager.Warning(_logSource, $"取消订阅CoreWebView2.SourceChanged事件失败: {ex.Message}");
                                }

                                try
                                {
                                    tabData.WebView.CoreWebView2.NavigationStarting -= WebView_NavigationStarting;
                                    ETLogManager.Debug(_logSource, "已取消订阅CoreWebView2.NavigationStarting事件");
                                }
                                catch (Exception ex)
                                {
                                    ETLogManager.Warning(_logSource, $"取消订阅CoreWebView2.NavigationStarting事件失败: {ex.Message}");
                                }

                                try
                                {
                                    tabData.WebView.CoreWebView2.NavigationCompleted -= WebView_NavigationCompleted;
                                    ETLogManager.Debug(_logSource, "已取消订阅CoreWebView2.NavigationCompleted事件");
                                }
                                catch (Exception ex)
                                {
                                    ETLogManager.Warning(_logSource, $"取消订阅CoreWebView2.NavigationCompleted事件失败: {ex.Message}");
                                }

                                // 移除其他可能存在的CoreWebView2事件 确保只解绑类中已定义的事件处理方法

                                // 移除所有资源请求过滤器
                                try { tabData.WebView.CoreWebView2.RemoveWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All); } catch { }

                                // 注释掉不可用的方法调用 tabData.WebView.CoreWebView2.ClearBrowsingDataAsync();
                            }
                            catch (Exception ex)
                            {
                                ETLogManager.Warning(_logSource, $"清除CoreWebView2事件失败: {ex.Message}");
                            }
                        }

                        // 导航到空白页以释放当前页面资源
                        try
                        {
                            tabData.WebView.Source = new Uri("about:blank");
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Warning(_logSource, $"导航到空白页失败: {ex.Message}");
                        }

                        // 分离并销毁WebView2
                        if (tabData.TabPage != null)
                        {
                            if (tabData.TabPage.Controls.Contains(tabData.WebView))
                            {
                                tabData.TabPage.Controls.Remove(tabData.WebView);
                            }

                            // 清空TabPage的所有控件
                            tabData.TabPage.Controls.Clear();
                        }

                        // 释放WebView2资源
                        tabData.WebView.Dispose();
                        tabData.WebView = null;

                        // 清理环境
                        if (tabData.Environment != null)
                        {
                            // CoreWebView2Environment没有提供显式的Dispose方法，但可以将引用设为null帮助GC回收
                            tabData.Environment = null;
                        }

                        // 提示GC回收
                        GC.Collect(0, GCCollectionMode.Optimized);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"清理WebView2资源失败: {ex.Message}");
                    }
                }

                // 使用锁保护字典的并发访问
                lock (_tabDataLock)
                {
                    // 移除标签页数据
                    TabDataDictionary.Remove(sectionId);

                    // 从映射中删除
                    List<string> keysToRemove = new List<string>();
                    foreach (KeyValuePair<string, string> pair in TabNameToSectionId)
                    {
                        if (pair.Value == sectionId)
                        {
                            keysToRemove.Add(pair.Key);
                        }
                    }

                    foreach (string key in keysToRemove)
                    {
                        TabNameToSectionId.Remove(key);
                    }
                }

                ETLogManager.Info(_logSource, $"已清理标签页资源: {tabName}, SectionId: {sectionId}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"清理标签页资源失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导航到指定URL
        /// </summary>
        /// <param name="url">URL</param>
        public void NavigateToUrl(string url)
        {
            try
            {
                // 检查当前标签页SectionId
                if (string.IsNullOrEmpty(CurrentTabSectionId))
                {
                    ETLogManager.Error(_logSource, "导航失败：未选中标签页");
                    return;
                }

                // 获取当前标签页数据
                if (!TabDataDictionary.TryGetValue(CurrentTabSectionId, out TabData tabData))
                {
                    ETLogManager.Error(_logSource, $"导航失败：未找到标签页 SectionId={CurrentTabSectionId}");
                    return;
                }

                // 确保WebView2已初始化
                if (tabData.WebView == null || !tabData.IsWebViewInitialized)
                {
                    ETLogManager.Error(_logSource, $"导航失败：标签页 {tabData.Config.Name} 的WebView2未初始化");
                    return;
                }

                // 格式化URL
                string formattedUrl = GetFormattedUrl(url);
                Uri uri = new Uri(formattedUrl);

                // 只更新LastUrl，不更新配置
                tabData.LastUrl = formattedUrl;

                // 导航到新URL
                tabData.WebView.Source = uri;

                ETLogManager.Info(_logSource, $"导航到URL: {formattedUrl}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"导航失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 后退
        /// </summary>
        public void GoBack()
        {
            if (!string.IsNullOrEmpty(CurrentTabSectionId) && TabDataDictionary.TryGetValue(CurrentTabSectionId, out TabData tabData))
            {
                try
                {
                    if (tabData.WebView.CanGoBack)
                    {
                        tabData.WebView.GoBack();
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, ex);
                }
            }
        }

        /// <summary>
        /// 前进
        /// </summary>
        public void GoForward()
        {
            if (!string.IsNullOrEmpty(CurrentTabSectionId) && TabDataDictionary.TryGetValue(CurrentTabSectionId, out TabData tabData))
            {
                try
                {
                    if (tabData.WebView.CanGoForward)
                    {
                        tabData.WebView.GoForward();
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, ex);
                }
            }
        }

        /// <summary>
        /// 刷新
        /// </summary>
        public void Refresh()
        {
            if (!string.IsNullOrEmpty(CurrentTabSectionId) && TabDataDictionary.TryGetValue(CurrentTabSectionId, out TabData tabData))
            {
                try
                {
                    tabData.WebView?.Reload();
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, ex);
                }
            }
        }

        /// <summary>
        /// 停止
        /// </summary>
        public void Stop()
        {
            if (!string.IsNullOrEmpty(CurrentTabSectionId) && TabDataDictionary.TryGetValue(CurrentTabSectionId, out TabData tabData))
            {
                try
                {
                    tabData.WebView?.Stop();
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, ex);
                }
            }
        }

        /// <summary>
        /// 导航到主页
        /// </summary>
        public void GoHome()
        {
            if (!string.IsNullOrEmpty(CurrentTabSectionId) && TabDataDictionary.TryGetValue(CurrentTabSectionId, out TabData tabData))
            {
                try
                {
                    string homeUrl = tabData.Config.HomeUrl;
                    if (string.IsNullOrEmpty(homeUrl))
                    {
                        homeUrl = "about:blank";
                    }

                    tabData.WebView.Source = new Uri(homeUrl);
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, ex);
                }
            }
        }

        /// <summary>
        /// 获取当前标签页配置
        /// </summary>
        /// <returns>标签页配置，未找到返回null</returns>
        public WebBrowserTabConfig GetCurrentTabConfig()
        {
            // 如果当前没有选中的标签页，返回null
            if (string.IsNullOrEmpty(CurrentTabSectionId))
                return null;

            // 尝试从标签页数据字典中获取数据
            if (TabDataDictionary.TryGetValue(CurrentTabSectionId, out TabData tabData))
                return tabData.Config;

            return null;
        }

        /// <summary>
        /// 获取当前WebView2控件
        /// </summary>
        /// <returns>WebView2控件，未找到返回null</returns>
        public WebView2 GetCurrentWebView()
        {
            // 如果当前没有选中的标签页，返回null
            if (string.IsNullOrEmpty(CurrentTabSectionId))
                return null;

            // 尝试从标签页数据字典中获取数据
            if (TabDataDictionary.TryGetValue(CurrentTabSectionId, out TabData tabData))
                return tabData.WebView;

            return null;
        }

        /// <summary>
        /// 重命名当前标签页
        /// </summary>
        /// <param name="newName">新名称</param>
        public void RenameCurrentTab(string newName)
        {
            if (string.IsNullOrEmpty(CurrentTabName) || !TabDataDictionary.TryGetValue(CurrentTabSectionId, out TabData tabData))
                return;

            if (string.IsNullOrEmpty(newName))
                return;

            try
            {
                // 更新配置
                tabData.Config.Name = newName;

                // 更新标签页文本
                tabData.TabPage.Text = newName;

                // 保存配置
                _configManager.SaveTabConfig(tabData.Config);

                // 触发标题变更事件
                TabTitleChanged?.Invoke(CurrentTabName, newName);

                ETLogManager.Info(_logSource, $"已重命名标签页: {newName}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"重命名标签页失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理所有标签页资源
        /// </summary>
        public void CleanupAllTabs()
        {
            // 创建标签页ID集合副本，避免在迭代时修改集合
            List<string> sectionIds = new List<string>(TabDataDictionary.Keys);

            // 记录清理开始情况
            ETLogManager.Info(_logSource, $"开始清理所有标签页资源，共 {sectionIds.Count} 个标签页");

            // 清理每个标签页资源
            foreach (string sectionId in sectionIds)
            {
                try
                {
                    // 使用已完善的CleanupTabResources方法清理单个标签页
                    CleanupTabResources(sectionId);
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, $"清理标签页 {sectionId} 资源失败: {ex.Message}");
                }
            }

            // 二次检查，确保所有数据已清除
            if (TabDataDictionary.Count > 0)
            {
                ETLogManager.Warning(_logSource, $"首次清理后仍有 {TabDataDictionary.Count} 个标签页未完全清理，进行强制清理");

                // 强制清空集合
                TabDataDictionary.Clear();
                TabNameToSectionId.Clear();
            }

            // 确保当前标签页引用被重置
            CurrentTabSectionId = null;

            // 强制GC回收
            GC.Collect();
            GC.WaitForPendingFinalizers();

            ETLogManager.Info(_logSource, "已清理所有标签页资源");
        }

        /// <summary>
        /// 保存当前标签页配置
        /// </summary>
        public void SaveCurrentTabConfig()
        {
            try
            {
                // 检查当前选中的标签页
                if (string.IsNullOrEmpty(CurrentTabName) || !TabDataDictionary.ContainsKey(CurrentTabSectionId))
                {
                    MessageBox.Show("未找到标签页配置", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 获取标签页配置
                WebBrowserTabConfig config = TabDataDictionary[CurrentTabSectionId].Config;

                // 判断是否为副本标签页
                if (TabDataDictionary[CurrentTabSectionId].IsClone)
                {
                    ETLogManager.Info(_logSource, $"当前标签页 {config.Name} 是副本，跳过保存配置");
                    MessageBox.Show("当前标签页是副本，无需保存配置", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 保存标签页配置
                _configManager.SaveTabConfig(config);

                MessageBox.Show("标签页配置已保存", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, ex);
                MessageBox.Show($"保存标签页配置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新当前标签页配置
        /// </summary>
        public void UpdateCurrentTabConfig(WebBrowserTabConfig newConfig)
        {
            try
            {
                // 获取当前标签页名称
                string tabName = CurrentTabName;
                if (string.IsNullOrEmpty(tabName))
                {
                    ETLogManager.Error(_logSource, "更新配置失败：未选中标签页");
                    return;
                }

                if (!TabDataDictionary.TryGetValue(CurrentTabSectionId, out TabData tabData))
                {
                    ETLogManager.Error(_logSource, $"更新配置失败：未找到标签页 {tabName} 的数据");
                    return;
                }

                // 检查代理设置是否发生变化
                bool proxyChanged = IsProxyConfigChanged(tabData.Config, newConfig);

                // 如果需要应用不同的代理设置，提示用户
                if (proxyChanged && tabData.IsWebViewInitialized && tabData.WebView?.CoreWebView2 != null)
                {
                    ETLogManager.Warning(_logSource, $"[{tabName}] 代理设置已更改，WebView2已初始化，需要重新打开标签页应用新的代理设置");

                    // 询问用户是否立即重新打开标签页以应用代理设置
                    DialogResult result = MessageBox.Show(
                        $"标签页 {tabName} 的代理设置已更改，需要关闭并重新打开标签页才能应用新的代理设置。\n\n是否立即重新打开标签页？",
                        "应用代理设置",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // 保存新配置
                        _configManager.SaveTabConfig(newConfig);

                        // 关闭并重新创建标签页
                        string newTabName = ReopenTabWithConfig(tabName);
                        if (!string.IsNullOrEmpty(newTabName))
                        {
                            ETLogManager.Info(_logSource, $"已重新打开标签页以应用新的代理设置：{newTabName}");
                        }
                        return;
                    }
                }

                // 更新配置
                WebBrowserTabConfig oldConfig = tabData.Config;

                // 确保保留原有的SectionId，这样修改Name属性时不会创建新的配置项
                if (string.IsNullOrEmpty(newConfig.SectionId) && !string.IsNullOrEmpty(oldConfig.SectionId))
                {
                    newConfig.SectionId = oldConfig.SectionId;
                    ETLogManager.Info(_logSource, $"保留原有的SectionId: {newConfig.SectionId} 用于标签页: {newConfig.Name}");
                }

                // 保留原有的ApiUrls列表，确保在配置更新时不丢失已收集的API URL
                if ((newConfig.ApiUrls == null || newConfig.ApiUrls.Count == 0) &&
                    oldConfig.ApiUrls != null && oldConfig.ApiUrls.Count > 0)
                {
                    newConfig.ApiUrls = new List<string>(oldConfig.ApiUrls);
                    ETLogManager.Info(_logSource, $"保留原有的API URLs列表: {oldConfig.ApiUrls.Count} 项已保留到新配置");
                }

                // 记录名称变更
                if (oldConfig.Name != newConfig.Name)
                {
                    ETLogManager.Info(_logSource, $"标签页名称变更: {oldConfig.Name} -> {newConfig.Name}, SectionId保持不变: {newConfig.SectionId}");
                }

                // 更新内存中的配置
                tabData.Config = newConfig;

                // 立即保存配置到文件（只有非副本标签页才保存）
                if (!tabData.IsClone)
                {
                    _configManager.SaveTabConfig(newConfig);
                    ETLogManager.Info(_logSource, $"已立即保存标签页配置到文件: {newConfig.Name}, SectionId: {newConfig.SectionId}");
                }
                else
                {
                    ETLogManager.Debug(_logSource, $"跳过保存副本标签页配置: {newConfig.Name}");
                }

                // 更新TabPage标题
                if (tabData.TabPage.Text != newConfig.Name)
                {
                    tabData.TabPage.Text = newConfig.Name;
                }

                // 如果标签页名称发生变化，更新字典和TabPage.Name
                if (tabName != newConfig.Name)
                {
                    // 更新字典
                    TabDataDictionary.Remove(tabName);
                    TabDataDictionary[newConfig.SectionId] = tabData;

                    // 更新TabPage.Name
                    tabData.TabPage.Name = newConfig.SectionId;

                    // 更新当前标签页名称
                    CurrentTabSectionId = newConfig.SectionId;
                }

                // 配置WebView2（如果已初始化）
                if (tabData.WebView?.CoreWebView2 != null)
                {
                    ConfigureWebView2(tabData.WebView, newConfig);
                }

                // 更新会话维持器
                if (oldConfig.EnableHttpClientRefresh != newConfig.EnableHttpClientRefresh ||
                    oldConfig.HttpClientRefreshInterval != newConfig.HttpClientRefreshInterval ||
                    oldConfig.HttpClientRefreshUrlOrKey != newConfig.HttpClientRefreshUrlOrKey ||
                    proxyChanged)
                {
                    UpdateTabSessionKeeper(newConfig.SectionId, tabData.WebView, newConfig);
                }

                ETLogManager.Info(_logSource, $"已更新标签页配置: {newConfig.Name}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"更新标签页配置失败: {ex.Message}");
            }
        }

        // 添加辅助方法检查代理设置是否发生变化
        private bool IsProxyConfigChanged(WebBrowserTabConfig oldConfig, WebBrowserTabConfig newConfig)
        {
            return oldConfig.ProxyServer != newConfig.ProxyServer ||
                   oldConfig.ProxyPort != newConfig.ProxyPort;
        }

        /// <summary>
        /// 关闭标签页并重新创建（用于应用代理设置等需要重新初始化WebView2的操作）
        /// </summary>
        /// <param name="sectionId">标签页名称</param>
        /// <returns>新标签页名称，失败返回null</returns>
        public string ReopenTabWithConfig(string sectionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId) || !TabDataDictionary.ContainsKey(sectionId))
                {
                    ETLogManager.Warning(_logSource, $"重新创建标签页失败：标签页 {sectionId} 不存在");
                    return null;
                }

                // 保存当前配置
                WebBrowserTabConfig currentConfig = TabDataDictionary[sectionId].Config.Clone() as WebBrowserTabConfig;
                if (currentConfig == null)
                {
                    ETLogManager.Error(_logSource, $"重新创建标签页失败：无法克隆标签页 {sectionId} 的配置");
                    return null;
                }

                // 记录代理设置
                if (!string.IsNullOrEmpty(currentConfig.ProxyServer) && currentConfig.ProxyPort > 0)
                {
                    ETLogManager.Info(_logSource, $"[{sectionId}] 重新创建标签页以应用代理设置：{currentConfig.ProxyServer}:{currentConfig.ProxyPort}");
                }

                // 关闭当前标签页
                bool closeResult = CloseTabBySectionId(sectionId);
                if (!closeResult)
                {
                    ETLogManager.Error(_logSource, $"重新创建标签页失败：无法关闭标签页 {sectionId}");
                    return null;
                }

                // 等待资源释放
                System.Threading.Thread.Sleep(500);

                // 重新创建标签页
                string newTabSectionId = CreateNewTab(currentConfig);

                // 设置当前标签页
                if (!string.IsNullOrEmpty(newTabSectionId) && TabDataDictionary.ContainsKey(newTabSectionId))
                {
                    CurrentTabSectionId = newTabSectionId;
                    _tabControl.SelectedTab = TabDataDictionary[newTabSectionId].TabPage;
                    TabSwitched?.Invoke(newTabSectionId);
                }

                ETLogManager.Info(_logSource, $"已重新创建标签页 {sectionId} -> {newTabSectionId}");
                return newTabSectionId;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"重新创建标签页异常：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 根据标签页名称刷新特定标签页
        /// </summary>
        /// <param name="tabName">要刷新的标签页名称</param>
        /// <returns>是否成功刷新</returns>
        public bool RefreshTabBySectionId(string sectionId)
        {
            if (string.IsNullOrEmpty(sectionId))
            {
                ETLogManager.Warning(_logSource, "刷新标签页失败：标签页名称为空");
                _sessionManager.AddSessionLog(sectionId, "刷新标签页失败：标签页名称为空");
                return false;
            }

            try
            {
                // 检查标签页是否存在
                if (TabDataDictionary.TryGetValue(sectionId, out TabData tabData))
                {
                    string tabName = tabData.Config.Name;

                    // 添加开始刷新日志
                    ETLogManager.Info(_logSource, $"[WebView2刷新] 准备刷新标签页: {tabName}, URL: {tabData.LastUrl}");

                    // 刷新标签页
                    if (tabData.WebView != null)
                    {
                        try
                        {
                            // 添加日志记录HttpClient刷新配置状态
                            ETLogManager.Debug(_logSource, $"[WebView2刷新] 标签页 {tabName} HttpClient配置状态: EnableHttpClientRefresh={tabData.Config.EnableHttpClientRefresh}");

                            // 直接调用WebView2的Reload方法刷新页面
                            tabData.WebView.Reload();

                            // 移除刷新完成的日志，仅在ETLog中记录
                            ETLogManager.Info(_logSource, $"已刷新标签页: {tabName}, URL: {tabData.LastUrl}");
                            return true;
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error(_logSource, $"执行页面刷新时出错: {tabName}, {ex.Message}");
                            _sessionManager.AddSessionLog(sectionId, $"[WebView2刷新] 执行页面刷新时出错: {tabName}, {ex.Message}");
                            return false;
                        }
                    }
                    else
                    {
                        ETLogManager.Warning(_logSource, $"标签页WebView为null，无法刷新: {tabName}");
                        _sessionManager.AddSessionLog(sectionId, $"[WebView2刷新] 标签页WebView为null，无法刷新: {tabName}");
                    }
                }
                else
                {
                    ETLogManager.Warning(_logSource, $"标签页不存在，无法刷新: {sectionId}");
                    _sessionManager.AddSessionLog(sectionId, $"[WebView2刷新] 标签页不存在，无法刷新: {sectionId}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"刷新标签页异常: {sectionId}, {ex.Message}");
                _sessionManager.AddSessionLog(sectionId, $"[WebView2刷新] 刷新标签页异常: {sectionId}, {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 获取当前标签页数据
        /// </summary>
        /// <returns>当前标签页数据，如果未选中标签页则返回null</returns>
        public TabData GetCurrentTabData()
        {
            if (string.IsNullOrEmpty(CurrentTabSectionId))
                return null;

            if (TabDataDictionary.TryGetValue(CurrentTabSectionId, out TabData tabData))
                return tabData;

            return null;
        }

        #endregion 标签页管理方法

        #region WebView2事件和方法

        /// <summary>
        /// 安全地在UI线程上执行WebView2操作
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="action">要执行的操作</param>
        private void SafeInvokeOnWebView2(WebView2 webView, Action action)
        {
            if (webView == null) return;

            try
            {
                if (webView.InvokeRequired)
                {
                    webView.Invoke(action);
                }
                else
                {
                    action();
                }
            }
            catch (ObjectDisposedException)
            {
                // WebView2已被释放，忽略操作
                ETLogManager.Warning(_logSource, "尝试在已释放的WebView2上执行操作");
            }
            catch (InvalidOperationException ex)
            {
                ETLogManager.Warning(_logSource, $"WebView2操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安全地在UI线程上执行WebView2异步操作
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="asyncAction">要执行的异步操作</param>
        private async Task SafeInvokeOnWebView2Async(WebView2 webView, Func<Task> asyncAction)
        {
            if (webView == null) return;

            try
            {
                if (webView.InvokeRequired)
                {
                    await Task.Run(() => webView.Invoke(new Action(async () => await asyncAction())));
                }
                else
                {
                    await asyncAction();
                }
            }
            catch (ObjectDisposedException)
            {
                // WebView2已被释放，忽略操作
                ETLogManager.Warning(_logSource, "尝试在已释放的WebView2上执行异步操作");
            }
            catch (InvalidOperationException ex)
            {
                ETLogManager.Warning(_logSource, $"WebView2异步操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证Cookie是否成功加载到WebView2中
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="targetUrl">目标URL</param>
        /// <param name="expectedCookieCount">期望的Cookie数量</param>
        /// <returns>验证是否成功</returns>
        private async Task<bool> VerifyCookiesAfterLoad(WebView2 webView, string targetUrl, int expectedCookieCount)
        {
            try
            {
                if (webView == null || string.IsNullOrEmpty(targetUrl))
                {
                    ETLogManager.Warning(_logSource, "无法验证Cookie：WebView2或URL为空");
                    return false;
                }

                // 等待一小段时间确保Cookie设置完全生效
                await Task.Delay(200).ConfigureAwait(true);

                // 使用TaskCompletionSource确保在UI线程上执行Cookie验证
                TaskCompletionSource<bool> tcs = new TaskCompletionSource<bool>();

                if (webView.InvokeRequired)
                {
                    webView.BeginInvoke(new Action(async () =>
                    {
                        try
                        {
                            bool result = await VerifyCookiesInternalAsync(webView, targetUrl, expectedCookieCount).ConfigureAwait(false);
                            tcs.SetResult(result);
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error(_logSource, $"验证Cookie时出错（UI线程）: {ex.Message}");
                            tcs.SetException(ex);
                        }
                    }));
                }
                else
                {
                    try
                    {
                        bool result = await VerifyCookiesInternalAsync(webView, targetUrl, expectedCookieCount).ConfigureAwait(false);
                        tcs.SetResult(result);
                    }
                    catch (Exception ex)
                    {
                        tcs.SetException(ex);
                    }
                }

                return await tcs.Task.ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"验证Cookie时出错: {ex.Message}");
                return false;
            }
            finally
            {
                ETLogManager.Info(_logSource, "=== Cookie验证完成 ===");
            }
        }

        /// <summary>
        /// 内部Cookie验证方法，必须在UI线程上调用
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="targetUrl">目标URL</param>
        /// <param name="expectedCookieCount">期望的Cookie数量</param>
        /// <returns>验证是否成功</returns>
        private async Task<bool> VerifyCookiesInternalAsync(WebView2 webView, string targetUrl, int expectedCookieCount)
        {
            try
            {
                // 在UI线程上安全地检查CoreWebView2
                CoreWebView2 coreWebView2 = null;
                try
                {
                    coreWebView2 = webView.CoreWebView2;
                }
                catch (InvalidOperationException ex)
                {
                    ETLogManager.Warning(_logSource, $"无法验证Cookie：无法访问CoreWebView2 - {ex.Message}");
                    return false;
                }

                if (coreWebView2 == null)
                {
                    ETLogManager.Warning(_logSource, "无法验证Cookie：CoreWebView2为空");
                    return false;
                }

                // 获取当前URL的所有Cookie
                var currentCookies = await coreWebView2.CookieManager.GetCookiesAsync(targetUrl).ConfigureAwait(true);

                ETLogManager.Info(_logSource, $"=== Cookie加载验证 ===");
                ETLogManager.Info(_logSource, $"目标URL: {targetUrl}");
                ETLogManager.Info(_logSource, $"期望Cookie数量: {expectedCookieCount}");
                ETLogManager.Info(_logSource, $"实际Cookie数量: {currentCookies.Count}");

                if (currentCookies.Count > 0)
                {
                    ETLogManager.Info(_logSource, $"WebView2中的Cookie列表: {currentCookies.Count}个");
                    // 只在Debug模式下显示详细Cookie信息
                    if (ETLogManager.IsDebugEnabled)
                    {
                        foreach (var cookie in currentCookies)
                        {
                            ETLogManager.Debug(_logSource, $"  ✓ {cookie.Name}=***** (Domain: {cookie.Domain}, Path: {cookie.Path})");
                        }
                    }

                    // 如果有Cookie存在，认为加载成功（不要求数量完全匹配，因为可能有域名匹配问题）
                    ETLogManager.Info(_logSource, "✅ Cookie验证成功：WebView2中存在Cookie");
                    return true;
                }
                else
                {
                    ETLogManager.Error(_logSource, "❌ 严重错误：WebView2中没有找到任何Cookie！");

                    // 尝试获取所有域的Cookie进行调试
                    try
                    {
                        // 尝试获取当前域的根路径Cookie
                        Uri uri = new Uri(targetUrl);
                        string rootUrl = $"{uri.Scheme}://{uri.Host}";
                        var allCookies = await coreWebView2.CookieManager.GetCookiesAsync(rootUrl).ConfigureAwait(true);
                        ETLogManager.Info(_logSource, $"🔍 调试信息：WebView2中 {rootUrl} 域有 {allCookies.Count} 个Cookie");
                        if (allCookies.Count > 0)
                        {
                            ETLogManager.Info(_logSource, "当前域的Cookie列表:");
                            foreach (var cookie in allCookies)
                            {
                                ETLogManager.Info(_logSource, $"  🔍 {cookie.Name}={cookie.Value} (Domain: {cookie.Domain}, Path: {cookie.Path})");
                            }
                        }

                        // 如果当前域没有Cookie，尝试获取主域的Cookie
                        if (allCookies.Count == 0 && uri.Host.Contains("."))
                        {
                            string[] hostParts = uri.Host.Split('.');
                            if (hostParts.Length >= 2)
                            {
                                string mainDomain = $"{uri.Scheme}://{hostParts[hostParts.Length - 2]}.{hostParts[hostParts.Length - 1]}";
                                var mainDomainCookies = await coreWebView2.CookieManager.GetCookiesAsync(mainDomain).ConfigureAwait(true);
                                ETLogManager.Info(_logSource, $"🔍 调试信息：主域 {mainDomain} 有 {mainDomainCookies.Count} 个Cookie");
                                if (mainDomainCookies.Count > 0)
                                {
                                    ETLogManager.Info(_logSource, "主域的Cookie列表:");
                                    foreach (var cookie in mainDomainCookies)
                                    {
                                        ETLogManager.Info(_logSource, $"  🔍 {cookie.Name}={cookie.Value} (Domain: {cookie.Domain}, Path: {cookie.Path})");
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception debugEx)
                    {
                        ETLogManager.Warning(_logSource, $"获取调试Cookie信息失败: {debugEx.Message}");
                    }

                    return false;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"内部验证Cookie时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 异步初始化WebView2环境和控件
        /// </summary>
        /// <param name="tabSectionId">标签页的SectionId</param>
        /// <returns>初始化任务</returns>
        private async Task InitializeWebView2Async(string tabSectionId)
        {
            if (string.IsNullOrEmpty(tabSectionId) || !TabDataDictionary.TryGetValue(tabSectionId, out TabData tabData))
            {
                ETLogManager.Error(_logSource, $"初始化WebView2失败: SectionId为空或找不到标签页数据: {tabSectionId}");
                return;
            }

            WebBrowserTabConfig config = tabData.Config;
            WebView2 webView = tabData.WebView;

            if (webView == null)
            {
                ETLogManager.Error(_logSource, $"[{config.Name}] 初始化WebView2失败: WebView2控件为空");
                return;
            }

            // 直接调用内部初始化方法，因为调用者已确保在UI线程上
            await InitializeWebView2InternalAsync(tabSectionId, tabData, config, webView);
        }

        /// <summary>
        /// 内部WebView2初始化方法，确保在UI线程上执行
        /// </summary>
        /// <param name="tabSectionId">标签页SectionId</param>
        /// <param name="tabData">标签页数据</param>
        /// <param name="config">标签页配置</param>
        /// <param name="webView">WebView2控件</param>
        /// <returns>初始化任务</returns>
        private async Task InitializeWebView2InternalAsync(string tabSectionId, TabData tabData, WebBrowserTabConfig config, WebView2 webView)
        {
            try
            {
                // 检查WebView2是否已经初始化
                if (tabData.IsWebViewInitialized)
                {
                    ETLogManager.Warning(_logSource, $"[{config.Name}] WebView2已经初始化，跳过重复初始化");
                    return;
                }

                ETLogManager.Info(_logSource, $"[{config.Name}] 开始初始化WebView2，IsClone={tabData.IsClone}");

                CoreWebView2Environment environment;

                // 副本标签页直接使用源标签页的环境，原始标签页创建新环境
                if (tabData.IsClone && tabData.Environment != null)
                {
                    // 副本标签页：直接使用源标签页的环境
                    environment = tabData.Environment;
                    ETLogManager.Info(_logSource, $"[{config.Name}] 副本标签页使用源标签页环境");
                }
                else
                {
                    // 原始标签页：创建新环境 创建用户数据文件夹
                    string userDataFolder = Path.Combine(
                        Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location),
                        WebBrowserConstants.WEBVIEW_CACHE_FOLDER,
                        $"WebView2_{config.EnsureSectionId()}");

                    // 确保目录存在
                    Directory.CreateDirectory(userDataFolder);

                    // 配置环境选项
                    CoreWebView2EnvironmentOptions options = new CoreWebView2EnvironmentOptions();

                    // 设置用户代理
                    string userAgent = string.IsNullOrEmpty(config.UserAgent) ? string.Empty : string.Format(USER_AGENT_ARG, config.UserAgent);

                    // 设置代理
                    string proxyServerArg = string.Empty;

                    if (!string.IsNullOrEmpty(config.ProxyServer) && config.ProxyPort > 0)
                    {
                        proxyServerArg = string.Format(PROXY_SERVER_ARG, config.ProxyServer, config.ProxyPort);

                        // 记录代理设置
                        ETLogManager.Info(_logSource, $"[{config.Name}] 配置代理服务器：{config.ProxyServer}:{config.ProxyPort}");
                    }

                    // 合并额外的浏览器参数
                    List<string> browserArgs = new List<string>();
                    if (!string.IsNullOrEmpty(userAgent))
                        browserArgs.Add(userAgent);
                    if (!string.IsNullOrEmpty(proxyServerArg))
                        browserArgs.Add(proxyServerArg);

                    // 设置额外的浏览器参数
                    if (browserArgs.Count > 0)
                    {
                        options.AdditionalBrowserArguments = string.Join(" ", browserArgs);
                    }

                    // 记录环境创建信息
                    string proxyInfo = string.IsNullOrEmpty(proxyServerArg) ? "无代理" : $"代理={proxyServerArg}";
                    ETLogManager.Info(_logSource, $"[{config.Name}] 正在创建WebView2环境: 数据目录={userDataFolder}, {proxyInfo}");

                    // 创建WebView2环境
                    environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder, options).ConfigureAwait(false);
                    tabData.Environment = environment;

                    // 记录环境创建成功
                    ETLogManager.Info(_logSource, $"[{config.Name}] WebView2环境创建成功");
                }

                // 在UI线程上初始化WebView2（调用者已确保在UI线程上）
                await webView.EnsureCoreWebView2Async(environment);

                // 设置WebView2已初始化标志
                tabData.IsWebViewInitialized = true;

                // 注册WebView2事件
                webView.NavigationStarting += WebView_NavigationStarting;
                webView.NavigationCompleted += WebView_NavigationCompleted;
                webView.SourceChanged += WebView_SourceChanged;

                // 触发事件以便主窗体更新UI
                TabSwitched?.Invoke(tabSectionId);

                ETLogManager.Info(_logSource, $"WebView2初始化完成: {webView.Source}, TabName: {tabData.Config.Name}");

                // 注册WebView2请求事件
                RegisterWebResourceRequestedHandler(webView, tabSectionId);

                // 配置WebView2
                ConfigureWebView2(webView, config);

                // 副本标签页和原始标签页的不同处理逻辑
                if (tabData.IsClone)
                {
                    // 副本标签页：直接导航，不加载Cookie文件（使用共享环境中的Cookie）
                    if (!string.IsNullOrEmpty(config.Url))
                    {
                        await SafeNavigateToUrlAsync(webView, config, tabData, true); // cookiesLoaded=true因为使用共享环境
                    }

                    // 副本标签页不启用会话刷新和Session维持
                    ETLogManager.Info(_logSource, $"[{config.Name}] 副本标签页跳过会话刷新初始化");
                }
                else
                {
                    // 原始标签页：正常的Cookie加载和导航流程
                    bool needToNavigate = !string.IsNullOrEmpty(config.Url);
                    bool cookiesLoaded = false;

                    // 尝试从文件加载Cookie
                    if (!string.IsNullOrEmpty(config.CookiePath) && File.Exists(config.CookiePath))
                    {
                        try
                        {
                            // 更新Cookie管理器配置
                            _cookieManager.CookiePath = config.CookiePath;
                            _cookieManager.CurrentUrl = config.Url;

                            // 从文件加载Cookie
                            CookieData cookieData = await _cookieManager.LoadCookiesFromFileAsync().ConfigureAwait(false);
                            if (cookieData != null && cookieData.Cookies != null && cookieData.Cookies.Count > 0)
                            {
                                ETLogManager.Info(_logSource, $"[{config.Name}] 开始设置 {cookieData.Cookies.Count} 个Cookie到WebView2");

                                // 设置Cookie到WebView2（保持UI线程上下文）
                                await _cookieManager.SetCookiesToWebView2Async(webView, cookieData).ConfigureAwait(true);

                                // 增加延迟时间确保Cookie设置完全生效
                                await Task.Delay(500).ConfigureAwait(true);

                                // 验证Cookie是否成功设置
                                bool cookieVerified = await VerifyCookiesAfterLoad(webView, config.Url, cookieData.Cookies.Count).ConfigureAwait(true);
                                if (cookieVerified)
                                {
                                    ETLogManager.Info(_logSource, $"[{config.Name}] ✅ Cookie验证成功，已从文件加载 {cookieData.Cookies.Count} 个Cookie");
                                    cookiesLoaded = true;
                                }
                                else
                                {
                                    ETLogManager.Warning(_logSource, $"[{config.Name}] ⚠️ Cookie验证失败，可能未完全生效");
                                    // 即使验证失败也标记为已加载，避免重复加载
                                    cookiesLoaded = true;
                                }
                            }
                            else
                            {
                                ETLogManager.Warning(_logSource, $"[{config.Name}] Cookie文件存在但没有有效的Cookie数据: {config.CookiePath}");
                            }
                        }
                        catch (Exception cookieEx)
                        {
                            ETLogManager.Error(_logSource, $"[{config.Name}] 加载Cookie失败: {cookieEx.Message}");
                        }
                    }
                    else if (!string.IsNullOrEmpty(config.CookiePath))
                    {
                        ETLogManager.Warning(_logSource, $"[{config.Name}] Cookie文件不存在: {config.CookiePath}");
                    }

                    // 导航到URL（在Cookie设置后进行）
                    if (needToNavigate)
                    {
                        await SafeNavigateToUrlAsync(webView, config, tabData, cookiesLoaded);
                    }

                    // 初始化会话刷新
                    _sessionManager.InitializeTabRefresh(tabSectionId, webView, config);

                    // 添加调试日志，确认HttpClient会话维持设置
                    if (config.EnableHttpClientRefresh)
                    {
                        ETLogManager.Info(_logSource, $"[HTTP会话维持] 标签页 {config.Name} 启用了HttpClient会话维持，间隔: {config.HttpClientRefreshInterval}秒, URL: {config.GetActualRefreshUrl()}");
                    }
                }

                ETLogManager.Info(_logSource, $"WebView2初始化完成: {config.Name}，IsClone={tabData.IsClone}");
            }
            catch (ArgumentException ex) when (ex.Message.Contains("already initialized"))
            {
                // 如果WebView2已经被初始化，记录警告并尝试配置现有实例
                ETLogManager.Warning(_logSource, $"[{config.Name}] WebView2已使用其他环境初始化，尝试配置现有实例: {ex.Message}");

                try
                {
                    // 等待一小段时间再尝试配置，确保CoreWebView2已完全初始化
                    await Task.Delay(500).ConfigureAwait(true);

                    // 检查CoreWebView2是否可用 - 必须在UI线程上执行
                    if (webView.CoreWebView2 != null)
                    {
                        tabData.IsWebViewInitialized = true;

                        // 注册事件
                        webView.NavigationStarting += WebView_NavigationStarting;
                        webView.NavigationCompleted += WebView_NavigationCompleted;
                        webView.SourceChanged += WebView_SourceChanged;

                        // 配置WebView2
                        ConfigureWebView2(webView, config);

                        // 标记是否需要导航
                        bool needToNavigate = !string.IsNullOrEmpty(config.Url);
                        bool cookiesLoaded = false;

                        // 尝试从文件加载Cookie
                        if (!string.IsNullOrEmpty(config.CookiePath) && File.Exists(config.CookiePath))
                        {
                            try
                            {
                                // 更新Cookie管理器配置
                                _cookieManager.CookiePath = config.CookiePath;
                                _cookieManager.CurrentUrl = config.Url;

                                // 从文件加载Cookie
                                CookieData cookieData = await _cookieManager.LoadCookiesFromFileAsync().ConfigureAwait(false);
                                if (cookieData != null && cookieData.Cookies != null && cookieData.Cookies.Count > 0)
                                {
                                    ETLogManager.Info(_logSource, $"[{config.Name}] 开始设置 {cookieData.Cookies.Count} 个Cookie到已初始化的WebView2");

                                    // 设置Cookie到WebView2（保持UI线程上下文）
                                    await _cookieManager.SetCookiesToWebView2Async(webView, cookieData).ConfigureAwait(true);

                                    // 增加延迟时间确保Cookie设置完全生效
                                    await Task.Delay(500).ConfigureAwait(true);

                                    // 验证Cookie是否成功设置
                                    bool cookieVerified = await VerifyCookiesAfterLoad(webView, config.Url, cookieData.Cookies.Count).ConfigureAwait(true);
                                    if (cookieVerified)
                                    {
                                        ETLogManager.Info(_logSource, $"[{config.Name}] ✅ Cookie验证成功，已从文件加载 {cookieData.Cookies.Count} 个Cookie");
                                        cookiesLoaded = true;
                                    }
                                    else
                                    {
                                        ETLogManager.Warning(_logSource, $"[{config.Name}] ⚠️ Cookie验证失败，可能未完全生效");
                                        // 即使验证失败也标记为已加载，避免重复加载
                                        cookiesLoaded = true;
                                    }
                                }
                                else
                                {
                                    ETLogManager.Warning(_logSource, $"[{config.Name}] Cookie文件存在但没有有效的Cookie数据: {config.CookiePath}");
                                }
                            }
                            catch (Exception cookieEx)
                            {
                                ETLogManager.Error(_logSource, $"[{config.Name}] 加载Cookie失败: {cookieEx.Message}");
                            }
                        }
                        else if (!string.IsNullOrEmpty(config.CookiePath))
                        {
                            ETLogManager.Warning(_logSource, $"[{config.Name}] Cookie文件不存在: {config.CookiePath}");
                        }

                        // 导航到URL（在Cookie设置后进行）
                        if (needToNavigate)
                        {
                            await SafeNavigateToUrlAsync(webView, config, tabData, cookiesLoaded);
                        }

                        // 初始化会话刷新
                        _sessionManager.InitializeTabRefresh(tabSectionId, webView, config);

                        ETLogManager.Info(_logSource, $"[{config.Name}] 成功配置已初始化的WebView2实例");

                        // 记录代理无法应用的警告
                        if (!string.IsNullOrEmpty(config.ProxyServer) && config.ProxyPort > 0)
                        {
                            string warningMsg = $"[{config.Name}] WebView2已使用其他环境初始化，无法应用新的代理设置：{config.ProxyServer}:{config.ProxyPort}。需要关闭并重新打开标签页应用代理设置。";
                            ETLogManager.Warning(_logSource, warningMsg);
                            MessageBox.Show($"标签页 {config.Name} 的代理设置无法应用，因为WebView2已经初始化。\n\n要应用新的代理设置，请关闭并重新打开此标签页。", "代理设置", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                }
                catch (Exception configEx)
                {
                    ETLogManager.Error(_logSource, $"[{config.Name}] 配置已初始化的WebView2实例失败: {configEx.Message}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, ex);
                MessageBox.Show($"初始化WebView2失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 安全地导航到URL，包含重试机制
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        /// <param name="tabData">标签页数据</param>
        /// <param name="cookiesLoaded">是否已加载Cookie</param>
        private async Task SafeNavigateToUrlAsync(WebView2 webView, WebBrowserTabConfig config, TabData tabData, bool cookiesLoaded)
        {
            string navigationUrl = GetFormattedUrl(config.Url);
            ETLogManager.Info(_logSource, $"[{config.Name}] 准备导航到URL: {navigationUrl} (Cookie已{(cookiesLoaded ? "加载" : "未加载")})");

            // 验证URL有效性
            if (string.IsNullOrEmpty(navigationUrl) || navigationUrl == ABOUT_BLANK)
            {
                ETLogManager.Warning(_logSource, $"[{config.Name}] URL为空或无效，跳过导航");
                return;
            }

            // 验证URL格式
            if (!IsValidUrl(navigationUrl))
            {
                ETLogManager.Error(_logSource, $"[{config.Name}] URL格式无效: {navigationUrl}");
                return;
            }

            // 如果Cookie已加载，增加额外延迟确保Cookie完全生效后再导航
            if (cookiesLoaded)
            {
                ETLogManager.Info(_logSource, $"[{config.Name}] Cookie已加载，等待500ms确保Cookie完全生效后再导航");
                await Task.Delay(500).ConfigureAwait(true);
            }

            // 尝试导航，最多重试3次
            int maxRetries = 3;
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    ETLogManager.Info(_logSource, $"[{config.Name}] 导航尝试 {attempt}/{maxRetries}: {navigationUrl}");

                    await SafeInvokeOnWebView2Async(webView, () =>
                    {
                        Uri uri = new Uri(navigationUrl);
                        webView.Source = uri;
                        tabData.LastUrl = navigationUrl;
                        return Task.CompletedTask;
                    });

                    ETLogManager.Info(_logSource, $"[{config.Name}] 导航成功: {navigationUrl}");
                    return; // 成功导航，退出重试循环
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, $"[{config.Name}] 导航尝试 {attempt} 失败: {navigationUrl}, 错误: {ex.Message}");

                    if (attempt < maxRetries)
                    {
                        // 等待一段时间后重试
                        int delayMs = attempt * 1000; // 递增延迟：1秒、2秒、3秒
                        ETLogManager.Info(_logSource, $"[{config.Name}] 等待 {delayMs}ms 后重试...");
                        await Task.Delay(delayMs);
                    }
                    else
                    {
                        // 所有重试都失败了，记录错误但不跳转到about:blank
                        ETLogManager.Error(_logSource, $"[{config.Name}] 所有导航尝试都失败，URL: {navigationUrl}");
                    }
                }
            }
        }

        /// <summary>
        /// 验证URL格式是否有效
        /// </summary>
        /// <param name="url">要验证的URL</param>
        /// <returns>是否有效</returns>
        private bool IsValidUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return false;

            try
            {
                Uri uri = new Uri(url);
                return uri.IsWellFormedOriginalString();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 导航到配置的URL
        /// </summary>
        private void NavigateToConfigUrl(WebView2 webView, WebBrowserTabConfig config, TabData tabData)
        {
            try
            {
                // 修正并获取有效URL
                string url = GetFormattedUrl(config.Url);
                if (string.IsNullOrEmpty(url))
                {
                    ETLogManager.Warning(_logSource, $"[{config.Name}] 导航到URL失败: URL为空");
                    return;
                }

                // 创建URI并验证格式
                Uri uri = new Uri(url);
                webView.Source = uri;
                tabData.LastUrl = url;
                ETLogManager.Info(_logSource, $"[{config.Name}] 导航到URL: {url}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"[{config.Name}] 导航失败: {config.Url}, 错误: {ex.Message}");
                // 不再自动跳转到about:blank，让用户知道导航失败
            }
        }

        /// <summary>
        /// 获取格式化的URL（确保URL包含协议前缀）
        /// </summary>
        private string GetFormattedUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return string.Empty; // 返回空字符串而不是about:blank

            // 如果已经是about:blank，直接返回
            if (url.Equals(ABOUT_BLANK, StringComparison.OrdinalIgnoreCase))
                return url;

            if (!url.StartsWith(HTTP_PREFIX, StringComparison.OrdinalIgnoreCase) &&
                !url.StartsWith(HTTPS_PREFIX, StringComparison.OrdinalIgnoreCase) &&
                !url.StartsWith(FILE_PREFIX, StringComparison.OrdinalIgnoreCase) &&
                !url.StartsWith(ABOUT_PREFIX, StringComparison.OrdinalIgnoreCase))
            {
                url = $"{HTTP_PREFIX}{url}";
                ETLogManager.Info(_logSource, $"已修正URL格式: {url}");
            }

            return url;
        }

        /// <summary>
        /// 配置WebView2
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        private void ConfigureWebView2(WebView2 webView, WebBrowserTabConfig config)
        {
            try
            {
                // 确保在UI线程上访问CoreWebView2
                if (webView.InvokeRequired)
                {
                    webView.Invoke(new Action(() => ConfigureWebView2Internal(webView, config)));
                }
                else
                {
                    ConfigureWebView2Internal(webView, config);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"配置WebView2失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 内部WebView2配置方法，确保在UI线程上执行
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        private void ConfigureWebView2Internal(WebView2 webView, WebBrowserTabConfig config)
        {
            try
            {
                if (webView?.CoreWebView2 == null)
                {
                    ETLogManager.Error(_logSource, "配置WebView2失败: CoreWebView2为空");
                    return;
                }

                // 设置用户代理
                if (!string.IsNullOrEmpty(config.UserAgent))
                {
                    webView.CoreWebView2.Settings.UserAgent = config.UserAgent;
                    ETLogManager.Info(_logSource, $"已设置用户代理: {config.UserAgent}");
                }

                // 配置标头和Cookie管理
                if (!string.IsNullOrEmpty(config.CookiePath))
                {
                    // 设置标头路径（兼容使用CookiePath）
                    _cookieManager.HeadersPath = config.CookiePath;

                    // 导入Cookie（如果文件存在）
                    if (File.Exists(config.CookiePath))
                    {
                        // 注意：不在这里立即导入Cookie，避免导航冲突 在InitializeWebView2Async方法中已经处理Cookie导入和导航顺序 保持向后兼容
                    }

                    // 设置Cookie自动导出
                    _cookieManager.SetupAutoCookieExport(webView, config.CookiePath);

                    ETLogManager.Info(_logSource, $"已配置标头和Cookie管理，路径: {config.CookiePath}");
                }

                // 设置标头捕获（改为在后台线程执行，避免阻塞）
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _cookieManager.SetupHeadersCaptureAsync(webView);
                        ETLogManager.Info(_logSource, "已设置WebView2标头捕获");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Warning(_logSource, $"设置标头捕获失败: {ex.Message}");
                    }
                });

                // 配置其他设置
                webView.CoreWebView2.Settings.IsStatusBarEnabled = true;
                webView.CoreWebView2.Settings.AreDefaultContextMenusEnabled = true;
                webView.CoreWebView2.Settings.IsBuiltInErrorPageEnabled = true;
                webView.CoreWebView2.Settings.AreDevToolsEnabled = true;

                // 禁用新窗口打开
                webView.CoreWebView2.NewWindowRequested += (s, e) =>
                {
                    e.Handled = true;
                    webView.CoreWebView2.Navigate(e.Uri);
                };

                ETLogManager.Info(_logSource, "WebView2配置已应用");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"配置WebView2失败: {ex.Message}");
            }
        }

        /// <summary>
        /// WebView导航开始事件处理
        /// </summary>
        public void WebView_NavigationStarting(object sender, CoreWebView2NavigationStartingEventArgs e)
        {
            try
            {
                if (sender is WebView2 webView)
                {
                    // 查找对应的标签页数据
                    foreach (KeyValuePair<string, TabData> pair in TabDataDictionary)
                    {
                        if (pair.Value.WebView == webView)
                        {
                            string url = e.Uri;
                            string tabName = pair.Value.Config?.Name ?? "未命名";
                            bool isClone = pair.Value.IsClone;

                            // 更新LastUrl并触发URL变更事件，以便立即更新地址栏
                            pair.Value.LastUrl = url;
                            TabUrlChanged?.Invoke(tabName, url);

                            ETLogManager.Info(_logSource, $"开始导航: {pair.Key} {(isClone ? "(副本)" : string.Empty)} -> {url}");
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"处理导航开始事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// WebView导航完成事件处理
        /// </summary>
        public void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            try
            {
                if (sender is WebView2 webView)
                {
                    // 查找对应的标签页数据
                    foreach (KeyValuePair<string, TabData> pair in TabDataDictionary)
                    {
                        if (pair.Value.WebView == webView)
                        {
                            bool success = e.IsSuccess;
                            string status = success ? "成功" : $"失败（错误码：{e.WebErrorStatus}）";

                            // 使用config中的名称，确保UI显示一致
                            string tabName = pair.Value.Config?.Name ?? "未命名";

                            // 检查标签页是否为副本
                            bool isClone = pair.Value.IsClone;

                            ETLogManager.Info(_logSource, $"导航完成: {tabName} ({pair.Key}){(isClone ? " (副本)" : string.Empty)} -> {status}");

                            // 如果导航成功，尝试获取网页标题和URL
                            if (success)
                            {
                                try
                                {
                                    // 安全地更新当前URL到地址栏
                                    SafeInvokeOnWebView2(webView, () =>
                                    {
                                        if (webView.Source != null)
                                        {
                                            string url = webView.Source.ToString();
                                            pair.Value.LastUrl = url;

                                            // 触发URL变更事件，确保地址栏更新
                                            TabUrlChanged?.Invoke(tabName, url);

                                            ETLogManager.Debug(_logSource, $"导航完成后更新URL: {tabName}{(isClone ? " (副本)" : string.Empty)} -> {url}");
                                        }

                                        // 获取并更新网页标题
                                        if (webView.CoreWebView2 != null)
                                        {
                                            string title = webView.CoreWebView2.DocumentTitle;
                                            if (!string.IsNullOrEmpty(title))
                                            {
                                                TabTitleChanged?.Invoke(tabName, title);
                                            }
                                        }
                                    });

                                    // 副本标签页不需要进行Cookie同步和HttpClient会话维持相关操作
                                    if (isClone)
                                    {
                                        ETLogManager.Debug(_logSource, $"标签页 {tabName} 是副本，跳过Cookie同步和HttpClient会话维持相关操作");
                                        break;
                                    }

                                    // 如果配置了Cookie路径，尝试获取和保存Cookie
                                    if (!string.IsNullOrEmpty(pair.Value.Config.CookiePath))
                                    {
                                        Task.Run(async () =>
                                        {
                                            try
                                            {
                                                // 更新Cookie管理器配置
                                                _cookieManager.CookiePath = pair.Value.Config.CookiePath;
                                                _cookieManager.CurrentUrl = webView.Source.ToString();

                                                // 获取并保存Cookie
                                                await _cookieManager.GetCookiesFromWebView2Async(webView).ConfigureAwait(false);
                                                ETLogManager.Info(_logSource, $"[{tabName}] 已获取并保存Cookie到文件: {pair.Value.Config.CookiePath}");

                                                // 同步Cookie到会话维持器
                                                _sessionManager.SyncCookiesToSessionKeeper(pair.Key, webView);
                                            }
                                            catch (Exception cookieEx)
                                            {
                                                ETLogManager.Error(_logSource, $"[{tabName}] 获取和保存Cookie失败: {cookieEx.Message}");
                                            }
                                        });
                                    }

                                    // 对HttpClient启用的标签页，同步请求头到会话维持器
                                    if (pair.Value.Config.EnableHttpClientRefresh)
                                    {
                                        // 获取对应的会话维持器
                                        Dictionary<string, WebBrowserSessionKeeper> sessionKeepers = _sessionManager.GetSessionKeepers();
                                        if (sessionKeepers.ContainsKey(pair.Key))
                                        {
                                            WebBrowserSessionKeeper sessionKeeper = sessionKeepers[pair.Key];
                                            Task.Run(async () =>
                                            {
                                                try
                                                {
                                                    // 同步请求头
                                                    await sessionKeeper.SyncHeadersFromWebView2Async(webView).ConfigureAwait(false);
                                                    ETLogManager.Info(_logSource, $"[{tabName}] 已同步请求头到会话维持器");
                                                }
                                                catch (Exception headerEx)
                                                {
                                                    ETLogManager.Error(_logSource, $"[{tabName}] 同步请求头失败: {headerEx.Message}");
                                                }
                                            });
                                        }
                                    }
                                }
                                catch (Exception titleEx)
                                {
                                    ETLogManager.Error(_logSource, $"获取网页标题失败: {titleEx.Message}");
                                }
                            }
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"处理导航完成事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// WebView源地址变更事件处理
        /// </summary>
        public void WebView_SourceChanged(object sender, CoreWebView2SourceChangedEventArgs e)
        {
            try
            {
                if (sender is WebView2 webView)
                {
                    // 查找对应的标签页数据
                    foreach (KeyValuePair<string, TabData> pair in TabDataDictionary)
                    {
                        if (pair.Value.WebView == webView)
                        {
                            // 获取标签页基本信息
                            string tabName = pair.Value.Config?.Name ?? "未命名";
                            bool isClone = pair.Value.IsClone;
                            string sectionId = pair.Key;

                            // 安全地记录最后URL
                            SafeInvokeOnWebView2(webView, () =>
                            {
                                if (webView.Source != null)
                                {
                                    string url = webView.Source.ToString();
                                    pair.Value.LastUrl = url;

                                    // 触发URL变更事件，确保更新地址栏
                                    TabUrlChanged?.Invoke(tabName, url);

                                    // 记录URL变更日志
                                    if (CurrentTabSectionId == sectionId)
                                    {
                                        ETLogManager.Debug(_logSource, $"当前标签页URL变更: {tabName} ({sectionId}){(isClone ? " (副本)" : string.Empty)} -> {url}");
                                    }
                                    else
                                    {
                                        ETLogManager.Debug(_logSource, $"非当前标签页URL变更: {tabName} ({sectionId}){(isClone ? " (副本)" : string.Empty)} -> {url}");
                                    }
                                }
                            });
                        }
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"处理源地址变更事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// TabControl选中标签页变更事件
        /// </summary>
        public void TabControl_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (_tabControl.SelectedTab == null)
                {
                    CurrentTabSectionId = null;
                    TabSwitched?.Invoke(null);
                    return;
                }

                // 获取当前选中标签页的SectionId（保存在TabPage的Name属性中）
                string sectionId = _tabControl.SelectedTab.Name;

                // 如果标签页数据中不存在该SectionId，可能是旧式标签页
                if (!TabDataDictionary.ContainsKey(sectionId))
                {
                    // 尝试找到与TabPage.Text匹配的标签页
                    foreach (KeyValuePair<string, TabData> pair in TabDataDictionary)
                    {
                        if (pair.Value.TabPage.Text == _tabControl.SelectedTab.Text)
                        {
                            sectionId = pair.Key;
                            break;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(sectionId) && TabDataDictionary.ContainsKey(sectionId))
                {
                    string oldTabSectionId = CurrentTabSectionId;
                    CurrentTabSectionId = sectionId;

                    TabData tabData = TabDataDictionary[sectionId];
                    string tabName = tabData.Config.Name;

                    // 只有当真正切换标签页时才触发事件
                    if (oldTabSectionId != CurrentTabSectionId)
                    {
                        // 安全地获取当前WebView2的URL并更新LastUrl
                        if (tabData.WebView != null)
                        {
                            SafeInvokeOnWebView2(tabData.WebView, () =>
                            {
                                if (tabData.WebView.Source != null)
                                {
                                    tabData.LastUrl = tabData.WebView.Source.ToString();
                                    // 触发URL变更事件，确保地址栏更新
                                    TabUrlChanged?.Invoke(tabName, tabData.LastUrl);
                                }
                            });
                        }

                        // 触发标签页切换事件
                        TabSwitched?.Invoke(sectionId);
                        ETLogManager.Info(_logSource, $"切换到标签页: {tabName}, SectionId: {sectionId}, URL: {tabData.LastUrl ?? "unknown"}");
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"标签页切换事件处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据WebView2控件获取标签页名称
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <returns>标签页名称</returns>
        private string GetTabNameByWebView(WebView2 webView)
        {
            foreach (KeyValuePair<string, TabData> pair in TabDataDictionary)
            {
                if (pair.Value.WebView == webView)
                {
                    return pair.Value.Config.Name;
                }
            }

            return null;
        }

        /// <summary>
        /// 注册WebResourceRequested事件处理程序
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="sectionId">标签页SectionId</param>
        private void RegisterWebResourceRequestedHandler(WebView2 webView, string sectionId)
        {
            if (webView == null)
                return;

            try
            {
                if (!TabDataDictionary.TryGetValue(sectionId, out TabData tabData))
                {
                    ETLogManager.Warning(_logSource, $"注册资源请求处理器失败: 找不到标签页数据 SectionId={sectionId}");
                    return;
                }

                string tabName = tabData.Config.Name;

                // 安全地注册资源请求处理器
                SafeInvokeOnWebView2(webView, () =>
                {
                    if (webView.CoreWebView2 != null)
                    {
                        // 添加网络请求过滤器，监控所有请求
                        webView.CoreWebView2.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);

                        // 创建事件处理器并保存引用
                        EventHandler<CoreWebView2WebResourceRequestedEventArgs> handler =
                            (sender, e) => WebView_WebResourceRequested(sender, e, sectionId);

                        // 保存处理器引用到TabData
                        tabData.WebResourceRequestedHandler = handler;

                        // 注册事件处理器
                        webView.CoreWebView2.WebResourceRequested += handler;

                        ETLogManager.Debug(_logSource, $"已注册资源请求处理器: {tabName}, SectionId={sectionId}");
                    }
                });
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"注册资源请求处理器失败: SectionId={sectionId}, {ex.Message}");
            }
        }

        /// <summary>
        /// 判断请求是否为API请求
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <param name="method">请求方法</param>
        /// <param name="config">标签页配置</param>
        /// <returns>是否为API请求</returns>
        private bool IsApiRequest(string url, string method, WebBrowserTabConfig config)
        {
            // 只处理POST或GET请求
            if (method != "POST" && method != "GET")
            {
                return false;
            }

            // 1. 检查URL是否包含配置的轻量模式关键字
            if (!string.IsNullOrEmpty(config.HttpClientRefreshUrlOrKey) && url.Contains(config.HttpClientRefreshUrlOrKey))
            {
                return true;
            }

            // 2. 检查URL是否包含常见API路径标识
            if (url.Contains("/api/") || url.Contains("/service/") || url.Contains("/rest/") ||
                url.Contains("/interface/") || url.Contains("/gateway/") ||
                url.EndsWith(".json") || url.EndsWith(".xml") || url.Contains("action"))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// WebView2网络请求事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <param name="sectionId">标签页Section ID</param>
        private void WebView_WebResourceRequested(object sender, CoreWebView2WebResourceRequestedEventArgs e, string sectionId)
        {
            try
            {
                // 获取请求信息
                string method = e.Request.Method;
                string url = e.Request.Uri;

                // 获取标签页名称
                string tabName = GetTabNameByWebView(sender as WebView2);
                if (string.IsNullOrEmpty(tabName))
                {
                    tabName = sectionId;
                }

                // 获取标签页配置和WebView
                WebBrowserTabConfig config = null;
                WebView2 webView = null;
                lock (TabDataDictionary)
                {
                    if (TabDataDictionary.TryGetValue(sectionId, out TabData tabData) && tabData.Config != null)
                    {
                        config = tabData.Config;
                        webView = tabData.WebView;
                    }
                }

                if (config == null || webView == null)
                {
                    return;
                }

                // 判断是否为API请求
                if (IsApiRequest(url, method, config))
                {
                    // 如果是新的API URL，添加到配置中
                    if (!config.ApiUrls.Contains(url))
                    {
                        config.ApiUrls.Add(url);
                        ETLogManager.Info(_logSource, $"检测到API请求: {sectionId} - {method} {url} - 当前API数量: {config.ApiUrls.Count}");

                        // 提取详细请求信息
                        try
                        {
                            // 创建请求头字典
                            Dictionary<string, string> headers = new Dictionary<string, string>();
                            Dictionary<string, string> cookies = new Dictionary<string, string>();
                            string rawCookieHeader = string.Empty;

                            // 提取请求头信息
                            try
                            {
                                CoreWebView2HttpRequestHeaders requestHeaders = e.Request.Headers;
                                if (requestHeaders != null)
                                {
                                    // 获取所有请求头
                                    foreach (KeyValuePair<string, string> header in requestHeaders)
                                    {
                                        string headerName = header.Key;
                                        string headerValue = header.Value;

                                        // 保存Cookie单独处理
                                        if (headerName.Equals("Cookie", StringComparison.OrdinalIgnoreCase))
                                        {
                                            rawCookieHeader = headerValue;
                                            // 解析Cookie字符串为字典
                                            string[] cookiePairs = headerValue.Split(';');
                                            foreach (string cookiePair in cookiePairs)
                                            {
                                                string[] parts = cookiePair.Split(new[] { '=' }, 2);
                                                if (parts.Length == 2 && !string.IsNullOrEmpty(parts[0].Trim()))
                                                {
                                                    cookies[parts[0].Trim()] = parts[1].Trim();
                                                }
                                            }
                                        }

                                        // 保存所有请求头
                                        headers[headerName] = headerValue;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                ETLogManager.Error(_logSource, $"提取请求头失败: {ex.Message}");
                            }

                            // 更新会话管理器的请求头信息
                            _sessionManager.UpdateSessionKeeperRequestHeaders(sectionId, headers);
                            ETLogManager.Info(_logSource, $"已提取API请求信息并更新到会话管理器: {sectionId} - {method} {url}");

                            // 更新CookiePath文件（如果配置了路径）
                            if (!string.IsNullOrEmpty(config.CookiePath))
                            {
                                Task.Run(async () =>
                                {
                                    try
                                    {
                                        await UpdateCookiePathFileAsync(webView, config, headers, sectionId);
                                    }
                                    catch (Exception cookieEx)
                                    {
                                        ETLogManager.Error(_logSource, $"更新CookiePath文件失败: {sectionId} - {cookieEx.Message}");
                                    }
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error(_logSource, $"提取并保存请求信息失败: {ex.Message}");
                        }
                    }
                }

                // 过滤掉静态资源或非主要请求（如图片、CSS、JS等），仅保留主要API请求
                if (IsUserInitiatedRequest(url, method))
                {
                    // 记录请求活动，重置刷新定时器
                    _sessionManager.RecordWebView2Activity(sectionId);

                    // 记录日志（精简）
                    if (ETLogManager.IsDebugEnabled)
                        ETLogManager.Debug(_logSource, $"检测到WebView2主动请求: {sectionId} - {method} {url}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"处理WebView2网络请求事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断请求是否为用户发起或网页主动发起的重要请求
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <param name="method">请求方法</param>
        /// <returns>是否为用户或网页主动发起的请求</returns>
        private bool IsUserInitiatedRequest(string url, string method)
        {
            try
            {
                // 忽略静态资源请求
                if (url.EndsWith(".jpg") || url.EndsWith(".jpeg") || url.EndsWith(".png") || url.EndsWith(".gif") ||
                    url.EndsWith(".css") || url.EndsWith(".js") || url.EndsWith(".svg") || url.EndsWith(".woff") ||
                    url.EndsWith(".woff2") || url.EndsWith(".ttf") || url.EndsWith(".ico"))
                {
                    return false;
                }

                // POST请求通常是用户操作或网页主动发起的
                if (method == "POST")
                {
                    return true;
                }

                // 包含API相关路径的GET请求可能是网页主动发起的
                if (method == "GET" && (url.Contains("/api/") || url.Contains("/service/") || url.Contains("/ajax/") ||
                    url.Contains("/action") || url.Contains("/interface/") || url.Contains("/gateway/") ||
                    url.EndsWith(".json") || url.EndsWith(".xml")))
                {
                    return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 更新标签页的会话维持器
        /// </summary>
        /// <param name="tabName">标签页名称</param>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        private void UpdateTabSessionKeeper(string tabName, WebView2 webView, WebBrowserTabConfig config)
        {
            try
            {
                if (string.IsNullOrEmpty(tabName))
                {
                    ETLogManager.Warning(_logSource, "更新会话维持器失败：标签页名称为空");
                    return;
                }

                // 记录调试信息
                ETLogManager.Debug(_logSource, $"[{tabName}] 会话维持器配置更新请求，WebView2刷新={config.EnableWebView2Refresh}，HttpClient刷新={config.EnableHttpClientRefresh}");

                // 获取标签页的SectionId
                string sectionId = GetSectionIdByTabName(tabName);
                if (string.IsNullOrEmpty(sectionId))
                {
                    ETLogManager.Warning(_logSource, $"[{tabName}] 无法更新会话维持器：未找到对应SectionId");
                    return;
                }

                // 区分处理WebView2刷新和HttpClient刷新，避免重复初始化
                if (config.EnableWebView2Refresh && webView != null)
                {
                    // 只初始化WebView2刷新定时器
                    _sessionManager.StartTabRefresher(sectionId, config.WebView2RefreshInterval);
                    ETLogManager.Info(_logSource, $"[{tabName}] 已配置WebView2刷新定时器，间隔: {config.WebView2RefreshInterval}秒");
                }
                else
                {
                    // 停止WebView2刷新定时器
                    _sessionManager.StopTabRefresh(sectionId);
                    ETLogManager.Info(_logSource, $"[{tabName}] WebView2刷新未启用，已停止相关定时器");
                }

                // 只处理HttpClient刷新配置，不调用InitializeTabRefresh避免重复初始化
                if (config.EnableHttpClientRefresh)
                {
                    // 确保HttpClient会话维持器使用正确的配置
                    bool result = _sessionManager.EnsureHttpClientKeeperRunning(sectionId, config);
                    if (result)
                    {
                        ETLogManager.Info(_logSource, $"[{tabName}] HttpClient会话维持已配置，间隔: {config.HttpClientRefreshInterval}秒");
                    }
                    else
                    {
                        ETLogManager.Warning(_logSource, $"[{tabName}] HttpClient会话维持配置失败");
                    }
                }
                else
                {
                    // 确保HttpClient会话维持器已停止
                    _sessionManager.StopSessionKeeper(sectionId);
                    ETLogManager.Info(_logSource, $"[{tabName}] HttpClient会话维持未启用，已停止相关服务");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"更新会话维持器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据标签页名称获取SectionId
        /// </summary>
        /// <param name="tabName">标签页名称</param>
        /// <returns>SectionId，如果找不到则返回null</returns>
        public string GetSectionIdByTabName(string tabName)
        {
            if (string.IsNullOrEmpty(tabName))
                return null;

            // 查找标签页的SectionId
            if (TabNameToSectionId.TryGetValue(tabName, out string sectionId))
            {
                return sectionId;
            }

            // 如果映射中找不到，尝试遍历查找
            foreach (KeyValuePair<string, TabData> pair in TabDataDictionary)
            {
                if (pair.Value.Config.Name == tabName)
                {
                    return pair.Key;
                }
            }

            ETLogManager.Error(_logSource, $"获取SectionId失败: 未找到标签页 {tabName}");
            return null;
        }

        #endregion WebView2事件和方法

        /// <summary>
        /// 克隆标签页，创建一个共享相同WebView2环境的副本
        /// </summary>
        /// <param name="sourceSectionId">源标签页的SectionId</param>
        /// <returns>新创建的副本标签页的SectionId，如果创建失败则返回null</returns>
        public string CloneTab(string sourceSectionId)
        {
            try
            {
                // 检查源标签页是否存在
                if (string.IsNullOrEmpty(sourceSectionId) || !TabDataDictionary.TryGetValue(sourceSectionId, out TabData sourceTabData))
                {
                    ETLogManager.Error(_logSource, $"克隆标签页失败: 未找到源标签页 SectionId={sourceSectionId}");
                    return null;
                }

                // 检查源标签页是否为副本，不允许从副本创建副本
                if (sourceTabData.IsClone)
                {
                    ETLogManager.Warning(_logSource, $"克隆标签页失败: 不能从副本标签页创建副本 SectionId={sourceSectionId}");
                    return null;
                }

                // 检查源标签页配置是否存在
                if (sourceTabData.Config == null)
                {
                    ETLogManager.Error(_logSource, $"克隆标签页失败: 源标签页配置为空 SectionId={sourceSectionId}");
                    return null;
                }

                // 检查源标签页环境是否已初始化，如果未初始化则尝试使用默认环境
                if (sourceTabData.Environment == null || !sourceTabData.IsWebViewInitialized)
                {
                    ETLogManager.Warning(_logSource, $"源标签页环境未完全初始化，将使用默认环境创建副本 SectionId={sourceSectionId}");
                    // 不直接返回null，而是继续使用默认环境
                }

                // 克隆标签页配置
                WebBrowserTabConfig cloneConfig = null;
                try
                {
                    cloneConfig = sourceTabData.Config.Clone() as WebBrowserTabConfig;
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, $"克隆标签页失败: 克隆配置时出现异常 SectionId={sourceSectionId}, 异常={ex.Message}");
                    return null;
                }

                if (cloneConfig == null)
                {
                    ETLogManager.Error(_logSource, $"克隆标签页失败: 无法克隆配置 SectionId={sourceSectionId}");
                    return null;
                }

                // 修改配置属性，生成新的SectionId，修改名称
                cloneConfig.GenerateNewSectionId();
                cloneConfig.Name = $"{cloneConfig.Name} (副本)";

                // 设置副本标签页的URL为源标签页的当前URL
                if (!string.IsNullOrEmpty(sourceTabData.LastUrl))
                {
                    cloneConfig.Url = sourceTabData.LastUrl;
                }

                // 禁用副本标签页的定时刷新和会话维持
                cloneConfig.EnableWebView2Refresh = false;
                cloneConfig.EnableHttpClientRefresh = false;

                // 创建新的TabPage
                TabPage tabPage = new TabPage(cloneConfig.Name)
                {
                    UseVisualStyleBackColor = true,
                    Name = cloneConfig.SectionId
                };

                // 添加到TabControl
                _tabControl.TabPages.Add(tabPage);

                // 创建新的WebView2控件
                WebView2 webView = new WebView2
                {
                    Dock = DockStyle.Fill,
                    DefaultBackgroundColor = System.Drawing.Color.White
                };

                // 添加WebView2控件到TabPage
                tabPage.Controls.Add(webView);

                // 创建标签页数据，标记为副本
                TabData cloneTabData = new TabData
                {
                    TabPage = tabPage,
                    WebView = webView,
                    Config = cloneConfig,
                    LastUrl = sourceTabData.LastUrl,
                    Environment = sourceTabData.Environment, // 共享环境
                    IsWebViewInitialized = false,
                    IsClone = true, // 明确标记为副本
                    ParentSectionId = sourceSectionId // 保存原始标签页的SectionId
                };

                // 添加到字典，使用新的SectionId作为键
                TabDataDictionary[cloneConfig.SectionId] = cloneTabData;

                // 更新名称到SectionId的映射
                TabNameToSectionId[cloneConfig.Name] = cloneConfig.SectionId;

                // WebView2将在首次使用时延迟初始化，避免在克隆时阻塞 设置WebView2未初始化标志，将在后续使用时自动初始化
                cloneTabData.IsWebViewInitialized = false;

                ETLogManager.Info(_logSource, $"副本标签页已创建，WebView2将延迟初始化: {cloneConfig.SectionId}");

                // 注册WebView2事件
                webView.NavigationStarting += WebView_NavigationStarting;
                webView.NavigationCompleted += WebView_NavigationCompleted;
                webView.SourceChanged += WebView_SourceChanged;

                // 注册WebResourceRequested事件处理程序
                RegisterWebResourceRequestedHandler(webView, cloneConfig.SectionId);

                // 触发事件以便主窗体更新UI
                TabSwitched?.Invoke(cloneConfig.SectionId);

                ETLogManager.Info(_logSource, $"已创建标签页副本: {cloneConfig.Name}, SectionId: {cloneConfig.SectionId}, 源标签页: {sourceTabData.Config.Name}");

                // 选择新创建的标签页
                _tabControl.SelectedTab = tabPage;

                // 异步初始化WebView2（不等待完成，避免阻塞UI） 导航将在初始化完成后进行
                _ = InitializeWebView2Async(cloneConfig.SectionId);

                return cloneConfig.SectionId;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"克隆标签页失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 关闭所有与指定父标签页关联的副本标签页
        /// </summary>
        /// <param name="parentSectionId">父标签页的SectionId</param>
        /// <returns>关闭的副本标签页数量</returns>
        public int CloseAllCloneTabsOfParent(string parentSectionId)
        {
            if (string.IsNullOrEmpty(parentSectionId))
            {
                ETLogManager.Warning(_logSource, "关闭副本标签页失败：父标签页SectionId为空");
                return 0;
            }

            try
            {
                // 查找所有是指定标签页副本的标签页
                List<string> cloneTabsToClose = new List<string>();

                foreach (KeyValuePair<string, TabData> pair in TabDataDictionary)
                {
                    if (pair.Value.IsClone && pair.Value.ParentSectionId == parentSectionId)
                    {
                        cloneTabsToClose.Add(pair.Key);
                        ETLogManager.Info(_logSource, $"找到父标签页 {parentSectionId} 的副本标签页: {pair.Value.Config.Name} (SectionId: {pair.Key})");
                    }
                }

                // 关闭找到的副本标签页
                int closedCount = 0;
                foreach (string cloneTabSectionId in cloneTabsToClose)
                {
                    if (CloseTabBySectionId(cloneTabSectionId))
                    {
                        closedCount++;
                    }
                }

                if (closedCount > 0)
                {
                    ETLogManager.Info(_logSource, $"已关闭 {closedCount} 个父标签页 {parentSectionId} 的副本标签页");
                }
                else if (cloneTabsToClose.Count > 0)
                {
                    ETLogManager.Warning(_logSource, $"未能成功关闭任何父标签页 {parentSectionId} 的副本标签页");
                }
                else
                {
                    ETLogManager.Info(_logSource, $"父标签页 {parentSectionId} 没有副本标签页需要关闭");
                }

                return closedCount;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"关闭副本标签页异常: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 更新CookiePath文件，保存当前WebView的Cookie和请求头信息
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        /// <param name="requestHeaders">API请求头信息</param>
        /// <param name="sectionId">标签页Section ID</param>
        private async Task UpdateCookiePathFileAsync(WebView2 webView, WebBrowserTabConfig config, Dictionary<string, string> requestHeaders, string sectionId)
        {
            try
            {
                if (webView == null || string.IsNullOrEmpty(config.CookiePath))
                {
                    return;
                }

                // 使用TaskCompletionSource来处理UI线程调用
                var tcs = new TaskCompletionSource<(string url, CookieData cookieData)>();

                // 确保在UI线程中执行WebView2相关操作
                if (webView.InvokeRequired)
                {
                    webView.BeginInvoke(new Action(async () =>
                    {
                        try
                        {
                            var result = await GetWebViewDataOnUIThread(webView, config, sectionId);
                            tcs.SetResult(result);
                        }
                        catch (Exception ex)
                        {
                            tcs.SetException(ex);
                        }
                    }));
                }
                else
                {
                    // 已经在UI线程中
                    try
                    {
                        var result = await GetWebViewDataOnUIThread(webView, config, sectionId);
                        tcs.SetResult(result);
                    }
                    catch (Exception ex)
                    {
                        tcs.SetException(ex);
                    }
                }

                // 等待UI线程操作完成
                var (currentUrl, cookieData) = await tcs.Task;

                // 检查是否成功获取到数据
                if (string.IsNullOrEmpty(currentUrl) || cookieData == null || cookieData.Cookies.Count == 0)
                {
                    ETLogManager.Debug(_logSource, $"跳过更新CookiePath文件，未获取到有效数据: {sectionId}");
                    return;
                }

                // 更新Cookie数据的URL为当前WebView URL
                cookieData.Url = currentUrl;

                // 创建额外的标头信息，包含API请求头
                var extraHeaders = new Dictionary<string, object>();

                // 添加基本信息
                extraHeaders["timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                extraHeaders["source"] = "WebBrowserTabManager";
                extraHeaders["request-type"] = "API";
                extraHeaders["section-id"] = sectionId;
                extraHeaders["tab-name"] = config.Name;

                // 合并API请求头信息
                if (requestHeaders != null)
                {
                    foreach (var kvp in requestHeaders)
                    {
                        string keyLower = kvp.Key.ToLower();
                        // 排除url字段和cookie相关字段，避免重复
                        if (keyLower != "url" &&
                            keyLower != "cookie" &&
                            keyLower != "cookies" &&
                            !extraHeaders.ContainsKey(kvp.Key))
                        {
                            extraHeaders[kvp.Key] = kvp.Value;
                        }
                    }
                }

                // 使用ETWebBrowserJsonFormatter创建标准格式JSON
                var options = new ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.HeadersOptions
                {
                    Source = "WebBrowserTabManager",
                    RequestType = "API",
                    IncludeTimestamp = true,
                    GenerateCookieHeader = true,
                    ExtraHeaders = extraHeaders
                };

                // 转换Cookie为ET格式
                var etCookieItems = _cookieManager.ConvertToETCookieItems(cookieData.Cookies);

                // 创建JSON内容
                string json = ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.CreateLoginInfoJson(
                    currentUrl, // 使用当前WebView URL，不是API请求URL
                    null, // 标头通过options.ExtraHeaders传递
                    etCookieItems,
                    options);

                // 保存到文件 - 使用安全的文件写入机制，防止文件访问冲突
                await Task.Run(() => SafeWriteTextToFile(config.CookiePath, json));

                ETLogManager.Info(_logSource, $"已更新CookiePath文件: {sectionId} - {config.CookiePath} - URL: {currentUrl}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"更新CookiePath文件异常: {sectionId} - {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 在UI线程中获取WebView数据的辅助方法
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        /// <param name="sectionId">标签页Section ID</param>
        /// <returns>URL和Cookie数据的元组</returns>
        private async Task<(string url, CookieData cookieData)> GetWebViewDataOnUIThread(WebView2 webView, WebBrowserTabConfig config, string sectionId)
        {
            try
            {
                if (webView.CoreWebView2 == null)
                {
                    ETLogManager.Debug(_logSource, $"跳过更新CookiePath文件，WebView2未初始化: {sectionId}");
                    return (string.Empty, null);
                }

                // 获取当前WebView的URL（不是API请求的URL）
                string currentUrl = webView.Source?.ToString() ?? string.Empty;
                if (string.IsNullOrEmpty(currentUrl) || currentUrl == "about:blank")
                {
                    ETLogManager.Debug(_logSource, $"跳过更新CookiePath文件，WebView URL为空或为about:blank: {sectionId}");
                    return (string.Empty, null);
                }

                // 设置Cookie管理器配置
                _cookieManager.HeadersPath = config.CookiePath;
                _cookieManager.CurrentUrl = currentUrl;

                // 从WebView2获取Cookie数据
                var cookieData = await _cookieManager.GetCookiesFromWebView2Async(webView);

                return (currentUrl, cookieData);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"在UI线程中获取WebView数据失败: {sectionId} - {ex.Message}");
                return (string.Empty, null);
            }
        }

        /// <summary>
        /// 安全地将文本写入文件，防止文件访问冲突
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="content">要写入的内容</param>
        /// <remarks>
        /// 使用互斥锁和FileShare机制防止多进程同时访问同一文件
        /// 包含重试机制处理文件被占用的情况
        /// </remarks>
        private void SafeWriteTextToFile(string filePath, string content)
        {
            // 重试次数和延迟
            int maxRetries = 3;
            int retryDelayMs = 500;
            int retryCount = 0;

            while (retryCount < maxRetries)
            {
                try
                {
                    // 创建文件互斥锁名称 - 将文件路径转换为有效的互斥锁名称
                    string mutexName = $"Global\\{filePath.Replace('\\', '_').Replace(':', '_').Replace('/', '_')}";
                    bool mutexCreated = false;

                    // 尝试获取互斥锁
                    using (System.Threading.Mutex mutex = new System.Threading.Mutex(false, mutexName, out mutexCreated))
                    {
                        // 尝试获取互斥锁，等待最多3秒
                        bool mutexAcquired = mutex.WaitOne(3000);
                        try
                        {
                            // 即使没能获取到互斥锁，也尝试使用FileShare方式写入
                            // 使用FileShare.ReadWrite允许其他进程同时读写文件，但FILE_SHARE_DELETE标志未设置，防止文件在写入时被删除
                            using (FileStream fs = new FileStream(
                                filePath,
                                FileMode.Create,
                                FileAccess.Write,
                                FileShare.ReadWrite,
                                4096,
                                FileOptions.WriteThrough))
                            using (StreamWriter writer = new StreamWriter(fs, System.Text.Encoding.UTF8))
                            {
                                writer.Write(content);
                                writer.Flush();
                                fs.Flush(true); // 强制将所有缓冲数据写入磁盘
                            }

                            // 写入成功，跳出重试循环
                            break;
                        }
                        finally
                        {
                            // 如果获取了互斥锁，释放它
                            if (mutexAcquired)
                            {
                                mutex.ReleaseMutex();
                            }
                        }
                    }
                }
                catch (IOException ex) when (ex.HResult == unchecked((int)0x80070020)) // 文件正被占用
                {
                    retryCount++;
                    if (retryCount >= maxRetries)
                    {
                        ETLogManager.Error(_logSource, $"保存Cookie文件失败，文件被占用且重试{maxRetries}次仍无法访问: {filePath}, {ex.Message}");
                        throw; // 重试次数已用完，抛出异常
                    }

                    ETLogManager.Warning(_logSource, $"Cookie文件被占用，等待 {retryDelayMs}ms 后重试 ({retryCount}/{maxRetries}): {filePath}");
                    System.Threading.Thread.Sleep(retryDelayMs);
                    retryDelayMs *= 2; // 每次重试延长等待时间
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, $"保存Cookie文件失败: {filePath} - {ex.Message}");
                    throw; // 其他类型的异常，直接抛出
                }
            }
        }

        #region IDisposable实现

        private bool _disposed = false;

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        ETLogManager.Info(_logSource, "开始释放WebBrowserTabManager资源");

                        // 清理所有标签页
                        CleanupAllTabs();

                        ETLogManager.Info(_logSource, "WebBrowserTabManager资源已释放");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"释放WebBrowserTabManager资源时出错: {ex.Message}");
                    }
                }

                _disposed = true;
            }
        }

        /// <summary>
        /// 终结器
        /// </summary>
        ~WebBrowserTabManager()
        {
            Dispose(false);
        }

        #endregion IDisposable实现
    }
}