/*
 * ========================================
 * ETAIv2 - Excel AI辅助工具库 v2.0
 * ========================================
 *
 * 文件名: AIExcelAssistant.cs
 * 描述: ETAIv2的主入口类，提供完整的AI辅助功能接口
 * 作者: ETAIv2开发团队
 * 创建时间: 2024
 * 版本: 2.0.0
 *
 * 主要功能:
 * - 作为ETAIv2库的统一入口点
 * - 协调各个服务组件的工作
 * - 提供新的异步API接口
 * - 保持与现有AIAssistant的兼容性
 * - 管理依赖注入和资源生命周期
 *
 * 使用方式:
 * ```csharp
 * using var assistant = new AIExcelAssistant();
 * var response = await assistant.ProcessExcelDataAsync(...);
 * ```
 *
 * 兼容性:
 * - 完全兼容现有的BatchProcessAIResponsesToExcelAsync方法
 * - 支持现有的.ai配置文件和.rule规则文件
 *
 * ========================================
 */

using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Office.Interop.Excel;
using Newtonsoft.Json.Linq;
using ET.ETAIv2.Models;
using ET.ETAIv2.Interfaces;
using ET.ETAIv2.Utils;
using ET.ETAIv2.Services.Core;
using ET.ETAIv2.Exceptions;
using ET;

namespace ET.ETAIv2
{
    /// <summary>
    /// ETAIv2主入口类，提供完整的AI辅助功能
    /// </summary>
    /// <remarks>
    /// 这个类是ETAIv2库的核心入口点，负责：
    /// 1. 管理所有服务组件的生命周期
    /// 2. 提供统一的API接口
    /// 3. 协调数据提取、AI处理、结果回填的完整流程
    /// 4. 保持与现有系统的兼容性
    ///
    /// 实现了IDisposable接口，确保资源的正确释放
    /// </remarks>
    /// <example>
    /// <code>
    /// // 基本使用方式
    /// using var assistant = new AIExcelAssistant();
    /// var response = await assistant.ProcessExcelDataAsync(
    ///     sourceRange, targetRange, promptRange, fileRange,
    ///     "model.ai", "rules.rule", DataSourceMode.ByRow);
    ///
    /// // 兼容模式使用
    /// var (response, isQuestionRow, requestJson) = await assistant.BatchProcessAIResponsesToExcelAsync(
    ///     questionRange, dataRange, fileRange, "model.ai", "rules.rule", "system.txt", ExcelFillOption.FillAll);
    /// </code>
    /// </example>
    public class AIExcelAssistant : IDisposable
    {
        #region 私有字段

        /// <summary>
        /// AI处理管理器，负责协调整个处理流程
        /// </summary>
        readonly IAIProcessingManager _processingManager;

        /// <summary>
        /// 配置管理器，负责加载.ai和.rule文件
        /// </summary>
        readonly IAIConfigManager _configManager;

        /// <summary>
        /// 错误处理器，负责异常处理和重试逻辑
        /// </summary>
        readonly IAIErrorHandler _errorHandler;

        /// <summary>
        /// 日志记录器，负责记录处理过程和调试信息
        /// </summary>
        readonly IAILogger _logger;

        /// <summary>
        /// 资源释放标志
        /// </summary>
        bool _disposed = false;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// 初始化所有服务组件并建立依赖关系
        /// </summary>
        /// <remarks>
        /// 采用手动依赖注入的方式，按以下顺序初始化组件：
        /// 1. 基础组件：日志、配置、错误处理
        /// 2. 服务组件：数据提取、文件处理、AI客户端、结果回填
        /// 3. 管理组件：处理管理器
        ///
        /// 这种设计确保了组件间的正确依赖关系和初始化顺序
        /// </remarks>
        public AIExcelAssistant()
        {
            // 初始化核心组件
            _logger = new AILogger();
            _configManager = new AIConfigManager();
            _errorHandler = new AIErrorHandler(_logger);

            // 初始化服务组件
            AIDataExtractor dataExtractor = new AIDataExtractor(_logger);
            AIFileProcessor fileProcessor = new AIFileProcessor(_logger);
            AIClient aiClient = new AIClient(_logger, _errorHandler);
            AIResultFiller resultFiller = new AIResultFiller(_logger);

            // 初始化处理管理器
            _processingManager = new AIProcessingManager(
                dataExtractor, fileProcessor, aiClient, resultFiller, _logger, _errorHandler);

            _logger.LogInfo("ETAIv2 AI助手已初始化");
        }

        #endregion

        #region 公共API方法

        /// <summary>
        /// 处理Excel数据的AI分析和回填（新接口）
        /// </summary>
        /// <param name="sourceRange">数据源区域 - 包含需要AI分析的原始数据</param>
        /// <param name="targetRange">回填区域 - AI分析结果将写入的目标区域</param>
        /// <param name="promptRange">提示词区域 - 包含列级AI指令的区域</param>
        /// <param name="fileRange">文件区域 - 包含文件路径的区域</param>
        /// <param name="modelConfigFile">模型配置文件路径 - .ai格式的配置文件</param>
        /// <param name="globalPromptFile">全局提示词文件路径 - .rule格式的规则文件</param>
        /// <param name="mode">数据处理模式 - 按行或按列分组处理</param>
        /// <param name="fileMode">文件处理模式 - 上传到OpenAI或本地读取</param>
        /// <param name="fillNullValues">是否回填null值 - true表示回填，false表示跳过</param>
        /// <param name="progress">进度报告器 - 用于向UI报告处理进度</param>
        /// <param name="cancellationToken">取消令牌 - 用于取消长时间运行的操作</param>
        /// <returns>AI处理响应，包含成功状态、结果数据、错误信息等</returns>
        /// <remarks>
        /// 这是ETAIv2的主要API方法，提供完整的AI辅助功能：
        ///
        /// <para><strong>处理流程：</strong></para>
        /// 1. 验证输入参数的有效性
        /// 2. 加载模型配置和全局提示词
        /// 3. 从Excel区域提取数据并分组
        /// 4. 处理相关文件（上传或本地读取）
        /// 5. 构建AI请求并发送到AI服务
        /// 6. 解析AI响应并回填到Excel
        /// 7. 清理临时资源
        ///
        /// <para><strong>错误处理：</strong></para>
        /// - 自动重试网络错误和临时故障
        /// - 详细的错误日志和用户友好的错误消息
        /// - 支持操作取消和超时处理
        ///
        /// <para><strong>性能优化：</strong></para>
        /// - 大数据集自动分批处理
        /// - 异步操作避免UI阻塞
        /// - 智能的文件缓存和重用
        /// </remarks>
        /// <example>
        /// <code>
        /// // 基本用法
        /// var response = await assistant.ProcessExcelDataAsync(
        ///     sourceRange: worksheet.Range["A1:C10"],
        ///     targetRange: worksheet.Range["D1:F10"],
        ///     promptRange: worksheet.Range["D1:F1"],
        ///     fileRange: worksheet.Range["G1:G10"],
        ///     modelConfigFile: @"config\gpt-4.ai",
        ///     globalPromptFile: @"config\analysis.rule"
        /// );
        /// </code>
        /// </example>
        /// <exception cref="ArgumentNullException">当必需的参数为null时抛出</exception>
        /// <exception cref="ArgumentException">当参数值无效时抛出</exception>
        /// <exception cref="AIProcessingException">当AI处理过程中发生错误时抛出</exception>
        /// <exception cref="OperationCanceledException">当操作被取消时抛出</exception>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<AIResponse> ProcessExcelDataAsync(
            Range sourceRange,
            Range targetRange,
            Range promptRange,
            Range fileRange,
            string modelConfigFile,
            string globalPromptFile,
            DataSourceMode mode = DataSourceMode.ByRow,
            FileProcessingMode fileMode = FileProcessingMode.UploadToOpenAI,
            bool fillNullValues = false,
            IProgress<ProcessingProgress> progress = null,
            CancellationToken cancellationToken = default)
        {
            string requestId = Guid.NewGuid().ToString("N").Substring(0, 8);
            DateTime startTime = DateTime.Now;

            try
            {
                _logger.LogInfo($"开始处理Excel数据，请求ID: {requestId}");
                progress?.Report(new ProcessingProgress("正在初始化配置...", 5));

                // 验证输入参数
                ValidateInputParameters(sourceRange, targetRange, promptRange, modelConfigFile, globalPromptFile);

                // 加载配置
                progress?.Report(new ProcessingProgress("正在加载配置...", 10));
                AIModelConfig modelConfig = _configManager.LoadModelConfig(modelConfigFile);
                string globalPrompt = _configManager.LoadGlobalPrompt(globalPromptFile);

                // 构建数据源配置
                AIDataSourceConfig dataSourceConfig = new AIDataSourceConfig
                {
                    SourceRange = sourceRange,
                    TargetRange = targetRange,
                    PromptRange = promptRange,
                    FileRange = fileRange,
                    Mode = mode,
                    GlobalPrompt = globalPrompt,
                    ModelConfig = modelConfig,
                    FileProcessingMode = fileMode,
                    FillNullValues = fillNullValues
                };

                // 调用处理管理器
                return await _processingManager.ProcessAsync(dataSourceConfig, progress, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInfo($"Excel数据处理已取消，请求ID: {requestId}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Excel数据处理失败，请求ID: {requestId}", ex);
                progress?.Report(new ProcessingProgress($"处理失败：{ex.Message}", 0));

                AIProcessingException wrappedException = _errorHandler.WrapException(ex, "Excel数据处理");
                return AIResponse.CreateFailure(requestId, wrappedException.Message);
            }
        }

        /// <summary>
        /// 兼容现有AIAssistant的批处理方法
        /// </summary>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<(JObject Response, bool IsQuestionRow, JObject RequestJson)>
            BatchProcessAIResponsesToExcelAsync(
                Range questionRuleRange,
                Range dataSourceRange,
                Range fileSourceRange,
                string modelFile,
                string ruleFile,
                string systemContentFile,
                ExcelFillOption fillOption)
        {
            try
            {
                _logger.LogInfo("开始批处理AI响应到Excel（兼容模式）");

                // 转换为新的接口参数
                DataSourceMode mode = DetermineDataSourceMode(questionRuleRange, dataSourceRange);
                bool fillNullValues = fillOption == ExcelFillOption.FillAll;

                // 调用新接口
                AIResponse response = await ProcessExcelDataAsync(
                    dataSourceRange,
                    questionRuleRange, // 在兼容模式下，问题规则区域作为目标区域
                    questionRuleRange,
                    fileSourceRange,
                    modelFile,
                    ruleFile,
                    mode,
                    FileProcessingMode.ReadLocally, // 兼容模式使用本地读取
                    fillNullValues);

                // 转换返回格式以兼容现有代码
                JObject responseJson = ConvertToLegacyFormat(response);
                JObject requestJson = BuildLegacyRequestJson(questionRuleRange, dataSourceRange, fileSourceRange);

                _logger.LogInfo("批处理AI响应到Excel完成（兼容模式）");
                return (responseJson, true, requestJson);
            }
            catch (Exception ex)
            {
                _logger.LogError("批处理AI响应到Excel失败（兼容模式）", ex);
                throw new AIProcessingException("批处理失败", ex);
            }
        }

        /// <summary>
        /// 验证输入参数
        /// </summary>
        void ValidateInputParameters(Range sourceRange, Range targetRange, Range promptRange,
            string modelConfigFile, string globalPromptFile)
        {
            if (sourceRange == null)
                throw new ArgumentNullException(nameof(sourceRange), "数据源区域不能为空");

            if (targetRange == null)
                throw new ArgumentNullException(nameof(targetRange), "回填区域不能为空");

            if (promptRange == null)
                throw new ArgumentNullException(nameof(promptRange), "提示词区域不能为空");

            if (string.IsNullOrEmpty(modelConfigFile))
                throw new ArgumentException("模型配置文件不能为空", nameof(modelConfigFile));

            if (string.IsNullOrEmpty(globalPromptFile))
                throw new ArgumentException("全局提示词文件不能为空", nameof(globalPromptFile));
        }

        /// <summary>
        /// 确定数据源模式
        /// </summary>
        DataSourceMode DetermineDataSourceMode(Range questionRange, Range dataRange)
        {
            try
            {
                // 根据区域形状判断处理模式
                int questionRows = questionRange.Rows.Count;
                int questionCols = questionRange.Columns.Count;
                int dataRows = dataRange.Rows.Count;
                int dataCols = dataRange.Columns.Count;

                // 如果问题区域是横向的，且数据区域行数多于列数，则为行模式
                if (questionCols > questionRows && dataRows > dataCols)
                    return DataSourceMode.ByRow;

                // 如果问题区域是纵向的，且数据区域列数多于行数，则为列模式  
                if (questionRows > questionCols && dataCols > dataRows)
                    return DataSourceMode.ByColumn;

                // 默认为行模式
                return DataSourceMode.ByRow;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"确定数据源模式失败，使用默认行模式: {ex.Message}");
                return DataSourceMode.ByRow;
            }
        }

        /// <summary>
        /// 转换为兼容格式
        /// </summary>
        JObject ConvertToLegacyFormat(AIResponse response)
        {
            JObject legacyResponse = new JObject();

            if (response.Success && response.Results?.Count > 0)
            {
                JArray resultsArray = new JArray();
                foreach (GroupResult result in response.Results)
                {
                    JObject resultObj = new JObject
                    {
                        ["groupId"] = result.GroupId,
                        ["values"] = JObject.FromObject(result.Values),
                        ["processingInfo"] = result.ProcessingInfo
                    };
                    resultsArray.Add(resultObj);
                }
                legacyResponse["results"] = resultsArray;
                legacyResponse["success"] = true;
            }
            else
            {
                legacyResponse["success"] = false;
                legacyResponse["error"] = response.ErrorMessage;
            }

            return legacyResponse;
        }

        /// <summary>
        /// 构建兼容的请求JSON
        /// </summary>
        JObject BuildLegacyRequestJson(Range questionRange, Range dataRange, Range fileRange)
        {
            return new JObject
            {
                [nameof(questionRange)] = questionRange?.Address,
                [nameof(dataRange)] = dataRange?.Address,
                [nameof(fileRange)] = fileRange?.Address,
                ["timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };
        }
        #endregion  
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _logger?.LogInfo("ETAIv2 AI助手正在释放资源");
                // 这里可以添加资源清理逻辑
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Excel填充选项（兼容现有代码）
    /// </summary>
    public enum ExcelFillOption
    {
        SkipNull,   // 跳过null值
        FillAll     // 填充所有值
    }
}
