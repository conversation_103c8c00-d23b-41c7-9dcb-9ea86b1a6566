using System;
using System.Collections.Generic;
using System.IO;
using ET.ETWebAutomation.Models;
using ET;

namespace ET.ETWebAutomation.Storage
{
    /// <summary>
    /// 会话状态存储类，负责会话数据的持久化存储
    /// </summary>
    public class ETWebSessionStorage
    {
        #region 私有字段

        private readonly ETIniFile _configFile;
        private readonly string _configPath;
        private const string SESSION_SECTION = "Sessions";

        #endregion 私有字段

        #region 构造函数

        /// <summary>
        /// 初始化会话存储
        /// </summary>
        public ETWebSessionStorage()
        {
            try
            {
                // 创建配置文件路径
                string configDir = Path.Combine(
                    Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location),
                    "Config");

                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                _configPath = Path.Combine(configDir, "ETOASession.ini");
                _configFile = new ETIniFile(_configPath);

                ETLogManager.Info($"会话存储初始化完成，配置文件: {_configPath}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("会话存储初始化失败", ex);
                throw new ETException("会话存储初始化失败", "ETOASessionStorage", ex);
            }
        }

        #endregion 构造函数

        #region 公共方法

        /// <summary>
        /// 保存会话数据
        /// </summary>
        /// <param name="sessionData">会话数据</param>
        public void SaveSessionData(ETWebSessionData sessionData)
        {
            try
            {
                if (sessionData == null)
                {
                    throw new ArgumentNullException(nameof(sessionData));
                }

                string sessionSection = $"{SESSION_SECTION}_{sessionData.SessionId}";

                // 保存基本会话信息
                _configFile.IniWriteValue(sessionSection, "SessionId", sessionData.SessionId);
                _configFile.IniWriteValue(sessionSection, "UserId", sessionData.UserId);
                _configFile.IniWriteValue(sessionSection, "Username", sessionData.Username);
                _configFile.IniWriteValue(sessionSection, "CreatedTime", sessionData.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss"));
                _configFile.IniWriteValue(sessionSection, "LastActivityTime", sessionData.LastActivityTime.ToString("yyyy-MM-dd HH:mm:ss"));
                _configFile.IniWriteValue(sessionSection, "ExpiryTime", sessionData.ExpiryTime.ToString("yyyy-MM-dd HH:mm:ss"));
                _configFile.IniWriteValue(sessionSection, "IsValid", sessionData.IsValid.ToString());
                _configFile.IniWriteValue(sessionSection, "Status", sessionData.Status.ToString());
                _configFile.IniWriteValue(sessionSection, "AuthToken", sessionData.AuthToken);
                _configFile.IniWriteValue(sessionSection, "RefreshToken", sessionData.RefreshToken);
                _configFile.IniWriteValue(sessionSection, "ClientIP", sessionData.ClientIP);
                _configFile.IniWriteValue(sessionSection, "UserAgent", sessionData.UserAgent);
                _configFile.IniWriteValue(sessionSection, "LoginMethod", sessionData.LoginMethod);
                _configFile.IniWriteValue(sessionSection, "HeartbeatInterval", sessionData.HeartbeatInterval.ToString());
                _configFile.IniWriteValue(sessionSection, "LastHeartbeatTime", sessionData.LastHeartbeatTime.ToString("yyyy-MM-dd HH:mm:ss"));
                _configFile.IniWriteValue(sessionSection, "HeartbeatFailureCount", sessionData.HeartbeatFailureCount.ToString());
                _configFile.IniWriteValue(sessionSection, "MaxHeartbeatFailures", sessionData.MaxHeartbeatFailures.ToString());

                // 保存Cookie信息
                if (sessionData.Cookies != null && sessionData.Cookies.Count > 0)
                {
                    string cookieSection = $"{sessionSection}_Cookies";
                    foreach (var cookie in sessionData.Cookies)
                    {
                        _configFile.IniWriteValue(cookieSection, cookie.Key, cookie.Value);
                    }
                }

                // 保存权限信息
                if (sessionData.Permissions != null && sessionData.Permissions.Count > 0)
                {
                    string permissionSection = $"{sessionSection}_Permissions";
                    for (int i = 0; i < sessionData.Permissions.Count; i++)
                    {
                        _configFile.IniWriteValue(permissionSection, $"Permission_{i}", sessionData.Permissions[i]);
                    }
                }

                // 保存角色信息
                if (sessionData.Roles != null && sessionData.Roles.Count > 0)
                {
                    string roleSection = $"{sessionSection}_Roles";
                    for (int i = 0; i < sessionData.Roles.Count; i++)
                    {
                        _configFile.IniWriteValue(roleSection, $"Role_{i}", sessionData.Roles[i]);
                    }
                }

                ETLogManager.Info($"会话数据保存成功，SessionId: {sessionData.SessionId}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"保存会话数据失败，SessionId: {sessionData?.SessionId}", ex);
                throw new ETException($"保存会话数据失败: {ex.Message}", "SaveSessionData", ex);
            }
        }

        /// <summary>
        /// 加载会话数据
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <returns>会话数据</returns>
        public ETWebSessionData LoadSessionData(string sessionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sessionId))
                {
                    throw new ArgumentException("会话ID不能为空");
                }

                string sessionSection = $"{SESSION_SECTION}_{sessionId}";

                // 检查会话是否存在
                string savedSessionId = _configFile.IniReadValue(sessionSection, "SessionId", "");
                if (string.IsNullOrEmpty(savedSessionId))
                {
                    ETLogManager.Warning($"未找到会话数据: {sessionId}");
                    return null;
                }

                var sessionData = new ETWebSessionData
                {
                    SessionId = _configFile.IniReadValue(sessionSection, "SessionId", ""),
                    UserId = _configFile.IniReadValue(sessionSection, "UserId", ""),
                    Username = _configFile.IniReadValue(sessionSection, "Username", ""),
                    IsValid = bool.Parse(_configFile.IniReadValue(sessionSection, "IsValid", "false")),
                    AuthToken = _configFile.IniReadValue(sessionSection, "AuthToken", ""),
                    RefreshToken = _configFile.IniReadValue(sessionSection, "RefreshToken", ""),
                    ClientIP = _configFile.IniReadValue(sessionSection, "ClientIP", ""),
                    UserAgent = _configFile.IniReadValue(sessionSection, "UserAgent", ""),
                    LoginMethod = _configFile.IniReadValue(sessionSection, "LoginMethod", ""),
                    HeartbeatInterval = int.Parse(_configFile.IniReadValue(sessionSection, "HeartbeatInterval", "300")),
                    HeartbeatFailureCount = int.Parse(_configFile.IniReadValue(sessionSection, "HeartbeatFailureCount", "0")),
                    MaxHeartbeatFailures = int.Parse(_configFile.IniReadValue(sessionSection, "MaxHeartbeatFailures", "3"))
                };

                // 解析状态枚举
                if (Enum.TryParse<SessionStatus>(_configFile.IniReadValue(sessionSection, "Status", "Active"), out SessionStatus status))
                {
                    sessionData.Status = status;
                }

                // 解析时间字段
                if (DateTime.TryParse(_configFile.IniReadValue(sessionSection, "CreatedTime", ""), out DateTime createdTime))
                {
                    sessionData.CreatedTime = createdTime;
                }

                if (DateTime.TryParse(_configFile.IniReadValue(sessionSection, "LastActivityTime", ""), out DateTime lastActivityTime))
                {
                    sessionData.LastActivityTime = lastActivityTime;
                }

                if (DateTime.TryParse(_configFile.IniReadValue(sessionSection, "ExpiryTime", ""), out DateTime expiryTime))
                {
                    sessionData.ExpiryTime = expiryTime;
                }

                if (DateTime.TryParse(_configFile.IniReadValue(sessionSection, "LastHeartbeatTime", ""), out DateTime lastHeartbeatTime))
                {
                    sessionData.LastHeartbeatTime = lastHeartbeatTime;
                }

                // 加载Cookie信息
                string cookieSection = $"{sessionSection}_Cookies";
                var cookieDict = _configFile.GetSection(cookieSection);
                if (cookieDict != null)
                {
                    foreach (var kvp in cookieDict)
                    {
                        if (!string.IsNullOrEmpty(kvp.Value))
                        {
                            sessionData.Cookies[kvp.Key] = kvp.Value;
                        }
                    }
                }

                // 加载权限信息
                string permissionSection = $"{sessionSection}_Permissions";
                var permissionDict = _configFile.GetSection(permissionSection);
                if (permissionDict != null)
                {
                    foreach (var kvp in permissionDict)
                    {
                        if (!string.IsNullOrEmpty(kvp.Value))
                        {
                            sessionData.Permissions.Add(kvp.Value);
                        }
                    }
                }

                // 加载角色信息
                string roleSection = $"{sessionSection}_Roles";
                var roleDict = _configFile.GetSection(roleSection);
                if (roleDict != null)
                {
                    foreach (var kvp in roleDict)
                    {
                        if (!string.IsNullOrEmpty(kvp.Value))
                        {
                            sessionData.Roles.Add(kvp.Value);
                        }
                    }
                }

                ETLogManager.Info($"会话数据加载成功，SessionId: {sessionId}");
                return sessionData;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"加载会话数据失败，SessionId: {sessionId}", ex);
                throw new ETException($"加载会话数据失败: {ex.Message}", "LoadSessionData", ex);
            }
        }

        /// <summary>
        /// 删除会话数据
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        public void DeleteSessionData(string sessionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sessionId))
                {
                    throw new ArgumentException("会话ID不能为空");
                }

                string sessionSection = $"{SESSION_SECTION}_{sessionId}";
                string cookieSection = $"{sessionSection}_Cookies";
                string permissionSection = $"{sessionSection}_Permissions";
                string roleSection = $"{sessionSection}_Roles";

                // 删除所有相关配置节
                _configFile.DeleteSection(sessionSection);
                _configFile.DeleteSection(cookieSection);
                _configFile.DeleteSection(permissionSection);
                _configFile.DeleteSection(roleSection);

                ETLogManager.Info($"会话数据删除成功，SessionId: {sessionId}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"删除会话数据失败，SessionId: {sessionId}", ex);
                throw new ETException($"删除会话数据失败: {ex.Message}", "DeleteSessionData", ex);
            }
        }

        /// <summary>
        /// 获取所有会话ID列表
        /// </summary>
        /// <returns>会话ID列表</returns>
        public List<string> GetAllSessionIds()
        {
            try
            {
                var sessionIds = new List<string>();
                var sections = _configFile.GetSectionNames();

                if (sections != null)
                {
                    foreach (string section in sections)
                    {
                        if (section.StartsWith(SESSION_SECTION + "_") &&
                            !section.Contains("_Cookies") &&
                            !section.Contains("_Permissions") &&
                            !section.Contains("_Roles"))
                        {
                            string sessionId = section.Substring((SESSION_SECTION + "_").Length);
                            sessionIds.Add(sessionId);
                        }
                    }
                }

                ETLogManager.Debug($"获取所有会话ID列表，共 {sessionIds.Count} 个会话");
                return sessionIds;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("获取所有会话ID列表失败", ex);
                return new List<string>();
            }
        }

        /// <summary>
        /// 清理过期的会话数据
        /// </summary>
        public void CleanExpiredSessions()
        {
            try
            {
                var sessionIds = GetAllSessionIds();
                int cleanedCount = 0;

                foreach (string sessionId in sessionIds)
                {
                    var sessionData = LoadSessionData(sessionId);
                    if (sessionData != null && sessionData.IsExpired)
                    {
                        DeleteSessionData(sessionId);
                        cleanedCount++;
                    }
                }

                ETLogManager.Info($"清理过期会话完成，清理了 {cleanedCount} 个过期会话");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("清理过期会话失败", ex);
            }
        }

        #endregion 公共方法
    }
}