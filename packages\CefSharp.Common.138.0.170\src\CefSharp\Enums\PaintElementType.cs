// Copyright © 2013 The CefSharp Authors. All rights reserved.
//
// Use of this source code is governed by a BSD-style license that can be found in the LICENSE file.

namespace CefSharp
{
    /// <summary>
    /// Paint element types.
    /// </summary>
    public enum PaintElementType
    {
        /// <summary>
        /// An enum constant representing the view option.
        /// </summary>
        View = 0,
        /// <summary>
        /// An enum constant representing the popup option.
        /// </summary>
        Popup
    };
}
