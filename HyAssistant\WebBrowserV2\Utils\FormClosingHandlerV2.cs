using ET;
using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using HyAssistant.WebBrowserV2.Core;

namespace HyAssistant.WebBrowserV2.Utils
{
    /// <summary>
    /// 窗体关闭处理器V2版本，负责处理WebBrowserV2窗体的关闭逻辑
    /// </summary>
    /// <remarks>
    /// V2版本优化：
    /// 1. 异步安全的关闭处理
    /// 2. 统一使用ETLogManager进行日志记录
    /// 3. 增强的资源清理机制
    /// 4. 支持优雅关闭和强制关闭
    /// 5. 完善的异常处理
    /// </remarks>
    public class FormClosingHandlerV2 : IDisposable
    {
        #region 字段和属性
        
        /// <summary>
        /// 主窗体引用
        /// </summary>
        private readonly Core.WebBrowserV2 _mainForm;
        
        /// <summary>
        /// 用于记录的对象
        /// </summary>
        private readonly object _logSource;
        
        /// <summary>
        /// 是否已释放
        /// </summary>
        private bool _disposed = false;
        
        /// <summary>
        /// 是否正在关闭
        /// </summary>
        private bool _isClosing = false;
        
        /// <summary>
        /// 关闭超时时间（毫秒）
        /// </summary>
        private const int CLOSE_TIMEOUT_MS = 30000; // 30秒
        
        #endregion

        #region 构造方法
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mainForm">主窗体</param>
        public FormClosingHandlerV2(Core.WebBrowserV2 mainForm)
        {
            _mainForm = mainForm ?? throw new ArgumentNullException(nameof(mainForm));
            _logSource = mainForm;
            
            // 注册窗体关闭事件
            RegisterFormClosingEvents();
            
            ETLogManager.Info(_logSource, "FormClosingHandlerV2初始化完成");
        }
        
        #endregion

        #region 事件注册
        
        /// <summary>
        /// 注册窗体关闭事件
        /// </summary>
        private void RegisterFormClosingEvents()
        {
            try
            {
                _mainForm.FormClosing += MainForm_FormClosing;
                _mainForm.FormClosed += MainForm_FormClosed;
                
                ETLogManager.Debug(_logSource, "窗体关闭事件注册完成");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, "注册窗体关闭事件");
            }
        }
        
        /// <summary>
        /// 注销窗体关闭事件
        /// </summary>
        private void UnregisterFormClosingEvents()
        {
            try
            {
                if (_mainForm != null)
                {
                    _mainForm.FormClosing -= MainForm_FormClosing;
                    _mainForm.FormClosed -= MainForm_FormClosed;
                }
                
                ETLogManager.Debug(_logSource, "窗体关闭事件注销完成");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, "注销窗体关闭事件");
            }
        }
        
        #endregion

        #region 事件处理
        
        /// <summary>
        /// 窗体关闭事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_disposed || _isClosing)
                return;
                
            try
            {
                _isClosing = true;
                ETLogManager.Info(_logSource, "开始处理窗体关闭事件");
                
                // 如果是用户关闭或系统关闭，进行确认
                if (e.CloseReason == CloseReason.UserClosing || e.CloseReason == CloseReason.WindowsShutDown)
                {
                    // 检查是否有未保存的数据或正在进行的操作
                    bool canClose = await CheckCanCloseAsync();
                    if (!canClose)
                    {
                        e.Cancel = true;
                        _isClosing = false;
                        ETLogManager.Info(_logSource, "窗体关闭被取消");
                        return;
                    }
                }
                
                // 执行关闭前的清理工作
                await PerformPreCloseCleanupAsync();
                
                ETLogManager.Info(_logSource, "窗体关闭事件处理完成");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, "处理窗体关闭事件", true);
                
                // 如果清理过程中出现异常，仍然允许关闭
                e.Cancel = false;
            }
        }
        
        /// <summary>
        /// 窗体已关闭事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void MainForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            try
            {
                ETLogManager.Info(_logSource, "窗体已关闭，执行最终清理");
                
                // 执行最终清理
                PerformFinalCleanup();
                
                ETLogManager.Info(_logSource, "窗体关闭处理完成");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, "处理窗体已关闭事件");
            }
        }
        
        #endregion

        #region 关闭检查和清理
        
        /// <summary>
        /// 检查是否可以关闭
        /// </summary>
        /// <returns>是否可以关闭</returns>
        private Task<bool> CheckCanCloseAsync()
        {
            try
            {
                ETLogManager.Debug(_logSource, "检查窗体是否可以关闭");

                // 检查是否有正在进行的重要操作
                if (_mainForm.TabManager != null)
                {
                    // 检查是否有正在初始化的标签页
                    var allConfigs = _mainForm.TabManager.GetAllTabConfigsSafely();
                    foreach (var config in allConfigs)
                    {
                        // 这里可以添加更多的检查逻辑
                        // 例如检查是否有正在进行的下载、上传等操作
                    }
                }

                // 如果有需要，可以显示确认对话框
                // var result = MessageBox.Show("确定要关闭浏览器吗？", "确认关闭",
                //     MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                // return result == DialogResult.Yes;

                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, "检查窗体关闭条件");
                return Task.FromResult(true); // 出现异常时允许关闭
            }
        }
        
        /// <summary>
        /// 执行关闭前的清理工作
        /// </summary>
        /// <returns>清理任务</returns>
        private async Task PerformPreCloseCleanupAsync()
        {
            try
            {
                ETLogManager.Info(_logSource, "开始执行关闭前清理");
                
                // 使用超时机制，避免清理过程阻塞太久
                var timeoutTask = Task.Delay(CLOSE_TIMEOUT_MS);
                var cleanupTask = PerformCleanupInternalAsync();
                
                var completedTask = await Task.WhenAny(cleanupTask, timeoutTask);
                
                if (completedTask == timeoutTask)
                {
                    ETLogManager.Warning(_logSource, "关闭前清理超时，强制继续关闭");
                }
                else
                {
                    ETLogManager.Info(_logSource, "关闭前清理完成");
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, "执行关闭前清理");
            }
        }
        
        /// <summary>
        /// 内部清理方法
        /// </summary>
        /// <returns>清理任务</returns>
        private async Task PerformCleanupInternalAsync()
        {
            try
            {
                // 停止所有会话
                if (_mainForm.SessionManager != null)
                {
                    ETLogManager.Debug(_logSource, "停止所有会话");
                    // 这里可以添加停止会话的逻辑
                }
                
                // 保存配置
                if (_mainForm.ConfigManager != null)
                {
                    ETLogManager.Debug(_logSource, "保存配置");
                    // 这里可以添加保存配置的逻辑
                }
                
                // 清理标签页
                if (_mainForm.TabManager != null)
                {
                    ETLogManager.Debug(_logSource, "清理标签页");
                    // 这里可以添加清理标签页的逻辑
                }
                
                // 等待一小段时间确保清理完成
                await Task.Delay(100);
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, "内部清理过程");
            }
        }
        
        /// <summary>
        /// 执行最终清理
        /// </summary>
        private void PerformFinalCleanup()
        {
            try
            {
                ETLogManager.Debug(_logSource, "执行最终清理");
                
                // 注销事件
                UnregisterFormClosingEvents();
                
                // 这里可以添加其他最终清理逻辑
                
                ETLogManager.Debug(_logSource, "最终清理完成");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, "执行最终清理");
            }
        }
        
        #endregion

        #region IDisposable实现
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        
        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        // 注销事件
                        UnregisterFormClosingEvents();
                        
                        ETLogManager.Info(_logSource, "FormClosingHandlerV2资源释放完成");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"释放FormClosingHandlerV2资源失败: {ex.Message}");
                    }
                }
                
                _disposed = true;
            }
        }
        
        #endregion
    }
}
