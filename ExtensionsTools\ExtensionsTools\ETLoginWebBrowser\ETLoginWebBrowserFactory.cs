using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ET.ETLoginWebBrowser
{
    /// <summary>
    /// ETLoginWebBrowser工厂类，提供便捷的创建和使用方法
    /// </summary>
    public static class ETLoginWebBrowserFactory
    {
        /// <summary>
        /// 获取指定URL的HTTP响应标头JSON数据
        /// </summary>
        /// <param name="url">要访问的URL</param>
        /// <param name="parentForm">父窗体（可选）</param>
        /// <returns>HTTP标头的JSON数据，失败返回空字符串</returns>
        public static string GetHeadersJson(string url, Form parentForm = null)
        {
            try
            {
                using (var loginBrowser = new ETLoginWebBrowser(url))
                {
                    var result = parentForm != null
                        ? loginBrowser.ShowDialog(parentForm)
                        : loginBrowser.ShowDialog();

                    //return result == DialogResult.OK ? loginBrowser.HeadersJson : string.Empty;
                    return loginBrowser.HeadersJson;
                }
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取指定URL的cookies JSON数据（保持向后兼容性）
        /// </summary>
        /// <param name="url">要访问的URL</param>
        /// <param name="parentForm">父窗体（可选）</param>
        /// <returns>cookies的JSON数据，失败返回空字符串</returns>
        [Obsolete("建议使用 GetHeadersJson 方法获取更完整的HTTP标头信息")]
        public static string GetCookiesJson(string url, Form parentForm = null)
        {
            try
            {
                using (var loginBrowser = new ETLoginWebBrowser(url))
                {
                    var result = parentForm != null
                        ? loginBrowser.ShowDialog(parentForm)
                        : loginBrowser.ShowDialog();

                    return result == DialogResult.OK ? loginBrowser.HeadersJson : string.Empty;
                }
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 异步获取指定URL的cookies JSON数据
        /// </summary>
        /// <param name="url">要访问的URL</param>
        /// <param name="parentForm">父窗体（可选）</param>
        /// <returns>cookies的JSON数据，失败返回空字符串</returns>
        public static async Task<string> GetCookiesJsonAsync(string url, Form parentForm = null)
        {
            return await Task.Run(() => GetHeadersJson(url, parentForm));
        }

        /// <summary>
        /// 获取cookies数据对象
        /// </summary>
        /// <param name="url">要访问的URL</param>
        /// <param name="parentForm">父窗体（可选）</param>
        /// <returns>CookieData对象，失败返回null</returns>
        public static CookieData GetCookieData(string url, Form parentForm = null)
        {
            try
            {
                var cookiesJson = GetHeadersJson(url, parentForm);

                if (string.IsNullOrEmpty(cookiesJson))
                    return null;

                return Newtonsoft.Json.JsonConvert.DeserializeObject<CookieData>(cookiesJson);
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// 异步获取cookies数据对象
        /// </summary>
        /// <param name="url">要访问的URL</param>
        /// <param name="parentForm">父窗体（可选）</param>
        /// <returns>CookieData对象，失败返回null</returns>
        public static async Task<CookieData> GetCookieDataAsync(string url, Form parentForm = null)
        {
            return await Task.Run(() => GetCookieData(url, parentForm));
        }

        /// <summary>
        /// 创建登录浏览器实例
        /// </summary>
        /// <param name="url">初始URL（可选）</param>
        /// <returns>ETLoginWebBrowser实例</returns>
        public static ETLoginWebBrowser CreateLoginBrowser(string url = null)
        {
            return string.IsNullOrEmpty(url)
                ? new ETLoginWebBrowser()
                : new ETLoginWebBrowser(url);
        }

        /// <summary>
        /// 显示登录浏览器并获取结果
        /// </summary>
        /// <param name="url">要访问的URL</param>
        /// <param name="parentForm">父窗体（可选）</param>
        /// <param name="onSuccess">成功回调</param>
        /// <param name="onCancel">取消回调</param>
        /// <param name="onError">错误回调</param>
        public static void ShowLoginBrowser(
            string url,
            Form parentForm = null,
            Action<string> onSuccess = null,
            Action onCancel = null,
            Action<Exception> onError = null)
        {
            try
            {
                using (var loginBrowser = new ETLoginWebBrowser(url))
                {
                    var result = parentForm != null
                        ? loginBrowser.ShowDialog(parentForm)
                        : loginBrowser.ShowDialog();

                    switch (result)
                    {
                        case DialogResult.OK:
                            onSuccess?.Invoke(loginBrowser.HeadersJson);
                            break;

                        case DialogResult.Cancel:
                            onCancel?.Invoke();
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                onError?.Invoke(ex);
            }
        }

        /// <summary>
        /// 异步显示登录浏览器并获取结果
        /// </summary>
        /// <param name="url">要访问的URL</param>
        /// <param name="parentForm">父窗体（可选）</param>
        /// <param name="onSuccess">成功回调</param>
        /// <param name="onCancel">取消回调</param>
        /// <param name="onError">错误回调</param>
        public static async Task ShowLoginBrowserAsync(
            string url,
            Form parentForm = null,
            Action<string> onSuccess = null,
            Action onCancel = null,
            Action<Exception> onError = null)
        {
            await Task.Run(() => ShowLoginBrowser(url, parentForm, onSuccess, onCancel, onError));
        }

        /// <summary>
        /// 验证URL格式
        /// </summary>
        /// <param name="url">要验证的URL</param>
        /// <returns>是否为有效URL</returns>
        public static bool IsValidUrl(string url)
        {
            if (string.IsNullOrWhiteSpace(url))
                return false;

            return Uri.TryCreate(url, UriKind.Absolute, out Uri result)
                && (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
        }

        /// <summary>
        /// 获取URL的域名
        /// </summary>
        /// <param name="url">URL</param>
        /// <returns>域名，失败返回空字符串</returns>
        public static string GetDomain(string url)
        {
            try
            {
                if (Uri.TryCreate(url, UriKind.Absolute, out Uri uri))
                {
                    return uri.Host;
                }
            }
            catch
            {
                // 忽略异常
            }

            return string.Empty;
        }

        /// <summary>
        /// 检查是否为HTTPS URL
        /// </summary>
        /// <param name="url">要检查的URL</param>
        /// <returns>是否为HTTPS</returns>
        public static bool IsHttpsUrl(string url)
        {
            try
            {
                if (Uri.TryCreate(url, UriKind.Absolute, out Uri uri))
                {
                    return uri.Scheme == Uri.UriSchemeHttps;
                }
            }
            catch
            {
                // 忽略异常
            }

            return false;
        }

        /// <summary>
        /// 构建完整的URL
        /// </summary>
        /// <param name="baseUrl">基础URL</param>
        /// <param name="path">路径</param>
        /// <returns>完整URL</returns>
        public static string BuildUrl(string baseUrl, string path = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(baseUrl))
                    return string.Empty;

                var uri = new Uri(baseUrl);

                if (!string.IsNullOrWhiteSpace(path))
                {
                    uri = new Uri(uri, path);
                }

                return uri.ToString();
            }
            catch
            {
                return baseUrl;
            }
        }
    }

    /// <summary>
    /// 登录浏览器配置选项
    /// </summary>
    public class LoginBrowserOptions
    {
        /// <summary>
        /// 初始URL
        /// </summary>
        public string InitialUrl { get; set; }

        /// <summary>
        /// 窗体标题
        /// </summary>
        public string Title { get; set; } = "登录浏览器 - Cookie获取";

        /// <summary>
        /// 窗体大小
        /// </summary>
        public System.Drawing.Size Size { get; set; } = new System.Drawing.Size(1024, 768);

        /// <summary>
        /// 最小窗体大小
        /// </summary>
        public System.Drawing.Size MinimumSize { get; set; } = new System.Drawing.Size(800, 600);

        /// <summary>
        /// 是否显示状态栏
        /// </summary>
        public bool ShowStatusBar { get; set; } = true;

        /// <summary>
        /// 自动获取cookies
        /// </summary>
        public bool AutoGetCookies { get; set; } = true;
    }

    /// <summary>
    /// 扩展的登录浏览器工厂方法
    /// </summary>
    public static class ETLoginWebBrowserExtensions
    {
        /// <summary>
        /// 使用配置选项创建登录浏览器
        /// </summary>
        /// <param name="options">配置选项</param>
        /// <returns>配置好的登录浏览器实例</returns>
        public static ETLoginWebBrowser CreateWithOptions(LoginBrowserOptions options)
        {
            var browser = new ETLoginWebBrowser(options.InitialUrl);

            // 应用配置
            browser.Text = options.Title;
            browser.Size = options.Size;
            browser.MinimumSize = options.MinimumSize;

            return browser;
        }
    }
}