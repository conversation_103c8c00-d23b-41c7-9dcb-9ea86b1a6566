using ET;
using System;
using System.Threading.Tasks;

namespace HyAssistant.WebBrowserV2.Utils
{
    /// <summary>
    /// WebBrowserV2版本日志助手类
    /// </summary>
    /// <remarks>
    /// V2版本优化：
    /// 1. 统一使用ETLogManager进行日志记录
    /// 2. 提供便捷的日志记录方法
    /// 3. 支持结构化日志记录
    /// 4. 增强异常日志记录功能
    /// </remarks>
    public class LoggingHelperV2 : IDisposable
    {
        #region 私有字段

        /// <summary>
        /// 日志源对象
        /// </summary>
        private readonly object _logSource;

        /// <summary>
        /// 是否已释放资源
        /// </summary>
        private bool _disposed = false;

        #endregion 私有字段

        #region 构造函数

        /// <summary>
        /// 初始化日志助手
        /// </summary>
        /// <param name="logSource">日志源对象</param>
        public LoggingHelperV2(object logSource)
        {
            _logSource = logSource ?? throw new ArgumentNullException(nameof(logSource));
        }

        #endregion 构造函数

        #region 基础日志方法

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogInfo(string message)
        {
            if (_disposed) return;

            try
            {
                ETLogManager.Info(_logSource, message);
            }
            catch (Exception ex)
            {
                // 日志记录失败时的备用处理
                System.Diagnostics.Debug.WriteLine($"日志记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogWarning(string message)
        {
            if (_disposed) return;

            try
            {
                ETLogManager.Warning(_logSource, message);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"日志记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogError(string message)
        {
            if (_disposed) return;

            try
            {
                ETLogManager.Error(_logSource, message);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"日志记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogDebug(string message)
        {
            if (_disposed) return;

            try
            {
                ETLogManager.Debug(_logSource, message);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"日志记录失败: {ex.Message}");
            }
        }

        #endregion 基础日志方法

        #region 异常日志方法

        /// <summary>
        /// 记录异常日志
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="operation">操作描述</param>
        public void LogException(Exception ex, string operation)
        {
            if (_disposed) return;

            try
            {
                string message = $"{operation}时发生异常: {ex.Message}";
                ETLogManager.Error(_logSource, message);

                // 如果是严重异常，记录堆栈信息
                if (IsCriticalException(ex))
                {
                    ETLogManager.Error(_logSource, $"异常堆栈: {ex.StackTrace}");
                }
            }
            catch (Exception logEx)
            {
                System.Diagnostics.Debug.WriteLine($"异常日志记录失败: {logEx.Message}");
            }
        }

        /// <summary>
        /// 记录异步异常日志
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="operation">异步操作描述</param>
        public void LogAsyncException(Exception ex, string operation)
        {
            if (_disposed) return;

            try
            {
                string message = $"异步操作[{operation}]异常: {ex.Message}";
                ETLogManager.Error(_logSource, message);

                // 异步异常的特殊处理
                if (ex is TaskCanceledException)
                {
                    ETLogManager.Warning(_logSource, $"异步操作[{operation}]被取消");
                }
                else if (ex is TimeoutException)
                {
                    ETLogManager.Warning(_logSource, $"异步操作[{operation}]超时");
                }
                else if (ex is AggregateException aggEx)
                {
                    foreach (var innerEx in aggEx.InnerExceptions)
                    {
                        ETLogManager.Error(_logSource, $"聚合异常内部异常: {innerEx.Message}");
                    }
                }
            }
            catch (Exception logEx)
            {
                System.Diagnostics.Debug.WriteLine($"异步异常日志记录失败: {logEx.Message}");
            }
        }

        #endregion 异常日志方法

        #region 结构化日志方法

        /// <summary>
        /// 记录操作开始日志
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="parameters">操作参数</param>
        public void LogOperationStart(string operation, params object[] parameters)
        {
            if (_disposed) return;

            try
            {
                string paramStr = parameters?.Length > 0 ? $", 参数: {string.Join(", ", parameters)}" : "";
                LogInfo($"开始执行操作: {operation}{paramStr}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"操作开始日志记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录操作完成日志
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="result">操作结果</param>
        /// <param name="elapsedMs">耗时（毫秒）</param>
        public void LogOperationComplete(string operation, object result = null, long elapsedMs = -1)
        {
            if (_disposed) return;

            try
            {
                string resultStr = result != null ? $", 结果: {result}" : "";
                string timeStr = elapsedMs >= 0 ? $", 耗时: {elapsedMs}ms" : "";
                LogInfo($"操作完成: {operation}{resultStr}{timeStr}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"操作完成日志记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录性能日志
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="elapsedMs">耗时（毫秒）</param>
        /// <param name="additionalInfo">附加信息</param>
        public void LogPerformance(string operation, long elapsedMs, string additionalInfo = null)
        {
            if (_disposed) return;

            try
            {
                string additional = !string.IsNullOrEmpty(additionalInfo) ? $", {additionalInfo}" : "";
                string level = elapsedMs > 5000 ? "性能警告" : "性能信息";

                string message = $"[{level}] {operation} 耗时: {elapsedMs}ms{additional}";

                if (elapsedMs > 5000)
                {
                    LogWarning(message);
                }
                else
                {
                    LogInfo(message);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"性能日志记录失败: {ex.Message}");
            }
        }

        #endregion 结构化日志方法

        #region 辅助方法

        /// <summary>
        /// 判断是否为严重异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <returns>是否为严重异常</returns>
        private bool IsCriticalException(Exception ex)
        {
            return ex is OutOfMemoryException ||
                   ex is StackOverflowException ||
                   ex is AccessViolationException ||
                   ex is AppDomainUnloadedException ||
                   ex is BadImageFormatException ||
                   ex is CannotUnloadAppDomainException ||
                   ex is InvalidProgramException ||
                   ex is System.Threading.ThreadAbortException;
        }

        #endregion 辅助方法

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 释放托管资源
                    try
                    {
                        LogInfo("LoggingHelperV2资源释放完成");
                    }
                    catch
                    {
                        // 忽略释放时的异常
                    }
                }

                _disposed = true;
            }
        }

        #endregion IDisposable实现
    }
}