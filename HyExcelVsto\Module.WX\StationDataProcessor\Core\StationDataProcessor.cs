using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Office.Interop.Excel;
using ET;
using HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces;

namespace HyExcelVsto.Module.Common.StationDataProcessor.Core
{
    /// <summary>
    /// 基站数据处理器核心类 负责协调各个组件完成完整的数据处理流程
    /// </summary>
    public class StationDataProcessor : IDataProcessor
    {
        #region 私有字段

        /// <summary>
        /// Excel数据访问层
        /// </summary>
        private readonly IExcelDataAccess _dataAccess;

        /// <summary>
        /// 数据分析器
        /// </summary>
        private readonly IDataAnalyzer _analyzer;

        /// <summary>
        /// 日志写入委托
        /// </summary>
        private readonly Action<string> _logWriter;

        /// <summary>
        /// 是否已释放资源
        /// </summary>
        private bool _disposed = false;

        /// <summary>
        /// 取消标记
        /// </summary>
        private volatile bool _cancellationRequested = false;

        /// <summary>
        /// 列名到列索引的缓存映射 - 性能优化避免重复查找列位置
        /// </summary>
        private readonly Dictionary<string, int> _columnIndexCache = new Dictionary<string, int>();

        /// <summary>
        /// 列索引缓存是否已初始化
        /// </summary>
        private bool _columnIndexCacheInitialized = false;

        /// <summary>
        /// 简化的首次出现判断缓存 - 已出现的站名频段索引集合
        /// </summary>
        private readonly HashSet<string> _seenStationFrequencyIndexes = new HashSet<string>();

        /// <summary>
        /// 简化的首次出现判断缓存 - 已出现的站名集合
        /// </summary>
        private readonly HashSet<string> _seenStationNames = new HashSet<string>();

        #region 性能优化缓存字段

        /// <summary>
        /// 站名频段索引到行号列表的映射缓存
        /// Key: 站名频段索引, Value: 包含该索引的所有行号列表（0基索引）
        /// </summary>
        private readonly Dictionary<string, List<int>> _stationFrequencyIndexRowsCache = new Dictionary<string, List<int>>();

        /// <summary>
        /// 预计算的合并值缓存
        /// Key: "stationFrequencyIndex|columnName", Value: 合并后的字符串值
        /// </summary>
        private readonly Dictionary<string, string> _mergedValuesCache = new Dictionary<string, string>();

        /// <summary>
        /// 站名频段索引缓存是否已初始化
        /// </summary>
        private bool _stationFrequencyIndexCacheInitialized = false;

        /// <summary>
        /// 合并值缓存是否已初始化
        /// </summary>
        private bool _mergedValuesCacheInitialized = false;

        /// <summary>
        /// 需要预计算合并值的目标列名集合
        /// </summary>
        private static string[] _targetMergeColumns => new string[] {
            StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_总下倾角"],
            StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_天线方位角"],
            StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_天线挂高"]
        };

        /// <summary>
        /// 预计算的统计值缓存
        /// Key: "stationFrequencyIndex|CELL_COUNT" 或 "stationFrequencyIndex|EQUIPMENT_COUNT"
        /// Value: 计算结果（int转string）
        /// </summary>
        private readonly Dictionary<string, string> _statisticsCache = new Dictionary<string, string>();

        /// <summary>
        /// 统计值缓存是否已初始化
        /// </summary>
        private bool _statisticsCacheInitialized = false;

        #endregion 性能优化缓存字段

        #endregion 私有字段

        #region 缓存结构定义

        /// <summary>
        /// 计算结果缓存结构 - 优化1：避免重复计算
        /// </summary>
        private class CalculationResults
        {
            /// <summary>
            /// 小区数
            /// </summary>
            public int CellCount { get; set; }

            /// <summary>
            /// 设备数
            /// </summary>
            public int EquipmentCount { get; set; }

            /// <summary>
            /// 是否有功分
            /// </summary>
            public string HasPowerSplitter { get; set; } = "";

            /// <summary>
            /// 异常提醒
            /// </summary>
            public string AnomalyWarning { get; set; } = "";

            /// <summary>
            /// 下倾角合并值
            /// </summary>
            public string DowntiltMerged { get; set; } = "";

            /// <summary>
            /// 方向角合并值
            /// </summary>
            public string AzimuthMerged { get; set; } = "";

            /// <summary>
            /// 挂高合并值
            /// </summary>
            public string AntennaHeightMerged { get; set; } = "";

            /// <summary>
            /// 经纬度索引
            /// </summary>
            public string CoordinateIndex { get; set; } = "";

            /// <summary>
            /// 是否已计算基础数据（小区数、设备数）
            /// </summary>
            public bool BasicDataCalculated { get; set; } = false;
        }

        #endregion 缓存结构定义

        #region 优化方法

        /// <summary>
        /// 优化2：设置下倾角、方向角、挂高列为文本格式，避免"2/2/2"被识别为日期
        /// </summary>
        private void SetColumnFormatsToText()
        {
            try
            {
                var columnsToFormat = new[] {
                    StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_总下倾角"],
                    StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_天线方位角"],
                    StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_天线挂高"]
                };

                foreach (string columnName in columnsToFormat)
                {
                    try
                    {
                        int columnIndex = GetColumnIndexFromCache(columnName);
                        if (columnIndex >= 0) // 修复：0基索引也是有效的
                        {
                            int dataRowCount = _dataAccess.GetDataRowCount();
                            if (dataRowCount > 0)
                            {
                                // 修复：将0基索引转换为1基索引用于Excel Cells集合
                                int excelColumnIndex = columnIndex + 1;
                                // 设置整列为文本格式（从第2行开始，跳过标题行）
                                var columnRange = _dataAccess.GetWorksheet().Range[
                                    _dataAccess.GetWorksheet().Cells[2, excelColumnIndex],
                                    _dataAccess.GetWorksheet().Cells[dataRowCount + 1, excelColumnIndex]
                                ];
                                columnRange.NumberFormat = "@"; // 文本格式
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Warning(this, $"设置列格式失败：{columnName}", ex);
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "设置列格式失败", ex);
            }
        }

        /// <summary>
        /// 将可能被Excel转换为日期序列号的值转换回原始文本格式
        /// </summary>
        /// <param name="rawValue">原始值</param>
        /// <param name="columnName">列名</param>
        /// <returns>原始文本格式</returns>
        private string ConvertToOriginalTextFormat(object rawValue, string columnName)
        {
            try
            {
                if (rawValue == null)
                {
                    return "";
                }

                string valueStr = rawValue.ToString();

                // 检查是否为日期相关的列
                if (columnName == StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_总下倾角"] ||
                    columnName == StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_天线方位角"] ||
                    columnName == StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_天线挂高"])
                {
                    // 尝试检测是否为Excel日期序列号（通常是5位数的整数，如37381）
                    if (double.TryParse(valueStr, out double numericValue))
                    {
                        // 如果是5位数左右的整数，很可能是日期序列号
                        if (numericValue > 30000 && numericValue < 50000 && Math.Abs(numericValue - Math.Round(numericValue)) < 0.001)
                        {
                            try
                            {
                                // 尝试将日期序列号转换回日期，然后提取可能的原始格式
                                DateTime date = DateTime.FromOADate(numericValue);

                                // 常见的误识别模式处理
                                if (date.Year >= 2000 && date.Year <= 2030)
                                {
                                    // 模式1: 2/2/2 -> 2022/2/2 (年份2000-2012，月日1-12)
                                    if (date.Year >= 2000 && date.Year <= 2012 && date.Month <= 12 && date.Day <= 12)
                                    {
                                        int year = date.Year - 2000;
                                        return $"{year}/{date.Month}/{date.Day}";
                                    }
                                    // 模式2: 其他日期格式，返回月/日
                                    else if (date.Month <= 12 && date.Day <= 31)
                                    {
                                        return $"{date.Month}/{date.Day}";
                                    }
                                }

                                // 如果无法推断，尝试简单的月/日格式
                                if (date.Month <= 12 && date.Day <= 31)
                                {
                                    return $"{date.Month}/{date.Day}";
                                }
                            }
                            catch
                            {
                                // 日期转换失败，返回原始字符串
                            }
                        }
                    }
                }

                return valueStr;
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, $"转换原始文本格式失败：{columnName}, {rawValue}", ex);
                return rawValue?.ToString() ?? "";
            }
        }

        /// <summary>
        /// 直接从Excel单元格读取显示文本（避免数据类型转换问题）
        /// </summary>
        /// <param name="row">行号</param>
        /// <param name="columnName">列名</param>
        /// <returns>单元格显示文本</returns>
        private string GetCellDisplayText(int row, string columnName)
        {
            try
            {
                int columnIndex = GetColumnIndexFromCache(columnName);
                if (columnIndex >= 0) // 修复：0基索引也是有效的
                {
                    // 修复：将0基索引转换为1基索引用于Excel Cells集合
                    var cell = _dataAccess.GetWorksheet().Cells[row, columnIndex + 1];
                    // 使用Text属性获取单元格的显示文本，而不是Value
                    return cell.Text?.ToString() ?? "";
                }
                return "";
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, $"获取单元格显示文本失败：行{row}，列{columnName}", ex);
                return "";
            }
        }

        #endregion 优化方法

        #region 事件

        /// <summary>
        /// 进度报告事件
        /// </summary>
        public event EventHandler<ProgressEventArgs> ProgressChanged;

        #endregion 事件

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dataAccess">Excel数据访问层</param>
        /// <param name="analyzer">数据分析器</param>
        /// <param name="logWriter">日志写入委托</param>
        public StationDataProcessor(
            IExcelDataAccess dataAccess,
            IDataAnalyzer analyzer,
            Action<string> logWriter = null)
        {
            _dataAccess = dataAccess ?? throw new ArgumentNullException(nameof(dataAccess));
            _analyzer = analyzer ?? throw new ArgumentNullException(nameof(analyzer));
            _logWriter = logWriter ?? (msg => ETLogManager.Info(this, msg));

            ETLogManager.Info(this, "StationDataProcessor初始化完成");
        }

        #endregion 构造函数

        #region IDataProcessor接口实现

        /// <summary>
        /// 处理基站数据的主要方法
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <returns>处理结果</returns>
        public async Task<ProcessResult> ProcessStationDataAsync(Worksheet worksheet)
        {
            var result = new ProcessResult();

            try
            {
                // 重置取消标记
                ResetCancellation();

                ETLogManager.Info(this, "开始处理基站数据");
                _logWriter?.Invoke("=== 开始处理基站数据 ===");

                // 步骤0: 加载数据缓存（性能优化）
                ReportProgress(2, "数据缓存", "加载数据到内存缓存以提高处理速度");
                if (IsCancellationRequested())
                {
                    result.Success = false;
                    result.Message = "处理已取消";
                    return result;
                }

                if (!_dataAccess.LoadDataCache())
                {
                    ETLogManager.Warning(this, "数据缓存加载失败，将使用直接读取模式");
                }

                // 初始化列索引缓存（性能优化 - 智能模式：只缓存已存在的列）
                InitializeColumnIndexCache(onlyExistingColumns: true);

                // 步骤1: 数据验证
                ReportProgress(5, "数据验证", "验证Excel数据格式和完整性");
                if (IsCancellationRequested())
                {
                    result.Success = false;
                    result.Message = "处理已取消";
                    return result;
                }

                if (!await ValidateDataAsync(worksheet))
                {
                    result.Success = false;
                    result.Message = "数据验证失败";
                    return result;
                }

                // 步骤2: 列隐藏处理
                ReportProgress(15, "隐藏列", "只显示指定列，隐藏其他数据列");
                if (IsCancellationRequested())
                {
                    result.Success = false;
                    result.Message = "处理已取消";
                    return result;
                }

                int hiddenCount = await _dataAccess.ShowOnlySpecifiedColumnsAsync(StationDataProcessorConfig.COLUMNS_TO_SHOW);

                // 步骤3: 新增列处理
                ReportProgress(40, "添加新增列", "计算和添加分析列");
                await ProcessNewColumnsAsync();

                // 步骤5: 设置筛选
                ReportProgress(95, "设置筛选", "为数据表设置自动筛选");
                await _dataAccess.SetAutoFilterAsync();

                // 完成处理
                ReportProgress(100, "处理完成", "所有数据处理步骤已完成");
                result.Success = true;
                result.Message = "数据处理成功完成";
                result.ProcessedRows = _dataAccess.GetDataRowCount();

                ETLogManager.Info(this, $"基站数据处理完成，处理行数：{result.ProcessedRows}");
                _logWriter?.Invoke($"=== 数据处理完成，共处理 {result.ProcessedRows} 行数据 ===");
            }
            catch (OperationCanceledException)
            {
                result.Success = false;
                result.Message = "操作已被用户取消";
                ETLogManager.Info(this, "基站数据处理被用户取消");
                _logWriter?.Invoke("处理已取消");
                ReportProgress(0, "已取消", "操作已被用户取消");
            }
            catch (ETException etEx)
            {
                result.Success = false;
                result.Message = $"Excel操作错误：{etEx.Message}";
                result.Exception = etEx;

                ETLogManager.Error(this, $"Excel操作失败：{etEx.Operation}", etEx);
                _logWriter?.Invoke($"Excel操作失败：{etEx.Message}");
                ReportProgress(0, "Excel错误", etEx.Message);

                // 如果设置了显示消息框，则显示详细错误信息
                if (etEx.ShowMessageBox)
                {
                    ShowErrorDialog("Excel操作错误", etEx.Message, etEx);
                }
            }
            catch (UnauthorizedAccessException uaEx)
            {
                result.Success = false;
                result.Message = "文件访问权限不足，请检查文件是否被其他程序占用";
                result.Exception = uaEx;

                ETLogManager.Error(this, "文件访问权限错误", uaEx);
                _logWriter?.Invoke($"文件访问错误：{uaEx.Message}");
                ReportProgress(0, "权限错误", "文件访问权限不足");

                ShowErrorDialog("文件访问错误", "文件访问权限不足，请检查文件是否被其他程序占用", uaEx);
            }
            catch (OutOfMemoryException oomEx)
            {
                result.Success = false;
                result.Message = "内存不足，请尝试处理较小的数据集或释放系统内存";
                result.Exception = oomEx;

                ETLogManager.Error(this, "内存不足错误", oomEx);
                _logWriter?.Invoke($"内存不足：{oomEx.Message}");
                ReportProgress(0, "内存不足", "系统内存不足");

                ShowErrorDialog("内存不足", "系统内存不足，请尝试处理较小的数据集或释放系统内存", oomEx);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"处理过程中发生未知错误：{ex.Message}";
                result.Exception = ex;

                ETLogManager.Error(this, "基站数据处理失败", ex);
                _logWriter?.Invoke($"处理失败：{ex.Message}");
                ReportProgress(0, "处理失败", ex.Message);

                ShowErrorDialog("处理失败", $"处理过程中发生未知错误：{ex.Message}", ex);
            }

            return result;
        }

        #endregion IDataProcessor接口实现

        #region 错误处理方法

        /// <summary>
        /// 显示错误对话框
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="message">消息</param>
        /// <param name="exception">异常对象</param>
        private void ShowErrorDialog(string title, string message, Exception exception)
        {
            try
            {
                string detailMessage = $"{message}\n\n详细信息：\n{exception?.ToString()}";

                // 记录到日志
                ETLogManager.Error(this, $"显示错误对话框：{title}", exception);

                // 显示用户友好的错误信息
                MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                // 防止错误处理本身出错
                ETLogManager.Error(this, "显示错误对话框失败", ex);
            }
        }

        /// <summary>
        /// 带重试机制的执行方法
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <param name="operation">要执行的操作</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <returns>执行结果</returns>
        private async Task ExecuteWithRetryAsync(string operationName, Func<Task> operation, int maxRetries = 3)
        {
            int retryCount = 0;
            Exception lastException = null;

            while (retryCount <= maxRetries)
            {
                try
                {
                    await operation();
                    return; // 成功执行，退出重试循环
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    retryCount++;

                    if (retryCount <= maxRetries)
                    {
                        ETLogManager.Warning(this, $"{operationName} 执行失败，第 {retryCount} 次重试", ex);
                        _logWriter?.Invoke($"{operationName} 失败，正在重试 ({retryCount}/{maxRetries})...");

                        // 等待一段时间后重试
                        await Task.Delay(1000 * retryCount);
                    }
                    else
                    {
                        ETLogManager.Error(this, $"{operationName} 重试 {maxRetries} 次后仍然失败", ex);
                        throw new ETException($"{operationName} 执行失败：{ex.Message}", operationName, ex, true);
                    }
                }
            }

            // 如果到这里说明重试次数用完了
            throw new ETException($"{operationName} 重试 {maxRetries} 次后仍然失败", operationName, lastException, true);
        }

        /// <summary>
        /// 验证操作前置条件
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <param name="conditions">验证条件</param>
        /// <returns>验证是否通过</returns>
        private bool ValidatePreConditions(string operationName, params Func<bool>[] conditions)
        {
            try
            {
                foreach (var condition in conditions)
                {
                    if (!condition())
                    {
                        ETLogManager.Warning(this, $"{operationName} 前置条件验证失败");
                        return false;
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"{operationName} 前置条件验证异常", ex);
                return false;
            }
        }

        #endregion 错误处理方法

        #region 私有方法

        /// <summary>
        /// 验证数据格式和完整性
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <returns>验证是否通过</returns>
        private async Task<bool> ValidateDataAsync(Worksheet worksheet)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 检查工作表是否有数据
                    int rowCount = _dataAccess.GetDataRowCount();
                    if (rowCount == 0)
                    {
                        ETLogManager.Error(this, "工作表中没有数据");
                        return false;
                    }

                    // 检查必要的列是否存在（只检查标题行，不读取数据）
                    string[] requiredColumns = {
                        StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_扇区中文名"],
                        StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_频段频点"]
                    };
                    foreach (string column in requiredColumns)
                    {
                        if (!_dataAccess.IsColumnExists(column))
                        {
                            ETLogManager.Error(this, $"缺少必要的列：{column}");
                            return false;
                        }
                    }

                    ETLogManager.Info(this, $"数据验证通过，数据行数：{rowCount}");
                    return true;
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(this, "数据验证过程中发生错误", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 处理新增列的添加和计算 - 简化版本：基础字段 → 排序 → 剩余字段
        /// </summary>
        private async Task ProcessNewColumnsAsync()
        {
            try
            {
                // 阶段1：添加基础字段（站名、频段、站名频段索引）
                ReportProgress(45, "基础字段", "添加站名、频段、站名频段索引");
                var basicCalculations = new Dictionary<string, Func<object[], object>>
                {
                    [StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名"]] = rowData => CalculateStationName(rowData),
                    [StationDataProcessorConfig.HEADER_COLUMNS["HEADER_频段"]] = rowData => CalculateFrequencyBand(rowData),
                    [StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"]] = rowData => CalculateStationFrequencyIndex(rowData)
                };

                int basicCount = await _dataAccess.AddCalculatedColumnsAsync(basicCalculations);

                // 阶段2：按站名频段索引和扇区中文名进行排序
                ReportProgress(50, "数据排序", "按站名频段索引和扇区中文名排序");
                await PerformIntermediateSortAsync();

                // 阶段3：添加逻辑站和物理站字段（优化版本：直接从缓存读取已有数据） 🔧
                ReportProgress(55, "逻辑物理站", "添加逻辑站和物理站字段");
                // 优化说明：阶段1已生成站名、频段、站名频段索引，其中站名=物理站名，站名频段索引=逻辑站名 因此无需重复计算，直接从扩大的数据缓存中读取这些信息即可

                // 🔧 增强修复：先清除列索引缓存，确保包含新增列
                ClearColumnIndexCache();

                bool cacheReloadSuccess = _dataAccess.LoadDataCache(forceReload: true);
                if (cacheReloadSuccess)
                {
                    ETLogManager.Info(this, "阶段3前数据缓存重新加载成功，包含基础字段");

                    // 🔧 重新初始化列索引缓存以包含新增列（智能模式：只缓存已存在的列）
                    InitializeColumnIndexCache(onlyExistingColumns: true);
                }
                else
                {
                    ETLogManager.Warning(this, "阶段3前数据缓存重新加载失败");
                }

                // 清空首次出现判断缓存，确保基于排序后的数据重新判断
                _seenStationFrequencyIndexes.Clear();
                _seenStationNames.Clear();

                // 🔧 优化：使用基于缓存数据的计算方法，避免重复计算
                var logicalPhysicalCalculations = new Dictionary<string, Func<object[], object>>
                {
                    [StationDataProcessorConfig.HEADER_COLUMNS["HEADER_逻辑站"]] = rowData => CalculateLogicalStationFromCache(rowData),
                    [StationDataProcessorConfig.HEADER_COLUMNS["HEADER_物理站"]] = rowData => CalculatePhysicalStationFromCache(rowData)
                };

                int logicalPhysicalCount = await _dataAccess.AddCalculatedColumnsAsync(logicalPhysicalCalculations);

                // 阶段3.5：扩大数据缓存，包含逻辑站和物理站字段

                // 🔧 增强修复：先清除列索引缓存，确保包含新增列
                ClearColumnIndexCache();

                bool cacheExpandSuccess = _dataAccess.LoadDataCache(forceReload: true);
                if (cacheExpandSuccess)
                {
                    ETLogManager.Info(this, "阶段3.5数据缓存扩大成功，包含逻辑站和物理站字段");

                    // 🔧 重新初始化列索引缓存以包含新增列（智能模式：只缓存已存在的列）
                    InitializeColumnIndexCache(onlyExistingColumns: true);
                }
                else
                {
                    ETLogManager.Warning(this, "阶段3.5数据缓存扩大失败");
                }

                // 阶段3.8：添加经度和纬度字段（在物理站之后，小区数之前）
                ReportProgress(57, "经纬度字段", "添加经度和纬度字段");
                var coordinateCalculations = new Dictionary<string, Func<object[], object>>
                {
                    [StationDataProcessorConfig.HEADER_COLUMNS["HEADER_经度"]] = rowData => CalculateLongitude(rowData),
                    [StationDataProcessorConfig.HEADER_COLUMNS["HEADER_纬度"]] = rowData => CalculateLatitude(rowData)
                };

                int coordinateCount = await _dataAccess.AddCalculatedColumnsAsync(coordinateCalculations);

                // 阶段3.9：扩大数据缓存，包含经度和纬度字段
                ClearColumnIndexCache();

                bool coordinateCacheSuccess = _dataAccess.LoadDataCache(forceReload: true);
                if (coordinateCacheSuccess)
                {
                    ETLogManager.Info(this, "阶段3.9数据缓存扩大成功，包含经度和纬度字段");
                    InitializeColumnIndexCache(onlyExistingColumns: true);
                }
                else
                {
                    ETLogManager.Warning(this, "阶段3.9数据缓存扩大失败");
                }

                // 阶段4：添加统计和合并字段（优化版本 - 基于扩大缓存的高性能计算）
                ReportProgress(60, "统计字段", "添加小区数、设备数等统计字段");

                // 🔧 修正：使用带行号上下文的计算方法
                int statisticsCount = await AddStatisticsColumnsWithRowContext();

                int totalCount = basicCount + logicalPhysicalCount + coordinateCount + statisticsCount;
                ETLogManager.Info(this, $"新增列处理完成，添加了 {totalCount} 列");

                // 阶段5：最终数据缓存更新 - 将所有原数据和新增字段一起重新加载到缓存
                ReportProgress(85, "缓存更新", "更新数据缓存，包含所有新增字段");

                // 🔧 增强修复：先清除列索引缓存，确保包含所有新增列
                ClearColumnIndexCache();

                bool finalCacheUpdateSuccess = _dataAccess.LoadDataCache(true); // 强制重新加载缓存
                if (finalCacheUpdateSuccess)
                {
                    ETLogManager.Info(this, "最终数据缓存更新成功，包含所有原数据和新增字段");

                    // 🔧 重新初始化列索引缓存以包含所有新增列（完整模式：缓存所有列，包括新增的）
                    InitializeColumnIndexCache(onlyExistingColumns: false);
                }
                else
                {
                    ETLogManager.Warning(this, "最终数据缓存更新失败，但不影响功能使用");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "处理新增列失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 添加统计字段（带行号上下文的修正版本）- 优化版本：缓存重复计算结果
        /// </summary>
        /// <returns>添加的列数</returns>
        private Task<int> AddStatisticsColumnsWithRowContext()
        {
            try
            {
                _logWriter?.Invoke("🔧 使用行号上下文方式添加统计字段（优化版本：缓存重复计算）...");

                // 优化2：在开始计算前，设置下倾角、方向角、挂高列为文本格式
                SetColumnFormatsToText();

                var columnDefinitions = new[]
                {
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_小区数"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_设备数"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_是否有功分"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_异常提醒"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_下倾角"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_方向角"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_挂高"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_经纬度索引"]
                };

                int addedCount = 0;
                int lastColumn = _dataAccess.GetLastColumnIndex();
                int currentColumn = lastColumn + 1;

                // 优化1：创建计算结果缓存，避免重复计算
                var calculationCache = new Dictionary<int, CalculationResults>();

                // 🔧 进度更新：为每个统计列计算进度
                int totalColumns = columnDefinitions.Length;
                int baseProgress = 60; // 统计字段的起始进度
                int progressRange = 25; // 统计字段的进度范围 (60% - 85%)

                for (int i = 0; i < columnDefinitions.Length; i++)
                {
                    string columnTitle = columnDefinitions[i];

                    // 计算当前列的进度
                    int currentProgress = baseProgress + (i * progressRange / totalColumns);
                    ReportProgress(currentProgress, "统计字段", $"正在添加 {columnTitle} ({i + 1}/{totalColumns})");

                    try
                    {
                        // 添加列标题 - 修复COM线程安全问题
                        _dataAccess.GetWorksheet().Cells[1, currentColumn] = columnTitle;
                        var titleCell = _dataAccess.GetWorksheet().Cells[1, currentColumn];
                        ETExcelExtensions.Formate设置背景色(titleCell, EnumColorNum.浅浅灰色);

                        // 🔧 优化2：对于下倾角、方向角、挂高列，在写入数据前先设置整列为文本格式
                        int dataRowCount = _dataAccess.GetDataRowCount();
                        if (columnTitle == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_下倾角"] ||
                            columnTitle == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_方向角"] ||
                            columnTitle == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_挂高"])
                        {
                            try
                            {
                                // 设置整列为文本格式（包括标题行和数据行）
                                var entireColumn = _dataAccess.GetWorksheet().Range[
                                    _dataAccess.GetWorksheet().Cells[1, currentColumn],
                                    _dataAccess.GetWorksheet().Cells[dataRowCount + 1, currentColumn]
                                ];
                                entireColumn.NumberFormat = "@"; // 文本格式
                                _logWriter?.Invoke($"✅ 已预先将列 {columnTitle} 设置为文本格式");
                            }
                            catch (Exception formatEx)
                            {
                                ETLogManager.Warning(this, $"预设列格式失败：{columnTitle}", formatEx);
                                _logWriter?.Invoke($"⚠️ 预设列 {columnTitle} 格式失败：{formatEx.Message}");
                            }
                        }

                        // 计算并填充数据 - 使用行号上下文和缓存优化
                        object[,] columnData = new object[dataRowCount, 1];

                        // 批量计算所有行的数据（带行号上下文和缓存优化）
                        for (int row = 2; row <= dataRowCount + 1; row++)
                        {
                            try
                            {
                                object[] rowData = _dataAccess.GetRowDataPublic(row);
                                object calculatedValue = CalculateStatisticsValueWithRowContextAndCache(columnTitle, rowData, row, calculationCache);
                                columnData[row - 2, 0] = calculatedValue;
                            }
                            catch (Exception calcEx)
                            {
                                ETLogManager.Warning(this, $"计算第{row}行数据失败：{columnTitle}", calcEx);
                                columnData[row - 2, 0] = "计算错误";
                            }
                        }

                        // 对异常提醒列进行特殊处理：非逻辑站行复制上一行的值
                        if (columnTitle == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_异常提醒"])
                        {
                            ProcessAnomalyWarningColumn(columnData, dataRowCount);
                        }

                        // 一次性写入整列数据 - 修复COM线程安全问题
                        if (dataRowCount > 0)
                        {
                            // 在UI线程上执行Excel COM操作，避免跨线程访问异常
                            var dataRange = _dataAccess.GetWorksheet().Range[
                                _dataAccess.GetWorksheet().Cells[2, currentColumn],
                                _dataAccess.GetWorksheet().Cells[dataRowCount + 1, currentColumn]
                            ];
                            dataRange.Value2 = columnData;

                            // 注意：列格式已在写入数据前预先设置，此处不需要重复设置
                        }

                        addedCount++;
                        currentColumn++;
                        _logWriter?.Invoke($"✅ 已添加列：{columnTitle}");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(this, $"添加统计列失败：{columnTitle}", ex);
                    }
                } // 关闭for循环

                _logWriter?.Invoke($"🔧 统计字段添加完成，共添加 {addedCount} 列，缓存命中 {calculationCache.Count} 次");
                return Task.FromResult(addedCount);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "添加统计字段失败", ex);
                return Task.FromResult(0);
            }
        }

        /// <summary>
        /// 执行中间排序：强制重新计算，然后按站名频段索引和扇区中文名排序
        /// </summary>
        private async Task PerformIntermediateSortAsync()
        {
            try
            {
                // 强制重新计算数据缓存
                bool cacheReloaded = _dataAccess.LoadDataCache(forceReload: true);
                if (!cacheReloaded)
                {
                    ETLogManager.Warning(this, "数据缓存重新计算失败，但继续排序");
                }

                // 直接按2列进行排序
                var sortColumns = new List<(string columnHeader, bool ascending)>
                {
                    (StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"], true),  // 主要排序条件
                    (StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_扇区中文名"], true)     // 次要排序条件
                };

                bool sortSuccess = await _dataAccess.SortByMultipleColumnsAsync(sortColumns);

                if (!sortSuccess)
                {
                    ETLogManager.Warning(this, "双列排序失败");
                }

                ETLogManager.Info(this, "中间排序执行完成：站名频段索引 → 扇区中文名");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "执行中间排序失败", ex);
                // 不抛出异常，允许继续处理
            }
        }

        /// <summary>
        /// 计算站名 - 使用FindColumnValue动态查找扇区中文名列
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <returns>站名</returns>
        private object CalculateStationName(object[] rowData)
        {
            try
            {
                // 使用FindColumnValue动态查找扇区中文名列（性能优化：使用缓存）
                string sectorName = FindColumnValue(rowData, StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_扇区中文名"])?.ToString() ?? "";
                return _analyzer.ParseStationName(sectorName);
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "计算站名失败", ex);
                return "计算错误";
            }
        }

        /// <summary>
        /// 计算频段
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <returns>频段</returns>
        private object CalculateFrequencyBand(object[] rowData)
        {
            try
            {
                // 需要根据实际列位置调整索引 这里假设上行中心频点在某个位置
                object frequencyValue = FindColumnValue(rowData, StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_频段频点"]);
                return _analyzer.ConvertToFrequencyBand(frequencyValue);
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "计算频段失败", ex);
                return "计算错误";
            }
        }

        /// <summary>
        /// 计算站名频段索引
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <returns>站名频段索引</returns>
        private object CalculateStationFrequencyIndex(object[] rowData)
        {
            try
            {
                string stationName = CalculateStationName(rowData)?.ToString() ?? "";
                string frequencyBand = CalculateFrequencyBand(rowData)?.ToString() ?? "";
                return _analyzer.GenerateStationFrequencyIndex(stationName, frequencyBand);
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "计算站名频段索引失败", ex);
                return "计算错误";
            }
        }

        /// <summary>
        /// 计算逻辑站标记（优化版本：从缓存中读取站名频段索引）
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <returns>逻辑站标记</returns>
        private object CalculateLogicalStationFromCache(object[] rowData)
        {
            try
            {
                // 🔧 优化：直接从缓存中读取站名频段索引，避免重复计算
                string stationFrequencyIndex = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"])?.ToString() ?? "";
                if (string.IsNullOrWhiteSpace(stationFrequencyIndex))
                {
                    return "";
                }

                // 检查是否为第一次出现的站名频段索引
                if (IsFirstOccurrence(stationFrequencyIndex, "StationFrequencyIndex"))
                {
                    return StationDataProcessorConfig.HEADER_COLUMNS["HEADER_逻辑站"];
                }

                return "";
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "计算逻辑站标记失败", ex);
                return "";
            }
        }

        /// <summary>
        /// 计算物理站标记（优化版本：从缓存中读取站名）
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <returns>物理站标记</returns>
        private object CalculatePhysicalStationFromCache(object[] rowData)
        {
            try
            {
                // 🔧 优化：直接从缓存中读取站名，避免重复计算
                string stationName = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名"])?.ToString() ?? "";
                if (string.IsNullOrWhiteSpace(stationName))
                {
                    return "";
                }

                // 检查是否为第一次出现的站名
                if (IsFirstOccurrence(stationName, "StationName"))
                {
                    return StationDataProcessorConfig.HEADER_COLUMNS["HEADER_物理站"];
                }

                return "";
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "计算物理站标记失败", ex);
                return "";
            }
        }

        /// <summary>
        /// 计算经度 - 直接从来源数据列获取扇区经度
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <returns>经度值</returns>
        private object CalculateLongitude(object[] rowData)
        {
            try
            {
                // 直接从来源数据列获取扇区经度
                object longitude = FindColumnValue(rowData, StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_扇区经度"]);
                return longitude?.ToString() ?? "";
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "计算经度失败", ex);
                return "";
            }
        }

        /// <summary>
        /// 计算纬度 - 直接从来源数据列获取扇区纬度
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <returns>纬度值</returns>
        private object CalculateLatitude(object[] rowData)
        {
            try
            {
                // 直接从来源数据列获取扇区纬度
                object latitude = FindColumnValue(rowData, StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_扇区纬度"]);
                return latitude?.ToString() ?? "";
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "计算纬度失败", ex);
                return "";
            }
        }

        /// <summary>
        /// 根据列名查找行数据中的值 - 使用缓存优化性能
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <param name="columnName">列名</param>
        /// <returns>列值</returns>
        private object FindColumnValue(object[] rowData, string columnName)
        {
            try
            {
                // 🔧 安全检查：确保rowData不为空
                if (rowData == null || rowData.Length == 0)
                {
                    ETLogManager.Warning(this, $"行数据为空，无法查找列：{columnName}");
                    return null;
                }

                // 从缓存中获取列索引（性能优化）
                int columnIndex = GetColumnIndexFromCache(columnName);

                // 🔧 增强的边界检查和错误处理
                if (columnIndex == -1)
                {
                    ETLogManager.Warning(this, $"未找到列：{columnName}");
                    return null;
                }

                if (columnIndex < 0)
                {
                    ETLogManager.Warning(this, $"列索引无效：{columnName}，索引：{columnIndex}");
                    return null;
                }

                if (columnIndex >= rowData.Length)
                {
                    // 🔧 关键修复：当列索引超出范围时，尝试重新加载数据缓存
                    ETLogManager.Warning(this, $"列索引超出范围：{columnName}，索引：{columnIndex}，数组长度：{rowData.Length}");

                    // 如果是在处理新增列（如站名频段索引），可能需要重新加载缓存
                    if (columnName == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"] ||
                        columnName == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名"] ||
                        columnName == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_频段"] ||
                        columnName == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_逻辑站"] ||
                        columnName == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_物理站"])
                    {
                        ETLogManager.Info(this, $"检测到新增列访问超出范围，列：{columnName}，这可能是数据缓存未及时更新导致的");
                        _logWriter?.Invoke($"⚠️ 检测到新增列 {columnName} 访问超出范围，可能需要重新加载数据缓存");

                        // 尝试强制重新加载数据缓存
                        try
                        {
                            bool cacheReloaded = _dataAccess.LoadDataCache(forceReload: true);
                            if (cacheReloaded)
                            {
                                _logWriter?.Invoke($"✅ 数据缓存重新加载成功，重新尝试访问列：{columnName}");

                                // 重新获取行数据并尝试访问 注意：这里不能递归调用，需要直接从数据访问层获取最新数据
                                ETLogManager.Info(this, $"数据缓存重新加载后，建议重新获取行数据以访问列：{columnName}");
                            }
                        }
                        catch (Exception reloadEx)
                        {
                            ETLogManager.Error(this, $"重新加载数据缓存失败：{columnName}", reloadEx);
                        }
                    }

                    return null;
                }

                return rowData[columnIndex];
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"查找列值失败：{columnName}，行数据长度：{rowData?.Length ?? 0}", ex);
                return null;
            }
        }

        /// <summary>
        /// 检查是否为第一次出现的值 - 简化版本
        /// </summary>
        /// <param name="value">要检查的值</param>
        /// <param name="columnType">列类型（StationFrequencyIndex或StationName）</param>
        /// <returns>是否为第一次出现</returns>
        private bool IsFirstOccurrence(string value, string columnType)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(value))
                {
                    return false;
                }

                switch (columnType)
                {
                    case "StationFrequencyIndex":
                        if (!_seenStationFrequencyIndexes.Contains(value))
                        {
                            _seenStationFrequencyIndexes.Add(value);
                            // 不要输出太多日志，否则会影响性能
                            //ETLogManager.Debug(this, $"首次出现的站名频段索引：{value}（当前已记录{_seenStationFrequencyIndexes.Count}个）");
                            return true;
                        }
                        else
                        {
                            // 🔧 调试：记录重复出现的情况
                            //ETLogManager.Debug(this, $"重复出现的站名频段索引：{value}");
                        }
                        break;

                    case "StationName":
                        if (!_seenStationNames.Contains(value))
                        {
                            _seenStationNames.Add(value);

                            return true;
                        }
                        else
                        {
                        }
                        break;
                }

                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"检查首次出现失败：{value}, {columnType}", ex);
                return false;
            }
        }

        /// <summary>
        /// 统计指定站名频段索引的行数 - 高性能优化版本（使用预计算缓存）
        /// </summary>
        /// <param name="stationFrequencyIndex">站名频段索引</param>
        /// <param name="currentRow">当前行号（逻辑站行）</param>
        /// <returns>行数</returns>
        private int CountRowsByStationFrequencyIndexFromCurrentRow(string stationFrequencyIndex, int currentRow)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(stationFrequencyIndex))
                {
                    return 0;
                }

                // 🚀 性能优化：确保预计算缓存已初始化
                if (!_statisticsCacheInitialized)
                {
                    PreCalculateAllMergedAndStatisticsValues();
                }

                // � 性能优化：直接从预计算缓存中获取结果 - O(1)时间复杂度
                string cacheKey = $"{stationFrequencyIndex}|CELL_COUNT";
                if (_statisticsCache.TryGetValue(cacheKey, out string cachedResult))
                {
                    if (int.TryParse(cachedResult, out int result))
                    {
                        //ETLogManager.Debug(this, $"从预计算缓存获取小区数：{cacheKey} = {result}");
                        return result;
                    }
                }

                // 🔄 缓存未命中时的回退处理（理论上不应该发生）
                ETLogManager.Warning(this, $"小区数预计算缓存未命中：{cacheKey}，回退到传统方法");
                return CountRowsByStationFrequencyIndexFallback(stationFrequencyIndex);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"从优化缓存获取小区数失败：{stationFrequencyIndex}", ex);
                // 回退到传统方法
                return CountRowsByStationFrequencyIndexFallback(stationFrequencyIndex);
            }
        }

        /// <summary>
        /// 统计指定站名频段索引的唯一设备数 - 高性能优化版本（使用预计算缓存）
        /// </summary>
        /// <param name="stationFrequencyIndex">站名频段索引</param>
        /// <param name="currentRow">当前行号（逻辑站行）</param>
        /// <returns>唯一设备数</returns>
        private int CountUniqueEquipmentByStationFrequencyIndexFromCurrentRow(string stationFrequencyIndex, int currentRow)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(stationFrequencyIndex))
                {
                    return 0;
                }

                // 🚀 性能优化：确保预计算缓存已初始化
                if (!_statisticsCacheInitialized)
                {
                    PreCalculateAllMergedAndStatisticsValues();
                }

                // � 性能优化：直接从预计算缓存中获取结果 - O(1)时间复杂度
                string cacheKey = $"{stationFrequencyIndex}|EQUIPMENT_COUNT";
                if (_statisticsCache.TryGetValue(cacheKey, out string cachedResult))
                {
                    if (int.TryParse(cachedResult, out int result))
                    {
                        //ETLogManager.Debug(this, $"从预计算缓存获取设备数：{cacheKey} = {result}");
                        return result;
                    }
                }

                // 🔄 缓存未命中时的回退处理（理论上不应该发生）
                ETLogManager.Warning(this, $"设备数预计算缓存未命中：{cacheKey}，回退到传统方法");
                return CountUniqueEquipmentByStationFrequencyIndexFallback(stationFrequencyIndex);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"从优化缓存获取设备数失败：{stationFrequencyIndex}", ex);
                // 回退到传统方法
                return CountUniqueEquipmentByStationFrequencyIndexFallback(stationFrequencyIndex);
            }
        }

        /// <summary>
        /// 检查指定站名频段索引是否有重复的方位角 - 优化版本（基于当前行位置往下搜索20行）
        /// </summary>
        /// <param name="stationFrequencyIndex">站名频段索引</param>
        /// <param name="currentRow">当前行号（逻辑站行）</param>
        /// <returns>是否有重复方位角</returns>
        private bool HasDuplicateAzimuthFromCurrentRow(string stationFrequencyIndex, int currentRow)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(stationFrequencyIndex))
                {
                    return false;
                }

                var azimuthValues = new List<string>();
                int dataRowCount = _dataAccess.GetDataRowCount();
                int maxSearchRow = Math.Min(currentRow + 20, dataRowCount + 1); // 最多搜索20行或到数据末尾

                //ETLogManager.Debug(this, $"优化检查方位角重复：{stationFrequencyIndex}，搜索范围：{currentRow} - {maxSearchRow}");

                // 从当前行开始往下搜索
                for (int i = currentRow; i <= maxSearchRow; i++)
                {
                    var rowData = GetRowDataFromDataAccess(i);
                    if (rowData != null && rowData.Length > 0)
                    {
                        // 🔧 优化：直接从缓存中读取站名频段索引，避免重复计算
                        string currentIndex = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"])?.ToString() ?? "";
                        if (currentIndex == stationFrequencyIndex)
                        {
                            string azimuth = FindColumnValue(rowData, StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_天线方位角"])?.ToString() ?? "";
                            if (!string.IsNullOrWhiteSpace(azimuth))
                            {
                                azimuthValues.Add(azimuth);
                            }
                        }
                        else if (azimuthValues.Count > 0)
                        {
                            // 如果已经找到匹配项，但当前行不匹配，说明已经超出了该站名频段索引的范围
                            //ETLogManager.Debug(this, $"提前结束方位角检查：{stationFrequencyIndex}，在第{i}行遇到不同索引：{currentIndex}");
                            break;
                        }
                    }
                }

                // 检查是否有重复值
                bool hasDuplicate = azimuthValues.Count != azimuthValues.Distinct().Count();
                //ETLogManager.Debug(this, $"方位角重复检查完成：{stationFrequencyIndex} = {hasDuplicate}");
                return hasDuplicate;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"检查方位角重复失败：{stationFrequencyIndex}", ex);
                return false;
            }
        }

        /// <summary>
        /// 从数据访问层获取行数据 - 使用高性能缓存
        /// </summary>
        /// <param name="row">行号</param>
        /// <returns>行数据数组</returns>
        private object[] GetRowDataFromDataAccess(int row)
        {
            try
            {
                // 直接使用数据访问层的缓存功能
                return _dataAccess.GetRowDataPublic(row);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"获取行数据失败：第{row}行", ex);
                return [];
            }
        }

        /// <summary>
        /// 获取当前处理行的行号 - 通过比较行数据内容来确定
        /// </summary>
        /// <param name="rowData">当前行数据</param>
        /// <returns>行号，如果找不到返回-1</returns>
        private int GetCurrentProcessingRow(object[] rowData)
        {
            try
            {
                if (rowData == null || rowData.Length == 0)
                {
                    return -1;
                }

                // 获取当前行的扇区中文名作为唯一标识
                string currentSectorName = FindColumnValue(rowData, StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_扇区中文名"])?.ToString() ?? "";
                if (string.IsNullOrWhiteSpace(currentSectorName))
                {
                    return -1;
                }

                // 在数据中查找匹配的行
                int dataRowCount = _dataAccess.GetDataRowCount();
                for (int i = 2; i <= dataRowCount + 1; i++) // 从第2行开始（跳过标题行）
                {
                    var compareRowData = GetRowDataFromDataAccess(i);
                    if (compareRowData != null && compareRowData.Length > 0)
                    {
                        string compareSectorName = FindColumnValue(compareRowData, StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_扇区中文名"])?.ToString() ?? "";
                        if (compareSectorName == currentSectorName)
                        {
                            // 进一步验证：比较站名频段索引确保是同一行 🔧 优化：直接从缓存中读取站名频段索引，避免重复计算
                            string currentIndex = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"])?.ToString() ?? "";
                            string compareIndex = FindColumnValue(compareRowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"])?.ToString() ?? "";
                            if (currentIndex == compareIndex)
                            {
                                return i;
                            }
                        }
                    }
                }

                return -1;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "获取当前处理行号失败", ex);
                return -1;
            }
        }

        /// <summary>
        /// 回退方法：传统的全表搜索统计行数
        /// </summary>
        /// <param name="stationFrequencyIndex">站名频段索引</param>
        /// <returns>行数</returns>
        private int CountRowsByStationFrequencyIndexFallback(string stationFrequencyIndex)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(stationFrequencyIndex))
                {
                    return 0;
                }

                int count = 0;
                int dataRowCount = _dataAccess.GetDataRowCount();

                for (int i = 2; i <= dataRowCount + 1; i++) // 从第2行开始（跳过标题行）
                {
                    var rowData = GetRowDataFromDataAccess(i);
                    if (rowData != null && rowData.Length > 0)
                    {
                        // 🔧 优化：直接从缓存中读取站名频段索引，避免重复计算
                        string currentIndex = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"])?.ToString() ?? "";
                        if (currentIndex == stationFrequencyIndex)
                        {
                            count++;
                        }
                    }
                }

                return count;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"回退统计行数失败：{stationFrequencyIndex}", ex);
                return 0;
            }
        }

        /// <summary>
        /// 回退方法：传统的全表搜索统计设备数
        /// </summary>
        /// <param name="stationFrequencyIndex">站名频段索引</param>
        /// <returns>设备数</returns>
        private int CountUniqueEquipmentByStationFrequencyIndexFallback(string stationFrequencyIndex)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(stationFrequencyIndex))
                {
                    return 0;
                }

                var uniqueSectorNames = new HashSet<string>();
                int dataRowCount = _dataAccess.GetDataRowCount();

                for (int i = 2; i <= dataRowCount + 1; i++) // 从第2行开始（跳过标题行）
                {
                    var rowData = GetRowDataFromDataAccess(i);
                    if (rowData != null && rowData.Length > 0)
                    {
                        // 🔧 优化：直接从缓存中读取站名频段索引，避免重复计算
                        string currentIndex = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"])?.ToString() ?? "";
                        if (currentIndex == stationFrequencyIndex)
                        {
                            string sectorName = FindColumnValue(rowData, StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_扇区中文名"])?.ToString() ?? "";
                            if (!string.IsNullOrWhiteSpace(sectorName))
                            {
                                uniqueSectorNames.Add(sectorName);
                            }
                        }
                    }
                }

                return uniqueSectorNames.Count;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"回退统计设备数失败：{stationFrequencyIndex}", ex);
                return 0;
            }
        }

        /// <summary>
        /// 高性能版本：从缓存中获取指定站名频段索引的合并值（优化版本）
        /// </summary>
        /// <param name="stationFrequencyIndex">站名频段索引</param>
        /// <param name="columnName">列名</param>
        /// <param name="currentRow">当前行号（逻辑站行）</param>
        /// <returns>合并值字符串</returns>
        private string GetMergedValuesByStationFrequencyIndexFromCurrentRow(string stationFrequencyIndex, string columnName, int currentRow)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(stationFrequencyIndex) || string.IsNullOrWhiteSpace(columnName))
                {
                    return "";
                }

                // � 性能优化：确保预计算缓存已初始化
                if (!_mergedValuesCacheInitialized)
                {
                    PreCalculateAllMergedAndStatisticsValues();
                }

                // 🚀 性能优化：直接从预计算缓存中获取结果 - O(1)时间复杂度
                string cacheKey = $"{stationFrequencyIndex}|{columnName}";
                if (_mergedValuesCache.TryGetValue(cacheKey, out string cachedResult))
                {
                    //ETLogManager.Debug(this, $"从预计算缓存获取合并值：{cacheKey} = {cachedResult}");
                    return cachedResult;
                }

                // 🔄 缓存未命中时的回退处理（理论上不应该发生）
                ETLogManager.Warning(this, $"预计算缓存未命中：{cacheKey}，回退到传统方法");
                return GetMergedValuesByStationFrequencyIndexFallback(stationFrequencyIndex, columnName);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"从优化缓存获取合并值失败：{stationFrequencyIndex}, {columnName}", ex);
                // 回退到传统方法
                return GetMergedValuesByStationFrequencyIndexFallback(stationFrequencyIndex, columnName);
            }
        }

        /// <summary>
        /// 使用行号上下文和缓存计算统计值（优化版本1：避免重复计算）
        /// </summary>
        /// <param name="columnTitle">列标题</param>
        /// <param name="rowData">行数据</param>
        /// <param name="currentRow">当前行号</param>
        /// <param name="calculationCache">计算结果缓存</param>
        /// <returns>计算结果</returns>
        private object CalculateStatisticsValueWithRowContextAndCache(string columnTitle, object[] rowData, int currentRow, Dictionary<int, CalculationResults> calculationCache)
        {
            try
            {
                // 检查是否为逻辑站行
                string logicalStation = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_逻辑站"])?.ToString() ?? "";
                bool isLogicalStationRow = logicalStation == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_逻辑站"];

                // 对于异常提醒列，特殊处理：非逻辑站行填写与上一行相同的值 注意：这个逻辑将在批量计算中特殊处理，这里先按正常逻辑处理
                if (columnTitle == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_异常提醒"])
                {
                    if (!isLogicalStationRow)
                    {
                        // 非逻辑站行：返回特殊标记，在批量计算中处理
                        return "COPY_FROM_PREVIOUS";
                    }
                }
                else
                {
                    // 其他列：只在逻辑站行进行计算
                    if (!isLogicalStationRow)
                    {
                        return "";
                    }
                }

                // 获取或创建缓存项
                if (!calculationCache.ContainsKey(currentRow))
                {
                    calculationCache[currentRow] = new CalculationResults();
                }

                var cache = calculationCache[currentRow];

                switch (columnTitle)
                {
                    case var title when title == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_小区数"]:
                        if (!cache.BasicDataCalculated)
                        {
                            // 第一次计算基础数据时，同时计算小区数和设备数
                            CalculateBasicDataWithCache(rowData, currentRow, cache);
                        }
                        return cache.CellCount;

                    case var title when title == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_设备数"]:
                        if (!cache.BasicDataCalculated)
                        {
                            // 第一次计算基础数据时，同时计算小区数和设备数
                            CalculateBasicDataWithCache(rowData, currentRow, cache);
                        }
                        return cache.EquipmentCount;

                    case var title when title == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_是否有功分"]:
                        if (string.IsNullOrEmpty(cache.HasPowerSplitter))
                        {
                            // 确保基础数据已计算
                            if (!cache.BasicDataCalculated)
                            {
                                CalculateBasicDataWithCache(rowData, currentRow, cache);
                            }
                            cache.HasPowerSplitter = CalculateHasPowerSplitterFromCache(cache);
                        }
                        return cache.HasPowerSplitter;

                    case var title when title == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_异常提醒"]:
                        if (string.IsNullOrEmpty(cache.AnomalyWarning))
                        {
                            // 确保基础数据已计算
                            if (!cache.BasicDataCalculated)
                            {
                                CalculateBasicDataWithCache(rowData, currentRow, cache);
                            }
                            cache.AnomalyWarning = CalculateAnomalyWarningFromCache(rowData, currentRow, cache);
                        }
                        return cache.AnomalyWarning;

                    case var title when title == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_下倾角"]:
                        if (string.IsNullOrEmpty(cache.DowntiltMerged))
                        {
                            cache.DowntiltMerged = CalculateDowntiltMergedWithRowContext(rowData, currentRow)?.ToString() ?? "";
                        }
                        return cache.DowntiltMerged;

                    case var title when title == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_方向角"]:
                        if (string.IsNullOrEmpty(cache.AzimuthMerged))
                        {
                            cache.AzimuthMerged = CalculateAzimuthMergedWithRowContext(rowData, currentRow)?.ToString() ?? "";
                        }
                        return cache.AzimuthMerged;

                    case var title when title == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_挂高"]:
                        if (string.IsNullOrEmpty(cache.AntennaHeightMerged))
                        {
                            cache.AntennaHeightMerged = CalculateAntennaHeightMergedWithRowContext(rowData, currentRow)?.ToString() ?? "";
                        }
                        return cache.AntennaHeightMerged;

                    case var title when title == StationDataProcessorConfig.HEADER_COLUMNS["HEADER_经纬度索引"]:
                        if (string.IsNullOrEmpty(cache.CoordinateIndex))
                        {
                            cache.CoordinateIndex = CalculateCoordinateIndexWithRowContext(rowData, currentRow)?.ToString() ?? "";
                        }
                        return cache.CoordinateIndex;

                    default:
                        return "";
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"计算统计值失败（缓存版本）：{columnTitle}，行号：{currentRow}", ex);
                return "计算错误";
            }
        }

        /// <summary>
        /// 计算基础数据（小区数和设备数）并缓存结果
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <param name="currentRow">当前行号</param>
        /// <param name="cache">缓存对象</param>
        private void CalculateBasicDataWithCache(object[] rowData, int currentRow, CalculationResults cache)
        {
            try
            {
                string stationFrequencyIndex = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"])?.ToString() ?? "";
                if (!string.IsNullOrWhiteSpace(stationFrequencyIndex))
                {
                    // 一次性计算小区数和设备数
                    cache.CellCount = CountRowsByStationFrequencyIndexFromCurrentRow(stationFrequencyIndex, currentRow);
                    cache.EquipmentCount = CountUniqueEquipmentByStationFrequencyIndexFromCurrentRow(stationFrequencyIndex, currentRow);
                    cache.BasicDataCalculated = true;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"计算基础数据失败，行号：{currentRow}", ex);
                cache.CellCount = 0;
                cache.EquipmentCount = 0;
                cache.BasicDataCalculated = true; // 标记为已计算，避免重复尝试
            }
        }

        /// <summary>
        /// 从缓存计算是否有功分
        /// </summary>
        /// <param name="cache">缓存对象</param>
        /// <returns>是否有功分</returns>
        private string CalculateHasPowerSplitterFromCache(CalculationResults cache)
        {
            try
            {
                if (cache.CellCount != cache.EquipmentCount)
                {
                    return "有功分";
                }
                return "";
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "从缓存计算功分判断失败", ex);
                return "";
            }
        }

        /// <summary>
        /// 从缓存计算异常提醒
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <param name="currentRow">当前行号</param>
        /// <param name="cache">缓存对象</param>
        /// <returns>异常提醒</returns>
        private string CalculateAnomalyWarningFromCache(object[] rowData, int currentRow, CalculationResults cache)
        {
            try
            {
                var anomalies = new List<string>();
                string stationFrequencyIndex = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"])?.ToString() ?? "";

                // 规则1：小区数 >= 4
                if (cache.CellCount >= 4)
                {
                    anomalies.Add("小区数异常");
                }

                // 规则2：天线方位角重复检测
                if (HasDuplicateAzimuthFromCurrentRow(stationFrequencyIndex, currentRow))
                {
                    anomalies.Add("方位角重复");
                }

                return anomalies.Any() ? string.Join(", ", anomalies) : "正常";
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, $"从缓存计算异常提醒失败，行号：{currentRow}", ex);
                return "计算错误";
            }
        }

        /// <summary>
        /// 处理异常提醒列的特殊逻辑：非逻辑站行复制上一行的值
        /// </summary>
        /// <param name="columnData">列数据数组</param>
        /// <param name="dataRowCount">数据行数</param>
        private void ProcessAnomalyWarningColumn(object[,] columnData, int dataRowCount)
        {
            try
            {
                _logWriter?.Invoke("🔧 处理异常提醒列：非逻辑站行复制上一行的值...");

                for (int i = 0; i < dataRowCount; i++)
                {
                    string currentValue = columnData[i, 0]?.ToString() ?? "";

                    // 如果当前行标记为需要复制上一行的值
                    if (currentValue == "COPY_FROM_PREVIOUS")
                    {
                        // 查找上一行的值
                        string previousValue = "";
                        for (int j = i - 1; j >= 0; j--)
                        {
                            string checkValue = columnData[j, 0]?.ToString() ?? "";
                            if (!string.IsNullOrWhiteSpace(checkValue) && checkValue != "COPY_FROM_PREVIOUS")
                            {
                                previousValue = checkValue;
                                break;
                            }
                        }

                        // 设置为上一行的值
                        columnData[i, 0] = previousValue;

                        // 调试日志
                        //ETLogManager.Debug(this, $"异常提醒列第{i + 2}行复制上一行值：{previousValue}");
                    }
                }

                _logWriter?.Invoke("✅ 异常提醒列处理完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "处理异常提醒列失败", ex);
            }
        }

        /// <summary>
        /// 获取上一行的异常提醒值（用于非逻辑站行） 简化版本：由于数据已排序，直接使用上一行计算结果
        /// </summary>
        /// <param name="currentRow">当前行号</param>
        /// <param name="columnData">当前列的计算结果数组</param>
        /// <returns>上一行的异常提醒值</returns>
        private string GetPreviousRowAnomalyWarning(int currentRow, object[,] columnData)
        {
            try
            {
                // 如果是第一行数据（行号2），返回空字符串
                if (currentRow <= 2)
                {
                    return "";
                }

                // 从已计算的结果中获取上一行的值
                int previousRowIndex = currentRow - 2 - 1; // 转换为数组索引，然后减1获取上一行
                if (previousRowIndex >= 0 && previousRowIndex < columnData.GetLength(0))
                {
                    string previousValue = columnData[previousRowIndex, 0]?.ToString() ?? "";
                    if (!string.IsNullOrWhiteSpace(previousValue))
                    {
                        return previousValue;
                    }
                }

                return "";
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, $"获取上一行异常提醒值失败，行号：{currentRow}", ex);
                return "";
            }
        }

        /// <summary>
        /// 计算下倾角合并（带行号上下文）
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <param name="currentRow">当前行号</param>
        /// <returns>下倾角合并值</returns>
        private object CalculateDowntiltMergedWithRowContext(object[] rowData, int currentRow)
        {
            try
            {
                // 只在逻辑站行填写合并值 - 直接从缓存中读取逻辑站信息
                string logicalStation = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_逻辑站"])?.ToString() ?? "";
                if (logicalStation != StationDataProcessorConfig.HEADER_COLUMNS["HEADER_逻辑站"])
                {
                    return "";
                }

                // 🔧 优化：直接从缓存中读取站名频段索引，避免重复计算
                string stationFrequencyIndex = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"])?.ToString() ?? "";
                return GetMergedValuesByStationFrequencyIndexFromCurrentRow(stationFrequencyIndex, StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_总下倾角"], currentRow);
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, $"计算下倾角合并失败，行号：{currentRow}", ex);
                return "";
            }
        }

        /// <summary>
        /// 计算方向角合并（带行号上下文）
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <param name="currentRow">当前行号</param>
        /// <returns>方向角合并值</returns>
        private object CalculateAzimuthMergedWithRowContext(object[] rowData, int currentRow)
        {
            try
            {
                // 只在逻辑站行填写合并值 - 直接从缓存中读取逻辑站信息
                string logicalStation = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_逻辑站"])?.ToString() ?? "";
                if (logicalStation != StationDataProcessorConfig.HEADER_COLUMNS["HEADER_逻辑站"])
                {
                    return "";
                }

                // 🔧 优化：直接从缓存中读取站名频段索引，避免重复计算
                string stationFrequencyIndex = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"])?.ToString() ?? "";
                return GetMergedValuesByStationFrequencyIndexFromCurrentRow(stationFrequencyIndex, StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_天线方位角"], currentRow);
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, $"计算方向角合并失败，行号：{currentRow}", ex);
                return "";
            }
        }

        /// <summary>
        /// 计算挂高合并（带行号上下文）
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <param name="currentRow">当前行号</param>
        /// <returns>挂高合并值</returns>
        private object CalculateAntennaHeightMergedWithRowContext(object[] rowData, int currentRow)
        {
            try
            {
                // 只在逻辑站行填写合并值 - 直接从缓存中读取逻辑站信息
                string logicalStation = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_逻辑站"])?.ToString() ?? "";
                if (logicalStation != StationDataProcessorConfig.HEADER_COLUMNS["HEADER_逻辑站"])
                {
                    return "";
                }

                // 🔧 优化：直接从缓存中读取站名频段索引，避免重复计算
                string stationFrequencyIndex = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"])?.ToString() ?? "";
                return GetMergedValuesByStationFrequencyIndexFromCurrentRow(stationFrequencyIndex, StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_天线挂高"], currentRow);
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, $"计算挂高合并失败，行号：{currentRow}", ex);
                return "";
            }
        }

        /// <summary>
        /// 计算经纬度索引（带行号上下文）
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <param name="currentRow">当前行号</param>
        /// <returns>经纬度索引值</returns>
        private object CalculateCoordinateIndexWithRowContext(object[] rowData, int currentRow)
        {
            try
            {
                // 只在逻辑站行填写经纬度索引 - 直接从缓存中读取逻辑站信息
                string logicalStation = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_逻辑站"])?.ToString() ?? "";
                if (logicalStation != StationDataProcessorConfig.HEADER_COLUMNS["HEADER_逻辑站"])
                {
                    return "";
                }

                // 获取当前行的经纬度数据
                string longitude = FindColumnValue(rowData, StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_扇区经度"])?.ToString() ?? "";
                string latitude = FindColumnValue(rowData, StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_扇区纬度"])?.ToString() ?? "";

                return CalculateCoordinateIndex(longitude, latitude);
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, $"计算经纬度索引失败，行号：{currentRow}", ex);
                return "";
            }
        }

        /// <summary>
        /// 计算经纬度索引：将经纬度的前3位小数组合 示例：116.239647, 22.936458 → 116.239-22.936
        /// </summary>
        /// <param name="longitude">经度字符串</param>
        /// <param name="latitude">纬度字符串</param>
        /// <returns>经纬度索引字符串</returns>
        private string CalculateCoordinateIndex(string longitude, string latitude)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(longitude) || string.IsNullOrWhiteSpace(latitude))
                {
                    return "";
                }

                // 尝试解析经纬度数值
                if (!double.TryParse(longitude, out double lonValue) || !double.TryParse(latitude, out double latValue))
                {
                    return "";
                }

                // 格式化为前3位小数，然后组合
                string lonFormatted = lonValue.ToString("F3");
                string latFormatted = latValue.ToString("F3");

                return $"{lonFormatted}-{latFormatted}";
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, $"计算经纬度索引失败：经度={longitude}, 纬度={latitude}", ex);
                return "";
            }
        }

        /// <summary>
        /// 回退方法：传统的全表搜索获取合并值
        /// </summary>
        /// <param name="stationFrequencyIndex">站名频段索引</param>
        /// <param name="columnName">列名</param>
        /// <returns>合并值字符串</returns>
        private string GetMergedValuesByStationFrequencyIndexFallback(string stationFrequencyIndex, string columnName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(stationFrequencyIndex) || string.IsNullOrWhiteSpace(columnName))
                {
                    return "";
                }

                var values = new List<string>();
                int dataRowCount = _dataAccess.GetDataRowCount();

                for (int i = 2; i <= dataRowCount + 1; i++) // 从第2行开始（跳过标题行）
                {
                    var rowData = GetRowDataFromDataAccess(i);
                    if (rowData != null && rowData.Length > 0)
                    {
                        // 🔧 优化：直接从缓存中读取站名频段索引，避免重复计算
                        string currentIndex = FindColumnValue(rowData, StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"])?.ToString() ?? "";
                        if (currentIndex == stationFrequencyIndex)
                        {
                            string value = FindColumnValue(rowData, columnName)?.ToString() ?? "";
                            if (!string.IsNullOrWhiteSpace(value))
                            {
                                values.Add(value);
                            }
                        }
                    }
                }

                return string.Join("/", values);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"回退获取合并值失败：{stationFrequencyIndex}, {columnName}", ex);
                return "";
            }
        }

        /// <summary>
        /// 报告进度
        /// </summary>
        /// <param name="percentage">进度百分比</param>
        /// <param name="currentStep">当前步骤</param>
        /// <param name="message">消息</param>
        private void ReportProgress(int percentage, string currentStep, string message)
        {
            try
            {
                var args = new ProgressEventArgs
                {
                    ProgressPercentage = percentage,
                    CurrentStep = currentStep,
                    Message = message
                };

                ProgressChanged?.Invoke(this, args);
                ETLogManager.Debug(this, $"进度报告：{percentage}% - {currentStep} - {message}");
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "报告进度失败", ex);
            }
        }

        #endregion 私有方法

        #region 列索引缓存方法

        /// <summary>
        /// 初始化列索引缓存 - 一次性查找所有需要的列位置（智能模式：只查找已存在的列）
        /// </summary>
        /// <param name="onlyExistingColumns">是否只缓存已存在的列，避免大量"未找到"日志</param>
        private void InitializeColumnIndexCache(bool onlyExistingColumns = true)
        {
            try
            {
                if (_columnIndexCacheInitialized)
                {
                    return; // 已经初始化过了
                }

                ETLogManager.Info(this, "开始初始化列索引缓存");
                _logWriter?.Invoke("正在初始化列索引缓存...");

                // 获取标题行范围
                var headerRange = _dataAccess.GetHeaderRange();
                if (headerRange == null)
                {
                    ETLogManager.Warning(this, "无法获取标题行范围，列索引缓存初始化失败");
                    return;
                }

                // 🔧 优化：定义需要缓存的列名，分为原始列和新增列
                var originalColumns = new[]
                {
                    StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_扇区中文名"],
                    StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_频段频点"],
                    StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_天线方位角"],
                    StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_总下倾角"],
                    StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_天线挂高"],
                    "区/市/县/旗", "所属营业部名称", "基站中文名", "设备厂家",
                    StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_扇区经度"],
                    StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_扇区纬度"],
                    "扇区地址", "开通时间", "上行带宽"
                };

                var newColumns = new[]
                {
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_频段"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_逻辑站"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_物理站"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_经度"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_纬度"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_小区数"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_设备数"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_是否有功分"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_异常提醒"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_下倾角"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_方向角"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_挂高"],
                    StationDataProcessorConfig.HEADER_COLUMNS["HEADER_经纬度索引"]
                };

                int cachedCount = 0;

                // 🔧 智能缓存策略：如果是智能模式，先获取所有现有列标题，避免无效查找
                HashSet<string> existingHeaders = null;
                if (onlyExistingColumns)
                {
                    existingHeaders = GetExistingColumnHeaders(headerRange);
                }

                // 缓存原始列（总是尝试缓存）
                foreach (string columnName in originalColumns)
                {
                    if (onlyExistingColumns && existingHeaders != null && !existingHeaders.Contains(columnName))
                    {
                        continue; // 跳过不存在的列，避免日志噪音
                    }

                    cachedCount += CacheColumnIndex(columnName, headerRange, suppressNotFoundLog: onlyExistingColumns);
                }

                // 缓存新增列（根据模式决定是否查找）
                foreach (string columnName in newColumns)
                {
                    if (onlyExistingColumns && existingHeaders != null && !existingHeaders.Contains(columnName))
                    {
                        continue; // 跳过不存在的列，避免日志噪音
                    }

                    cachedCount += CacheColumnIndex(columnName, headerRange, suppressNotFoundLog: onlyExistingColumns);
                }

                _columnIndexCacheInitialized = true;
                ETLogManager.Info(this, $"列索引缓存初始化完成，成功缓存 {cachedCount} 个列");
                _logWriter?.Invoke($"列索引缓存初始化完成，缓存了 {cachedCount} 个列");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "初始化列索引缓存失败", ex);
                _columnIndexCacheInitialized = false;
            }
        }

        /// <summary>
        /// 获取现有的列标题集合
        /// </summary>
        /// <param name="headerRange">标题行范围</param>
        /// <returns>现有列标题的集合</returns>
        private HashSet<string> GetExistingColumnHeaders(Range headerRange)
        {
            var headers = new HashSet<string>();
            try
            {
                int maxColumns = Math.Min(headerRange.Columns.Count, headerRange.Worksheet.UsedRange?.Columns.Count ?? headerRange.Columns.Count);

                for (int i = 1; i <= maxColumns; i++)
                {
                    try
                    {
                        string cellValue = headerRange.Cells[1, i].Value2?.ToString()?.Trim();
                        if (!string.IsNullOrEmpty(cellValue))
                        {
                            headers.Add(cellValue);
                        }
                    }
                    catch
                    {
                        // 忽略单个单元格访问错误
                        continue;
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "获取现有列标题失败", ex);
            }
            return headers;
        }

        /// <summary>
        /// 缓存单个列的索引
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <param name="headerRange">标题行范围</param>
        /// <param name="suppressNotFoundLog">是否抑制"未找到"日志</param>
        /// <returns>成功缓存返回1，否则返回0</returns>
        private int CacheColumnIndex(string columnName, Range headerRange, bool suppressNotFoundLog = false)
        {
            try
            {
                // 🔧 优化：使用静默查找模式，避免日志噪音
                var column = ETExcelExtensions.FindColumnByHeaderTitle(columnName, headerRange,
                    suppressNotFoundLog ? null : _logWriter);

                if (column != null)
                {
                    int columnIndex = column.Column - 1; // 转换为0基索引
                    _columnIndexCache[columnName] = columnIndex;
                    //ETLogManager.Debug(this, $"缓存列索引：{columnName} -> {columnIndex}");
                    return 1;
                }
                else if (!suppressNotFoundLog)
                {
                    ETLogManager.Info(this, $"未找到标题为 '{columnName}' 的列");
                }

                return 0;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"缓存列索引失败：{columnName}", ex);
                return 0;
            }
        }

        /// <summary>
        /// 清除列索引缓存
        /// </summary>
        private void ClearColumnIndexCache()
        {
            try
            {
                _columnIndexCache.Clear();
                _columnIndexCacheInitialized = false;
                ETLogManager.Info(this, "列索引缓存已清除");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "清除列索引缓存失败", ex);
            }
        }

        /// <summary>
        /// 从缓存中获取列索引
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <returns>列索引，如果未找到返回-1</returns>
        private int GetColumnIndexFromCache(string columnName)
        {
            try
            {
                // 确保缓存已初始化（使用智能模式，避免日志噪音）
                if (!_columnIndexCacheInitialized)
                {
                    InitializeColumnIndexCache(onlyExistingColumns: true);
                }

                // 从缓存中获取列索引
                if (_columnIndexCache.TryGetValue(columnName, out int columnIndex))
                {
                    return columnIndex;
                }

                // 缓存中没有，尝试实时查找并缓存
                var headerRange = _dataAccess.GetHeaderRange();
                if (headerRange != null)
                {
                    // 🔧 优化：实时查找时使用静默模式，避免重复的"未找到"日志
                    var column = ETExcelExtensions.FindColumnByHeaderTitle(columnName, headerRange, null);
                    if (column != null)
                    {
                        columnIndex = column.Column - 1; // 转换为0基索引
                        _columnIndexCache[columnName] = columnIndex; // 缓存结果
                        //ETLogManager.Debug(this, $"实时缓存列索引：{columnName} -> {columnIndex}");
                        return columnIndex;
                    }
                }

                // 🔧 优化：只在调试模式下记录未找到的列，减少日志噪音
                ETLogManager.Debug(this, $"未找到列：{columnName}");
                return -1;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"获取列索引失败：{columnName}", ex);
                return -1;
            }
        }

        #endregion 列索引缓存方法

        #region 性能优化缓存方法

        /// <summary>
        /// 初始化站名频段索引到行号的映射缓存
        /// </summary>
        private void InitializeStationFrequencyIndexCache()
        {
            try
            {
                if (_stationFrequencyIndexCacheInitialized)
                {
                    return;
                }

                ETLogManager.Info(this, "开始初始化站名频段索引映射缓存");
                _stationFrequencyIndexRowsCache.Clear();

                // 直接访问二维数组缓存，避免转换开销
                var concreteDataAccess = _dataAccess as ExcelDataAccess;
                if (concreteDataAccess == null)
                {
                    ETLogManager.Warning(this, "无法转换为具体的ExcelDataAccess类型，无法初始化站名频段索引映射");
                    return;
                }

                if (!concreteDataAccess._dataCacheLoaded)
                {
                    concreteDataAccess.LoadDataCache();
                }

                var dataCache = concreteDataAccess._dataCache;
                if (dataCache == null)
                {
                    ETLogManager.Warning(this, "数据缓存为空，无法初始化站名频段索引映射");
                    return;
                }

                // 获取站名频段索引列的位置
                int stationFrequencyIndexColumn = GetColumnIndexFromCache(StationDataProcessorConfig.HEADER_COLUMNS["HEADER_站名频段索引"]);
                if (stationFrequencyIndexColumn < 0)
                {
                    ETLogManager.Warning(this, "未找到站名频段索引列，无法初始化映射缓存");
                    return;
                }

                // 遍历所有数据行，建立索引映射（跳过标题行）
                for (int row = 2; row <= concreteDataAccess._dataCacheRowCount; row++) // 1-based索引
                {
                    int cacheCol = stationFrequencyIndexColumn + 1; // 转换为1-based索引
                    if (cacheCol <= concreteDataAccess._dataCacheColumnCount)
                    {
                        string stationFrequencyIndex = dataCache[row, cacheCol]?.ToString() ?? "";
                        if (!string.IsNullOrWhiteSpace(stationFrequencyIndex))
                        {
                            if (!_stationFrequencyIndexRowsCache.ContainsKey(stationFrequencyIndex))
                            {
                                _stationFrequencyIndexRowsCache[stationFrequencyIndex] = new List<int>();
                            }
                            _stationFrequencyIndexRowsCache[stationFrequencyIndex].Add(row - 1); // 转换为0-based索引存储
                        }
                    }
                }

                _stationFrequencyIndexCacheInitialized = true;
                ETLogManager.Info(this, $"站名频段索引映射缓存初始化完成，共缓存 {_stationFrequencyIndexRowsCache.Count} 个索引");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "初始化站名频段索引映射缓存失败", ex);
                _stationFrequencyIndexCacheInitialized = false;
            }
        }

        /// <summary>
        /// 批量预计算所有站名频段索引的合并值和统计值（一次计算，多次使用）
        /// </summary>
        private void PreCalculateAllMergedAndStatisticsValues()
        {
            try
            {
                if (_mergedValuesCacheInitialized && _statisticsCacheInitialized)
                {
                    return;
                }

                ETLogManager.Info(this, "开始批量预计算合并值和统计值缓存");
                _mergedValuesCache.Clear();
                _statisticsCache.Clear();

                // 确保站名频段索引映射已初始化
                if (!_stationFrequencyIndexCacheInitialized)
                {
                    InitializeStationFrequencyIndexCache();
                }

                if (!_stationFrequencyIndexCacheInitialized)
                {
                    ETLogManager.Warning(this, "站名频段索引映射未初始化，无法预计算合并值和统计值");
                    return;
                }

                // 获取目标列的索引
                var targetColumnIndexes = new Dictionary<string, int>();
                foreach (string columnName in _targetMergeColumns)
                {
                    int columnIndex = GetColumnIndexFromCache(columnName);
                    if (columnIndex >= 0)
                    {
                        targetColumnIndexes[columnName] = columnIndex;
                    }
                }

                // 获取扇区中文名列的索引（用于设备数统计）
                int sectorNameColumnIndex = GetColumnIndexFromCache(StationDataProcessorConfig.SOURCE_COLUMNS["SOURCE_扇区中文名"]);

                // 获取具体的数据访问实例
                var concreteDataAccess = _dataAccess as ExcelDataAccess;
                if (concreteDataAccess == null)
                {
                    ETLogManager.Warning(this, "无法转换为具体的ExcelDataAccess类型，无法预计算合并值");
                    return;
                }

                var dataCache = concreteDataAccess._dataCache;
                int totalCalculations = 0;

                // 为每个站名频段索引预计算合并值和统计值
                foreach (var kvp in _stationFrequencyIndexRowsCache)
                {
                    string stationFrequencyIndex = kvp.Key;
                    List<int> rowIndexes = kvp.Value;

                    // 1. 预计算合并值
                    foreach (var columnKvp in targetColumnIndexes)
                    {
                        string columnName = columnKvp.Key;
                        int columnIndex = columnKvp.Value;

                        // 构建缓存键
                        string cacheKey = $"{stationFrequencyIndex}|{columnName}";

                        // 收集该站名频段索引在指定列的所有非空值
                        var values = new List<string>();
                        foreach (int rowIndex in rowIndexes)
                        {
                            int cacheRow = rowIndex + 1; // 转换为1-based索引
                            int cacheCol = columnIndex + 1; // 转换为1-based索引

                            if (cacheRow <= concreteDataAccess._dataCacheRowCount &&
                                cacheCol <= concreteDataAccess._dataCacheColumnCount)
                            {
                                string value = dataCache[cacheRow, cacheCol]?.ToString() ?? "";
                                if (!string.IsNullOrWhiteSpace(value))
                                {
                                    values.Add(value);
                                }
                            }
                        }

                        // 缓存合并结果
                        string mergedValue = string.Join("/", values);
                        _mergedValuesCache[cacheKey] = mergedValue;
                        totalCalculations++;
                    }

                    // 2. 预计算统计值 小区数 = 行数
                    string cellCountKey = $"{stationFrequencyIndex}|CELL_COUNT";
                    _statisticsCache[cellCountKey] = rowIndexes.Count.ToString();
                    totalCalculations++;

                    // 设备数 = 扇区中文名去重后的数量
                    if (sectorNameColumnIndex >= 0)
                    {
                        var uniqueSectorNames = new HashSet<string>();
                        foreach (int rowIndex in rowIndexes)
                        {
                            int cacheRow = rowIndex + 1; // 转换为1-based索引
                            int cacheCol = sectorNameColumnIndex + 1; // 转换为1-based索引

                            if (cacheRow <= concreteDataAccess._dataCacheRowCount &&
                                cacheCol <= concreteDataAccess._dataCacheColumnCount)
                            {
                                string sectorName = dataCache[cacheRow, cacheCol]?.ToString() ?? "";
                                if (!string.IsNullOrWhiteSpace(sectorName))
                                {
                                    uniqueSectorNames.Add(sectorName);
                                }
                            }
                        }

                        string equipmentCountKey = $"{stationFrequencyIndex}|EQUIPMENT_COUNT";
                        _statisticsCache[equipmentCountKey] = uniqueSectorNames.Count.ToString();
                        totalCalculations++;
                    }
                }

                _mergedValuesCacheInitialized = true;
                _statisticsCacheInitialized = true;
                ETLogManager.Info(this, $"合并值和统计值缓存预计算完成，共计算 {totalCalculations} 个值");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "批量预计算合并值和统计值失败", ex);
                _mergedValuesCacheInitialized = false;
                _statisticsCacheInitialized = false;
            }
        }

        /// <summary>
        /// 清理性能优化相关的缓存
        /// </summary>
        private void ClearPerformanceOptimizationCaches()
        {
            try
            {
                _stationFrequencyIndexRowsCache.Clear();
                _mergedValuesCache.Clear();
                _statisticsCache.Clear();
                _stationFrequencyIndexCacheInitialized = false;
                _mergedValuesCacheInitialized = false;
                _statisticsCacheInitialized = false;

                ETLogManager.Info(this, "性能优化缓存已清理");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "清理性能优化缓存失败", ex);
            }
        }

        /// <summary>
        /// 强制重新初始化所有缓存（在数据变更后调用）
        /// </summary>
        public void RefreshAllCaches()
        {
            try
            {
                ETLogManager.Info(this, "开始刷新所有缓存");

                // 清理现有缓存
                ClearPerformanceOptimizationCaches();

                // 重新初始化数据缓存
                _dataAccess.LoadDataCache(forceReload: true);

                // 重新初始化列索引缓存
                _columnIndexCacheInitialized = false;
                InitializeColumnIndexCache(onlyExistingColumns: true);

                ETLogManager.Info(this, "所有缓存刷新完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "刷新所有缓存失败", ex);
            }
        }

        #endregion 性能优化缓存方法

        #region 资源释放

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    ETLogManager.Info(this, "StationDataProcessor正在释放资源");

                    // 清除列索引缓存
                    ClearColumnIndexCache();

                    // 清除性能优化缓存
                    ClearPerformanceOptimizationCaches();

                    // 释放数据访问层资源
                    if (_dataAccess is IDisposable disposableDataAccess)
                    {
                        disposableDataAccess.Dispose();
                    }
                }
                catch
                {
                    // 忽略释放时的异常
                }

                _disposed = true;
            }
        }

        #endregion 资源释放

        #region 取消处理

        /// <summary>
        /// 取消正在进行的处理
        /// </summary>
        public void CancelProcessing()
        {
            _cancellationRequested = true;
            ETLogManager.Warning(this, "收到取消处理请求");
            _logWriter?.Invoke("收到取消处理请求");
        }

        /// <summary>
        /// 检查是否请求取消
        /// </summary>
        /// <returns>如果请求取消返回true</returns>
        private bool IsCancellationRequested()
        {
            return _cancellationRequested;
        }

        private void ResetCancellation()
        {
            _cancellationRequested = false;
        }

        #endregion 取消处理
    }
}