# ETWebAutomation API 使用文档

## 概述

ETWebAutomation 是一个专为OA系统自动化操作设计的C#类库，提供了完整的登录认证、API交互、文件上传、浏览器模拟操作和会话管理功能。

## 核心组件

### 1. ETWebClient - 主客户端类

ETWebClient 是整个系统的核心入口点，提供了统一的API接口来访问所有功能模块。

#### 初始化

```csharp
// 创建客户端实例
var client = new ETWebClient("https://your-oa-system.com");

// 订阅事件（可选）
client.GlobalException += OnGlobalException;
client.LoginStatusChanged += OnLoginStatusChanged;
client.PerformanceAlert += OnPerformanceAlert;
```

#### 登录认证

```csharp
// 自动登录
bool loginSuccess = await client.LoginAsync("username", "password");

if (loginSuccess)
{
    Console.WriteLine("登录成功");
    Console.WriteLine($"用户ID: {client.LoginInfo.UserId}");
    Console.WriteLine($"认证令牌: {client.LoginInfo.Token}");
}
```

#### 登出

```csharp
// 登出系统
await client.LogoutAsync();
```

### 2. API交互

#### 基本API调用

```csharp
// GET请求
var userData = await client.ApiClient.GetAsync<UserInfo>("/api/user/profile");

// POST请求
var createResult = await client.ApiClient.PostAsync<CreateResult>("/api/documents", documentData);

// PUT请求
var updateResult = await client.ApiClient.PutAsync<UpdateResult>("/api/documents/123", updateData);

// DELETE请求
var deleteResult = await client.ApiClient.DeleteAsync<DeleteResult>("/api/documents/123");
```

#### 带参数的API调用

```csharp
// 查询参数
var searchParams = new Dictionary<string, object>
{
    ["keyword"] = "搜索关键词",
    ["page"] = 1,
    ["size"] = 20
};

var searchResult = await client.ApiClient.GetAsync<SearchResult>("/api/search", searchParams);
```

### 3. 文件上传

#### 单文件上传

```csharp
// 上传单个文件
var uploadResult = await client.FileUploader.UploadFileAsync(
    "/api/upload", 
    @"C:\path\to\file.pdf",
    "document"
);

if (uploadResult.Success)
{
    Console.WriteLine($"文件上传成功，文件ID: {uploadResult.FileId}");
}
```

#### 多文件上传

```csharp
// 上传多个文件
var files = new[]
{
    @"C:\path\to\file1.pdf",
    @"C:\path\to\file2.docx",
    @"C:\path\to\file3.xlsx"
};

var uploadResults = await client.FileUploader.UploadMultipleFilesAsync("/api/upload/batch", files);

foreach (var result in uploadResults)
{
    if (result.Success)
    {
        Console.WriteLine($"文件 {result.FileName} 上传成功");
    }
    else
    {
        Console.WriteLine($"文件 {result.FileName} 上传失败: {result.ErrorMessage}");
    }
}
```

#### 带进度监控的上传

```csharp
// 订阅上传进度事件
client.FileUploader.UploadProgress += (sender, e) =>
{
    Console.WriteLine($"上传进度: {e.ProgressPercentage}%");
};

var result = await client.FileUploader.UploadFileAsync("/api/upload", filePath);
```

### 4. 浏览器模拟操作

#### 创建模拟浏览器

```csharp
// 创建模拟操作浏览器
var browser = client.CreateSimulationBrowser("https://your-oa-system.com/workflow");

// 等待页面加载
await browser.WaitForPageLoadAsync();
```

#### 页面操作

```csharp
// 点击元素
await browser.ClickElementAsync("#submit-button");

// 输入文本
await browser.SetElementValueAsync("#input-field", "输入内容");

// 选择下拉框
await browser.SelectOptionAsync("#dropdown", "选项值");

// 执行JavaScript
var result = await browser.ExecuteScriptAsync("return document.title;");
```

#### 表单提交

```csharp
// 填写并提交表单
var formData = new Dictionary<string, object>
{
    ["title"] = "文档标题",
    ["content"] = "文档内容",
    ["category"] = "工作文档"
};

await browser.FillFormAsync("#document-form", formData);
await browser.SubmitFormAsync("#document-form");
```

### 5. 会话管理

#### 会话监控

```csharp
// 获取会话统计信息
var sessionStats = client.GetSessionStatistics();

Console.WriteLine($"会话状态: {(sessionStats.IsActive ? "活跃" : "非活跃")}");
Console.WriteLine($"最后心跳: {sessionStats.LastHeartbeat}");
Console.WriteLine($"会话存活时间: {sessionStats.SessionAge}");
```

#### 会话配置

```csharp
// 配置心跳间隔（秒）
client.SessionManager.HeartbeatInterval = 300; // 5分钟

// 启用自动重登
client.SessionManager.AutoReloginEnabled = true;
client.SessionManager.MaxReloginAttempts = 3;
```

### 6. 配置管理

#### 读取配置

```csharp
// 读取配置值
string serverUrl = client.GetConfig("Server", "Url", "https://default-url.com");
int timeout = int.Parse(client.GetConfig("Network", "Timeout", "30"));
```

#### 设置配置

```csharp
// 设置配置值
client.SetConfig("Server", "Url", "https://new-server.com");
client.SetConfig("Network", "Timeout", "60");
```

#### 全局设置

```csharp
// 设置全局设置
client.SetGlobalSetting("debug_mode", true);
client.SetGlobalSetting("max_retry_count", 3);

// 获取全局设置
bool debugMode = client.GetGlobalSetting<bool>("debug_mode", false);
int maxRetry = client.GetGlobalSetting<int>("max_retry_count", 1);
```

### 7. 性能监控

#### 获取性能统计

```csharp
// 获取性能统计信息
var perfStats = client.GetPerformanceStatistics();

Console.WriteLine($"CPU使用率: {perfStats.CpuUsage}%");
Console.WriteLine($"内存使用: {perfStats.MemoryUsage} MB");
Console.WriteLine($"网络延迟: {perfStats.NetworkLatency} ms");
```

#### 健康检查

```csharp
// 执行健康检查
var healthResult = await client.PerformHealthCheckAsync();

if (healthResult.IsHealthy)
{
    Console.WriteLine("系统状态正常");
}
else
{
    Console.WriteLine("系统状态异常:");
    foreach (var detail in healthResult.Details)
    {
        Console.WriteLine($"  {detail.Key}: {detail.Value}");
    }
}
```

## 事件处理

### 全局异常处理

```csharp
client.GlobalException += (sender, e) =>
{
    Console.WriteLine($"全局异常: {e.Exception.Message}");
    Console.WriteLine($"来源: {e.Source}");
    
    // 标记为已处理
    e.Handled = true;
};
```

### 登录状态变更

```csharp
client.LoginStatusChanged += (sender, e) =>
{
    if (e.IsLoggedIn)
    {
        Console.WriteLine($"用户 {e.Username} 已登录");
    }
    else
    {
        Console.WriteLine($"用户 {e.Username} 已登出");
    }
};
```

### 性能警报

```csharp
client.PerformanceAlert += (sender, e) =>
{
    Console.WriteLine($"性能警报: {e.Message}");
    Console.WriteLine($"严重级别: {e.Severity}");
};
```

## 最佳实践

### 1. 资源管理

```csharp
// 使用 using 语句确保资源正确释放
using (var client = new ETWebClient("https://your-oa-system.com"))
{
    await client.LoginAsync("username", "password");
    
    // 执行操作...
    
    await client.LogoutAsync();
} // 自动调用 Dispose()
```

### 2. 异常处理

```csharp
try
{
    var result = await client.ApiClient.GetAsync<DataModel>("/api/data");
    // 处理结果...
}
catch (ETException ex)
{
    // 处理业务异常
    Console.WriteLine($"业务异常: {ex.Message}");
}
catch (Exception ex)
{
    // 处理其他异常
    Console.WriteLine($"系统异常: {ex.Message}");
}
```

### 3. 配置管理

```csharp
// 在应用启动时加载配置
var config = new Dictionary<string, object>
{
    ["server_url"] = "https://your-oa-system.com",
    ["timeout"] = 30,
    ["retry_count"] = 3,
    ["debug_mode"] = false
};

foreach (var setting in config)
{
    client.SetGlobalSetting(setting.Key, setting.Value);
}
```

## 故障排除

### 常见问题

1. **登录失败**
   - 检查用户名和密码是否正确
   - 确认OA系统URL是否可访问
   - 查看日志文件获取详细错误信息

2. **API调用失败**
   - 检查网络连接
   - 确认API端点是否正确
   - 验证认证令牌是否有效

3. **文件上传失败**
   - 检查文件是否存在
   - 确认文件大小是否超过限制
   - 验证上传权限

4. **浏览器操作失败**
   - 确认页面元素是否存在
   - 检查页面是否完全加载
   - 验证JavaScript执行环境

### 日志查看

```csharp
// 启用详细日志
ETLogManager.SetLogLevel(LogLevel.Debug);

// 查看日志文件位置
string logPath = ETLogManager.GetLogFilePath();
Console.WriteLine($"日志文件位置: {logPath}");
```

## 版本信息

- **当前版本**: 1.0.0
- **最后更新**: 2025-08-02
- **兼容性**: .NET Framework 4.7.2+, .NET Core 3.1+, .NET 5.0+

## 技术支持

如需技术支持，请查看：
- 故障排除文档
- 最佳实践指南
- 示例代码库
