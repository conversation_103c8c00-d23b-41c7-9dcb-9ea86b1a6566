using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;
using System.Diagnostics;
using Microsoft.Office.Interop.Excel;
using Common.Utility;
using ET;
using ET.ETAIv2;
using ET.ETAIv2.Models;

namespace HyExcelVsto.Module.AI
{
    /// <summary>
    /// AI辅助工具v2窗体
    /// </summary>
    public partial class frmAIv2 : Form
    {
        private AIExcelAssistant _aiAssistant;
        private CancellationTokenSource _cancellationTokenSource;
        private string _configPath;

        public frmAIv2()
        {
            InitializeComponent();
            InitializeAIAssistant();
        }

        /// <summary>
        /// 初始化AI助手
        /// </summary>
        private void InitializeAIAssistant()
        {
            try
            {
                _aiAssistant = new AIExcelAssistant();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show($"初始化AI助手失败：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void frmAIv2_Load(object sender, EventArgs e)
        {
            try
            {
                // 获取配置文件目录路径
                _configPath = Path.GetDirectoryName(ETConfig.GetConfigDirectory("dummy.txt", "AiModel"));

                // 加载模型配置文件
                ETForm.LoadComboBox(comboModelConfig, _configPath, "*-json.ai");
                if (comboModelConfig.Items.Count > 0)
                    comboModelConfig.Text = "gemini-gemini-2-local-json.ai";

                // 加载规则文件（全局提示词）
                ETForm.LoadComboBox(comboGlobalPrompt, _configPath, "*.rule");
                if (comboGlobalPrompt.Items.Count > 0)
                    comboGlobalPrompt.Text = "无线通用.rule";

                // 设置默认Range
                SetDefaultRanges();

                // 设置默认选项
                radioRowMode.Checked = true;
                radioUploadFiles.Checked = true;
                checkFillNullValues.Checked = false;

                // 初始化UI状态
                SetExecutingState(false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show($"加载配置时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置默认Range
        /// </summary>
        private void SetDefaultRanges()
        {
            try
            {
                dynamic activeSheet = Globals.ThisAddIn.Application.ActiveSheet;

                // 设置默认区域
                ucDataSource.SelectedRange = activeSheet.Range["K2:K3"];      // 数据源
                ucTargetRange.SelectedRange = activeSheet.Range["F2:G3"];     // 回填区域
                ucPromptRange.SelectedRange = activeSheet.Range["F1:G1"];      // 提示词区域
                ucFileSource.SelectedRange = activeSheet.Range["D2:D3"];      // 文件源
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                // 设置默认Range失败不影响主要功能
            }
        }

        /// <summary>
        /// 执行AI处理
        /// </summary>
        private async void ButtonExecute_Click(object sender, EventArgs e)
        {
            try
            {
                // 更新UI状态
                SetExecutingState(true);

                // 验证输入
                ValidateInputs();

                // 清空日志和重置进度
                textBoxLog.Clear();
                progressBar.Value = 0;
                UpdateProgressLabel("准备开始...");

                // 创建取消令牌
                _cancellationTokenSource = new CancellationTokenSource();

                // 创建进度报告器
                Progress<ProcessingProgress> progress = new Progress<ProcessingProgress>(UpdateProgress);

                // 确定处理模式
                DataSourceMode mode = radioRowMode.Checked ? DataSourceMode.ByRow : DataSourceMode.ByColumn;
                FileProcessingMode fileMode = radioUploadFiles.Checked ?
                    FileProcessingMode.UploadToOpenAI : FileProcessingMode.ReadLocally;

                // 调用ETAIv2库进行处理
                AIResponse response = await _aiAssistant.ProcessExcelDataAsync(
                    ucDataSource.SelectedRange,
                    ucTargetRange.SelectedRange,
                    ucPromptRange.SelectedRange,
                    ucFileSource.SelectedRange,
                    comboModelConfig.Text,
                    comboGlobalPrompt.Text,
                    mode,
                    fileMode,
                    checkFillNullValues.Checked,
                    progress,
                    _cancellationTokenSource.Token);

                // 显示结果
                ShowResults(response);
            }
            catch (OperationCanceledException)
            {
                AppendLog("操作已取消。");
                UpdateProgressLabel("已取消");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                AppendLog($"错误：{ex.Message}");
                UpdateProgressLabel("处理失败");

                if (ex.InnerException != null)
                {
                    AppendLog($"详细错误：{ex.InnerException.Message}");
                }
            }
            finally
            {
                SetExecutingState(false);
            }
        }

        /// <summary>
        /// 停止处理
        /// </summary>
        private void ButtonStop_Click(object sender, EventArgs e)
        {
            try
            {
                _cancellationTokenSource?.Cancel();
                AppendLog("正在取消操作...");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                AppendLog($"取消操作时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 验证输入参数
        /// </summary>
        private void ValidateInputs()
        {
            if (ucDataSource.SelectedRange == null)
                throw new ArgumentException("请选择数据源区域！");

            if (ucTargetRange.SelectedRange == null)
                throw new ArgumentException("请选择回填区域！");

            if (ucPromptRange.SelectedRange == null)
                throw new ArgumentException("请选择提示词区域！");

            if (string.IsNullOrEmpty(comboModelConfig.Text))
                throw new ArgumentException("请选择模型配置！");

            if (string.IsNullOrEmpty(comboGlobalPrompt.Text))
                throw new ArgumentException("请选择全局提示词文件！");
        }

        /// <summary>
        /// 更新进度显示
        /// </summary>
        private void UpdateProgress(ProcessingProgress progress)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<ProcessingProgress>(UpdateProgress), progress);
                return;
            }

            progressBar.Value = Math.Min(progress.Percentage, 100);
            UpdateProgressLabel(progress.Message);
            AppendLog($"{DateTime.Now:HH:mm:ss} {progress.Message}");
        }

        /// <summary>
        /// 显示处理结果
        /// </summary>
        private void ShowResults(AIResponse response)
        {
            // 检查是否需要跨线程调用
            if (InvokeRequired)
            {
                Invoke(new Action<AIResponse>(ShowResults), response);
                return;
            }

            if (response.Success)
            {
                AppendLog($"处理完成！共处理 {response.Results?.Count ?? 0} 个数据组。");

                // 简化显示：不显示每个单独的数据组回填信息 只显示总体统计信息
                if (response.Results?.Any() == true)
                {
                    var totalValues = response.Results.Sum(r => r.Values?.Count ?? 0);
                    var batchCount = response.Metadata.ContainsKey("batchCount") ? (int)response.Metadata["batchCount"] : 1;

                    if (batchCount > 1)
                    {
                        AppendLog($"分 {batchCount} 个批次处理完成，共回填 {totalValues} 个值");
                    }
                    else
                    {
                        AppendLog($"共回填 {totalValues} 个值");
                    }

                    // 显示处理信息（取第一个结果的处理信息作为代表）
                    var firstResult = response.Results.FirstOrDefault();
                    if (firstResult != null && !string.IsNullOrEmpty(firstResult.ProcessingInfo))
                    {
                        AppendLog($"处理信息: {firstResult.ProcessingInfo}");
                    }
                }

                progressBar.Value = 100;
                UpdateProgressLabel("处理完成");
            }
            else
            {
                AppendLog($"处理失败：{response.ErrorMessage}");
                UpdateProgressLabel("处理失败");
            }
        }

        /// <summary>
        /// 设置执行状态
        /// </summary>
        private void SetExecutingState(bool executing)
        {
            // 检查是否需要跨线程调用
            if (InvokeRequired)
            {
                Invoke(new Action<bool>(SetExecutingState), executing);
                return;
            }

            buttonExecute.Enabled = !executing;
            buttonStop.Enabled = executing;

            // 禁用/启用配置控件
            comboModelConfig.Enabled = !executing;
            comboGlobalPrompt.Enabled = !executing;
            radioRowMode.Enabled = !executing;
            radioColumnMode.Enabled = !executing;
            radioUploadFiles.Enabled = !executing;
            radioLocalFiles.Enabled = !executing;
            checkFillNullValues.Enabled = !executing;
        }

        /// <summary>
        /// 添加日志
        /// </summary>
        private void AppendLog(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(AppendLog), message);
                return;
            }

            try
            {
                textBoxLog.AppendText($"{message}\r\n");
                textBoxLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
            }
        }

        /// <summary>
        /// 安全更新进度标签
        /// </summary>
        private void UpdateProgressLabel(string text)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(UpdateProgressLabel), text);
                return;
            }

            labelProgress.Text = text;
        }

        /// <summary>
        /// 打开配置目录
        /// </summary>
        private void ButtonOpenConfigDir_Click(object sender, EventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(_configPath) && Directory.Exists(_configPath))
                {
                    Process.Start("explorer.exe", _configPath);
                }
                else
                {
                    MessageBox.Show($"配置目录不存在：{_configPath}", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show($"打开配置目录失败：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                // 取消正在进行的操作
                _cancellationTokenSource?.Cancel();

                // 释放AI助手资源
                _aiAssistant?.Dispose();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
            }

            base.OnFormClosing(e);
        }
    }
}