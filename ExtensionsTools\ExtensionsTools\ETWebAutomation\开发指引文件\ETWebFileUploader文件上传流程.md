# 📤 ETWebFileUploader 文件上传流程详解（简化版本）

## 📋 概述

ETWebFileUploader是专门处理文件上传的简化模块，专注于基本的单文件上传功能，适用于ETWebAutomation的OA自动对接场景。已移除批量上传、分块上传、断点续传、进度监控等复杂功能。

## 🏗️ 类结构图（简化版本）

```mermaid
classDiagram
    class ETWebFileUploader {
        +int MaxFileSize
        +string[] AllowedExtensions
        +int RetryCount

        +UploadFileAsync(endpoint, filePath, formData) Task~ETWebUploadResult~
        +UploadFileWithRetryAsync(endpoint, filePath, formData, retryCount) Task~ETWebUploadResult~
        +ValidateFile(filePath) (bool, string)
        +CreateUploadResult(filePath, success, message, fileId) ETWebUploadResult

        -UploadFileDirectAsync(endpoint, filePath, formData) Task~ETWebUploadResult~
        +Dispose() void
    }

    ETWebFileUploader --> ETWebUploadResult
    ETWebFileUploader --> ETWebApiClient
    ETWebFileUploader --> ETWebUploadConfigHelper
```

## 📁 文件上传决策流程（简化版本）

```mermaid
flowchart TD
    A[开始文件上传] --> B[验证文件有效性]
    B --> C{文件是否有效}
    C -->|无效| D[返回验证错误]
    C -->|有效| E[直接上传模式]
    E --> F{上传是否成功}
    F -->|成功| G[返回成功结果]
    F -->|失败| H{是否还有重试次数}
    H -->|是| I[等待指数退避延迟]
    I --> E
    H -->|否| J[返回失败结果]

    style A fill:#e1f5fe
    style G fill:#c8e6c9
    style D fill:#ffcdd2
    style J fill:#ffcdd2
```

## 🔍 文件验证流程（简化版本）

```mermaid
sequenceDiagram
    participant Uploader as ETWebFileUploader
    participant Log as ETLogManager

    Uploader->>Uploader: ValidateFile(filePath)
    Uploader->>Uploader: 检查文件是否存在

    alt 文件不存在
        Uploader->>Log: 记录文件不存在错误
        Uploader-->>Uploader: 返回(false, "文件不存在")
    else 文件存在
        Uploader->>Uploader: 获取文件信息
        Uploader->>Uploader: 检查文件大小

        alt 文件过大
            Uploader->>Log: 记录文件过大日志
            Uploader-->>Uploader: 返回(false, "文件大小超过限制")
        else 文件大小合适
            Uploader->>Uploader: 检查文件扩展名

            alt 扩展名不允许
                Uploader->>Log: 记录扩展名不允许日志
                Uploader-->>Uploader: 返回(false, "不支持的文件类型")
            else 扩展名允许
                Uploader->>Log: 记录验证通过日志
                Uploader-->>Uploader: 返回(true, null)
            end
        end
    end
```

### 文件验证实现（简化版本）

```csharp
private (bool IsValid, string ErrorMessage) ValidateFile(string filePath)
{
    if (!File.Exists(filePath))
    {
        return (false, "文件不存在");
    }

    var fileInfo = new FileInfo(filePath);

    // 检查文件大小
    if (fileInfo.Length > MaxFileSize * 1024 * 1024)
    {
        return (false, $"文件大小超过限制 ({MaxFileSize}MB)");
    }

    // 检查文件扩展名
    var extension = fileInfo.Extension.ToLower();
    if (AllowedExtensions != null && AllowedExtensions.Length > 0)
    {
        bool isAllowed = false;
        foreach (var allowedExt in AllowedExtensions)
        {
            if (extension == allowedExt.ToLower())
            {
                isAllowed = true;
                break;
            }
        }

        if (!isAllowed)
        {
            return (false, $"不支持的文件类型: {extension}");
        }
    }

    return (true, null);
}
```

## 📤 单文件上传流程（简化版本）

```mermaid
sequenceDiagram
    participant Client as ETWebClient
    participant Uploader as ETWebFileUploader
    participant Api as ETWebApiClient
    participant Server as OA服务器

    Client->>Uploader: UploadFileAsync(endpoint, filePath, formData)
    Uploader->>Uploader: ValidateFile(filePath)

    alt 文件验证失败
        Uploader-->>Client: 返回验证错误
    else 文件验证成功
        loop 重试机制
            Uploader->>Uploader: UploadFileDirectAsync(endpoint, filePath, formData)
            Uploader->>Uploader: 读取文件字节
            Uploader->>Api: PostMultipartAsync(endpoint, multipartData)
            Api->>Server: 发送文件数据
            Server-->>Api: 返回上传结果
            Api-->>Uploader: 返回响应

            alt 上传成功
                Uploader-->>Client: 返回成功结果
            else 上传失败且有重试次数
                Uploader->>Uploader: 等待指数退避延迟
            else 上传失败且无重试次数
                Uploader-->>Client: 返回失败结果
            end
        end
    end
```

### 单文件上传实现（简化版本）

```csharp
/// <summary>
/// 上传单个文件（简化版本）
/// </summary>
/// <param name="endpoint">上传接口地址</param>
/// <param name="filePath">文件路径</param>
/// <param name="formData">表单数据</param>
/// <returns>上传结果</returns>
public async Task<ETWebUploadResult> UploadFileAsync(string endpoint, string filePath,
    Dictionary<string, object> formData = null)
{
    return await UploadFileWithRetryAsync(endpoint, filePath, formData, RetryCount);
}

/// <summary>
/// 带重试机制的文件上传
/// </summary>
private async Task<ETWebUploadResult> UploadFileWithRetryAsync(string endpoint, string filePath,
    Dictionary<string, object> formData, int retryCount)
{
    var startTime = DateTime.Now;

    // 文件验证
    var (isValid, errorMessage) = ValidateFile(filePath);
    if (!isValid)
    {
        return CreateUploadResult(filePath, false, errorMessage, null);
    }

    // 重试上传
    for (int attempt = 0; attempt <= retryCount; attempt++)
    {
        try
        {
            var result = await UploadFileDirectAsync(endpoint, filePath, formData);
            if (result.IsSuccess)
            {
                ETLogManager.Info("ETWebFileUploader", $"文件上传成功: {filePath}");
                return result;
            }

            if (attempt < retryCount)
            {
                var delay = TimeSpan.FromSeconds(Math.Pow(2, attempt)); // 指数退避
                ETLogManager.Warning("ETWebFileUploader", $"文件上传失败，{delay.TotalSeconds}秒后重试: {result.ErrorMessage}");
                await Task.Delay(delay);
            }
            else
            {
                ETLogManager.Error("ETWebFileUploader", $"文件上传最终失败: {result.ErrorMessage}");
                return result;
            }
        }
        catch (Exception ex)
        {
            if (attempt < retryCount)
            {
                var delay = TimeSpan.FromSeconds(Math.Pow(2, attempt));
                ETLogManager.Warning("ETWebFileUploader", $"文件上传异常，{delay.TotalSeconds}秒后重试: {ex.Message}");
                await Task.Delay(delay);
            }
            else
            {
                ETLogManager.Error("ETWebFileUploader", $"文件上传最终异常: {ex.Message}", ex);
                return CreateUploadResult(filePath, false, $"上传异常: {ex.Message}", null);
            }
        }
    }

    return CreateUploadResult(filePath, false, "上传失败", null);
}
```

## 🎯 使用示例

### 基本使用示例

```csharp
// 创建文件上传器实例
using (var uploader = new ETWebFileUploader(apiClient))
{
    // 配置上传参数
    uploader.MaxFileSize = 50; // 50MB
    uploader.RetryCount = 3;
    uploader.AllowedExtensions = new[] { ".pdf", ".doc", ".docx", ".jpg", ".png" };

    // 上传单个文件
    var result = await uploader.UploadFileAsync("https://api.example.com/upload", @"C:\temp\document.pdf");

    if (result.IsSuccess)
    {
        Console.WriteLine($"文件上传成功: {result.FileId}");
    }
    else
    {
        Console.WriteLine($"文件上传失败: {result.ErrorMessage}");
    }
}
```

### 配置管理示例

```csharp
// 初始化配置
ETWebUploadConfigHelper.Initialize();

// 创建上传器并应用配置
using (var uploader = new ETWebFileUploader(apiClient))
{
    ETWebUploadConfigHelper.ApplyConfigToUploader(uploader);

    // 执行上传
    var result = await uploader.UploadFileAsync(endpoint, filePath);
}
```

## 📋 核心特性（简化版本）

### ✅ 保留的功能
- ✅ **基本文件上传** - 支持单文件直接上传
- ✅ **文件验证** - 文件大小和扩展名验证
- ✅ **重试机制** - 失败时自动重试，支持指数退避
- ✅ **配置管理** - 通过ETWebUploadConfigHelper管理配置
- ✅ **日志记录** - 完整的上传过程日志
- ✅ **资源管理** - 实现IDisposable接口，正确释放资源

### ❌ 移除的复杂功能
- ❌ **批量上传** - 不再支持多文件并发上传
- ❌ **分块上传** - 不再支持大文件分块上传
- ❌ **断点续传** - 不再支持上传中断后的续传
- ❌ **进度监控** - 不再提供上传进度回调和UI
- ❌ **并发控制** - 不再需要并发限制机制
- ❌ **完整性校验** - 不再进行文件哈希验证

## 🔧 技术架构（简化版本）

### 核心组件
- **ETWebFileUploader** - 主要上传类，提供基本上传功能
- **ETWebUploadConfigHelper** - 配置管理辅助类（已简化）
- **ETWebUploadResult** - 上传结果模型
- **ETWebApiClient** - HTTP客户端，处理实际的网络请求

### 依赖关系
```
ETWebFileUploader
├── ETWebApiClient (HTTP请求)
├── ETWebUploadConfigHelper (配置管理)
├── ETLogManager (日志记录)
└── ETWebUploadResult (结果模型)
```

---

**📅 文档版本**: v2.0 (简化版本)
**🔄 最后更新**: 2024年12月
**👨‍💻 维护团队**: ETWebAutomation开发组
**📝 更新说明**: 移除复杂功能，专注于基本文件上传需求
