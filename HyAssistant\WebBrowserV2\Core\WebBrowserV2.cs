using ET;
using ET.ETLoginWebBrowser;
using Microsoft.Web.WebView2.WinForms;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HyAssistant.WebBrowserV2.Managers;
using HyAssistant.WebBrowserV2.Forms;
using HyAssistant.WebBrowserV2.Utils;

namespace HyAssistant.WebBrowserV2.Core
{
    /// <summary>
    /// WebBrowserV2主窗体 - 多页签式网页浏览器控件的优化版本
    /// </summary>
    /// <remarks>
    /// V2版本主要优化：
    /// 1. 异步编程安全性提升 - 避免UI阻塞，正确使用ConfigureAwait
    /// 2. 日志系统统一使用ETLogManager - 提升问题定位效率
    /// 3. Cookie处理逻辑优化 - 确保CookiePath生成和传递的正确性
    /// 4. 代码结构模块化 - 使用partial class拆分大文件
    /// 5. 异常处理增强 - 统一异常处理和恢复机制
    /// </remarks>
    public partial class WebBrowserV2 : Form
    {
        #region 核心字段和属性

        /// <summary>
        /// 窗体是否真正关闭标识
        /// </summary>
        private bool _isRealClosing = false;

        /// <summary>
        /// 是否最小化到托盘标识
        /// </summary>
        private bool _isMinimizedToTray = false;

        /// <summary>
        /// 配置管理器V2版本（JSON格式）
        /// </summary>
        private WebBrowserConfigManagerV2 _configManagerV2;

        /// <summary>
        /// Cookie管理器V2版本（兼容标头管理）
        /// </summary>
        private WebBrowserCookieManagerV2 _cookieManagerV2;

        /// <summary>
        /// 会话管理器V2版本
        /// </summary>
        private WebBrowserSessionManagerV2 _sessionManagerV2;

        /// <summary>
        /// 标签页管理器V2版本
        /// </summary>
        private WebBrowserTabManagerV2 _tabManagerV2;

        /// <summary>
        /// 标签页菜单管理器V2版本
        /// </summary>
        private TabMenuManagerV2 _tabMenuManagerV2;

        /// <summary>
        /// 窗体关闭处理器V2版本
        /// </summary>
        private FormClosingHandlerV2 _formClosingHandlerV2;

        /// <summary>
        /// 资源管理器V2版本
        /// </summary>
        private WebBrowserResourceManagerV2 _resourceManagerV2;

        /// <summary>
        /// 鼠标位置监控定时器
        /// </summary>
        private System.Windows.Forms.Timer _mouseMonitorTimer;

        /// <summary>
        /// Cookie路径管理器V2版本
        /// </summary>
        private CookiePathManagerV2 _cookiePathManagerV2;

        /// <summary>
        /// Cookie传递管理器V2版本
        /// </summary>
        private CookieTransferManagerV2 _cookieTransferManagerV2;

        /// <summary>
        /// 操作恢复管理器V2版本
        /// </summary>
        private OperationRecoveryManagerV2 _operationRecoveryManagerV2;

        /// <summary>
        /// 日志助手V2版本
        /// </summary>
        private LoggingHelperV2 _loggingHelperV2;

        /// <summary>
        /// 是否正在创建标签页标识
        /// </summary>
        private bool _isCreatingTab = false;

        #endregion 核心字段和属性

        #region 公共事件

        /// <summary>
        /// 窗体真正关闭时触发的事件
        /// </summary>
        public event EventHandler RealClose;

        // V2版本事件已移除，使用现有的事件系统
        // 如需要可以在将来重新添加并实现相应的触发逻辑

        #endregion 公共事件

        #region 公共属性

        /// <summary>
        /// 配置管理器（只读访问）
        /// </summary>
        public WebBrowserConfigManagerV2 ConfigManager => _configManagerV2;

        /// <summary>
        /// Cookie管理器（只读访问）
        /// </summary>
        public WebBrowserCookieManagerV2 CookieManager => _cookieManagerV2;

        /// <summary>
        /// 会话管理器（只读访问）
        /// </summary>
        public WebBrowserSessionManagerV2 SessionManager => _sessionManagerV2;

        /// <summary>
        /// 标签页管理器（只读访问）
        /// </summary>
        public WebBrowserTabManagerV2 TabManager => _tabManagerV2;

        /// <summary>
        /// 是否正在关闭
        /// </summary>
        public bool IsClosing => _isRealClosing;

        /// <summary>
        /// 是否最小化到托盘
        /// </summary>
        public bool IsMinimizedToTray => _isMinimizedToTray;

        #endregion 公共属性

        #region 兼容性属性（用于MenuOperations等partial class）

        /// <summary>
        /// 标签页管理器（兼容性访问）
        /// </summary>
        protected WebBrowserTabManagerV2 _tabManager => _tabManagerV2;

        /// <summary>
        /// 配置管理器（兼容性访问）
        /// </summary>
        protected WebBrowserConfigManagerV2 _configManager => _configManagerV2;

        /// <summary>
        /// Cookie管理器（兼容性访问）
        /// </summary>
        protected WebBrowserCookieManagerV2 _cookieManager => _cookieManagerV2;

        /// <summary>
        /// 会话管理器（兼容性访问）
        /// </summary>
        protected WebBrowserSessionManagerV2 _sessionManager => _sessionManagerV2;

        /// <summary>
        /// 标签页菜单管理器（兼容性访问）
        /// </summary>
        protected TabMenuManagerV2 _tabMenuManager => _tabMenuManagerV2;

        /// <summary>
        /// 配置管理器V2（公共访问）
        /// </summary>
        public WebBrowserConfigManagerV2 ConfigManagerV2 => _configManagerV2;

        #endregion 兼容性属性

        #region 构造函数和初始化

        /// <summary>
        /// WebBrowserV2构造函数
        /// </summary>
        public WebBrowserV2()
        {
            try
            {
                // V2版本优化: 使用ETLogManager统一日志记录
                ETLogManager.Info(this, "开始初始化WebBrowserV2窗体");

                // 初始化窗体组件
                InitializeComponent();

                // 设置窗体图标
                try
                {
                    Icon = System.Drawing.Icon.ExtractAssociatedIcon(Application.ExecutablePath);
                }
                catch (Exception iconEx)
                {
                    ETLogManager.Warning(this, $"设置窗体图标失败: {iconEx.Message}");
                }

                // V2版本新增: 初始化日志助手
                _loggingHelperV2 = new LoggingHelperV2(this);
                _loggingHelperV2.LogInfo("WebBrowserV2窗体组件初始化完成");

                // 添加窗体大小改变事件
                SizeChanged += WebBrowserV2_SizeChanged;

                // V2版本优化: 异步安全的管理器初始化
                InitializeManagersSafely();

                // 初始化鼠标监控定时器
                InitializeMouseMonitorTimer();

                // V2版本新增: 注册全局异常处理
                RegisterGlobalExceptionHandlers();

                _loggingHelperV2.LogInfo("WebBrowserV2窗体初始化完成");
            }
            catch (Exception ex)
            {
                // V2版本优化: 使用增强的异常处理
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "WebBrowserV2构造函数", true, true);
                throw; // 构造函数异常需要重新抛出
            }
        }

        /// <summary>
        /// V2版本优化: 异步安全的管理器初始化
        /// </summary>
        private void InitializeManagersSafely()
        {
            try
            {
                _loggingHelperV2.LogInfo("开始初始化各管理器");

                // 按依赖顺序初始化管理器
                _configManagerV2 = new WebBrowserConfigManagerV2(this);
                _cookiePathManagerV2 = new CookiePathManagerV2(this);
                _cookieTransferManagerV2 = new CookieTransferManagerV2(this);
                _operationRecoveryManagerV2 = new OperationRecoveryManagerV2(this);

                _cookieManagerV2 = new WebBrowserCookieManagerV2(this, null, null);
                _sessionManagerV2 = new WebBrowserSessionManagerV2(this);
                _tabManagerV2 = new WebBrowserTabManagerV2(tabControl1, _configManagerV2, _cookieManagerV2, _sessionManagerV2, this);

                // 🔧 修复：创建TabMenuManagerV2并绑定事件
                _tabMenuManagerV2 = new TabMenuManagerV2(this, _tabManagerV2);
                _tabMenuManagerV2.TabMenuItemClicked += TabMenuManager_TabMenuItemClicked;
                _tabMenuManagerV2.NewTabMenuItemClicked += TabMenuManager_NewTabMenuItemClicked;

                _formClosingHandlerV2 = new FormClosingHandlerV2(this);
                _resourceManagerV2 = new WebBrowserResourceManagerV2(this);

                _loggingHelperV2.LogInfo("所有管理器初始化完成");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "初始化管理器", true, true);
                throw;
            }
        }

        /// <summary>
        /// V2版本新增: 初始化鼠标监控定时器
        /// </summary>
        private void InitializeMouseMonitorTimer()
        {
            try
            {
                _mouseMonitorTimer = new System.Windows.Forms.Timer
                {
                    Interval = WebBrowserConstantsV2.MOUSE_MONITOR_INTERVAL,
                    Enabled = false
                };
                _mouseMonitorTimer.Tick += MouseMonitorTimer_Tick;

                _loggingHelperV2.LogInfo("鼠标监控定时器初始化完成");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "初始化鼠标监控定时器");
            }
        }

        /// <summary>
        /// V2版本新增: 注册全局异常处理
        /// </summary>
        private void RegisterGlobalExceptionHandlers()
        {
            try
            {
                // 注册应用程序域未处理异常
                AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;

                // 注册UI线程未处理异常
                Application.ThreadException += OnThreadException;

                _loggingHelperV2.LogInfo("全局异常处理器注册完成");
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "注册全局异常处理器");
            }
        }

        #endregion 构造函数和初始化

        #region 资源释放

        /// <summary>
        /// V2版本优化: 安全的资源释放
        /// </summary>
        /// <param name="disposing">是否释放托管资源</param>
        protected override void Dispose(bool disposing)
        {
            try
            {
                if (disposing)
                {
                    _loggingHelperV2?.LogInfo("开始释放WebBrowserV2资源");

                    // V2版本优化: 按顺序安全释放资源
                    DisposeManagersSafely();
                    DisposeTimersSafely();
                    DisposeEventHandlersSafely();

                    // 释放组件
                    components?.Dispose();

                    _loggingHelperV2?.LogInfo("WebBrowserV2资源释放完成");
                }
            }
            catch (Exception ex)
            {
                // 资源释放时的异常不应该阻止程序继续
                ETLogManager.Error(this, $"释放WebBrowserV2资源时发生异常: {ex.Message}");
            }
            finally
            {
                base.Dispose(disposing);
            }
        }

        /// <summary>
        /// V2版本新增: 安全释放管理器资源
        /// </summary>
        private void DisposeManagersSafely()
        {
            try
            {
                _resourceManagerV2?.Dispose();
                _formClosingHandlerV2?.Dispose();
                _tabMenuManagerV2?.Dispose();
                _tabManagerV2?.Dispose();
                _sessionManagerV2?.Dispose();
                _cookieManagerV2?.Dispose();
                _configManagerV2?.Dispose();
                _operationRecoveryManagerV2?.Dispose();
                _cookieTransferManagerV2?.Dispose();
                _cookiePathManagerV2?.Dispose();
                _loggingHelperV2?.Dispose();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"释放管理器资源时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// V2版本新增: 安全释放定时器资源
        /// </summary>
        private void DisposeTimersSafely()
        {
            try
            {
                if (_mouseMonitorTimer != null)
                {
                    _mouseMonitorTimer.Stop();
                    _mouseMonitorTimer.Tick -= MouseMonitorTimer_Tick;
                    _mouseMonitorTimer.Dispose();
                    _mouseMonitorTimer = null;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"释放定时器资源时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// V2版本新增: 鼠标监控定时器Tick事件处理
        /// </summary>
        private void MouseMonitorTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                // 如果会话日志TextBox可见，检查鼠标位置
                if (sessionLogTextBox != null && sessionLogTextBox.Visible)
                {
                    // 获取当前鼠标在窗体中的位置
                    Point mousePos = PointToClient(Control.MousePosition);

                    // 如果鼠标既不在会话日志TextBox区域内，也不在状态栏区域内，则隐藏TextBox
                    if (!sessionLogTextBox.Bounds.Contains(mousePos) &&
                        !statusStrip1.Bounds.Contains(mousePos))
                    {
                        sessionLogTextBox.Visible = false;
                        _mouseMonitorTimer?.Stop();
                    }
                }
                else
                {
                    // 如果TextBox已不可见，停止定时器
                    _mouseMonitorTimer?.Stop();
                }
            }
            catch (Exception ex)
            {
                WebBrowserExceptionHandlerV2.HandleException(this, ex, "监控鼠标位置");
                _mouseMonitorTimer?.Stop();
            }
        }

        /// <summary>
        /// V2版本新增: 安全注销事件处理器
        /// </summary>
        private void DisposeEventHandlersSafely()
        {
            try
            {
                // 注销全局异常处理
                AppDomain.CurrentDomain.UnhandledException -= OnUnhandledException;
                Application.ThreadException -= OnThreadException;

                // 注销窗体事件
                SizeChanged -= WebBrowserV2_SizeChanged;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"注销事件处理器时发生异常: {ex.Message}");
            }
        }

        #endregion 资源释放
    }
}
