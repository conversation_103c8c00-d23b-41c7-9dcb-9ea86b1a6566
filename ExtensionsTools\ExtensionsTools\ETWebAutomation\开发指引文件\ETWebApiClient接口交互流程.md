# 🌐 ETWebApiClient API交互流程详解

## 📋 概述

ETWebApiClient是基于Flurl.Http的API交互模块，负责处理与OA系统的HTTP通信，提供GET、POST、PUT、DELETE等RESTful操作，支持自动JSON序列化、智能重试、Cookie管理等功能。

## 🏗️ 类结构图

```mermaid
classDiagram
    class ETWebApiClient {
        +IFlurlClient HttpClient
        +ETWebLoginInfo AuthInfo
        +string BaseUrl
        +int DefaultTimeout
        +int MaxRetryCount
        
        +GetAsync~T~(endpoint, queryParams, retryCount) Task~ETWebApiResponse~T~~
        +PostAsync~T~(endpoint, data, retryCount) Task~ETWebApiResponse~T~~
        +PutAsync~T~(endpoint, data, retryCount) Task~ETWebApiResponse~T~~
        +DeleteAsync~T~(endpoint, retryCount) Task~ETWebApiResponse~T~~
        +SetAuthenticationInfo(loginInfo) void
        +SetDefaultHeaders(headers) void
        +SetTimeout(seconds) void
        
        -BuildRequest(endpoint, method) IFlurlRequest
        -AddAuthentication(request) IFlurlRequest
        -ProcessResponse~T~(response) ETWebApiResponse~T~
        -HandleException(ex, endpoint, method) ETWebApiResponse~T~
    }
    
    ETWebApiClient --> IFlurlClient
    ETWebApiClient --> ETWebApiRequest
    ETWebApiClient --> ETWebApiResponse
    ETWebApiClient --> ETWebRetryHelper
    ETWebApiClient --> ETWebJsonHelper
```

## 🔄 HTTP请求完整流程

```mermaid
sequenceDiagram
    participant Client as ETWebClient
    participant Api as ETWebApiClient
    participant Flurl as IFlurlClient
    participant Retry as ETWebRetryHelper
    participant Json as ETWebJsonHelper
    participant Log as ETLogManager
    participant Server as OA服务器
    
    Client->>Api: GetAsync<T>(endpoint, params)
    Api->>Api: 验证认证信息
    
    alt 认证信息无效
        Api-->>Client: 返回认证错误
    else 认证信息有效
        Api->>Api: BuildRequest(endpoint, GET)
        Api->>Api: AddAuthentication(request)
        Api->>Api: AddQueryParameters(params)
        
        Api->>Retry: ExecuteWithRetryAsync(requestFunc)
        
        loop 重试循环
            Retry->>Flurl: 发送HTTP请求
            Flurl->>Server: HTTP GET请求
            Server-->>Flurl: HTTP响应
            Flurl-->>Retry: 响应结果
            
            alt 请求成功
                Retry-->>Api: 返回成功响应
            else 需要重试
                Retry->>Log: 记录重试日志
                Retry->>Retry: 计算重试延迟
                Retry->>Retry: 等待重试间隔
            else 重试次数耗尽
                Retry-->>Api: 返回最终失败响应
            end
        end
        
        Api->>Api: ProcessResponse<T>(response)
        Api->>Json: 反序列化响应数据
        Json-->>Api: 强类型对象
        
        Api->>Log: 记录API调用日志
        Api-->>Client: 返回ETWebApiResponse<T>
    end
```

## 🛠️ 请求构建流程

```mermaid
flowchart TD
    A[开始构建请求] --> B[创建基础Flurl请求]
    B --> C[设置请求URL]
    C --> D[添加认证信息]
    D --> E{认证方式}
    E -->|Token| F[添加Authorization头]
    E -->|Cookie| G[添加Cookie信息]
    E -->|Session| H[添加Session信息]
    F --> I[设置请求头]
    G --> I
    H --> I
    I --> J[设置超时时间]
    J --> K[设置User-Agent]
    K --> L[添加自定义头]
    L --> M{请求方法}
    M -->|GET| N[添加查询参数]
    M -->|POST/PUT| O[设置请求体]
    M -->|DELETE| P[设置删除参数]
    N --> Q[请求构建完成]
    O --> R[序列化请求数据]
    R --> S[设置Content-Type]
    S --> Q
    P --> Q
    
    style A fill:#e1f5fe
    style Q fill:#c8e6c9
```

### 请求构建实现

```csharp
private IFlurlRequest BuildRequest(string endpoint, HttpMethod method)
{
    try
    {
        // 1. 构建完整URL
        var fullUrl = BaseUrl.TrimEnd('/') + "/" + endpoint.TrimStart('/');
        var request = fullUrl.WithClient(HttpClient);
        
        // 2. 设置基础配置
        request = request
            .WithTimeout(DefaultTimeout)
            .WithHeader("User-Agent", "ETWebAutomation/1.0")
            .WithHeader("Accept", "application/json")
            .WithHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        
        // 3. 添加认证信息
        if (AuthInfo != null)
        {
            request = AddAuthentication(request);
        }
        
        // 4. 添加自定义头
        foreach (var header in DefaultHeaders)
        {
            request = request.WithHeader(header.Key, header.Value);
        }
        
        ETLogManager.Debug($"构建请求: {method} {fullUrl}");
        return request;
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"构建请求失败: {ex.Message}", ex);
        throw;
    }
}

private IFlurlRequest AddAuthentication(IFlurlRequest request)
{
    // 1. 添加Token认证
    if (!string.IsNullOrEmpty(AuthInfo.Token))
    {
        request = request.WithOAuthBearerToken(AuthInfo.Token);
    }
    
    // 2. 添加Cookie认证
    if (AuthInfo.Cookies != null && AuthInfo.Cookies.Count > 0)
    {
        foreach (var cookie in AuthInfo.Cookies)
        {
            request = request.WithCookie(cookie.Name, cookie.Value);
        }
    }
    
    // 3. 添加自定义认证头
    if (AuthInfo.Headers != null)
    {
        foreach (var header in AuthInfo.Headers)
        {
            request = request.WithHeader(header.Key, header.Value);
        }
    }
    
    return request;
}
```

## 📤 POST请求数据处理流程

```mermaid
graph TD
    A[POST请求开始] --> B[检查请求数据类型]
    B --> C{数据类型}
    C -->|对象| D[JSON序列化]
    C -->|字符串| E[直接使用]
    C -->|表单数据| F[构建FormData]
    C -->|文件数据| G[构建MultipartContent]
    
    D --> H[设置Content-Type为application/json]
    E --> I[检测Content-Type]
    F --> J[设置Content-Type为application/x-www-form-urlencoded]
    G --> K[设置Content-Type为multipart/form-data]
    
    H --> L[发送请求]
    I --> L
    J --> L
    K --> L
    
    L --> M[等待响应]
    M --> N{响应状态}
    N -->|成功| O[处理响应数据]
    N -->|失败| P[处理错误响应]
    
    O --> Q[返回成功结果]
    P --> R[返回错误结果]
    
    style A fill:#e1f5fe
    style Q fill:#c8e6c9
    style R fill:#ffcdd2
```

### POST请求实现

```csharp
public async Task<ETWebApiResponse<T>> PostAsync<T>(string endpoint, object data, int retryCount = -1)
{
    var requestId = Guid.NewGuid().ToString();
    var startTime = DateTime.Now;
    
    try
    {
        ETLogManager.Info($"[{requestId}] 开始POST请求: {endpoint}");
        
        // 1. 构建请求
        var request = BuildRequest(endpoint, HttpMethod.Post);
        
        // 2. 处理请求数据
        if (data != null)
        {
            if (data is string stringData)
            {
                // 字符串数据直接发送
                request = request.WithStringContent(stringData, "application/json");
            }
            else if (data is Dictionary<string, object> formData)
            {
                // 表单数据
                request = request.WithFormUrlEncodedContent(formData);
            }
            else
            {
                // 对象数据JSON序列化
                var jsonData = ETWebJsonHelper.ToJson(data);
                request = request.WithStringContent(jsonData, "application/json");
            }
        }
        
        // 3. 执行带重试的请求
        var actualRetryCount = retryCount >= 0 ? retryCount : MaxRetryCount;
        var retryConfig = ETWebRetryHelper.CreateApiRetryConfig(actualRetryCount);
        
        var response = await ETWebRetryHelper.ExecuteWithRetryAsync(async () =>
        {
            return await request.PostAsync();
        }, retryConfig);
        
        // 4. 处理响应
        var result = await ProcessResponse<T>(response, requestId);
        
        var duration = DateTime.Now - startTime;
        ETLogManager.Info($"[{requestId}] POST请求完成: {endpoint}, 耗时: {duration.TotalMilliseconds}ms");
        
        return result;
    }
    catch (Exception ex)
    {
        var duration = DateTime.Now - startTime;
        ETLogManager.Error($"[{requestId}] POST请求异常: {endpoint}, 耗时: {duration.TotalMilliseconds}ms, 错误: {ex.Message}", ex);
        
        return HandleException<T>(ex, endpoint, "POST", requestId);
    }
}
```

## 🔄 响应处理流程

```mermaid
flowchart TD
    A[接收HTTP响应] --> B[检查响应状态码]
    B --> C{状态码类型}
    C -->|2xx成功| D[读取响应内容]
    C -->|4xx客户端错误| E[处理客户端错误]
    C -->|5xx服务器错误| F[处理服务器错误]
    C -->|其他| G[处理未知错误]
    
    D --> H[检查Content-Type]
    H --> I{内容类型}
    I -->|application/json| J[JSON反序列化]
    I -->|text/plain| K[文本处理]
    I -->|text/html| L[HTML处理]
    I -->|其他| M[二进制处理]
    
    J --> N[验证JSON格式]
    N --> O{JSON是否有效}
    O -->|有效| P[转换为强类型对象]
    O -->|无效| Q[记录JSON格式错误]
    
    P --> R[创建成功响应对象]
    K --> S[创建文本响应对象]
    L --> T[创建HTML响应对象]
    M --> U[创建二进制响应对象]
    Q --> V[创建JSON错误响应]
    
    E --> W[提取错误信息]
    F --> X[提取服务器错误]
    G --> Y[提取未知错误]
    
    W --> Z[创建客户端错误响应]
    X --> AA[创建服务器错误响应]
    Y --> BB[创建未知错误响应]
    
    R --> CC[返回响应结果]
    S --> CC
    T --> CC
    U --> CC
    V --> CC
    Z --> CC
    AA --> CC
    BB --> CC
    
    style A fill:#e1f5fe
    style CC fill:#c8e6c9
    style Q fill:#ffcdd2
    style W fill:#ffcdd2
    style X fill:#ffcdd2
    style Y fill:#ffcdd2
```

### 响应处理实现

```csharp
private async Task<ETWebApiResponse<T>> ProcessResponse<T>(IFlurlResponse response, string requestId)
{
    var apiResponse = new ETWebApiResponse<T>
    {
        ResponseId = Guid.NewGuid().ToString(),
        RequestId = requestId,
        StatusCode = (int)response.StatusCode,
        IsSuccess = response.ResponseMessage.IsSuccessStatusCode,
        ResponseTime = DateTime.Now
    };
    
    try
    {
        // 1. 读取响应内容
        var content = await response.GetStringAsync();
        apiResponse.RawContent = content;
        
        // 2. 提取响应头
        apiResponse.Headers = response.Headers.ToDictionary(h => h.Name, h => h.Value);
        apiResponse.ContentType = response.Headers.FirstOrDefault(h => h.Name == "Content-Type")?.Value;
        
        // 3. 处理成功响应
        if (apiResponse.IsSuccess)
        {
            if (!string.IsNullOrEmpty(content))
            {
                // 尝试JSON反序列化
                if (typeof(T) == typeof(string))
                {
                    apiResponse.Data = (T)(object)content;
                }
                else if (ETWebJsonHelper.IsValidJson(content))
                {
                    try
                    {
                        apiResponse.Data = ETWebJsonHelper.FromJson<T>(content);
                        apiResponse.DataJson = content;
                    }
                    catch (JsonException jsonEx)
                    {
                        ETLogManager.Warning($"[{requestId}] JSON反序列化失败: {jsonEx.Message}");
                        apiResponse.ErrorMessage = $"JSON反序列化失败: {jsonEx.Message}";
                        apiResponse.IsSuccess = false;
                    }
                }
                else
                {
                    // 非JSON内容
                    if (typeof(T) == typeof(object))
                    {
                        apiResponse.Data = (T)(object)content;
                    }
                    else
                    {
                        ETLogManager.Warning($"[{requestId}] 响应内容不是有效的JSON格式");
                        apiResponse.ErrorMessage = "响应内容不是有效的JSON格式";
                        apiResponse.IsSuccess = false;
                    }
                }
            }
        }
        else
        {
            // 4. 处理错误响应
            apiResponse.ErrorMessage = $"HTTP {apiResponse.StatusCode}: {response.ResponseMessage.ReasonPhrase}";
            
            // 尝试提取详细错误信息
            if (!string.IsNullOrEmpty(content) && ETWebJsonHelper.IsValidJson(content))
            {
                try
                {
                    var errorInfo = ETWebJsonHelper.FromJsonDynamic(content);
                    if (errorInfo?.message != null)
                    {
                        apiResponse.ErrorMessage += $" - {errorInfo.message}";
                    }
                    else if (errorInfo?.error != null)
                    {
                        apiResponse.ErrorMessage += $" - {errorInfo.error}";
                    }
                }
                catch
                {
                    // 忽略错误信息解析异常
                }
            }
        }
        
        return apiResponse;
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"[{requestId}] 处理响应时发生异常: {ex.Message}", ex);
        
        apiResponse.IsSuccess = false;
        apiResponse.ErrorMessage = $"处理响应异常: {ex.Message}";
        apiResponse.Exception = ex;
        
        return apiResponse;
    }
}
```

## 🔁 智能重试机制

```mermaid
graph TD
    A[API请求失败] --> B[分析异常类型]
    B --> C{异常类型}
    C -->|网络超时| D[可重试]
    C -->|连接失败| D
    C -->|服务器5xx错误| D
    C -->|认证401错误| E[需要重新认证]
    C -->|权限403错误| F[不可重试]
    C -->|客户端4xx错误| F
    C -->|JSON解析错误| F
    
    D --> G[检查重试次数]
    G --> H{是否超过最大重试次数}
    H -->|否| I[计算重试延迟]
    H -->|是| J[重试失败]
    
    I --> K[执行延迟等待]
    K --> L[重新发送请求]
    L --> M{请求是否成功}
    M -->|成功| N[返回成功结果]
    M -->|失败| A
    
    E --> O[尝试自动重新认证]
    O --> P{重新认证是否成功}
    P -->|成功| L
    P -->|失败| Q[认证失败]
    
    F --> R[直接返回失败]
    J --> R
    Q --> R
    
    style A fill:#fff3e0
    style N fill:#c8e6c9
    style R fill:#ffcdd2
    style Q fill:#ffcdd2
    style J fill:#ffcdd2
```

### 重试配置

```csharp
public class ETWebApiRetryConfig
{
    public int MaxRetryCount { get; set; } = 3;
    public TimeSpan InitialDelay { get; set; } = TimeSpan.FromSeconds(1);
    public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);
    public double BackoffMultiplier { get; set; } = 2.0;
    public bool EnableJitter { get; set; } = true;
    
    public List<HttpStatusCode> RetryableStatusCodes { get; set; } = new List<HttpStatusCode>
    {
        HttpStatusCode.InternalServerError,
        HttpStatusCode.BadGateway,
        HttpStatusCode.ServiceUnavailable,
        HttpStatusCode.GatewayTimeout,
        HttpStatusCode.RequestTimeout
    };
    
    public List<Type> RetryableExceptions { get; set; } = new List<Type>
    {
        typeof(HttpRequestException),
        typeof(TaskCanceledException),
        typeof(SocketException),
        typeof(TimeoutException)
    };
}
```

## 📊 性能监控集成

```mermaid
graph LR
    A[API请求开始] --> B[记录开始时间]
    B --> C[发送HTTP请求]
    C --> D[记录结束时间]
    D --> E[计算请求耗时]
    E --> F[记录性能数据]
    F --> G[检查性能阈值]
    G --> H{是否超过阈值}
    H -->|是| I[触发性能警告]
    H -->|否| J[正常记录]
    I --> K[发送性能报告]
    J --> L[更新统计数据]
    K --> L
    L --> M[完成性能监控]
```

### 性能监控实现

```csharp
private void RecordApiPerformance(string endpoint, string method, TimeSpan duration, bool success)
{
    try
    {
        // 记录到性能监控系统
        ETWebPerformanceHelper.RecordApiCall(endpoint, duration, success);
        
        // 检查性能阈值
        var thresholdMs = ETWebConfigHelper.GetApiPerformanceThreshold();
        if (duration.TotalMilliseconds > thresholdMs)
        {
            ETLogManager.Warning($"API请求性能警告: {method} {endpoint} 耗时 {duration.TotalMilliseconds}ms，超过阈值 {thresholdMs}ms");
            
            // 触发性能警告事件
            OnApiPerformanceWarning?.Invoke(new ApiPerformanceWarningEventArgs
            {
                Endpoint = endpoint,
                Method = method,
                Duration = duration,
                Threshold = TimeSpan.FromMilliseconds(thresholdMs)
            });
        }
        
        // 更新API调用统计
        ApiCallStatistics.RecordCall(endpoint, method, duration, success);
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"记录API性能数据时发生异常: {ex.Message}", ex);
    }
}
```

---

**📅 文档版本**: v1.0  
**🔄 最后更新**: 2024年12月  
**👨‍💻 维护团队**: ETWebAutomation开发组
