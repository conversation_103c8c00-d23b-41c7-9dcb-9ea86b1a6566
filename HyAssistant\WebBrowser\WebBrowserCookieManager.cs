using ET;
using ET.ETLoginWebBrowser;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace HyAssistant
{
    /// <summary>
    /// WebBrowser 标头管理器，负责HTTP请求标头的捕获、导入导出和管理
    /// </summary>
    public partial class WebBrowserCookieManager : IDisposable
    {
        #region 字段和属性

        /// <summary>
        /// 用于记录的对象
        /// </summary>
        private readonly object _logSource;

        /// <summary>
        /// 标头文件路径
        /// </summary>
        public string HeadersPath { get; set; }

        /// <summary>
        /// Cookie文件路径（保持向后兼容，映射到HeadersPath）
        /// </summary>
        public string CookiePath
        {
            get => HeadersPath;
            set => HeadersPath = value;
        }

        /// <summary>
        /// 当前URL
        /// </summary>
        public string CurrentUrl { get; set; }

        /// <summary>
        /// 标头数据的JSON字符串
        /// </summary>
        private string _headersJson = string.Empty;

        /// <summary>
        /// 最后一次捕获的请求标头
        /// </summary>
        private Dictionary<string, string> _lastRequestHeaders = new Dictionary<string, string>();

        /// <summary>
        /// 存储所有捕获的请求标头
        /// </summary>
        private readonly List<Dictionary<string, string>> _allCapturedRequests = new List<Dictionary<string, string>>();

        /// <summary>
        /// Cookie数据（保持向后兼容）
        /// </summary>
        public CookieData CookieData { get; set; } = new CookieData();

        /// <summary>
        /// 是否已释放
        /// </summary>
        private bool _disposed = false;

        /// <summary>
        /// 自动导出标头的定时器
        /// </summary>
        private System.Windows.Forms.Timer _autoExportTimer;

        /// <summary>
        /// 获取捕获的请求总数
        /// </summary>
        public int CapturedRequestsCount => _allCapturedRequests.Count;

        /// <summary>
        /// 获取捕获的API请求数量
        /// </summary>
        public int ApiRequestsCount => _allCapturedRequests.Count(r =>
            r.ContainsKey("request-type") && r["request-type"] == "API");

        #endregion 字段和属性

        #region 构造方法

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logSource">用于记录日志的对象</param>
        /// <param name="headersPath">标头文件路径（兼容旧参数名cookiePath）</param>
        /// <param name="url">当前URL</param>
        public WebBrowserCookieManager(object logSource, string headersPath = "", string url = "")
        {
            _logSource = logSource ?? this;
            HeadersPath = headersPath;
            CurrentUrl = url;
        }

        /// <summary>
        /// 构造函数（保持向后兼容）
        /// </summary>
        /// <param name="logSource">用于记录日志的对象</param>
        /// <param name="cookiePath">Cookie文件路径</param>
        /// <param name="url">当前URL</param>
        [Obsolete("请使用 WebBrowserCookieManager(logSource, headersPath, url) 构造函数")]
        public static WebBrowserCookieManager CreateWithCookiePath(object logSource, string cookiePath = "", string url = "")
        {
            return new WebBrowserCookieManager(logSource, cookiePath, url);
        }

        #endregion 构造方法

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放托管和非托管资源
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
            {
                return;
            }

            if (disposing)
            {
                try
                {
                    // 停止并释放自动导出定时器
                    if (_autoExportTimer != null)
                    {
                        _autoExportTimer.Stop();
                        _autoExportTimer.Dispose();
                        _autoExportTimer = null;
                    }

                    // 清空Cookie数据
                    if (CookieData != null && CookieData.Cookies != null)
                    {
                        CookieData.Cookies.Clear();
                    }

                    // 清空标头数据
                    _allCapturedRequests.Clear();
                    _lastRequestHeaders.Clear();
                    _headersJson = string.Empty;
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, $"释放CookieManager资源时出错: {ex.Message}");
                }
            }

            _disposed = true;
        }

        /// <summary>
        /// 终结器
        /// </summary>
        ~WebBrowserCookieManager()
        {
            Dispose(false);
        }

        #endregion IDisposable实现

        #region WebView2事件处理

        /// <summary>
        /// 设置WebView2的标头捕获
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        public async Task SetupHeadersCaptureAsync(WebView2 webView)
        {
            try
            {
                if (webView == null)
                {
                    ETLogManager.Warning(_logSource, "设置标头捕获失败: WebView2为空");
                    return;
                }

                // 确保在UI线程上执行
                if (webView.InvokeRequired)
                {
                    TaskCompletionSource<bool> tcs = new TaskCompletionSource<bool>();

                    webView.BeginInvoke(new Action(async () =>
                    {
                        try
                        {
                            await SetupHeadersCaptureInternalAsync(webView).ConfigureAwait(false);
                            tcs.SetResult(true);
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error(_logSource, $"设置标头捕获失败（UI线程）: {ex.Message}");
                            tcs.SetException(ex);
                        }
                    }));

                    await tcs.Task.ConfigureAwait(false);
                }
                else
                {
                    await SetupHeadersCaptureInternalAsync(webView).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"设置WebView2标头捕获失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 内部标头捕获设置方法，必须在UI线程上调用
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        private Task SetupHeadersCaptureInternalAsync(WebView2 webView)
        {
            try
            {
                // 在UI线程上安全地检查CoreWebView2
                CoreWebView2 coreWebView2 = null;
                try
                {
                    coreWebView2 = webView.CoreWebView2;
                }
                catch (InvalidOperationException ex)
                {
                    ETLogManager.Warning(_logSource, $"设置标头捕获失败: 无法访问CoreWebView2 - {ex.Message}");
                    return Task.CompletedTask;
                }

                if (coreWebView2 == null)
                {
                    ETLogManager.Warning(_logSource, "设置标头捕获失败: CoreWebView2为空");
                    return Task.CompletedTask;
                }

                // 添加网络资源请求过滤器，捕获所有请求
                coreWebView2.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);

                // 注册网络资源请求事件
                coreWebView2.WebResourceRequested += WebView_WebResourceRequested;

                ETLogManager.Info(_logSource, "已设置WebView2标头捕获");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"内部设置标头捕获失败: {ex.Message}");
                return Task.CompletedTask;
            }
        }

        /// <summary>
        /// 移除WebView2的标头捕获
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        public void RemoveHeadersCapture(WebView2 webView)
        {
            try
            {
                if (webView == null)
                {
                    return;
                }

                // 确保在UI线程上执行
                if (webView.InvokeRequired)
                {
                    webView.BeginInvoke(new Action(() =>
                    {
                        try
                        {
                            RemoveHeadersCaptureInternal(webView);
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error(_logSource, $"移除标头捕获失败（UI线程）: {ex.Message}");
                        }
                    }));
                }
                else
                {
                    RemoveHeadersCaptureInternal(webView);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"移除WebView2标头捕获失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 内部移除标头捕获方法，必须在UI线程上调用
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        private void RemoveHeadersCaptureInternal(WebView2 webView)
        {
            try
            {
                // 在UI线程上安全地检查CoreWebView2
                CoreWebView2 coreWebView2 = null;
                try
                {
                    coreWebView2 = webView.CoreWebView2;
                }
                catch (InvalidOperationException ex)
                {
                    ETLogManager.Warning(_logSource, $"移除标头捕获失败: 无法访问CoreWebView2 - {ex.Message}");
                    return;
                }

                if (coreWebView2 != null)
                {
                    coreWebView2.WebResourceRequested -= WebView_WebResourceRequested;
                    ETLogManager.Info(_logSource, "已移除WebView2标头捕获");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"内部移除标头捕获失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 网络资源请求事件处理，用于获取完整的HTTP请求标头（包括API接口）
        /// </summary>
        private void WebView_WebResourceRequested(object sender, CoreWebView2WebResourceRequestedEventArgs e)
        {
            try
            {
                var request = e.Request;
                var uri = request.Uri;

                // 创建请求信息字典
                var requestInfo = new Dictionary<string, string>();

                // 添加基本信息
                requestInfo["url"] = uri;
                requestInfo["method"] = request.Method;
                requestInfo["timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");

                // 添加所有请求标头
                foreach (var header in request.Headers)
                {
                    requestInfo[header.Key.ToLower()] = header.Value;
                }

                // 判断是否为API请求（简单判断：包含api、ajax、xhr等关键词或特定文件扩展名）
                bool isApiRequest = IsApiRequest(uri);

                // 标记请求类型
                requestInfo["request-type"] = isApiRequest ? "API" : "Resource";
                requestInfo["source"] = "WebResourceRequested";

                // 添加当前网页URL
                if (!string.IsNullOrEmpty(CurrentUrl))
                {
                    requestInfo["page-url"] = CurrentUrl;
                }

                // 添加到捕获列表
                _allCapturedRequests.Add(requestInfo);

                // 如果是API请求，更新最后的标头信息
                if (isApiRequest)
                {
                    _lastRequestHeaders = new Dictionary<string, string>(requestInfo);
                    _headersJson = JsonConvert.SerializeObject(requestInfo, Formatting.Indented);

                    Debug.Print($"=== 捕获到API请求 ===");
                    Debug.Print($"时间: {requestInfo["timestamp"]}");
                    Debug.Print($"URL: {uri}");
                    Debug.Print($"方法: {request.Method}");
                    Debug.Print($"标头数量: {request.Headers.Count()}");
                }

                // 限制捕获列表大小，避免内存过多占用
                if (_allCapturedRequests.Count > 100)
                {
                    _allCapturedRequests.RemoveAt(0);
                }

                // 输出统计信息
                var totalRequests = _allCapturedRequests.Count;
                var apiRequests = _allCapturedRequests.Count(r => r.ContainsKey("request-type") && r["request-type"] == "API");
                Debug.Print($"[统计] 总请求: {totalRequests}, API请求: {apiRequests}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"处理网络资源请求时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否为API请求
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <returns>是否为API请求</returns>
        private bool IsApiRequest(string url)
        {
            if (string.IsNullOrEmpty(url))
                return false;

            var lowerUrl = url.ToLower();

            // API关键词判断
            var apiKeywords = new[] { "/api/", "/ajax/", "/xhr/", "/rest/", "/service/", "/webservice/" };
            if (apiKeywords.Any(keyword => lowerUrl.Contains(keyword)))
                return true;

            // 文件扩展名判断（排除静态资源）
            var staticExtensions = new[] { ".js", ".css", ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg", ".woff", ".woff2", ".ttf", ".eot" };
            if (staticExtensions.Any(ext => lowerUrl.EndsWith(ext)))
                return false;

            return false;
        }

        #endregion WebView2事件处理

        #region Cookie管理方法

        /// <summary>
        /// 使用脚本获取当前页面的Cookie
        /// </summary>
        private async Task<List<CookieItem>> GetCookiesFromDocumentAsync(WebView2 webView)
        {
            List<CookieItem> cookieItems = new List<CookieItem>();

            try
            {
                if (webView == null)
                {
                    return cookieItems;
                }

                // 在UI线程上安全地检查CoreWebView2
                CoreWebView2 coreWebView2 = null;
                try
                {
                    coreWebView2 = webView.CoreWebView2;
                }
                catch (InvalidOperationException ex)
                {
                    ETLogManager.Warning(_logSource, $"获取Cookie失败: 无法访问CoreWebView2 - {ex.Message}");
                    return cookieItems;
                }

                if (coreWebView2 == null)
                {
                    return cookieItems;
                }

                // 使用JavaScript获取document.cookie
                string cookieScript = "document.cookie";
                string documentCookies = await coreWebView2.ExecuteScriptAsync(cookieScript).ConfigureAwait(true);

                // ExecuteScriptAsync返回的是JSON字符串，需要解析
                documentCookies = JsonConvert.DeserializeObject<string>(documentCookies);

                if (string.IsNullOrEmpty(documentCookies))
                {
                    ETLogManager.Info(_logSource, "document.cookie为空");
                    return cookieItems;
                }

                ETLogManager.Info(_logSource, $"获取到document.cookie: {documentCookies}");

                // 解析cookie字符串
                string[] cookies = documentCookies.Split(';');
                foreach (string cookieStr in cookies)
                {
                    try
                    {
                        string[] parts = cookieStr.Trim().Split('=');
                        if (parts.Length >= 2)
                        {
                            string name = parts[0].Trim();
                            string value = string.Join("=", parts.Skip(1)).Trim(); // 处理值中可能包含=的情况

                            CookieItem cookieItem = new CookieItem
                            {
                                Name = name,
                                Value = value,
                                // 由于document.cookie无法获取这些属性，使用默认值
                                Domain = new Uri(CurrentUrl).Host,
                                Path = "/",
                                HttpOnly = false, // document.cookie无法获取HttpOnly cookie
                                Secure = webView.Source.Scheme.Equals("https", StringComparison.OrdinalIgnoreCase)
                            };

                            cookieItems.Add(cookieItem);
                        }
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"解析document.cookie失败: {ex.Message}");
                    }
                }

                ETLogManager.Info(_logSource, $"从document.cookie解析出 {cookieItems.Count} 个Cookie");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"使用脚本获取Cookie失败: {ex.Message}");
            }

            return cookieItems;
        }

        /// <summary>
        /// 通过执行脚本直接获取JSESSIONID
        /// </summary>
        private async Task<CookieItem> GetJSessionIdAsync(WebView2 webView)
        {
            try
            {
                if (webView == null)
                {
                    return null;
                }

                // 在UI线程上安全地检查CoreWebView2
                CoreWebView2 coreWebView2 = null;
                try
                {
                    coreWebView2 = webView.CoreWebView2;
                }
                catch (InvalidOperationException ex)
                {
                    ETLogManager.Warning(_logSource, $"获取JSessionId失败: 无法访问CoreWebView2 - {ex.Message}");
                    return null;
                }

                if (coreWebView2 == null)
                {
                    return null;
                }

                // 使用JavaScript获取JSESSIONID
                string script = @"
                (function() {
                    // 查找document.cookie中的JSESSIONID
                    let cookies = document.cookie.split(';');
                    for (let cookie of cookies) {
                        cookie = cookie.trim();
                        if (cookie.startsWith('JSESSIONID=')) {
                            return cookie.substring('JSESSIONID='.length);
                        }
                    }

                    // 从localStorage查找
                    let jsessionId = localStorage.getItem('JSESSIONID');
                    if (jsessionId) return jsessionId;

                    // 从所有a标签中查找URL中的jsessionid参数
                    let links = document.getElementsByTagName('a');
                    for (let link of links) {
                        let href = link.href;
                        if (href.includes('jsessionid=')) {
                            let match = href.match(/jsessionid=([^&;]+)/);
                            if (match && match[1]) return match[1];
                        }
                    }

                    // 从所有form标签中查找隐藏的jsessionid字段
                    let forms = document.getElementsByTagName('form');
                    for (let form of forms) {
                        let inputs = form.getElementsByTagName('input');
                        for (let input of inputs) {
                            if (input.name === 'jsessionid' || input.name === 'JSESSIONID') {
                                return input.value;
                            }
                        }
                    }

                    return null;
                })();
                ";

                string resultJson = await coreWebView2.ExecuteScriptAsync(script).ConfigureAwait(true);
                string jsessionId = JsonConvert.DeserializeObject<string>(resultJson);

                if (!string.IsNullOrEmpty(jsessionId))
                {
                    ETLogManager.Info(_logSource, $"找到JSESSIONID: {jsessionId}");
                    return new CookieItem
                    {
                        Name = "JSESSIONID",
                        Value = jsessionId,
                        Domain = new Uri(CurrentUrl).Host,
                        Path = "/",
                        HttpOnly = true  // JSESSIONID通常是HttpOnly的
                    };
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"获取JSESSIONID失败: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 从WebView2获取Cookie并保存到文件（保持向后兼容）
        /// </summary>
        public async Task<CookieData> GetCookiesFromWebView2Async(WebView2 webView)
        {
            try
            {
                if (webView == null)
                {
                    ETLogManager.Error(_logSource, "获取Cookie失败: WebView2为空");
                    return null;
                }

                // 如果不在UI线程，则切换到UI线程执行所有操作
                if (webView.InvokeRequired)
                {
                    TaskCompletionSource<CookieData> tcs = new TaskCompletionSource<CookieData>();
                    webView.Invoke(new Action(async () =>
                    {
                        try
                        {
                            CookieData result = await GetCookiesFromWebView2InternalAsync(webView).ConfigureAwait(true);
                            tcs.SetResult(result);
                        }
                        catch (Exception ex)
                        {
                            tcs.SetException(ex);
                        }
                    }));
                    return await tcs.Task.ConfigureAwait(true);
                }
                else
                {
                    return await GetCookiesFromWebView2InternalAsync(webView).ConfigureAwait(true);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"获取Cookie失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 在UI线程上执行的内部获取Cookie方法
        /// </summary>
        private async Task<CookieData> GetCookiesFromWebView2InternalAsync(WebView2 webView)
        {
            try
            {
                if (webView == null)
                {
                    ETLogManager.Error(_logSource, "获取Cookie失败: WebView2为空");
                    return null;
                }

                // 在UI线程上安全地检查CoreWebView2
                CoreWebView2 coreWebView2 = null;
                try
                {
                    coreWebView2 = webView.CoreWebView2;
                }
                catch (InvalidOperationException ex)
                {
                    ETLogManager.Error(_logSource, $"获取Cookie失败: 无法访问CoreWebView2 - {ex.Message}");
                    return null;
                }

                if (coreWebView2 == null)
                {
                    ETLogManager.Error(_logSource, "获取Cookie失败: CoreWebView2为空");
                    return null;
                }

                // 获取当前页面的URL
                string currentUrl = webView.Source?.ToString() ?? string.Empty;
                if (string.IsNullOrEmpty(currentUrl))
                {
                    ETLogManager.Warning(_logSource, "获取Cookie失败: 当前页面URL为空");
                    return null;
                }

                // 创建Cookie数据对象
                CookieData cookieData = new CookieData
                {
                    Url = currentUrl,
                    Cookies = new List<CookieItem>()
                };

                try
                {
                    // 解析当前URL
                    Uri currentUri = new Uri(currentUrl);
                    string currentDomain = currentUri.Host;

                    // 1. 使用WebView2内置方法获取当前域名的Cookie
                    List<CoreWebView2Cookie> cookieList;
                    try
                    {
                        cookieList = await coreWebView2.CookieManager.GetCookiesAsync(currentUrl).ConfigureAwait(true);
                        ETLogManager.Info(_logSource, $"WebView2内置方法获取了 {cookieList.Count} 个Cookie");
                    }
                    catch (NotImplementedException ex)
                    {
                        ETLogManager.Warning(_logSource, $"当前WebView2运行时版本不支持GetCookiesAsync方法: {ex.Message}");
                        cookieList = new List<CoreWebView2Cookie>();
                    }
                    catch (InvalidCastException ex)
                    {
                        ETLogManager.Warning(_logSource, $"WebView2版本兼容性问题: {ex.Message}");
                        cookieList = new List<CoreWebView2Cookie>();
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"获取Cookie时发生异常: {ex.Message}");
                        cookieList = new List<CoreWebView2Cookie>();
                    }

                    // 添加从WebView2获取的Cookie（只添加当前域名的）
                    foreach (CoreWebView2Cookie cookie in cookieList)
                    {
                        try
                        {
                            // 检查cookie是否属于当前域名
                            if (!string.IsNullOrEmpty(cookie.Domain) &&
                                (cookie.Domain.TrimStart('.') == currentDomain ||
                                 currentDomain.EndsWith(cookie.Domain.TrimStart('.'))))
                            {
                                CookieItem cookieItem = new CookieItem
                                {
                                    Domain = cookie.Domain,
                                    Path = cookie.Path,
                                    Name = cookie.Name,
                                    Value = cookie.Value,
                                    Expires = cookie.Expires,
                                    Secure = cookie.IsSecure,
                                    HttpOnly = cookie.IsHttpOnly,
                                    SameSite = cookie.SameSite.ToString()
                                };

                                cookieData.Cookies.Add(cookieItem);
                            }
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error(_logSource, $"处理单个Cookie时发生异常: {ex.Message}");
                            continue;
                        }
                    }

                    // 2. 使用脚本获取document.cookie中的Cookie
                    List<CookieItem> documentCookies = await GetCookiesFromDocumentAsync(webView).ConfigureAwait(true);
                    foreach (CookieItem cookie in documentCookies)
                    {
                        // 检查是否已存在相同名称的Cookie且属于当前域名
                        if (!cookieData.Cookies.Any(c => c.Name == cookie.Name) &&
                            (!string.IsNullOrEmpty(cookie.Domain) &&
                             (cookie.Domain.TrimStart('.') == currentDomain ||
                              currentDomain.EndsWith(cookie.Domain.TrimStart('.')))))
                        {
                            cookieData.Cookies.Add(cookie);
                        }
                    }

                    // 3. 尝试获取JSESSIONID (特殊处理)
                    CookieItem jsessionIdCookie = await GetJSessionIdAsync(webView).ConfigureAwait(true);
                    if (jsessionIdCookie != null &&
                        !cookieData.Cookies.Any(c => c.Name == jsessionIdCookie.Name) &&
                        (!string.IsNullOrEmpty(jsessionIdCookie.Domain) &&
                         (jsessionIdCookie.Domain.TrimStart('.') == currentDomain ||
                          currentDomain.EndsWith(jsessionIdCookie.Domain.TrimStart('.')))))
                    {
                        cookieData.Cookies.Add(jsessionIdCookie);
                    }

                    // 更新当前Cookie数据
                    CookieData = cookieData;

                    // 保存到文件（如果指定了路径）
                    if (!string.IsNullOrEmpty(CookiePath))
                    {
                        await SaveCookiesToFileAsync(CookiePath).ConfigureAwait(true);
                    }

                    ETLogManager.Info(_logSource, $"当前页面总共获取了 {cookieData.Cookies.Count} 个Cookie");
                    return cookieData;
                }
                catch (UriFormatException ex)
                {
                    ETLogManager.Error(_logSource, $"解析URL失败: {ex.Message}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"获取Cookie失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 将Cookie设置到WebView2
        /// </summary>
        public async Task SetCookiesToWebView2Async(WebView2 webView, CookieData cookieData = null)
        {
            try
            {
                if (webView == null)
                {
                    ETLogManager.Error(_logSource, "设置Cookie失败: WebView2为空");
                    return;
                }

                // 使用当前Cookie数据或传入的数据
                CookieData data = cookieData ?? CookieData;
                if (data?.Cookies == null || data.Cookies.Count == 0)
                {
                    ETLogManager.Warning(_logSource, "没有Cookie可设置");
                    return;
                }

                // 如果传入了CookieData且含有URL，则更新当前URL
                if (cookieData != null && !string.IsNullOrEmpty(cookieData.Url))
                {
                    CurrentUrl = cookieData.Url;
                }

                // 确保在UI线程上设置Cookie
                if (webView.InvokeRequired)
                {
                    // 使用TaskCompletionSource确保异步操作完成
                    TaskCompletionSource<bool> tcs = new TaskCompletionSource<bool>();

                    webView.BeginInvoke(new Action(async () =>
                    {
                        try
                        {
                            await SetCookiesInternal(webView, data).ConfigureAwait(false);
                            ETLogManager.Debug(_logSource, "Cookie设置完成（通过BeginInvoke）");
                            tcs.SetResult(true);
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error(_logSource, $"设置Cookie失败（BeginInvoke）: {ex.Message}");
                            tcs.SetException(ex);
                        }
                    }));

                    // 等待UI线程操作完成
                    await tcs.Task.ConfigureAwait(false);
                }
                else
                {
                    await SetCookiesInternal(webView, data).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"设置Cookie失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 内部Cookie设置方法，必须在UI线程上调用
        /// </summary>
        private async Task SetCookiesInternal(WebView2 webView, CookieData data)
        {
            try
            {
                if (webView == null)
                {
                    ETLogManager.Error(_logSource, "设置Cookie失败: WebView2为空");
                    return;
                }

                // 在UI线程上安全地检查CoreWebView2
                CoreWebView2 coreWebView2 = null;
                try
                {
                    coreWebView2 = webView.CoreWebView2;
                }
                catch (InvalidOperationException ex)
                {
                    ETLogManager.Error(_logSource, $"设置Cookie失败: 无法访问CoreWebView2 - {ex.Message}");
                    return;
                }

                if (coreWebView2 == null)
                {
                    ETLogManager.Error(_logSource, "设置Cookie失败: CoreWebView2为空");
                    return;
                }

                int setCookieCount = 0;
                int failedCookieCount = 0;

                foreach (CookieItem cookieItem in data.Cookies)
                {
                    try
                    {
                        // 验证Cookie数据
                        if (string.IsNullOrEmpty(cookieItem.Name))
                        {
                            ETLogManager.Warning(_logSource, "跳过空名称的Cookie");
                            failedCookieCount++;
                            continue;
                        }

                        // 确保Domain和Path不为空
                        string domain = string.IsNullOrEmpty(cookieItem.Domain) ?
                            (string.IsNullOrEmpty(CurrentUrl) ? "localhost" : new Uri(CurrentUrl).Host) :
                            cookieItem.Domain;
                        string path = string.IsNullOrEmpty(cookieItem.Path) ? "/" : cookieItem.Path;

                        ETLogManager.Info(_logSource, $"设置Cookie: {cookieItem.Name}=*****, Domain={domain}, Path={path}");

                        // 删除现有的同名Cookie（避免死锁）
                        try
                        {
                            var existingCookies = await coreWebView2.CookieManager.GetCookiesAsync(CurrentUrl).ConfigureAwait(true);
                            var existingCookie = existingCookies.FirstOrDefault(c => c.Name == cookieItem.Name);
                            if (existingCookie != null)
                            {
                                coreWebView2.CookieManager.DeleteCookie(existingCookie);
                                ETLogManager.Info(_logSource, $"删除现有Cookie: {cookieItem.Name}");
                            }
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Warning(_logSource, $"删除现有Cookie时出错: {ex.Message}");
                        }

                        // 创建CoreWebView2Cookie
                        CoreWebView2Cookie cookie = coreWebView2.CookieManager.CreateCookie(
                            cookieItem.Name,
                            cookieItem.Value ?? string.Empty,
                            domain,
                            path);

                        // 设置过期时间
                        if (cookieItem.Expires > DateTime.MinValue && cookieItem.Expires > DateTime.Now)
                        {
                            cookie.Expires = cookieItem.Expires;
                        }
                        else
                        {
                            // 如果没有设置过期时间或已过期，设置为会话Cookie（不设置Expires）
                            cookie.Expires = DateTime.Now.AddYears(1); // 设置一个较长的过期时间
                            ETLogManager.Info(_logSource, $"Cookie {cookieItem.Name} 设置为长期Cookie");
                        }

                        cookie.IsHttpOnly = cookieItem.HttpOnly;
                        cookie.IsSecure = cookieItem.Secure;

                        // 设置SameSite
                        if (!string.IsNullOrEmpty(cookieItem.SameSite) &&
                            Enum.TryParse<CoreWebView2CookieSameSiteKind>(cookieItem.SameSite, out CoreWebView2CookieSameSiteKind sameSite))
                        {
                            cookie.SameSite = sameSite;
                        }
                        else
                        {
                            // 默认设置为None以确保跨站点兼容性
                            cookie.SameSite = CoreWebView2CookieSameSiteKind.None;
                        }

                        // 添加或更新Cookie
                        coreWebView2.CookieManager.AddOrUpdateCookie(cookie);
                        setCookieCount++;

                        ETLogManager.Info(_logSource, $"成功设置Cookie: {cookieItem.Name}");
                    }
                    catch (Exception ex)
                    {
                        failedCookieCount++;
                        ETLogManager.Error(_logSource, $"设置Cookie失败: {cookieItem.Name}, 错误: {ex.Message}");
                    }
                }

                ETLogManager.Info(_logSource, $"Cookie设置完成: 成功 {setCookieCount} 个, 失败 {failedCookieCount} 个");

                // 测试：验证Cookie设置是否有效
                await VerifyCookiesAfterSetting(webView).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"内部设置Cookie失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证Cookie设置后的结果，用于测试
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        private async Task VerifyCookiesAfterSetting(WebView2 webView)
        {
            try
            {
                if (webView == null || string.IsNullOrEmpty(CurrentUrl))
                {
                    ETLogManager.Warning(_logSource, "无法验证Cookie设置：WebView2或URL为空");
                    return;
                }

                // 在UI线程上安全地检查CoreWebView2
                CoreWebView2 coreWebView2 = null;
                try
                {
                    coreWebView2 = webView.CoreWebView2;
                }
                catch (InvalidOperationException ex)
                {
                    ETLogManager.Warning(_logSource, $"无法验证Cookie设置：无法访问CoreWebView2 - {ex.Message}");
                    return;
                }

                if (coreWebView2 == null)
                {
                    ETLogManager.Warning(_logSource, "无法验证Cookie设置：CoreWebView2为空");
                    return;
                }

                // 等待一小段时间确保Cookie设置生效
                await Task.Delay(200).ConfigureAwait(true);

                // 获取当前URL的所有Cookie
                var currentCookies = await coreWebView2.CookieManager.GetCookiesAsync(CurrentUrl).ConfigureAwait(true);

                ETLogManager.Info(_logSource, $"=== Cookie设置验证 ===");
                ETLogManager.Info(_logSource, $"目标URL: {CurrentUrl}");
                ETLogManager.Info(_logSource, $"WebView2中实际Cookie数量: {currentCookies.Count}");

                if (currentCookies.Count > 0)
                {
                    ETLogManager.Info(_logSource, $"WebView2中的Cookie列表: {currentCookies.Count}个");
                    // 只在Debug模式下显示详细Cookie信息
                    if (ETLogManager.IsDebugEnabled)
                    {
                        foreach (var cookie in currentCookies)
                        {
                            ETLogManager.Debug(_logSource, $"  ✓ {cookie.Name}=***** (Domain: {cookie.Domain}, Path: {cookie.Path})");
                        }
                    }
                }
                else
                {
                    ETLogManager.Warning(_logSource, "警告：WebView2中没有找到任何Cookie！");
                }

                // 同时通过JavaScript验证document.cookie
                try
                {
                    string documentCookieScript = "document.cookie";
                    string documentCookies = await webView.CoreWebView2.ExecuteScriptAsync(documentCookieScript).ConfigureAwait(true);

                    // ExecuteScriptAsync返回的是JSON字符串，需要解析
                    documentCookies = JsonConvert.DeserializeObject<string>(documentCookies);

                    ETLogManager.Info(_logSource, $"document.cookie内容: {documentCookies ?? "(空)"}");
                }
                catch (Exception jsEx)
                {
                    ETLogManager.Warning(_logSource, $"无法通过JavaScript获取document.cookie: {jsEx.Message}");
                }

                ETLogManager.Info(_logSource, "=== Cookie验证完成 ===");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"验证Cookie设置时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取用于HttpClient的CookieContainer
        /// </summary>
        public CookieContainer GetCookieContainerForHttpClient()
        {
            CookieContainer cookieContainer = new CookieContainer();

            if (CookieData?.Cookies == null || CookieData.Cookies.Count == 0)
            {
                return cookieContainer;
            }

            try
            {
                Uri uri = new Uri(CurrentUrl);
                foreach (CookieItem cookieItem in CookieData.Cookies)
                {
                    try
                    {
                        // 创建Cookie
                        Cookie cookie = new Cookie(cookieItem.Name, cookieItem.Value)
                        {
                            Domain = !string.IsNullOrEmpty(cookieItem.Domain) ?
                                    cookieItem.Domain.TrimStart('.') : uri.Host,
                            Path = cookieItem.Path,
                            HttpOnly = cookieItem.HttpOnly,
                            Secure = cookieItem.Secure
                        };

                        if (cookieItem.Expires > DateTime.MinValue)
                        {
                            cookie.Expires = cookieItem.Expires;
                        }

                        cookieContainer.Add(cookie);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"添加Cookie到CookieContainer失败: {cookieItem.Name}, {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"创建CookieContainer失败: {ex.Message}");
            }

            return cookieContainer;
        }

        /// <summary>
        /// 从HttpClient响应更新Cookie
        /// </summary>
        public void UpdateCookiesFromHttpClient(HttpResponseMessage response)
        {
            if (response?.Headers == null || !response.Headers.Contains("Set-Cookie"))
            {
                return;
            }

            try
            {
                IEnumerable<string> setCookieHeaders = response.Headers.GetValues("Set-Cookie");
                if (setCookieHeaders == null)
                {
                    return;
                }

                foreach (string setCookieHeader in setCookieHeaders)
                {
                    try
                    {
                        // 解析Set-Cookie头
                        string[] parts = setCookieHeader.Split(';');
                        if (parts.Length == 0)
                        {
                            continue;
                        }

                        // 解析Cookie名称和值
                        string[] nameValue = parts[0].Split('=');
                        if (nameValue.Length < 2)
                        {
                            continue;
                        }

                        string name = nameValue[0].Trim();
                        string value = nameValue[1].Trim();

                        // 创建或更新Cookie
                        CookieItem cookieItem = CookieData.Cookies.Find(c => c.Name == name && c.Domain == new Uri(CurrentUrl).Host);
                        if (cookieItem == null)
                        {
                            cookieItem = new CookieItem
                            {
                                Name = name,
                                Domain = new Uri(CurrentUrl).Host,
                                Path = "/"
                            };
                            CookieData.Cookies.Add(cookieItem);
                        }

                        cookieItem.Value = value;
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"解析Set-Cookie头失败: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"从HttpClient响应更新Cookie失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存Cookie到JSON文件（新格式：包含url、headers、cookies三部分）
        /// </summary>
        public async Task SaveCookiesToFileAsync(string filePath = null)
        {
            string path = filePath ?? CookiePath;
            if (string.IsNullOrEmpty(path))
            {
                ETLogManager.Warning(_logSource, "保存Cookie失败: 未指定文件路径");
                return;
            }

            if (CookieData == null)
            {
                ETLogManager.Warning(_logSource, "保存Cookie失败: CookieData为null");
                return;
            }

            // 重试次数和延迟
            int maxRetries = 3;
            int retryDelayMs = 500;
            int retryCount = 0;

            while (retryCount < maxRetries)
            {
                try
                {
                    // 确保目录存在
                    string cookieDir = Path.GetDirectoryName(path);
                    if (!string.IsNullOrEmpty(cookieDir) && !Directory.Exists(cookieDir))
                    {
                        Directory.CreateDirectory(cookieDir);
                    }

                    // 即使Cookie为空，也保存当前的URL信息
                    if (CookieData.Cookies == null)
                    {
                        CookieData.Cookies = new List<CookieItem>();
                    }

                    // 使用通用模块构建新格式的JSON数据
                    var options = new ETWebBrowserJsonFormatter.HeadersOptions
                    {
                        Source = "WebBrowserCookieManager",
                        RequestType = "Page",
                        ExtraHeaders = CreateExtraHeadersFromCurrentData()
                    };

                    // 转换CookieItem列表为ET类型
                    var etCookieItems = ConvertToETCookieItems(CookieData.Cookies);

                    // 序列化为JSON
                    string json = ETWebBrowserJsonFormatter.CreateLoginInfoJson(
                        CookieData.Url ?? CurrentUrl ?? string.Empty,
                        null, // 标头通过options.ExtraHeaders传递
                        etCookieItems,
                        options);

                    // 使用FileStream和FileShare，而不是直接用File.WriteAllText
                    await Task.Run(() =>
                    {
                        try
                        {
                            // 创建文件互斥锁名称 - 将文件路径转换为有效的互斥锁名称
                            string mutexName = $"Global\\{path.Replace('\\', '_').Replace(':', '_').Replace('/', '_')}";
                            bool mutexCreated = false;

                            // 尝试获取互斥锁
                            using (Mutex mutex = new System.Threading.Mutex(false, mutexName, out mutexCreated))
                            {
                                // 尝试获取互斥锁，等待最多3秒
                                bool mutexAcquired = mutex.WaitOne(3000);
                                try
                                {
                                    // 即使没能获取到互斥锁，也尝试使用FileShare方式写入
                                    // 使用FileShare.ReadWrite允许其他进程同时读写文件 但FILE_SHARE_DELETE标志未设置，防止文件在写入时被删除
                                    using (FileStream fs = new FileStream(
                                        path,
                                        FileMode.Create,
                                        FileAccess.Write,
                                        FileShare.ReadWrite,
                                        4096,
                                        FileOptions.WriteThrough))
                                    using (StreamWriter writer = new StreamWriter(fs, System.Text.Encoding.UTF8))
                                    {
                                        writer.Write(json);
                                        writer.Flush();
                                        fs.Flush(true); // 强制将所有缓冲数据写入磁盘
                                    }
                                }
                                finally
                                {
                                    // 如果获取了互斥锁，释放它
                                    if (mutexAcquired)
                                    {
                                        mutex.ReleaseMutex();
                                    }
                                }
                            }
                        }
                        catch (System.Threading.AbandonedMutexException)
                        {
                            // 互斥锁被遗弃，尝试直接写入
                            using (FileStream fs = new FileStream(
                                path,
                                FileMode.Create,
                                FileAccess.Write,
                                FileShare.ReadWrite,
                                4096,
                                FileOptions.WriteThrough))
                            using (StreamWriter writer = new StreamWriter(fs, System.Text.Encoding.UTF8))
                            {
                                writer.Write(json);
                                writer.Flush();
                                fs.Flush(true);
                            }
                        }
                    }).ConfigureAwait(true);

                    ETLogManager.Info(_logSource, $"已保存 {CookieData.Cookies.Count} 个Cookie到文件: {path}");
                    break; // 成功写入，跳出重试循环
                }
                catch (IOException ex) when (ex.HResult == unchecked((int)0x80070020)) // 文件正被占用
                {
                    retryCount++;
                    if (retryCount >= maxRetries)
                    {
                        ETLogManager.Error(_logSource, $"保存Cookie失败，文件被占用且重试{maxRetries}次仍无法访问: {path}, {ex.Message}");
                        throw; // 重试次数已用完，抛出异常
                    }

                    ETLogManager.Warning(_logSource, $"文件被占用，等待 {retryDelayMs}ms 后重试 ({retryCount}/{maxRetries}): {path}");
                    await Task.Delay(retryDelayMs).ConfigureAwait(true);
                    retryDelayMs *= 2; // 每次重试延长等待时间
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, $"保存Cookie失败: {ex.Message}");
                    break; // 其他类型的异常，直接跳出循环
                }
            }
        }

        /// <summary>
        /// 从JSON文件加载标头
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>标头数据的JSON字符串</returns>
        public async Task<string> LoadHeadersFromFileAsync(string filePath = null)
        {
            string path = filePath ?? HeadersPath;
            if (string.IsNullOrEmpty(path))
            {
                ETLogManager.Warning(_logSource, "加载标头失败: 未指定文件路径");
                return string.Empty;
            }

            if (!File.Exists(path))
            {
                ETLogManager.Warning(_logSource, $"标头文件不存在: {path}");
                return string.Empty;
            }

            // 重试次数和延迟
            int maxRetries = 3;
            int retryCount = 0;
            int retryDelay = 500; // 毫秒

            while (retryCount < maxRetries)
            {
                try
                {
                    // 使用互斥锁防止多个进程同时读取同一文件
                    string mutexName = $"HeadersFile_{Path.GetFileName(path)}";
                    bool mutexCreated = false;

                    // 尝试获取互斥锁
                    using (Mutex mutex = new System.Threading.Mutex(false, mutexName, out mutexCreated))
                    {
                        // 尝试获取互斥锁，等待最多3秒
                        bool mutexAcquired = mutex.WaitOne(3000);
                        try
                        {
                            // 即使没能获取到互斥锁，也尝试使用FileShare方式读取
                            using (FileStream fs = new FileStream(
                                path,
                                FileMode.Open,
                                FileAccess.Read,
                                FileShare.ReadWrite))
                            {
                                byte[] buffer = new byte[fs.Length];
                                await fs.ReadAsync(buffer, 0, buffer.Length).ConfigureAwait(true);
                                _headersJson = Encoding.UTF8.GetString(buffer);
                            }
                        }
                        finally
                        {
                            if (mutexAcquired)
                            {
                                mutex.ReleaseMutex();
                            }
                        }
                    }

                    // 尝试解析JSON以验证格式
                    try
                    {
                        var headersData = JsonConvert.DeserializeObject<Dictionary<string, string>>(_headersJson);
                        if (headersData != null)
                        {
                            if (headersData.ContainsKey("url"))
                            {
                                CurrentUrl = headersData["url"];
                            }

                            // 如果存在cookie字段，解析cookie并创建CookieData对象
                            if (headersData.ContainsKey("cookie"))
                            {
                                var cookieData = new CookieData
                                {
                                    Url = CurrentUrl,
                                    Cookies = new List<CookieItem>()
                                };

                                string[] cookies = headersData["cookie"].Split(';');
                                foreach (string cookieStr in cookies)
                                {
                                    try
                                    {
                                        string[] parts = cookieStr.Trim().Split('=');
                                        if (parts.Length >= 2)
                                        {
                                            string name = parts[0].Trim();
                                            string value = string.Join("=", parts.Skip(1)).Trim(); // 处理值中可能包含=的情况

                                            CookieItem cookieItem = new CookieItem
                                            {
                                                Name = name,
                                                Value = value,
                                                Domain = new Uri(CurrentUrl).Host,
                                                Path = "/",
                                                HttpOnly = true, // 默认设置为true以确保安全性
                                                Secure = CurrentUrl.StartsWith("https", StringComparison.OrdinalIgnoreCase)
                                            };

                                            cookieData.Cookies.Add(cookieItem);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        ETLogManager.Error(_logSource, $"解析单个Cookie失败: {ex.Message}");
                                    }
                                }

                                // 更新当前CookieData
                                CookieData = cookieData;
                                ETLogManager.Info(_logSource, $"从标头JSON解析出 {cookieData.Cookies.Count} 个Cookie");
                            }
                        }
                    }
                    catch (JsonException ex)
                    {
                        ETLogManager.Warning(_logSource, $"标头文件JSON格式无效: {ex.Message}");
                    }

                    ETLogManager.Info(_logSource, $"已从文件加载标头数据: {path}");
                    return _headersJson;
                }
                catch (System.Threading.AbandonedMutexException)
                {
                    // 互斥锁被遗弃，尝试直接读取
                    using (FileStream fs = new FileStream(
                        path,
                        FileMode.Open,
                        FileAccess.Read,
                        FileShare.ReadWrite))
                    {
                        byte[] buffer = new byte[fs.Length];
                        await fs.ReadAsync(buffer, 0, buffer.Length).ConfigureAwait(true);
                        _headersJson = Encoding.UTF8.GetString(buffer);
                    }
                    ETLogManager.Info(_logSource, $"已从文件加载标头数据: {path}");
                    return _headersJson;
                }
                catch (IOException ex) when (ex.HResult == unchecked((int)0x80070020)) // 文件正被占用
                {
                    retryCount++;
                    if (retryCount >= maxRetries)
                    {
                        ETLogManager.Error(_logSource, $"加载标头失败，文件被占用且重试次数已达上限: {ex.Message}");
                        return string.Empty;
                    }
                    ETLogManager.Warning(_logSource, $"文件被占用，{retryDelay}ms后重试 ({retryCount}/{maxRetries}): {ex.Message}");
                    await Task.Delay(retryDelay).ConfigureAwait(true);
                    retryDelay *= 2; // 指数退避
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, $"加载标头失败: {ex.Message}");
                    return string.Empty;
                }
            }

            return string.Empty;
        }

        /// <summary>
        /// 从JSON文件加载Cookie（保持向后兼容）
        /// </summary>
        public async Task<CookieData> LoadCookiesFromFileAsync(string filePath = null)
        {
            string path = filePath ?? CookiePath;
            if (string.IsNullOrEmpty(path))
            {
                ETLogManager.Warning(_logSource, "加载Cookie失败: 未指定文件路径");
                return null;
            }

            if (!File.Exists(path))
            {
                ETLogManager.Warning(_logSource, $"Cookie文件不存在: {path}");
                return null;
            }

            // 重试次数和延迟
            int maxRetries = 3;
            int retryDelayMs = 500;
            int retryCount = 0;

            while (retryCount < maxRetries)
            {
                try
                {
                    // 从文件读取JSON，使用FileShare.ReadWrite允许其他进程读写文件
                    string json = await Task.Run(() =>
                    {
                        try
                        {
                            // 创建文件互斥锁名称
                            string mutexName = $"Global\\{path.Replace('\\', '_').Replace(':', '_').Replace('/', '_')}";
                            bool mutexCreated = false;

                            // 尝试获取互斥锁
                            using (Mutex mutex = new System.Threading.Mutex(false, mutexName, out mutexCreated))
                            {
                                // 尝试获取互斥锁，等待最多3秒
                                bool mutexAcquired = mutex.WaitOne(3000);
                                try
                                {
                                    // 即使没能获取到互斥锁，也尝试使用FileShare方式读取
                                    using (FileStream fs = new FileStream(
                                        path,
                                        FileMode.Open,
                                        FileAccess.Read,
                                        FileShare.ReadWrite,
                                        4096,
                                        FileOptions.SequentialScan))
                                    using (StreamReader reader = new StreamReader(fs, System.Text.Encoding.UTF8))
                                    {
                                        return reader.ReadToEnd();
                                    }
                                }
                                finally
                                {
                                    // 如果获取了互斥锁，释放它
                                    if (mutexAcquired)
                                    {
                                        mutex.ReleaseMutex();
                                    }
                                }
                            }
                        }
                        catch (System.Threading.AbandonedMutexException)
                        {
                            // 互斥锁被遗弃，尝试直接读取
                            using (FileStream fs = new FileStream(
                                path,
                                FileMode.Open,
                                FileAccess.Read,
                                FileShare.ReadWrite,
                                4096,
                                FileOptions.SequentialScan))
                            using (StreamReader reader = new StreamReader(fs, System.Text.Encoding.UTF8))
                            {
                                return reader.ReadToEnd();
                            }
                        }
                    }).ConfigureAwait(true);

                    // 使用通用模块尝试解析新格式或老格式
                    CookieData cookieData = null;

                    // 首先尝试解析为新格式（使用通用模块）
                    if (ETWebBrowserJsonFormatter.IsStandardFormat(json))
                    {
                        try
                        {
                            var loginInfo = ETWebBrowserJsonFormatter.ParseLoginInfoJson(json);
                            cookieData = new CookieData
                            {
                                Url = loginInfo.Url,
                                Cookies = ConvertFromETCookieItems(loginInfo.Cookies)
                            };

                            ETLogManager.Info(_logSource, $"从文件加载新格式数据，包含 {cookieData.Cookies.Count} 个Cookie");
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Warning(_logSource, $"解析新格式数据失败: {ex.Message}");
                        }
                    }

                    // 如果新格式解析失败，尝试老格式
                    if (cookieData == null)
                    {
                        try
                        {
                            cookieData = JsonConvert.DeserializeObject<CookieData>(json);
                            ETLogManager.Info(_logSource, $"从文件加载老格式数据，包含 {cookieData?.Cookies?.Count ?? 0} 个Cookie");
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error(_logSource, $"无法解析Cookie文件格式: {ex.Message}");
                            return null;
                        }
                    }

                    // 更新当前Cookie数据
                    if (cookieData != null)
                    {
                        CookieData = cookieData;
                        if (!string.IsNullOrEmpty(cookieData.Url))
                        {
                            CurrentUrl = cookieData.Url;
                        }
                    }

                    ETLogManager.Info(_logSource, $"已从文件加载 {CookieData?.Cookies?.Count ?? 0} 个Cookie: {path}");
                    return CookieData;
                }
                catch (IOException ex) when (ex.HResult == unchecked((int)0x80070020)) // 文件正被占用
                {
                    retryCount++;
                    if (retryCount >= maxRetries)
                    {
                        ETLogManager.Error(_logSource, $"加载Cookie失败，文件被占用且重试{maxRetries}次仍无法访问: {path}, {ex.Message}");
                        return null; // 重试次数已用完，返回null
                    }

                    ETLogManager.Warning(_logSource, $"文件被占用，等待 {retryDelayMs}ms 后重试 ({retryCount}/{maxRetries}): {path}");
                    await Task.Delay(retryDelayMs).ConfigureAwait(true);
                    retryDelayMs *= 2; // 每次重试延长等待时间
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, $"加载Cookie失败: {ex.Message}");
                    return null;
                }
            }

            return null; // 所有重试失败
        }

        /// <summary>
        /// 从文件导入Cookie到WebView2
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="cookiePath">Cookie文件路径</param>
        /// <returns>导入的Cookie数据</returns>
        public async Task<CookieData> ImportCookiesAsync(WebView2 webView, string cookiePath)
        {
            try
            {
                if (webView == null)
                {
                    ETLogManager.Error(_logSource, "导入Cookie失败: WebView2为空");
                    return null;
                }

                // 从文件加载Cookie（避免死锁）
                CookieData cookieData = await LoadCookiesFromFileAsync(cookiePath).ConfigureAwait(false);
                if (cookieData?.Cookies == null || cookieData.Cookies.Count == 0)
                {
                    ETLogManager.Warning(_logSource, $"Cookie文件中没有有效的Cookie: {cookiePath}");
                    return null;
                }

                // 设置Cookie到WebView2（保持UI线程上下文）
                await SetCookiesToWebView2Async(webView, cookieData).ConfigureAwait(true);

                ETLogManager.Info(_logSource, $"已从文件导入并设置 {cookieData.Cookies.Count} 个Cookie");
                return cookieData;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"导入Cookie失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 设置自动导出Cookie
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="cookiePath">Cookie文件路径</param>
        /// <param name="interval">导出间隔（毫秒）</param>
        /// <returns>定时器</returns>
        public System.Windows.Forms.Timer SetupAutoCookieExport(WebView2 webView, string cookiePath, int interval = 60000)
        {
            try
            {
                // 清理已有的定时器
                if (_autoExportTimer != null)
                {
                    _autoExportTimer.Stop();
                    _autoExportTimer.Dispose();
                    _autoExportTimer = null;
                }

                // 设置Cookie文件路径
                CookiePath = cookiePath;

                // 创建定时器
                System.Windows.Forms.Timer timer = new System.Windows.Forms.Timer
                {
                    Interval = interval,
                    Enabled = true
                };

                // 设置定时器回调事件（使用Task.Run避免在UI线程上执行异步操作）
                timer.Tick += (sender, e) =>
                {
                    // 在后台线程执行异步操作，避免阻塞UI线程
                    Task.Run(async () =>
                    {
                        try
                        {
                            // 如果已释放，则停止定时器
                            if (_disposed)
                            {
                                // 直接停止定时器（Timer的操作是线程安全的）
                                timer.Stop();
                                timer.Dispose();
                                return;
                            }

                            // 检查WebView2是否可用（在UI线程上执行）
                            bool webViewAvailable = false;
                            string currentUrl = null;

                            if (webView.InvokeRequired)
                            {
                                webView.Invoke(new Action(() =>
                                {
                                    try
                                    {
                                        webViewAvailable = webView.CoreWebView2 != null;
                                        if (webViewAvailable)
                                        {
                                            currentUrl = webView.Source?.ToString();
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        ETLogManager.Warning(_logSource, $"检查WebView状态失败: {ex.Message}");
                                        webViewAvailable = false;
                                    }
                                }));
                            }
                            else
                            {
                                try
                                {
                                    webViewAvailable = webView.CoreWebView2 != null;
                                    if (webViewAvailable)
                                    {
                                        currentUrl = webView.Source?.ToString();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    ETLogManager.Warning(_logSource, $"检查WebView状态失败: {ex.Message}");
                                    webViewAvailable = false;
                                }
                            }

                            if (webViewAvailable)
                            {
                                if (!string.IsNullOrEmpty(currentUrl))
                                {
                                    CurrentUrl = currentUrl;
                                }

                                // 从WebView2获取Cookie
                                CookieData cookieData = await GetCookiesFromWebView2Async(webView).ConfigureAwait(false);
                                if (cookieData != null && cookieData.Cookies.Count > 0)
                                {
                                    // 保存Cookie到文件
                                    await SaveCookiesToFileAsync().ConfigureAwait(false);
                                    ETLogManager.Debug(_logSource, $"已自动导出 {cookieData.Cookies.Count} 个Cookie到文件: {CookiePath}");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error(_logSource, $"自动导出Cookie失败: {ex.Message}");
                        }
                    });
                };

                // 保存定时器引用
                _autoExportTimer = timer;

                return timer;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"设置自动导出Cookie失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 设置自动导出Cookie的定时器（旧方法，已过时）
        /// </summary>
        [Obsolete("请使用三参数版本的SetupAutoCookieExport方法")]
        public System.Windows.Forms.Timer SetupAutoCookieExport(WebView2 webView, int interval = 60000)
        {
            return SetupAutoCookieExport(webView, CookiePath, interval);
        }

        /// <summary>
        /// 从当前数据创建额外的标头信息（用于通用模块）
        /// </summary>
        /// <returns>额外标头信息字典</returns>
        private Dictionary<string, object> CreateExtraHeadersFromCurrentData()
        {
            var extraHeaders = new Dictionary<string, object>();

            try
            {
                // 如果有已捕获的标头数据，合并进来
                if (!string.IsNullOrEmpty(_headersJson))
                {
                    try
                    {
                        var existingHeaders = JsonConvert.DeserializeObject<Dictionary<string, string>>(_headersJson);
                        if (existingHeaders != null)
                        {
                            foreach (var kvp in existingHeaders)
                            {
                                // 排除url字段，因为我们在外层已经有了
                                if (kvp.Key.ToLower() != "url" && !extraHeaders.ContainsKey(kvp.Key))
                                {
                                    extraHeaders[kvp.Key] = kvp.Value;
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Warning(_logSource, $"解析已有标头数据失败: {ex.Message}");
                    }
                }

                // 如果有最后捕获的请求标头，也合并进来
                if (_lastRequestHeaders != null && _lastRequestHeaders.Count > 0)
                {
                    foreach (var kvp in _lastRequestHeaders)
                    {
                        if (kvp.Key.ToLower() != "url" && !extraHeaders.ContainsKey(kvp.Key))
                        {
                            extraHeaders[kvp.Key] = kvp.Value;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"创建额外标头数据失败: {ex.Message}");
            }

            return extraHeaders;
        }

        #endregion Cookie管理方法

        #region 标头操作方法

        /// <summary>
        /// 清空所有捕获的请求信息
        /// </summary>
        public void ClearCapturedRequests()
        {
            _allCapturedRequests.Clear();
            _lastRequestHeaders.Clear();
            _headersJson = string.Empty;
            ETLogManager.Info(_logSource, "已清空所有捕获的请求信息");
        }

        /// <summary>
        /// 设置当前URL并更新标头数据
        /// </summary>
        /// <param name="url">当前URL</param>
        public void SetCurrentUrl(string url)
        {
            CurrentUrl = url;

            // 如果有标头数据，更新其中的URL
            if (!string.IsNullOrEmpty(_headersJson))
            {
                try
                {
                    var headersData = JsonConvert.DeserializeObject<Dictionary<string, string>>(_headersJson);
                    if (headersData != null)
                    {
                        headersData["url"] = url;
                        headersData["page-url"] = url;
                        _headersJson = JsonConvert.SerializeObject(headersData, Formatting.Indented);
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Warning(_logSource, $"更新标头URL失败: {ex.Message}");
                }
            }
        }

        #endregion 标头操作方法
    }

    /// <summary>
    /// Cookie数据类，用于JSON序列化
    /// </summary>
    public class CookieData
    {
        /// <summary>
        /// 关联的URL
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// Cookie列表
        /// </summary>
        public List<CookieItem> Cookies { get; set; } = new List<CookieItem>();
    }

    /// <summary>
    /// Cookie项，表示单个Cookie
    /// </summary>
    public class CookieItem
    {
        /// <summary>
        /// 域名
        /// </summary>
        public string Domain { get; set; }

        /// <summary>
        /// 路径
        /// </summary>
        public string Path { get; set; } = "/";

        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 值
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime Expires { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 是否仅HTTP
        /// </summary>
        public bool HttpOnly { get; set; }

        /// <summary>
        /// 是否安全
        /// </summary>
        public bool Secure { get; set; }

        /// <summary>
        /// 同站策略
        /// </summary>
        public string SameSite { get; set; } = "None";
    }

    /// <summary>
    /// WebBrowserCookieManager的扩展方法
    /// </summary>
    public partial class WebBrowserCookieManager
    {
        /// <summary>
        /// 转换HyAssistant.CookieItem列表为ET.ETLoginWebBrowser.CookieItem列表
        /// </summary>
        /// <param name="hyItems">HyAssistant的CookieItem列表</param>
        /// <returns>ET的CookieItem列表</returns>
        public List<ET.ETLoginWebBrowser.CookieItem> ConvertToETCookieItems(List<CookieItem> hyItems)
        {
            if (hyItems == null) return new List<ET.ETLoginWebBrowser.CookieItem>();

            var etItems = new List<ET.ETLoginWebBrowser.CookieItem>();
            foreach (var hyItem in hyItems)
            {
                etItems.Add(new ET.ETLoginWebBrowser.CookieItem
                {
                    Name = hyItem.Name,
                    Value = hyItem.Value,
                    Domain = hyItem.Domain,
                    Path = hyItem.Path,
                    Expires = hyItem.Expires,
                    HttpOnly = hyItem.HttpOnly,
                    Secure = hyItem.Secure,
                    SameSite = hyItem.SameSite
                });
            }
            return etItems;
        }

        /// <summary>
        /// 转换ET.ETLoginWebBrowser.CookieItem列表为HyAssistant.CookieItem列表
        /// </summary>
        /// <param name="etItems">ET的CookieItem列表</param>
        /// <returns>HyAssistant的CookieItem列表</returns>
        private List<CookieItem> ConvertFromETCookieItems(List<ET.ETLoginWebBrowser.CookieItem> etItems)
        {
            if (etItems == null) return new List<CookieItem>();

            var hyItems = new List<CookieItem>();
            foreach (var etItem in etItems)
            {
                hyItems.Add(new CookieItem
                {
                    Name = etItem.Name,
                    Value = etItem.Value,
                    Domain = etItem.Domain,
                    Path = etItem.Path,
                    Expires = etItem.Expires,
                    HttpOnly = etItem.HttpOnly,
                    Secure = etItem.Secure,
                    SameSite = etItem.SameSite
                });
            }
            return hyItems;
        }
    }
}