# 🗂️ ETWebAutomation - Public方法/变量协调字典

## 🎯 阶段说明与类对应关系
- **第一阶段**: 基础架构搭建 ✅**已完成**
  - 涉及类：Models下的所有数据模型类
- **第二阶段**: 登录认证模块 ⏳待开始
  - 涉及类：ETWebLoginBrowser（窗体类）、ETWebAuthStorage
- **第三阶段**: API交互模块 ⏳待开始
  - 涉及类：ETWebApiClient、相关Helper类
- **第四阶段**: 文件上传模块 ✅**已精简完成**
  - 涉及类：ETWebFileUploader ✅、ETWebUploadConfigHelper ✅、ETWebUploadConfigForm ✅
  - 状态：已完成核心功能精简，移除复杂特性，保留基础上传功能
- **第五阶段**: 模拟操作浏览器 ⏳待开始
  - 涉及类：ETWebSimulationBrowser（窗体类）
- **第六阶段**: 会话管理模块 ⏳待开始
  - 涉及类：ETWebSessionManager、ETWebSessionStorage
- **第七阶段**: 主客户端集成 ⏳待开始
  - 涉及类：ETWebClient（主类）
- **第八阶段**: 文档和示例 ⏳待开始
  - 涉及类：Examples下的示例类

## 🔄 最新更新状态
- **✅ 已精简优化**: ETWebFileUploader、ETWebUploadConfigHelper、ETWebUploadConfigForm
- **📝 文档已更新**: README.md、方法变量协调字典.md
- **🎯 下一步计划**: 继续开发登录认证模块或API交互模块

## 📊 精简优化总结

### 🎯 精简策略
| 类名 | 原始复杂度 | 精简后 | 移除功能 | 保留功能 |
|------|------------|--------|----------|----------|
| **ETWebFileUploader** | 700+行 | 292行 | 批量上传、断点续传、并发控制、完整性校验 | 单文件上传、重试机制、基础验证 |
| **ETWebUploadConfigHelper** | 15+配置项 | 6个核心配置 | 高级配置项 | 基础配置管理 |
| **ETWebUploadConfigForm** | 复杂界面 | 简化界面 | 高级功能UI | 基础配置UI |

### 🚀 优化效果
- **代码复杂度降低**: 总体代码量减少约50%
- **维护性提升**: 专注核心功能，减少潜在bug
- **性能优化**: 去除复杂逻辑，提高执行效率
- **用户体验**: 界面简洁，配置更直观

### 🔧 技术债务管理
- **渐进式开发**: 后续可根据需求逐步添加高级功能
- **接口保持**: 保留扩展接口，便于未来功能增强
- **文档同步**: 及时更新文档，确保开发一致性

## 📚 字典说明

### 🎯 核心目的
统一管理ETWebAutomation项目多文件间的Public方法和变量命名，避免开发过程中的调用混乱和命名冲突。

### 🔄 使用流程
1. **步骤开始前**: 仔细阅读字典中已有的类、方法、变量定义
2. **开发过程中**: 严格按照字典中的命名和接口进行开发
3. **步骤完成后**: 立即将新增的public方法和变量添加到字典中
4. **命名冲突**: 如发现命名冲突，优先使用字典中已定义的名称

### ⚠️ 重要规则
- **每个步骤开始前必须读取此字典**
- **每个步骤完成后必须更新此字典**
- **严格按照字典中的方法签名进行调用**
- **新增方法必须包含完整的参数和返回值类型**

---

## 📋 类和方法字典

### 1. ETWebClient类 (ETWebClient.cs)

**类描述**: OA系统自动化客户端主类，整合所有功能模块，提供统一接口，实现IDisposable接口

#### Public方法
```csharp
public ETWebClient(string baseUrl)
    /// <summary>初始化ETWebClient</summary>

public async Task<bool> LoginAsync(string username, string password)
    /// <summary>登录OA系统</summary>

public async Task LogoutAsync()
    /// <summary>登出OA系统</summary>

public string GetConfig(string section, string key, string defaultValue = "")
    /// <summary>获取配置值</summary>

public void SetConfig(string section, string key, string value)
    /// <summary>设置配置值</summary>

public T GetGlobalSetting<T>(string key, T defaultValue = default(T))
    /// <summary>获取全局设置</summary>

public void SetGlobalSetting(string key, object value)
    /// <summary>设置全局设置</summary>

public PerformanceStatistics GetPerformanceStatistics()
    /// <summary>获取性能统计信息</summary>

public SessionStatistics GetSessionStatistics()
    /// <summary>获取会话统计信息</summary>

public ETWebSimulationBrowser CreateSimulationBrowser(string targetUrl = null)
    /// <summary>创建模拟操作浏览器</summary>

public async Task<HealthCheckResult> PerformHealthCheckAsync()
    /// <summary>执行健康检查</summary>

public void Reset()
    /// <summary>清理资源并重置状态</summary>

public void Dispose()
    /// <summary>释放资源</summary>
```

#### Public属性
```csharp
public string BaseUrl { get; private set; }     // OA系统基础URL
public bool IsLoggedIn { get; private set; }    // 是否已登录
public ETWebLoginInfo LoginInfo { get; private set; } // 登录信息
public ETWebApiClient ApiClient { get; }         // API客户端
public ETWebSessionManager SessionManager { get; } // 会话管理器
public ETWebFileUploader FileUploader { get; }   // 文件上传器
public ETWebSimulationBrowser SimulationBrowser { get; } // 模拟操作浏览器
public string CurrentUsername { get; }          // 当前用户名
public IReadOnlyDictionary<string, object> GlobalSettings { get; } // 全局设置
```

#### 事件定义
```csharp
public event EventHandler<GlobalExceptionEventArgs> GlobalException; // 全局异常事件
public event EventHandler<LoginStatusChangedEventArgs> LoginStatusChanged; // 登录状态变更事件
public event EventHandler<PerformanceEventArgs> PerformanceAlert; // 性能监控事件
```

---

### 2. ETWebLoginBrowser类 (ETWebLoginBrowser.cs)

**类描述**: 基于CefSharp的OA系统登录浏览器，专门处理登录认证
**窗体文件**: 包含ETWebLoginBrowser.cs、ETWebLoginBrowser.Designer.cs、ETWebLoginBrowser.resx

#### Public方法
```csharp
public ETWebLoginBrowser(string loginUrl)
    /// <summary>初始化登录浏览器</summary>
    /// <param name="loginUrl">登录URL</param>

public async Task<ETWebLoginInfo> ShowLoginDialogAsync()
    /// <summary>显示登录对话框并获取认证信息</summary>
    /// <returns>登录信息</returns>

public async Task<bool> AutoLoginAsync(string username, string password)
    /// <summary>自动登录</summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <returns>登录是否成功</returns>

public ETWebLoginInfo GetLoginInfo()
    /// <summary>获取登录信息</summary>
    /// <returns>登录信息</returns>

public void Close()
    /// <summary>关闭浏览器</summary>

public void InitializeComponent()
    /// <summary>初始化窗体组件（Designer.cs中实现）</summary>
```

#### Public属性
```csharp
public string LoginUrl { get; set; }     // 登录URL
public bool IsLoginSuccessful { get; }   // 登录是否成功
public Dictionary<string, string> Cookies { get; } // Cookie信息
```

#### 窗体相关属性
```csharp
public ChromiumWebBrowser Browser { get; }  // CefSharp浏览器控件
public Button BtnClose { get; }             // 关闭按钮
public Label LblStatus { get; }             // 状态标签
public ProgressBar ProgressBar { get; }     // 进度条
```

---

### 3. ETWebApiClient类 (ETWebApiClient.cs)

**类描述**: 基于Flurl.Http的API交互客户端，处理所有HTTP请求

#### Public方法
```csharp
public ETWebApiClient(string baseUrl)
    /// <summary>初始化API客户端</summary>

public void SetAuthenticationInfo(ETWebLoginInfo loginInfo)
    /// <summary>设置认证信息</summary>

public async Task<T> GetAsync<T>(string endpoint)
    /// <summary>GET请求</summary>

public async Task<T> GetAsync<T>(string endpoint, object queryParams = null, int retryCount = -1)
    /// <summary>GET请求（带查询参数和重试）</summary>

public async Task<T> PostAsync<T>(string endpoint, object data)
    /// <summary>POST请求</summary>

public async Task<T> PostAsync<T>(string endpoint, object data, int retryCount)
    /// <summary>POST请求（带重试）</summary>

public async Task<T> PostFormAsync<T>(string endpoint, object formData, int retryCount = -1)
    /// <summary>POST表单数据请求</summary>

public async Task<T> PutAsync<T>(string endpoint, object data)
    /// <summary>PUT请求</summary>

public async Task<T> PutAsync<T>(string endpoint, object data, int retryCount)
    /// <summary>PUT请求（带重试）</summary>

public async Task<bool> DeleteAsync(string endpoint)
    /// <summary>DELETE请求</summary>

public async Task<bool> DeleteAsync(string endpoint, int retryCount)
    /// <summary>DELETE请求（带重试）</summary>

public async Task<List<T>> BatchGetAsync<T>(IEnumerable<string> endpoints)
    /// <summary>批量GET请求</summary>

public void ClearCache()
    /// <summary>清除请求缓存</summary>

public void ClearExpiredCache()
    /// <summary>清除过期缓存</summary>

public (int TotalItems, int ExpiredItems) GetCacheStats()
    /// <summary>获取缓存统计信息</summary>

public async Task<bool> TestConnectionAsync(string endpoint = "")
    /// <summary>测试连接</summary>

public void Dispose()
    /// <summary>释放资源</summary>
```

#### Public属性
```csharp
public string BaseUrl { get; set; }      // API基础URL
public int TimeoutSeconds { get; set; }  // 超时时间
public bool IsAuthenticated { get; }     // 是否已认证
public int MaxConcurrentRequests { get; set; }  // 最大并发请求数
public bool EnableRequestCache { get; set; }    // 是否启用请求缓存
public int CacheExpirationMinutes { get; set; } // 缓存过期时间（分钟）
public int DefaultRetryCount { get; set; }      // 默认重试次数
public int RetryIntervalMs { get; set; }        // 重试间隔（毫秒）
```

---

### 4. ETWebSessionManager类 (ETWebSessionManager.cs)

**类描述**: 会话状态管理器，负责维护登录状态和定期刷新，实现IDisposable接口

#### Public方法
```csharp
public ETWebSessionManager(ETWebApiClient apiClient)
    /// <summary>初始化会话管理器</summary>

public void InitializeSession(string username, string password, ETWebSessionData sessionData = null)
    /// <summary>初始化会话（设置用户凭据）</summary>

public async Task<bool> StartSessionMonitoringAsync()
    /// <summary>开始会话监控</summary>

public void StopSessionMonitoring()
    /// <summary>停止会话监控</summary>

public async Task<bool> RefreshSessionAsync()
    /// <summary>刷新会话</summary>

public async Task<bool> IsSessionValidAsync()
    /// <summary>检查会话是否有效</summary>

public void SaveSessionState()
    /// <summary>保存会话状态</summary>

public void CleanupExpiredSessions()
    /// <summary>清理过期会话</summary>

public SessionStatistics GetSessionStatistics()
    /// <summary>获取会话统计信息</summary>

public void Dispose()
    /// <summary>释放资源</summary>
```

#### Public属性
```csharp
public bool IsMonitoring { get; }        // 是否正在监控
public DateTime LastHeartbeat { get; }   // 最后心跳时间
public int HeartbeatInterval { get; set; } // 心跳间隔（秒）
public ETWebSessionData CurrentSession { get; } // 当前会话数据
public bool AutoReloginEnabled { get; set; } // 是否启用自动重登
public int MaxReloginAttempts { get; set; } // 最大重登尝试次数
public int CurrentReloginAttempts { get; } // 当前重登尝试次数
```

#### 事件定义
```csharp
public event EventHandler<SessionStatusChangedEventArgs> SessionStatusChanged; // 会话状态变更事件
public event EventHandler<HeartbeatFailedEventArgs> HeartbeatFailed; // 心跳失败事件
public event EventHandler<AutoReloginEventArgs> AutoReloginAttempted; // 自动重登事件
```

#### 内部类
```csharp
public class SessionStatusChangedEventArgs : EventArgs
    /// <summary>会话状态变更事件参数</summary>
    /// <property>SessionStatus OldStatus</property>
    /// <property>SessionStatus NewStatus</property>
    /// <property>string SessionId</property>
    /// <property>DateTime Timestamp</property>

public class HeartbeatFailedEventArgs : EventArgs
    /// <summary>心跳失败事件参数</summary>
    /// <property>DateTime Timestamp</property>
    /// <property>string Reason</property>
    /// <property>Exception Exception</property>

public class AutoReloginEventArgs : EventArgs
    /// <summary>自动重登事件参数</summary>
    /// <property>bool Success</property>
    /// <property>int AttemptNumber</property>
    /// <property>string Reason</property>
    /// <property>Exception Exception</property>
    /// <property>DateTime Timestamp</property>

public class SessionStatistics
    /// <summary>会话统计信息</summary>
    /// <property>string CurrentSessionId</property>
    /// <property>TimeSpan SessionAge</property>
    /// <property>DateTime LastActivity</property>
    /// <property>bool IsActive</property>
    /// <property>int HeartbeatFailures</property>
    /// <property>TimeSpan RemainingTime</property>
    /// <property>bool IsMonitoring</property>
    /// <property>DateTime LastHeartbeat</property>
    /// <property>TimeSpan HeartbeatInterval</property>
    /// <property>int ReloginAttempts</property>
    /// <property>int MaxReloginAttempts</property>
```

---

### 5. ETWebFileUploader类 (ETWebFileUploader.cs) ✅ **已精简优化**

**类描述**: 简化的文件上传处理器，专注于基本的单文件上传功能，移除了复杂的高级特性

**精简说明**:
- ❌ 移除了批量上传、断点续传、并发上传、文件完整性校验等复杂功能
- ✅ 保留了核心的单文件上传、文件验证、重试机制、基础进度监控
- 📊 代码量从700+行精简到292行，提高了稳定性和可维护性

#### Public方法（精简版本）
```csharp
public ETWebFileUploader(ETWebApiClient apiClient)
    /// <summary>初始化文件上传器</summary>

public async Task<ETWebUploadResult> UploadFileAsync(string endpoint, string filePath, object formData = null)
    /// <summary>上传单个文件（简化版本）</summary>

public async Task<ETWebUploadResult> UploadFileWithRetryAsync(string endpoint, string filePath, object formData = null, int retryCount = 3)
    /// <summary>上传单个文件（带重试）</summary>

public void Dispose()
    /// <summary>释放资源</summary>
```

#### Public属性（精简版本）
```csharp
public int MaxFileSize { get; set; }     // 最大文件大小（MB）
public string[] AllowedExtensions { get; set; } // 允许的文件扩展名
public int RetryCount { get; set; }      // 上传重试次数
```

#### 已移除的功能
```csharp
// ❌ 以下功能已在精简版本中移除：
// - 批量上传功能 (UploadFilesAsync, UploadFilesConcurrentAsync)
// - 断点续传功能 (EnableResumableUpload, UploadSession相关)
// - 并发上传控制 (MaxConcurrentUploads)
// - 文件完整性校验 (EnableIntegrityCheck, ValidateFileIntegrityAsync)
// - 分块上传 (ChunkSize)
// - 复杂的进度回调 (SetProgressCallback, SetDetailedProgressCallback)
// - 上传会话管理 (GetUploadSession, ClearUploadSession等)
// - 上传统计信息 (GetUploadStatistics)
```

#### 内部类
```csharp
public class UploadSession
    /// <summary>上传会话信息</summary>
    /// <property>string SessionId</property>
    /// <property>string FilePath</property>
    /// <property>string FileName</property>
    /// <property>long FileSize</property>
    /// <property>long UploadedBytes</property>
    /// <property>DateTime StartTime</property>
    /// <property>DateTime LastUpdateTime</property>
    /// <property>DateTime? CompletedTime</property>
    /// <property>bool IsCompleted</property>
    /// <property>bool IsPaused</property>
    /// <property>bool IsCancelled</property>
    /// <property>string FileId</property>
    /// <property>int ProgressPercentage</property>
    /// <property>long UploadSpeed</property>

public class UploadStatistics
    /// <summary>上传统计信息</summary>
    /// <property>int TotalSessions</property>
    /// <property>int CompletedSessions</property>
    /// <property>int ActiveSessions</property>
    /// <property>int PausedSessions</property>
    /// <property>int CancelledSessions</property>
    /// <property>long TotalBytes</property>
    /// <property>long UploadedBytes</property>
    /// <property>double AverageSpeed</property>
    /// <property>int OverallProgress</property>
    /// <property>double SuccessRate</property>
```

---

### 6. ETWebSimulationBrowser类 (ETWebSimulationBrowser.cs)

**类描述**: 模拟操作浏览器窗体，提供网页自动化操作功能
**窗体文件**: 包含ETWebSimulationBrowser.cs、ETWebSimulationBrowser.Designer.cs、ETWebSimulationBrowser.resx

#### Public方法
```csharp
public ETWebSimulationBrowser(string url = "")
    /// <summary>初始化模拟操作浏览器</summary>

public async Task NavigateAsync(string url)
    /// <summary>导航到指定URL</summary>

public async Task<bool> FillTextAsync(string selector, string text)
    /// <summary>填写文本框（DOM操作方式）</summary>

public async Task<bool> ClickElementAsync(string selector)
    /// <summary>点击元素（DOM操作方式）</summary>

public async Task<bool> SelectOptionAsync(string selector, string value)
    /// <summary>选择下拉框选项（DOM操作方式）</summary>

public async Task<bool> ClickAtAsync(int x, int y)
    /// <summary>在指定坐标点击（坐标操作方式）</summary>

public async Task<bool> SendKeysAsync(string keys)
    /// <summary>发送键盘输入（坐标操作方式）</summary>

public async Task<bool> WaitForElementAsync(string selector, int timeoutMs = 10000)
    /// <summary>等待元素出现</summary>

public async Task<string> GetPageHtmlAsync()
    /// <summary>获取页面HTML内容</summary>

public async Task<string> GetElementTextAsync(string selector)
    /// <summary>获取元素文本内容</summary>

public async Task<string> GetElementAttributeAsync(string selector, string attribute)
    /// <summary>获取元素属性值</summary>

public async Task<object> ExecuteScriptAsync(string script)
    /// <summary>执行JavaScript代码</summary>

public async Task<Bitmap> TakeScreenshotAsync()
    /// <summary>截取页面屏幕截图</summary>

public async Task<bool> SaveScreenshotAsync(string filePath)
    /// <summary>保存页面截图到文件</summary>

public async Task<bool> ScrollPageAsync(int x, int y)
    /// <summary>滚动页面</summary>

public async Task<bool> ScrollToTopAsync()
    /// <summary>滚动到页面顶部</summary>

public async Task<bool> ScrollToBottomAsync()
    /// <summary>滚动到页面底部</summary>

public void InitializeComponent()
    /// <summary>初始化窗体组件（Designer.cs中实现）</summary>
```

#### Public属性
```csharp
public string CurrentUrl { get; }        // 当前URL
public bool IsPageLoaded { get; }        // 页面是否加载完成
```

#### 窗体相关属性
```csharp
public ChromiumWebBrowser Browser { get; }     // CefSharp浏览器控件
public TextBox TxtUrl { get; }                 // URL输入框
public Button BtnNavigate { get; }             // 导航按钮
public Button BtnBack { get; }                 // 后退按钮
public Button BtnForward { get; }              // 前进按钮
public Button BtnRefresh { get; }              // 刷新按钮
public Label LblStatus { get; }                // 状态标签
public ProgressBar ProgressBar { get; }        // 进度条
public Panel PanelControls { get; }            // 控制面板
public MenuStrip MenuStrip { get; }            // 菜单栏
```

#### 事件定义
```csharp
public event EventHandler PageLoadCompleted;  // 页面加载完成事件
public event EventHandler<string> ApiDataReceived; // API数据获取完成事件
public event EventHandler<Exception> OperationError; // 操作错误事件
```

#### 核心私有方法（已实现）
```csharp
private void InitializeBrowser()
    /// <summary>初始化浏览器控件</summary>

private void InitializeTimer()
    /// <summary>初始化定时器</summary>

private async Task CheckLoginStatusAsync()
    /// <summary>检查登录状态（异步版本）</summary>

private async Task ExtractLoginInfoAsync()
    /// <summary>提取登录信息（异步版本）</summary>

private async Task<Dictionary<string, string>> GetAllCookiesAsync()
    /// <summary>获取所有Cookie</summary>

private async Task<Dictionary<string, string>> GetUserInfoFromPageAsync()
    /// <summary>从页面获取用户信息</summary>

private async Task TryAutoFillLoginForm()
    /// <summary>尝试自动填写登录表单</summary>

private async Task TryClickLoginButton()
    /// <summary>尝试点击登录按钮</summary>

private bool IsLoginSuccessUrl(string url)
    /// <summary>判断URL是否表示登录成功</summary>
```

#### 辅助类
```csharp
public class CookieVisitor : ICookieVisitor
    /// <summary>Cookie访问器，用于获取浏览器中的所有Cookie</summary>
    /// <property>List<Cookie> Cookies</property>
    /// <property>Task Task</property>
    /// <method>bool Visit(Cookie cookie, int count, int total, ref bool deleteCookie)</method>

public class ETWebException : ETException
    /// <summary>ETWebException异常类</summary>
    /// <nested>ErrorCodes常量类</nested>
```

---

### 7. ETWebAuthStorage类 (Storage/ETWebAuthStorage.cs)

**类描述**: 认证信息存储类，负责安全存储和管理用户认证信息

#### Public方法
```csharp
public ETWebAuthStorage()
    /// <summary>初始化认证信息存储</summary>

public void SaveAuthInfo(ETWebLoginInfo loginInfo, string username)
    /// <summary>保存认证信息</summary>
    /// <param name="loginInfo">登录信息</param>
    /// <param name="username">用户名（用作存储键）</param>

public ETWebLoginInfo LoadAuthInfo(string username)
    /// <summary>加载认证信息</summary>
    /// <param name="username">用户名</param>
    /// <returns>登录信息，未找到返回null</returns>

public void DeleteAuthInfo(string username)
    /// <summary>删除认证信息</summary>
    /// <param name="username">用户名</param>

public List<string> GetSavedUsernames()
    /// <summary>获取所有已保存的用户名列表</summary>
    /// <returns>用户名列表</returns>

public bool HasAuthInfo(string username)
    /// <summary>检查用户认证信息是否存在</summary>
    /// <param name="username">用户名</param>
    /// <returns>是否存在</returns>

public void CleanExpiredAuthInfo(int expireDays = 30)
    /// <summary>清理过期的认证信息</summary>
    /// <param name="expireDays">过期天数，默认30天</param>
```

#### Private字段
```csharp
private readonly string _configPath;  // 配置文件路径
private const string AUTH_SECTION = "Authentication";  // 认证配置节名称
```

#### 核心特性
- **安全存储**: 使用ETIniFile进行本地配置文件存储
- **多用户支持**: 支持多个OA账户的认证信息管理
- **自动过期清理**: 支持自动清理过期的认证信息
- **ExtensionsTools集成**: 完全集成ETIniFile、ETLogManager、ETException
- **错误处理**: 完善的异常处理和日志记录

---

### 8. ETWebSessionStorage类 (Storage/ETWebSessionStorage.cs)

**类描述**: 会话状态存储类，负责会话数据的持久化存储

#### Public方法
```csharp
public ETWebSessionStorage()
    /// <summary>初始化会话存储</summary>

public void SaveSessionData(ETWebSessionData sessionData)
    /// <summary>保存会话数据</summary>

public ETWebSessionData LoadSessionData(string sessionId)
    /// <summary>加载会话数据</summary>

public void DeleteSessionData(string sessionId)
    /// <summary>删除会话数据</summary>

public List<string> GetAllSessionIds()
    /// <summary>获取所有会话ID列表</summary>

public void CleanExpiredSessions()
    /// <summary>清理过期的会话数据</summary>
```

---

## 🔗 类间调用关系图

### 依赖关系
```
ETWebClient (主客户端)
  ├── ETWebLoginBrowser (登录认证)
  ├── ETWebApiClient (API交互)
  │   ├── ETWebSessionManager (会话管理)
  │   └── ETWebFileUploader (文件上传)
  └── ETWebSimulationBrowser (模拟操作)

ExtensionsTools集成
  ├── ETIniFile (配置管理)
  ├── ETLogManager (日志记录)
  └── ETException (异常处理)

Storage存储模块
  ├── ETWebAuthStorage (认证信息存储)
  └── ETWebSessionStorage (会话状态存储)
```

### 调用流程
```
1. ETWebClient 初始化
2. ETWebLoginBrowser 执行登录获取认证信息
3. ETWebApiClient 设置认证信息
4. ETWebSessionManager 开始会话监控
5. 用户调用各种功能方法
6. ETWebSimulationBrowser 执行自动化操作
7. ETWebFileUploader 处理文件上传
```

---

### 9. ETWebJsonHelper类 (Helpers/ETWebJsonHelper.cs)

**类描述**: JSON处理辅助类，提供JSON序列化、反序列化和数据处理功能

#### Public静态方法
```csharp
public static string ToJson(object obj, bool prettyFormat = false)
    /// <summary>将对象序列化为JSON字符串</summary>

public static string ToJsonSafe(object obj, string defaultValue = "{}", bool prettyFormat = false)
    /// <summary>将对象序列化为JSON字符串（安全版本，不抛出异常）</summary>

public static T FromJson<T>(string json)
    /// <summary>将JSON字符串反序列化为指定类型的对象</summary>

public static T FromJsonSafe<T>(string json, T defaultValue = default(T))
    /// <summary>将JSON字符串反序列化为指定类型的对象（安全版本）</summary>

public static dynamic FromJsonDynamic(string json)
    /// <summary>将JSON字符串反序列化为动态对象</summary>

public static bool IsValidJson(string json)
    /// <summary>验证JSON字符串格式是否正确</summary>

public static string ExtractValue(string json, string path)
    /// <summary>从JSON字符串中提取指定路径的值</summary>

public static T ExtractValue<T>(string json, string path, T defaultValue = default(T))
    /// <summary>从JSON字符串中提取指定路径的强类型值</summary>

public static string MergeJson(string json1, string json2)
    /// <summary>合并两个JSON对象</summary>

public static string PrettyFormat(string json)
    /// <summary>美化JSON字符串格式</summary>

public static string CompactFormat(string json)
    /// <summary>压缩JSON字符串（移除空白字符）</summary>

public static Dictionary<string, object> ObjectToDictionary(object obj)
    /// <summary>将对象转换为字典</summary>

public static T DictionaryToObject<T>(Dictionary<string, object> dictionary)
    /// <summary>将字典转换为指定类型的对象</summary>
```

---

### 10. ETWebCookieHelper类 (Helpers/ETWebCookieHelper.cs)

**类描述**: Cookie处理辅助类，提供Cookie的解析、构建和管理功能

#### Public静态方法
```csharp
public static Dictionary<string, string> ParseCookieString(string cookieString)
    /// <summary>解析Cookie字符串为字典</summary>

public static Dictionary<string, string> ParseSetCookieHeaders(IEnumerable<string> setCookieHeaders)
    /// <summary>解析Set-Cookie响应头</summary>

public static string BuildCookieString(Dictionary<string, string> cookies)
    /// <summary>将Cookie字典构建为Cookie字符串</summary>

public static CookieJar CreateCookieJar(Dictionary<string, string> cookies, string domain = null, string path = "/")
    /// <summary>创建CookieJar对象</summary>

public static Dictionary<string, string> MergeCookies(Dictionary<string, string> cookies1, Dictionary<string, string> cookies2, bool overwrite = true)
    /// <summary>合并两个Cookie字典</summary>

public static Dictionary<string, string> FilterCookies(Dictionary<string, string> cookies, IEnumerable<string> includeNames = null, IEnumerable<string> excludeNames = null)
    /// <summary>过滤Cookie字典</summary>

public static bool ContainsCookie(Dictionary<string, string> cookies, string cookieName)
    /// <summary>检查Cookie是否包含指定的名称</summary>

public static string GetCookieValue(Dictionary<string, string> cookies, string cookieName, string defaultValue = null)
    /// <summary>获取Cookie值</summary>

public static void SetCookieValue(Dictionary<string, string> cookies, string cookieName, string cookieValue)
    /// <summary>设置Cookie值</summary>

public static bool RemoveCookie(Dictionary<string, string> cookies, string cookieName)
    /// <summary>移除Cookie</summary>

public static string EncodeCookieValue(string value)
    /// <summary>URL编码Cookie值</summary>

public static string DecodeCookieValue(string encodedValue)
    /// <summary>URL解码Cookie值</summary>

public static string SerializeCookies(Dictionary<string, string> cookies)
    /// <summary>将Cookie字典序列化为字符串（用于持久化存储）</summary>

public static Dictionary<string, string> DeserializeCookies(string serializedCookies)
    /// <summary>从序列化字符串反序列化Cookie字典</summary>
```

---

### 11. ETWebConfigHelper类 (Helpers/ETWebConfigHelper.cs)

**类描述**: 配置管理辅助类，基于ETIniFile提供OA系统配置管理功能

#### Public静态方法
```csharp
public static void Initialize()
    /// <summary>初始化配置文件</summary>

public static string GETWebBaseUrl()
    /// <summary>获取OA系统基础URL</summary>

public static void SETWebBaseUrl(string url)
    /// <summary>设置OA系统基础URL</summary>

public static string GETWebLoginPath()
    /// <summary>获取OA登录路径</summary>

public static void SETWebLoginPath(string path)
    /// <summary>设置OA登录路径</summary>

public static string GETWebApiPath()
    /// <summary>获取OA API路径</summary>

public static void SETWebApiPath(string path)
    /// <summary>设置OA API路径</summary>

public static string GetFullLoginUrl()
    /// <summary>获取完整的登录URL</summary>

public static string GetFullApiBaseUrl()
    /// <summary>获取完整的API基础URL</summary>

public static int GetRequestTimeout()
    /// <summary>获取请求超时时间（秒）</summary>

public static void SetRequestTimeout(int seconds)
    /// <summary>设置请求超时时间（秒）</summary>

public static int GetMaxConcurrentRequests()
    /// <summary>获取最大并发请求数</summary>

public static void SetMaxConcurrentRequests(int count)
    /// <summary>设置最大并发请求数</summary>

public static bool GetAutoRetryEnabled()
    /// <summary>获取是否启用自动重试</summary>

public static void SetAutoRetryEnabled(bool enabled)
    /// <summary>设置是否启用自动重试</summary>

public static int GetDefaultRetryCount()
    /// <summary>获取默认重试次数</summary>

public static void SetDefaultRetryCount(int count)
    /// <summary>设置默认重试次数</summary>

public static int GetRetryInterval()
    /// <summary>获取重试间隔（毫秒）</summary>

public static void SetRetryInterval(int intervalMs)
    /// <summary>设置重试间隔（毫秒）</summary>

public static bool GetCacheEnabled()
    /// <summary>获取是否启用缓存</summary>

public static void SetCacheEnabled(bool enabled)
    /// <summary>设置是否启用缓存</summary>

public static int GetCacheExpirationMinutes()
    /// <summary>获取缓存过期时间（分钟）</summary>

public static void SetCacheExpirationMinutes(int minutes)
    /// <summary>设置缓存过期时间（分钟）</summary>

public static string GetConfig(string section, string key, string defaultValue = "")
    /// <summary>获取配置值</summary>

public static void SetConfig(string section, string key, string value)
    /// <summary>设置配置值</summary>

public static string GetConfigFilePath()
    /// <summary>获取配置文件路径</summary>

public static bool ConfigFileExists()
    /// <summary>检查配置文件是否存在</summary>

public static void ResetToDefault()
    /// <summary>重置配置为默认值</summary>
```

---

### 12. ETWebStorageHelper类 (Helpers/ETWebStorageHelper.cs)

**类描述**: 本地存储辅助类，提供数据的安全存储和管理功能

#### Public静态方法
```csharp
public static void Initialize()
    /// <summary>初始化存储目录</summary>

public static void SaveAuthInfo(string username, ETWebLoginInfo loginInfo)
    /// <summary>保存认证信息</summary>

public static ETWebLoginInfo LoadAuthInfo(string username)
    /// <summary>加载认证信息</summary>

public static bool DeleteAuthInfo(string username)
    /// <summary>删除认证信息</summary>

public static List<string> GetSavedUsernames()
    /// <summary>获取所有已保存的用户名</summary>

public static void SaveSessionData(string sessionId, ETWebSessionData sessionData)
    /// <summary>保存会话数据</summary>

public static ETWebSessionData LoadSessionData(string sessionId)
    /// <summary>加载会话数据</summary>

public static bool DeleteSessionData(string sessionId)
    /// <summary>删除会话数据</summary>

public static List<string> GetAllSessionIds()
    /// <summary>获取所有会话ID</summary>

public static void SaveCacheData(string cacheKey, object data, DateTime expirationTime)
    /// <summary>保存缓存数据</summary>

public static T LoadCacheData<T>(string cacheKey)
    /// <summary>加载缓存数据</summary>

public static int CleanExpiredCache()
    /// <summary>清理过期缓存</summary>

public static (int AuthFiles, int SessionFiles, int CacheFiles, long TotalSize) GetStorageStats()
    /// <summary>获取存储统计信息</summary>

public static bool ClearAllStorage()
    /// <summary>清理所有存储数据</summary>
```

---

### 13. ETWebRetryHelper类 (Helpers/ETWebRetryHelper.cs)

**类描述**: 重试策略辅助类，提供智能重试和容错机制

#### Public静态方法
```csharp
public static async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, RetryConfig config = null, string operationName = "Unknown", CancellationToken cancellationToken = default)
    /// <summary>执行带重试的操作</summary>

public static async Task ExecuteWithRetryAsync(Func<Task> operation, RetryConfig config = null, string operationName = "Unknown", CancellationToken cancellationToken = default)
    /// <summary>执行带重试的无返回值操作</summary>

public static ErrorType ClassifyError(Exception exception)
    /// <summary>分类错误类型</summary>

public static bool ShouldRetry(Exception exception, RetryConfig config)
    /// <summary>判断是否应该重试</summary>

public static int CalculateDelay(int attemptNumber, RetryConfig config)
    /// <summary>计算重试延迟</summary>

public static RetryConfig GetDefaultRetryConfig()
    /// <summary>获取默认重试配置</summary>

public static RetryConfig CreateNetworkRetryConfig()
    /// <summary>创建网络请求专用重试配置</summary>

public static RetryConfig CreateAuthRetryConfig()
    /// <summary>创建认证专用重试配置</summary>

public static RetryConfig CreateFastRetryConfig()
    /// <summary>创建快速重试配置</summary>

public static void RecordRetryStats(string operationName, int attempts, bool success, TimeSpan totalTime, IEnumerable<ErrorType> errorTypes)
    /// <summary>记录重试统计</summary>

public static RetryStats GetRetryStats(string operationName)
    /// <summary>获取重试统计信息</summary>

public static Dictionary<string, RetryStats> GetAllRetryStats()
    /// <summary>获取所有重试统计信息</summary>

public static void ClearRetryStats()
    /// <summary>清除重试统计信息</summary>
```

#### 内部类
```csharp
public class RetryConfig
    /// <summary>重试配置类</summary>
    /// <property>int MaxRetryCount</property>
    /// <property>int BaseIntervalMs</property>
    /// <property>int MaxIntervalMs</property>
    /// <property>RetryStrategy Strategy</property>
    /// <property>double BackoffMultiplier</property>
    /// <property>double RandomFactor</property>
    /// <property>bool EnableJitter</property>
    /// <property>HashSet<ErrorType> RetryableErrors</property>
    /// <property>HashSet<int> RetryableStatusCodes</property>

public class RetryStats
    /// <summary>重试统计信息类</summary>
    /// <property>int TotalAttempts</property>
    /// <property>int SuccessfulRetries</property>
    /// <property>int FailedRetries</property>
    /// <property>TimeSpan TotalRetryTime</property>
    /// <property>Dictionary<ErrorType, int> ErrorTypeCounts</property>
```

---

### 14. ETWebUploadProgressForm类 (ETWebUploadProgressForm.cs)

**类描述**: 文件上传进度监控窗体，提供可视化的上传进度管理界面
**窗体文件**: 包含ETWebUploadProgressForm.cs、ETWebUploadProgressForm.Designer.cs、ETWebUploadProgressForm.resx

#### Public方法
```csharp
public ETWebUploadProgressForm(ETWebFileUploader fileUploader)
    /// <summary>初始化上传进度监控窗体</summary>

public void InitializeComponent()
    /// <summary>初始化窗体组件（Designer.cs中实现）</summary>
```

#### Public属性
```csharp
// 窗体控件属性在Designer.cs中定义
```

#### 窗体相关属性
```csharp
public Panel PanelMain { get; }              // 主面板
public Panel PanelContent { get; }           // 内容面板
public ListView LvUploads { get; }           // 上传列表视图
public Panel PanelButtons { get; }           // 按钮面板
public Button BtnPause { get; }              // 暂停/恢复按钮
public Button BtnCancel { get; }             // 取消按钮
public Button BtnClearCompleted { get; }     // 清除已完成按钮
public Button BtnCancelAll { get; }          // 全部取消按钮
public Panel PanelStats { get; }             // 统计面板
public GroupBox GroupBoxStats { get; }       // 统计分组框
public Label LblTotalFiles { get; }          // 总文件数标签
public Label LblCompletedFiles { get; }      // 已完成文件数标签
public Label LblActiveFiles { get; }         // 活跃文件数标签
public Label LblTotalSize { get; }           // 总大小标签
public Label LblUploadedSize { get; }        // 已上传大小标签
public Label LblOverallProgress { get; }     // 总进度标签
public Label LblAverageSpeed { get; }        // 平均速度标签
public Panel PanelProgressBar { get; }       // 进度条面板
public ProgressBar ProgressBarOverall { get; } // 总体进度条
public Label LblOverallTitle { get; }        // 总体进度标题
```

---

### 15. ETWebUploadProgressHelper类 (Helpers/ETWebUploadProgressHelper.cs)

**类描述**: 上传进度监控辅助类，提供进度窗体管理和进度回调功能

#### Public静态方法
```csharp
public static ETWebUploadProgressForm ShowProgressForm(ETWebFileUploader fileUploader, Form parent = null)
    /// <summary>显示上传进度监控窗体</summary>

public static void HideProgressForm(ETWebFileUploader fileUploader)
    /// <summary>隐藏上传进度监控窗体</summary>

public static void CloseProgressForm(ETWebFileUploader fileUploader)
    /// <summary>关闭上传进度监控窗体</summary>

public static ETWebUploadProgressForm GetProgressForm(ETWebFileUploader fileUploader)
    /// <summary>获取上传进度监控窗体</summary>

public static bool HasProgressForm(ETWebFileUploader fileUploader)
    /// <summary>检查是否存在进度监控窗体</summary>

public static void CloseAllProgressForms()
    /// <summary>关闭所有进度监控窗体</summary>

public static int GetActiveProgressFormCount()
    /// <summary>获取活跃的进度监控窗体数量</summary>

public static void CleanupDisposedForms()
    /// <summary>清理已释放的进度监控窗体</summary>

public static Action<int> CreateProgressCallback(Action<int> onProgress = null, Action onCompleted = null)
    /// <summary>创建简单的进度回调函数</summary>

public static Action<string, long, long> CreateDetailedProgressCallback(Action<string, long, long> onDetailedProgress = null, Action<string> onCompleted = null)
    /// <summary>创建详细进度回调函数</summary>

public static string FormatFileSize(long bytes)
    /// <summary>格式化文件大小显示</summary>

public static string FormatUploadSpeed(long bytesPerSecond)
    /// <summary>格式化上传速度显示</summary>

public static string CalculateRemainingTime(long uploadedBytes, long totalBytes, long bytesPerSecond)
    /// <summary>计算剩余时间</summary>
```

---

### 16. ETWebUploadConfigHelper类 (Helpers/ETWebUploadConfigHelper.cs) ✅ **已精简优化**

**类描述**: 文件上传配置管理辅助类，提供简化的上传参数配置管理功能

**精简说明**:
- ❌ 移除了复杂的高级配置项（分块大小、并发数、断点续传、完整性校验等）
- ✅ 保留了核心的基础配置（文件大小、重试次数、超时时间、文件类型限制）
- 📊 配置项从15+个精简到6个核心配置，简化配置管理

#### Public静态方法（精简版本）
```csharp
public static void Initialize()
    /// <summary>初始化上传配置</summary>

public static int GetMaxFileSize()
    /// <summary>获取最大文件大小（MB）</summary>

public static void SetMaxFileSize(int maxSizeMB)
    /// <summary>设置最大文件大小（MB）</summary>

public static int GetRetryCount()
    /// <summary>获取重试次数</summary>

public static void SetRetryCount(int retryCount)
    /// <summary>设置重试次数</summary>

public static int GetRequestTimeout()
    /// <summary>获取请求超时时间（秒）</summary>

public static void SetRequestTimeout(int timeoutSeconds)
    /// <summary>设置请求超时时间（秒）</summary>

public static string[] GetAllowedExtensions()
    /// <summary>获取允许的文件扩展名</summary>

public static void SetAllowedExtensions(string[] extensions)
    /// <summary>设置允许的文件扩展名</summary>

public static void AddAllowedExtension(string extension)
    /// <summary>添加允许的文件扩展名</summary>

public static void RemoveAllowedExtension(string extension)
    /// <summary>移除允许的文件扩展名</summary>

public static bool IsExtensionAllowed(string extension)
    /// <summary>检查文件扩展名是否被允许</summary>

public static void ApplyConfigToUploader(ETWebFileUploader uploader)
    /// <summary>应用配置到文件上传器（简化版本）</summary>

public static void GetConfigFromUploader(ETWebFileUploader uploader)
    /// <summary>从文件上传器获取配置（简化版本）</summary>

public static void ResetToDefault()
    /// <summary>重置为默认配置</summary>

public static string GetConfigFilePath()
    /// <summary>获取配置文件路径</summary>

public static bool ConfigFileExists()
    /// <summary>检查配置文件是否存在</summary>
```

#### 已移除的配置项
```csharp
// ❌ 以下配置项已在精简版本中移除：
// - GetChunkSize/SetChunkSize (分块大小)
// - GetMaxConcurrentUploads/SetMaxConcurrentUploads (最大并发数)
// - GetResumableUploadEnabled/SetResumableUploadEnabled (断点续传)
// - GetIntegrityCheckEnabled/SetIntegrityCheckEnabled (完整性校验)
// - GetAutoRetryEnabled/SetAutoRetryEnabled (自动重试)
// - GetProgressUpdateInterval/SetProgressUpdateInterval (进度更新间隔)
```

---

### 17. ETWebUploadValidationHelper类 (Helpers/ETWebUploadValidationHelper.cs)

**类描述**: 文件上传验证辅助类，提供全面的文件验证功能

#### Public静态方法
```csharp
public static ValidationResult ValidateFileExists(string filePath)
    /// <summary>验证文件是否存在</summary>

public static ValidationResult ValidateFileSize(string filePath, int maxSizeMB)
    /// <summary>验证文件大小</summary>

public static ValidationResult ValidateFileExtension(string filePath, string[] allowedExtensions)
    /// <summary>验证文件扩展名</summary>

public static ValidationResult ValidateFileAccess(string filePath)
    /// <summary>验证文件访问权限</summary>

public static ValidationResult ValidateFileMimeType(string filePath, string[] allowedMimeTypes = null)
    /// <summary>验证文件MIME类型</summary>

public static async Task<ValidationResult> ValidateFileIntegrityAsync(string filePath, string expectedMD5)
    /// <summary>验证文件完整性（MD5校验）</summary>

public static ValidationResult ValidateFileSafety(string filePath)
    /// <summary>检查文件是否为恶意文件</summary>

public static List<FileValidationResult> ValidateFiles(string[] filePaths, int maxSizeMB, string[] allowedExtensions)
    /// <summary>批量验证文件</summary>

public static FileValidationResult ValidateFile(string filePath, int maxSizeMB, string[] allowedExtensions)
    /// <summary>验证单个文件（完整验证）</summary>
```

#### 内部类
```csharp
public class ValidationResult
    /// <summary>验证结果</summary>
    /// <property>bool IsValid</property>
    /// <property>string ErrorMessage</property>
    /// <method>static ValidationResult Success()</method>
    /// <method>static ValidationResult Fail(string errorMessage)</method>

public class FileValidationResult : ValidationResult
    /// <summary>文件验证结果</summary>
    /// <property>string FilePath</property>
    /// <property>string FileName</property>
    /// <property>long FileSize</property>
```

---

### 18. ETWebUploadConfigForm类 (ETWebUploadConfigForm.cs) ✅ **已精简优化**

**类描述**: 文件上传配置窗体，提供简化的可视化上传参数配置界面
**窗体文件**: 包含ETWebUploadConfigForm.cs、ETWebUploadConfigForm.Designer.cs、ETWebUploadConfigForm.resx

**精简说明**:
- ❌ 禁用了复杂功能的UI控件（分块大小、并发数、断点续传、完整性校验等）
- ✅ 保留了基础配置界面（文件大小、重试次数、超时时间、文件类型管理）
- 🎨 界面简洁明了，明确标识已简化的功能项

#### Public方法（精简版本）
```csharp
public ETWebUploadConfigForm()
    /// <summary>初始化文件上传配置窗体</summary>

public static DialogResult ShowConfigDialog(Form parent = null)
    /// <summary>显示配置对话框</summary>

public void InitializeComponent()
    /// <summary>初始化窗体组件（Designer.cs中实现）</summary>
```

#### 窗体相关属性（精简版本）
```csharp
// ✅ 活跃的控件（可正常使用）
public NumericUpDown NumMaxFileSize { get; }     // 最大文件大小数值框
public NumericUpDown NumRetryCount { get; }      // 重试次数数值框
public NumericUpDown NumTimeout { get; }         // 超时时间数值框
public ListBox LstExtensions { get; }            // 扩展名列表框
public TextBox TxtNewExtension { get; }          // 新扩展名文本框
public Button BtnAddExtension { get; }           // 添加扩展名按钮
public Button BtnRemoveExtension { get; }        // 移除扩展名按钮
public Button BtnSave { get; }                   // 保存按钮
public Button BtnCancel { get; }                 // 取消按钮
public Button BtnReset { get; }                  // 重置按钮

// ❌ 已禁用的控件（显示但不可用，标记为"已简化"）
public NumericUpDown NumChunkSize { get; }       // 分块大小数值框 (已禁用)
public NumericUpDown NumMaxConcurrent { get; }   // 最大并发数数值框 (已禁用)
public NumericUpDown NumProgressInterval { get; } // 进度更新间隔数值框 (已禁用)
public CheckBox ChkResumableUpload { get; }      // 断点续传复选框 (已禁用)
public CheckBox ChkIntegrityCheck { get; }       // 完整性校验复选框 (已禁用)
public CheckBox ChkAutoRetry { get; }            // 自动重试复选框 (已禁用)
```

#### 界面优化特性
```csharp
// 🎨 界面优化说明：
// - 窗体标题显示为"文件上传配置 (简化版本)"
// - 已禁用的控件标签添加"(已简化)"或"(已移除)"后缀
// - 保持完整的配置验证和保存功能
// - 只保存简化版本支持的配置项
```

---

### 19. ETWebBrowserAutomationHelper类 (Helpers/ETWebBrowserAutomationHelper.cs)

**类描述**: 浏览器自动化操作辅助类，提供高级的浏览器自动化功能

#### Public静态方法
```csharp
public static async Task<bool> WaitForPageLoadAsync(ChromiumWebBrowser browser, int timeoutMs = 30000)
    /// <summary>等待页面完全加载</summary>

public static async Task<bool> WaitForElementAsync(ChromiumWebBrowser browser, string selector, int timeoutMs = 10000, bool visible = true)
    /// <summary>智能等待元素出现</summary>

public static async Task<bool> FillFormAsync(ChromiumWebBrowser browser, Dictionary<string, string> formData)
    /// <summary>智能填写表单</summary>

public static async Task<bool> SmartClickAsync(ChromiumWebBrowser browser, string selector, int retryCount = 3)
    /// <summary>智能点击元素（带重试）</summary>

public static async Task<bool> ScrollToElementAsync(ChromiumWebBrowser browser, string selector)
    /// <summary>滚动到指定元素</summary>

public static async Task<Rectangle?> GetElementBoundsAsync(ChromiumWebBrowser browser, string selector)
    /// <summary>获取元素的位置和大小</summary>

public static async Task<bool> ElementExistsAsync(ChromiumWebBrowser browser, string selector)
    /// <summary>检查元素是否存在</summary>

public static async Task<string> GetPageTitleAsync(ChromiumWebBrowser browser)
    /// <summary>获取页面标题</summary>

public static string GetPageUrl(ChromiumWebBrowser browser)
    /// <summary>获取页面URL</summary>

public static async Task<Dictionary<string, string>> GetAllCookiesAdvancedAsync(ChromiumWebBrowser browser)
    /// <summary>获取所有Cookie（高级版本）</summary>

public static async Task<bool> SetCookieAsync(ChromiumWebBrowser browser, string name, string value, string domain = null, string path = "/")
    /// <summary>设置Cookie</summary>

public static ChromiumWebBrowser CreateBrowser(string url = "about:blank", string userAgent = null)
    /// <summary>创建浏览器实例</summary>

public static void SafeCloseBrowser(ChromiumWebBrowser browser)
    /// <summary>安全关闭浏览器</summary>
```

---

### 20. ETWebBrowserScriptManager类 (Helpers/ETWebBrowserScriptManager.cs)

**类描述**: 浏览器脚本管理器，用于管理和执行复杂的自动化脚本

#### Public方法
```csharp
public ETWebBrowserScriptManager(ChromiumWebBrowser browser)
    /// <summary>初始化浏览器脚本管理器</summary>

public void AddScriptTemplate(string name, string script)
    /// <summary>添加自定义脚本模板</summary>

public string GetScriptTemplate(string name)
    /// <summary>获取脚本模板</summary>

public List<string> GetScriptTemplateNames()
    /// <summary>获取所有脚本模板名称</summary>

public async Task<object> ExecuteScriptTemplateAsync(string templateName, Dictionary<string, object> variables = null)
    /// <summary>执行脚本模板</summary>

public async Task<object> ExecuteCustomScriptAsync(string script)
    /// <summary>执行自定义脚本</summary>

public async Task<List<object>> ExecuteScriptBatchAsync(List<string> scripts)
    /// <summary>批量执行脚本</summary>

public async Task<LoginFormInfo> DetectLoginFormAsync()
    /// <summary>检测登录表单</summary>

public async Task<List<LinkInfo>> GetAllLinksAsync()
    /// <summary>获取页面所有链接</summary>

public async Task<PageMetadata> GetPageMetadataAsync()
    /// <summary>获取页面元数据</summary>

public async Task<string> LoadScriptFromFileAsync(string filePath)
    /// <summary>从文件加载脚本</summary>

public async Task<bool> SaveScriptToFileAsync(string script, string filePath)
    /// <summary>保存脚本到文件</summary>
```

#### 内部类
```csharp
public class LoginFormInfo
    /// <summary>登录表单信息</summary>
    /// <property>bool Found</property>
    /// <property>string Action</property>
    /// <property>string Method</property>

public class LinkInfo
    /// <summary>链接信息</summary>
    /// <property>string Text</property>
    /// <property>string Href</property>
    /// <property>string Target</property>

public class PageMetadata
    /// <summary>页面元数据</summary>
    /// <property>string Title</property>
    /// <property>string Url</property>
    /// <property>string Description</property>
    /// <property>string Keywords</property>
    /// <property>string Author</property>
```

---

### 21. ETWebBrowserSessionManager类 (Helpers/ETWebBrowserSessionManager.cs)

**类描述**: 浏览器会话管理器，用于管理多个浏览器实例和会话状态

#### Public静态方法
```csharp
public static BrowserSession CreateSession(string sessionId = null, string url = "about:blank")
    /// <summary>创建新的浏览器会话</summary>

public static BrowserSession GetSession(string sessionId)
    /// <summary>获取浏览器会话</summary>

public static bool RemoveSession(string sessionId)
    /// <summary>移除浏览器会话</summary>

public static List<BrowserSession> GetActiveSessions()
    /// <summary>获取所有活跃会话</summary>

public static SessionStatistics GetSessionStatistics()
    /// <summary>获取会话统计信息</summary>

public static int CleanupExpiredSessions(TimeSpan maxAge)
    /// <summary>清理过期会话</summary>

public static int CloseAllSessions()
    /// <summary>关闭所有会话</summary>

public static async Task<int> BatchNavigateAsync(string url, List<string> sessionIds = null)
    /// <summary>批量导航到URL</summary>

public static async Task<Dictionary<string, object>> BatchExecuteScriptAsync(string script, List<string> sessionIds = null)
    /// <summary>批量执行JavaScript</summary>
```

#### 内部类
```csharp
public class BrowserSession
    /// <summary>浏览器会话信息</summary>
    /// <property>string SessionId</property>
    /// <property>ETWebSimulationBrowser Browser</property>
    /// <property>DateTime CreatedTime</property>
    /// <property>DateTime LastAccessTime</property>
    /// <property>bool IsActive</property>
    /// <property>string InitialUrl</property>
    /// <property>string CurrentUrl</property>
    /// <property>TimeSpan Age</property>
    /// <property>TimeSpan IdleTime</property>

public class SessionStatistics
    /// <summary>会话统计信息</summary>
    /// <property>int TotalSessions</property>
    /// <property>int ActiveSessions</property>
    /// <property>int InactiveSessions</property>
    /// <property>DateTime OldestSessionTime</property>
    /// <property>DateTime NewestSessionTime</property>
    /// <property>TimeSpan AverageSessionAge</property>
```

---

### 22. ETWebAutoReloginHelper类 (Helpers/ETWebAutoReloginHelper.cs)

**类描述**: 自动重登辅助类，处理会话过期后的自动重新登录功能

#### Public方法
```csharp
public ETWebAutoReloginHelper(ETWebApiClient apiClient, ETWebSessionManager sessionManager, string baseUrl)
    /// <summary>初始化自动重登辅助类</summary>

public void SetCredentials(string username, string password)
    /// <summary>设置登录凭据</summary>

public async Task<bool> ExecuteAutoReloginAsync(int attemptNumber = 1)
    /// <summary>执行自动重登</summary>

public bool CanExecuteAutoRelogin()
    /// <summary>检查是否可以执行自动重登</summary>

public void ClearCredentials()
    /// <summary>清除保存的凭据</summary>
```

#### Public属性
```csharp
public bool IsReloginInProgress { get; }  // 是否正在重登过程中
public string LastUsername { get; }       // 最后使用的用户名
public string BaseUrl { get; set; }       // OA系统基础URL
```

#### 事件定义
```csharp
public event EventHandler<ReloginStartedEventArgs> ReloginStarted;     // 重登开始事件
public event EventHandler<ReloginCompletedEventArgs> ReloginCompleted; // 重登完成事件
public event EventHandler<ReloginFailedEventArgs> ReloginFailed;       // 重登失败事件
```

#### 内部类
```csharp
public class ReloginStartedEventArgs : EventArgs
    /// <summary>重登开始事件参数</summary>
    /// <property>int AttemptNumber</property>
    /// <property>string Username</property>
    /// <property>DateTime Timestamp</property>

public class ReloginCompletedEventArgs : EventArgs
    /// <summary>重登完成事件参数</summary>
    /// <property>bool Success</property>
    /// <property>int AttemptNumber</property>
    /// <property>string Username</property>
    /// <property>ETWebLoginInfo LoginInfo</property>
    /// <property>DateTime Timestamp</property>

public class ReloginFailedEventArgs : EventArgs
    /// <summary>重登失败事件参数</summary>
    /// <property>int AttemptNumber</property>
    /// <property>string Reason</property>
    /// <property>string Username</property>
    /// <property>Exception Exception</property>
    /// <property>DateTime Timestamp</property>

public class LoginResult
    /// <summary>登录结果</summary>
    /// <property>bool Success</property>
    /// <property>ETWebLoginInfo LoginInfo</property>
    /// <property>ETWebSessionData SessionData</property>
    /// <property>string ErrorMessage</property>
    /// <property>Exception Exception</property>
```

---

### 23. ETWebPerformanceHelper类 (Helpers/ETWebPerformanceHelper.cs)

**类描述**: 性能监控和优化辅助类，提供性能指标收集和分析功能

#### Public静态方法
```csharp
public static PerformanceMonitor StartMonitoring(string operationName)
    /// <summary>开始监控操作</summary>

public static async Task<T> MonitorAsync<T>(string operationName, Func<Task<T>> operation)
    /// <summary>监控异步操作</summary>

public static T Monitor<T>(string operationName, Func<T> operation)
    /// <summary>监控同步操作</summary>

public static void RecordMetric(PerformanceMetric metric)
    /// <summary>记录性能指标</summary>

public static PerformanceStats GetStats(string operationName)
    /// <summary>获取操作的性能统计</summary>

public static Dictionary<string, PerformanceStats> GetAllStats()
    /// <summary>获取所有性能统计</summary>

public static List<PerformanceMetric> GetRecentMetrics(int count = 100)
    /// <summary>获取最近的性能指标</summary>

public static List<PerformanceMetric> GetRecentMetrics(string operationName, int count = 100)
    /// <summary>获取指定操作的最近指标</summary>

public static string AnalyzePerformance()
    /// <summary>分析性能瓶颈</summary>

public static List<string> DetectAnomalies()
    /// <summary>检测性能异常</summary>

public static void ClearAllData()
    /// <summary>清除所有性能数据</summary>

public static (int TotalOperations, int UniqueOperations, TimeSpan TotalDuration, long TotalMemory) GetSummary()
    /// <summary>获取性能数据摘要</summary>
```

#### 内部类
```csharp
public class PerformanceMonitor : IDisposable
    /// <summary>性能监控器</summary>
    /// <method>void Dispose()</method>
    /// <method>void SetError(string errorMessage)</method>
    /// <method>void AddCustomData(string key, object value)</method>

public class PerformanceMetric
    /// <summary>性能指标类</summary>
    /// <property>string OperationName</property>
    /// <property>DateTime StartTime</property>
    /// <property>DateTime EndTime</property>
    /// <property>TimeSpan Duration</property>
    /// <property>bool IsSuccess</property>
    /// <property>string ErrorMessage</property>
    /// <property>long MemoryBefore</property>
    /// <property>long MemoryAfter</property>
    /// <property>long MemoryUsed</property>
    /// <property>Dictionary<string, object> CustomData</property>

public class PerformanceStats
    /// <summary>性能统计类</summary>
    /// <property>string OperationName</property>
    /// <property>int TotalCount</property>
    /// <property>int SuccessCount</property>
    /// <property>int FailureCount</property>
    /// <property>double SuccessRate</property>
    /// <property>TimeSpan TotalDuration</property>
    /// <property>TimeSpan AverageDuration</property>
    /// <property>TimeSpan MinDuration</property>
    /// <property>TimeSpan MaxDuration</property>
    /// <property>long TotalMemoryUsed</property>
    /// <property>long AverageMemoryUsed</property>
    /// <property>DateTime FirstRecordTime</property>
    /// <property>DateTime LastRecordTime</property>
```

---

## 📝 更新记录

### 字典更新日志
| 时间 | 步骤 | 更新内容 | 更新者 |
|------|------|----------|--------|
| 2025-08-02 10:24 | 初始化 | 创建基础字典结构，定义核心类接口 | AI |
| 2025-08-02 11:30 | 1.1完成 | 完成目录结构创建和所有核心类基础文件，包含完整的窗体文件结构 | AI |
| 2025-08-02 12:30 | 1.4完成 | 集成ExtensionsTools模块，添加存储类，更新所有类的方法接口 | AI |
| 2025-08-02 14:30 | 2.1完成 | 完成ETWebLoginBrowser类核心功能开发，添加自动登录、Cookie提取、认证信息管理等功能 | AI |
| 2025-08-02 15:00 | 2.2-2.3完成 | 完成ETWebAuthStorage类开发，实现认证信息的安全存储和管理功能 | AI |
| 2025-08-02 15:30 | 阶段2完成 | 完成登录认证模块所有功能，包括界面优化和自检，质量优秀 | AI |
| 2025-08-02 16:30 | 3.1进行中 | 开始开发ETWebApiClient类完整功能，添加数据处理、重试机制、缓存功能 | AI |
| 2025-08-02 17:00 | 3.1完成 | 完成ETWebApiClient类核心功能，创建ETWebJsonHelper、ETWebCookieHelper、ETWebConfigHelper辅助类 | AI |
| 2025-08-02 17:30 | 3.2完成 | 完成数据处理功能，创建ETWebStorageHelper存储辅助类 | AI |
| 2025-08-02 18:00 | 3.3完成 | 完成重试和容错机制，创建ETWebRetryHelper智能重试策略类 | AI |
| 2025-08-02 18:30 | 3.4完成 | 完成性能优化，创建ETWebPerformanceHelper性能监控类 | AI |
| 2025-08-02 18:45 | 阶段3完成 | API交互模块全部完成，包含完整的数据处理、重试机制、性能优化功能 | AI |
| 2025-08-02 19:15 | 4.1完成 | 完成ETWebFileUploader类高级功能开发，包含断点续传、文件完整性校验、并发上传、上传会话管理等功能 | AI |
| 2025-08-02 19:45 | 4.2完成 | 完成上传进度监控功能，创建ETWebUploadProgressForm窗体和ETWebUploadProgressHelper辅助类 | AI |
| 2025-08-02 20:15 | 4.3完成 | 完成高级功能开发，创建ETWebUploadConfigHelper配置管理类和ETWebUploadValidationHelper验证辅助类 | AI |
| 2025-08-02 20:45 | 4.4完成 | 完成用户界面组件开发，创建ETWebUploadConfigForm配置窗体 | AI |
| 2025-08-02 21:00 | 阶段4完成 | 文件上传模块全部完成，包含完整的文件上传、进度监控、配置管理、验证功能 | AI |
| 2025-08-02 21:30 | 5.1完成 | 完成ETWebSimulationBrowser窗体开发，包含完整的浏览器自动化功能和ETWebBrowserAutomationHelper辅助类 | AI |
| 2025-08-02 22:00 | 5.2完成 | 完成高级自动化功能开发，创建ETWebBrowserScriptManager脚本管理器和ETWebBrowserSessionManager会话管理器 | AI |
| 2025-08-02 22:15 | 阶段5完成 | 模拟操作浏览器模块全部完成，包含完整的浏览器自动化、脚本管理、会话管理功能 | AI |
| 2025-08-02 22:30 | 6.1完成 | 完成ETWebSessionManager类核心功能开发，包含会话监控、心跳维护、自动重登、状态持久化等功能 | AI |
| 2025-08-02 22:45 | 6.3完成 | 完成自动重登功能开发，创建ETWebAutoReloginHelper辅助类，集成到ETWebSessionManager中 | AI |
| 2025-08-02 23:15 | 7.1-7.4完成 | 完成ETWebClient主类开发，集成全局异常处理、配置管理、性能优化等功能 | AI |
| 2025-08-02 23:30 | 8.1-8.4完成 | 完成文档编写，包括API使用文档、使用示例、最佳实践指南、故障排除指南 | AI |

### 命名规范
- **类名**: 使用PascalCase (如: ETWebClient)
- **方法名**: 使用PascalCase (如: LoginAsync)
- **属性名**: 使用PascalCase (如: BaseUrl)
- **常量名**: 使用UPPER_CASE (如: DEFAULT_TIMEOUT)
- **私有字段**: 以下划线开头 (如: _apiClient)
- **窗体控件**: 使用匈牙利命名法前缀 (如: BtnClose, TxtUrl, LblStatus)

### 窗体开发规范
- **文件结构**: 每个窗体必须包含.cs、.Designer.cs、.resx三个文件
- **设计器分离**: UI初始化代码必须在Designer.cs中
- **资源管理**: 图标、图片等资源通过resx文件管理
- **控件命名**: 遵循匈牙利命名法，便于识别控件类型

---

## 🚨 AI更新指令

### 每完成一个步骤后必须执行：

1. **读取当前字典**: 确保了解已有的类的方法和变量定义
2. **更新对应类的方法和属性**: 添加新开发的public方法和属性
3. **检查命名冲突**: 确保新增的命名不与现有的冲突
4. **更新调用关系**: 如有新的类间调用关系，更新关系图
5. **记录更新日志**: 在更新记录中添加本次更新的内容

### 更新格式示例：

```csharp
public async Task<bool> MethodName(string param1, int param2 = 0)
{
    /// <summary>
    /// 方法功能描述
    /// </summary>
    /// <param name="param1">参数1描述</param>
    /// <param name="param2">参数2描述，默认值为0</param>
    /// <returns>返回值描述</returns>
}
```

---

## 🚀 第一阶段开发规则

### 📋 第一阶段：基础架构搭建 (2025-08-02 开始)

#### 🎯 开发目标
建立项目基础框架，创建核心类的基础结构，配置依赖包，集成ExtensionsTools模块。

#### 🗂️ 协调字典使用规范（第一阶段专用）

**每个步骤开始前必须执行：**
1. **读取完整字典** - 仔细阅读所有已定义的类、方法、变量
2. **理解现有架构** - 明确类结构和调用关系
3. **规划新类接口** - 设计类的公共接口
4. **避免命名冲突** - 确保新增的命名不与现有的冲突

**每个步骤完成后必须执行：**
1. **立即更新字典** - 将新开发的类完整添加到字典中
2. **详细记录接口** - 包含所有public方法的完整签名和参数说明
3. **更新调用关系** - 如有新的类间调用，更新依赖关系图
4. **记录更新日志** - 在字典更新记录中添加详细的更新内容

#### 🚨 第一阶段强制规则
- **步骤开始**: 必须先读取字典，了解现有类的接口定义
- **开发过程**: 严格按照字典中的方法签名进行调用
- **步骤结束**: 必须先更新字典，再在进度控制文件中标记完成
- **命名统一**: 新增类必须遵循现有的命名规范
- **接口兼容**: 确保新类与现有类的无缝集成
- **窗体文件完整**: 涉及窗体的类必须包含.cs、.Designer.cs、.resx三个文件

#### 📊 第一阶段已完成类
已完成以下核心类的基础结构：
- ✅ ETWebClient (ETWebClient.cs) - 主客户端类，已实现基础架构
- ✅ ETWebLoginBrowser (ETWebLoginBrowser.cs + .Designer.cs + .resx) - 登录浏览器窗体类
- ✅ ETWebApiClient (ETWebApiClient.cs) - API交互客户端，基于Flurl.Http
- ✅ ETWebSessionManager (ETWebSessionManager.cs) - 会话管理器
- ✅ ETWebFileUploader (ETWebFileUploader.cs) - 文件上传处理器
- ✅ ETWebSimulationBrowser (ETWebSimulationBrowser.cs + .Designer.cs + .resx) - 模拟操作浏览器窗体类
- ✅ ETWebLoginInfo (Models/ETWebLoginInfo.cs) - 登录信息模型
- ✅ ETWebApiRequest (Models/ETWebApiRequest.cs) - API请求模型
- ✅ ETWebApiResponse (Models/ETWebApiResponse.cs) - API响应模型
- ✅ ETWebUploadResult (Models/ETWebUploadResult.cs) - 上传结果模型
- ✅ ETWebSessionData (Models/ETWebSessionData.cs) - 会话数据模型
- ✅ ETWebAuthStorage (Storage/ETWebAuthStorage.cs) - 认证信息存储类
- ✅ ETWebSessionStorage (Storage/ETWebSessionStorage.cs) - 会话状态存储类

**🎯 第一阶段基础架构搭建已完成，所有核心类、数据模型和存储模块已创建完毕。**

#### 🔧 ExtensionsTools集成完成情况
- ✅ **ETIniFile** - 已集成到所有需要配置管理的类中
- ✅ **ETLogManager** - 已集成到所有类中，提供统一的日志记录
- ✅ **ETException** - 已集成到所有类中，提供统一的异常处理

---

**📌 重要提醒：此字典是ETWebAutomation多文件协调开发的核心工具，必须严格维护，确保所有开发都基于此字典进行！**

**🚨 第一阶段开发规则：每个步骤开始前必须读取此字典，每个步骤完成后必须立即更新对应类的方法和属性定义！**
