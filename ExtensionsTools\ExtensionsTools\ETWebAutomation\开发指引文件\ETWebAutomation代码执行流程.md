# 🚀 ETWebAutomation 代码执行流程文档

## 📋 概述

ETWebAutomation是一个专为OA系统自动化操作设计的C#辅助库，基于CefSharp和Flurl.Http技术栈，提供完整的登录认证、API交互、文件上传、会话管理和网页自动化操作功能。

## 🏗️ 模块架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                    ETWebClient (主客户端)                        │
│  ┌─────────────────┬─────────────────┬─────────────────────────┐ │
│  │  登录认证模块    │   API交互模块    │      文件上传模块        │ │
│  │ ETWebLoginBrowser│  ETWebApiClient  │   ETWebFileUploader      │ │
│  └─────────────────┴─────────────────┴─────────────────────────┘ │
│  ┌─────────────────┬─────────────────┬─────────────────────────┐ │
│  │   会话管理模块   │  模拟操作模块    │      辅助工具模块        │ │
│  │ETWebSessionManager│ETWebSimulationBrowser│    Helpers/        │ │
│  └─────────────────┴─────────────────┴─────────────────────────┘ │
│  ┌─────────────────┬─────────────────┬─────────────────────────┐ │
│  │   数据模型层     │   存储管理层     │   ExtensionsTools集成   │ │
│  │    Models/      │    Storage/     │ ETIniFile/ETLogManager  │ │
│  └─────────────────┴─────────────────┴─────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 🔄 核心执行流程

### 1. 系统初始化流程

```mermaid
graph TD
    A[创建ETWebClient实例] --> B[加载配置文件ETWebConfig.ini]
    B --> C[初始化各子模块]
    C --> D[ETWebLoginBrowser初始化]
    C --> E[ETWebApiClient初始化]
    C --> F[ETWebSessionManager初始化]
    C --> G[ETWebFileUploader初始化]
    C --> H[ETWebSimulationBrowser初始化]
    D --> I[设置事件监听器]
    E --> I
    F --> I
    G --> I
    H --> I
    I --> J[系统就绪]
```

### 2. 登录认证流程

```mermaid
graph TD
    A[调用LoginAsync] --> B[创建ETWebLoginBrowser窗体]
    B --> C[加载登录页面]
    C --> D{是否自动登录模式}
    D -->|是| E[自动填充用户名密码]
    D -->|否| F[等待用户手动输入]
    E --> G[提交登录表单]
    F --> G
    G --> H[监听页面变化]
    H --> I{登录是否成功}
    I -->|成功| J[提取认证信息]
    I -->|失败| K[返回错误信息]
    J --> L[创建ETWebLoginInfo对象]
    L --> M[保存Cookie和Token]
    M --> N[更新会话状态]
    N --> O[关闭登录浏览器]
    O --> P[返回登录结果]
```

### 3. API交互流程

```mermaid
graph TD
    A[调用API方法] --> B[创建ETWebApiRequest对象]
    B --> C[设置认证信息]
    C --> D[添加请求头和Cookie]
    D --> E[构建Flurl请求]
    E --> F[发送HTTP请求]
    F --> G{请求是否成功}
    G -->|成功| H[解析响应数据]
    G -->|失败| I[检查是否需要重试]
    I -->|需要| J[执行重试逻辑]
    I -->|不需要| K[返回错误响应]
    J --> F
    H --> L[创建ETWebApiResponse对象]
    L --> M[反序列化JSON数据]
    M --> N[返回响应结果]
```

### 4. 文件上传流程

```mermaid
graph TD
    A[调用UploadFileAsync] --> B[验证文件有效性]
    B --> C{文件是否有效}
    C -->|无效| D[返回验证错误]
    C -->|有效| E[检查文件大小]
    E --> F{是否启用分块上传}
    F -->|是| G[分块上传流程]
    F -->|否| H[直接上传流程]
    G --> I[计算文件分块]
    I --> J[逐块上传]
    J --> K[更新上传进度]
    K --> L{所有分块完成}
    L -->|否| J
    L -->|是| M[合并文件]
    H --> N[创建表单数据]
    N --> O[发送上传请求]
    O --> P[监控上传进度]
    M --> Q[返回上传结果]
    P --> Q
```

### 5. 会话管理流程

```mermaid
graph TD
    A[启动会话监控] --> B[设置心跳定时器]
    B --> C[定期发送心跳请求]
    C --> D{心跳是否成功}
    D -->|成功| E[更新最后活动时间]
    D -->|失败| F[增加失败计数]
    E --> G[等待下次心跳]
    F --> H{失败次数超限}
    H -->|否| G
    H -->|是| I{是否启用自动重登}
    I -->|否| J[触发会话失效事件]
    I -->|是| K[执行自动重登]
    K --> L{重登是否成功}
    L -->|成功| M[重置失败计数]
    L -->|失败| N[增加重登尝试次数]
    M --> G
    N --> O{重登次数超限}
    O -->|否| K
    O -->|是| J
```

### 6. 模拟操作流程

```mermaid
graph TD
    A[创建ETWebSimulationBrowser] --> B[初始化CefSharp浏览器]
    B --> C[加载目标页面]
    C --> D[等待页面加载完成]
    D --> E{选择操作方式}
    E -->|DOM操作| F[执行JavaScript脚本]
    E -->|坐标操作| G[模拟鼠标键盘事件]
    F --> H[查找DOM元素]
    H --> I[执行元素操作]
    G --> J[计算坐标位置]
    J --> K[发送系统事件]
    I --> L[等待操作完成]
    K --> L
    L --> M{是否有后续操作}
    M -->|是| E
    M -->|否| N[返回操作结果]
```

## 📊 数据流转图

```mermaid
graph LR
    A[用户输入] --> B[ETWebClient]
    B --> C[ETWebLoginBrowser]
    C --> D[ETWebLoginInfo]
    D --> E[ETWebSessionManager]
    E --> F[ETWebSessionData]
    B --> G[ETWebApiClient]
    G --> H[ETWebApiRequest]
    H --> I[Flurl.Http]
    I --> J[ETWebApiResponse]
    B --> K[ETWebFileUploader]
    K --> L[ETWebUploadResult]
    B --> M[ETWebSimulationBrowser]
    
    N[ETWebConfigHelper] --> B
    O[ETWebJsonHelper] --> G
    P[ETWebCookieHelper] --> C
    Q[ETWebStorageHelper] --> E
    
    R[ETIniFile] --> N
    S[ETLogManager] --> B
    T[ETException] --> B
```

## 🔧 关键组件说明

### ETWebClient - 主客户端类
- **职责**: 统一入口，协调各子模块
- **关键方法**: LoginAsync, GetApiDataAsync, PostApiDataAsync, UploadFileAsync
- **生命周期**: 实现IDisposable，确保资源正确释放

### ETWebLoginBrowser - 登录认证模块
- **技术栈**: CefSharp.WinForms
- **核心功能**: 自动化登录、认证信息提取、验证码处理
- **输出**: ETWebLoginInfo对象，包含Token、Cookie、用户信息

### ETWebApiClient - API交互模块
- **技术栈**: Flurl.Http
- **核心功能**: HTTP请求封装、JSON序列化、错误处理、重试机制
- **支持方法**: GET、POST、PUT、DELETE等RESTful操作

### ETWebSessionManager - 会话管理模块
- **核心功能**: 会话状态监控、心跳维护、自动重登
- **存储**: 基于ETWebSessionStorage持久化会话数据
- **事件**: SessionStatusChanged、HeartbeatFailed、AutoReloginCompleted

### ETWebFileUploader - 文件上传模块
- **核心功能**: 单文件/批量上传、进度监控、断点续传
- **验证**: 文件大小、扩展名、完整性检查
- **回调**: 进度更新、上传完成事件

### ETWebSimulationBrowser - 模拟操作模块
- **技术栈**: CefSharp.WinForms + Windows API
- **操作模式**: DOM操作（JavaScript）+ 坐标操作（鼠标键盘事件）
- **功能**: 页面导航、元素操作、事件模拟、截图录制

## 🛠️ 辅助工具模块

### Helper类功能概览
- **ETWebConfigHelper**: 基于ETIniFile的配置管理
- **ETWebJsonHelper**: JSON序列化/反序列化、数据提取
- **ETWebCookieHelper**: Cookie管理、格式转换
- **ETWebStorageHelper**: 本地数据存储、加密保护
- **ETWebRetryHelper**: 重试策略、错误分类
- **ETWebPerformanceHelper**: 性能监控、资源统计
- **ETWebAutoReloginHelper**: 自动重登逻辑
- **ETWebBrowserAutomationHelper**: 浏览器自动化辅助

### Models数据模型
- **ETWebLoginInfo**: 登录认证信息模型
- **ETWebApiRequest**: API请求数据模型
- **ETWebApiResponse**: API响应数据模型
- **ETWebSessionData**: 会话状态数据模型
- **ETWebUploadResult**: 文件上传结果模型

### Storage存储管理
- **ETWebAuthStorage**: 认证信息加密存储
- **ETWebSessionStorage**: 会话数据持久化存储

## 🔗 ExtensionsTools集成

### 核心集成模块
- **ETIniFile**: 配置文件读写管理
- **ETLogManager**: 统一日志记录系统
- **ETException**: 异常处理和错误管理

### 集成优势
- 统一的配置管理策略
- 完整的日志追踪体系
- 标准化的异常处理机制
- 与现有系统无缝集成

## 📈 性能优化策略

### 并发控制
- API请求并发限制（SemaphoreSlim）
- 文件上传队列管理
- 浏览器操作串行化

### 资源管理
- 及时释放CefSharp资源
- HTTP连接池复用
- 内存使用监控

### 缓存策略
- API响应缓存
- 配置信息缓存
- 会话状态缓存

## 🔒 安全考虑

### 数据保护
- 认证信息加密存储
- 敏感数据内存清理
- 传输过程HTTPS保护

### 访问控制
- 会话超时管理
- 权限验证机制
- 操作日志记录

## 🔧 详细模块功能说明

### ETWebClient - 主客户端类
**文件**: `ETWebClient.cs`
**职责**: 系统统一入口，协调各功能模块

#### 核心方法
- `LoginAsync(username, password)` - 执行登录认证
- `GetApiDataAsync<T>(endpoint)` - 发送GET请求
- `PostApiDataAsync<T>(endpoint, data)` - 发送POST请求
- `UploadFileAsync(endpoint, filePath, formData)` - 上传文件
- `SetConfig(section, key, value)` - 设置配置项
- `SetGlobalSetting(key, value)` - 设置全局设置

#### 属性
- `BaseUrl` - OA系统基础URL
- `IsLoggedIn` - 登录状态
- `LoginInfo` - 登录信息对象
- `SessionManager` - 会话管理器
- `ApiClient` - API客户端
- `FileUploader` - 文件上传器

### ETWebLoginBrowser - 登录认证模块
**文件**: `ETWebLoginBrowser.cs` + `.Designer.cs` + `.resx`
**技术**: CefSharp.WinForms
**职责**: 自动化OA系统登录流程

#### 核心方法
- `AutoLoginAsync(username, password)` - 自动登录
- `ManualLoginAsync()` - 手动登录
- `GetLoginInfo()` - 获取登录信息
- `ExtractAuthenticationInfo()` - 提取认证信息
- `HandleCaptcha()` - 处理验证码

#### 事件
- `LoginCompleted` - 登录完成事件
- `LoginFailed` - 登录失败事件
- `CaptchaRequired` - 需要验证码事件

### ETWebApiClient - API交互模块
**文件**: `ETWebApiClient.cs`
**技术**: Flurl.Http
**职责**: HTTP请求封装和API交互

#### 核心方法
- `GetAsync<T>(endpoint, queryParams, retryCount)` - GET请求
- `PostAsync<T>(endpoint, data, retryCount)` - POST请求
- `PutAsync<T>(endpoint, data, retryCount)` - PUT请求
- `DeleteAsync<T>(endpoint, retryCount)` - DELETE请求
- `SetAuthenticationInfo(loginInfo)` - 设置认证信息

#### 特性
- 自动JSON序列化/反序列化
- 智能重试机制
- Cookie自动管理
- 请求/响应日志记录

### ETWebSessionManager - 会话管理模块
**文件**: `ETWebSessionManager.cs`
**职责**: 维护登录状态和会话生命周期

#### 核心方法
- `StartMonitoring(loginInfo)` - 启动会话监控
- `StopMonitoring()` - 停止会话监控
- `SendHeartbeat()` - 发送心跳请求
- `CheckSessionValidity()` - 检查会话有效性
- `TriggerAutoRelogin()` - 触发自动重登

#### 属性
- `HeartbeatInterval` - 心跳间隔（秒）
- `AutoReloginEnabled` - 是否启用自动重登
- `MaxReloginAttempts` - 最大重登尝试次数
- `CurrentSession` - 当前会话数据

#### 事件
- `SessionStatusChanged` - 会话状态变更
- `HeartbeatFailed` - 心跳失败
- `AutoReloginCompleted` - 自动重登完成

### ETWebFileUploader - 文件上传模块
**文件**: `ETWebFileUploader.cs`
**职责**: 处理文件上传操作

#### 核心方法
- `UploadFileAsync(endpoint, filePath, formData)` - 单文件上传
- `UploadFilesAsync(endpoint, filePaths, formData)` - 批量上传
- `UploadWithProgressAsync(endpoint, filePath, progressCallback)` - 带进度上传
- `ValidateFile(filePath)` - 文件验证
- `SetProgressCallback(callback)` - 设置进度回调

#### 属性
- `MaxFileSize` - 最大文件大小（MB）
- `AllowedExtensions` - 允许的文件扩展名
- `ChunkSize` - 分块大小
- `EnableResumableUpload` - 是否启用断点续传
- `MaxConcurrentUploads` - 最大并发上传数

### ETWebSimulationBrowser - 模拟操作模块
**文件**: `ETWebSimulationBrowser.cs` + `.Designer.cs` + `.resx`
**技术**: CefSharp + Windows API
**职责**: 网页自动化操作

#### DOM操作方法
- `FillTextAsync(selector, text)` - 填充文本
- `ClickElementAsync(selector)` - 点击元素
- `SelectOptionAsync(selector, value)` - 选择下拉选项
- `SubmitFormAsync(selector)` - 提交表单
- `WaitForElementAsync(selector, timeout)` - 等待元素出现

#### 坐标操作方法
- `ClickAtAsync(x, y)` - 坐标点击
- `SendKeysAsync(text)` - 发送键盘输入
- `MouseMoveAsync(x, y)` - 鼠标移动
- `ScrollToAsync(x, y)` - 滚动到位置

#### 页面操作方法
- `NavigateAsync(url)` - 导航到URL
- `RefreshAsync()` - 刷新页面
- `GoBackAsync()` - 后退
- `GoForwardAsync()` - 前进
- `TakeScreenshotAsync()` - 截图

## 🛠️ Helper类详细说明

### ETWebConfigHelper - 配置管理
**文件**: `Helpers/ETWebConfigHelper.cs`
**基础**: ETIniFile
**职责**: 统一配置管理

#### 主要方法
- `GETWebBaseUrl()` / `SETWebBaseUrl(url)` - OA基础URL
- `GetRequestTimeout()` / `SetRequestTimeout(seconds)` - 请求超时
- `GetMaxConcurrentRequests()` / `SetMaxConcurrentRequests(count)` - 最大并发数
- `GetAutoRetryEnabled()` / `SetAutoRetryEnabled(enabled)` - 自动重试
- `GetConfig(section, key, defaultValue)` - 通用配置读取
- `SetConfig(section, key, value)` - 通用配置设置

### ETWebJsonHelper - JSON处理
**文件**: `Helpers/ETWebJsonHelper.cs`
**基础**: Newtonsoft.Json
**职责**: JSON序列化和数据处理

#### 主要方法
- `ToJson(obj, prettyFormat)` - 对象转JSON
- `FromJson<T>(json)` - JSON转对象
- `FromJsonDynamic(json)` - JSON转动态对象
- `IsValidJson(json)` - 验证JSON格式
- `ExtractValue(json, path)` - 提取JSON路径值
- `MergeJson(json1, json2)` - 合并JSON对象

### ETWebCookieHelper - Cookie管理
**文件**: `Helpers/ETWebCookieHelper.cs`
**职责**: Cookie处理和格式转换

#### 主要方法
- `ParseCookieString(cookieString)` - 解析Cookie字符串
- `FormatCookieString(cookies)` - 格式化Cookie字符串
- `CreateCookieJar(cookies, domain, path)` - 创建CookieJar
- `MergeCookies(cookies1, cookies2, overwrite)` - 合并Cookie
- `FilterCookies(cookies, includeNames, excludeNames)` - 过滤Cookie
- `ValidateCookie(name, value)` - 验证Cookie

### ETWebStorageHelper - 存储管理
**文件**: `Helpers/ETWebStorageHelper.cs`
**职责**: 本地数据存储和加密

#### 主要方法
- `SaveData(key, data, encrypt)` - 保存数据
- `LoadData<T>(key, decrypt)` - 加载数据
- `DeleteData(key)` - 删除数据
- `DataExists(key)` - 检查数据存在
- `EncryptData(data, key)` - 加密数据
- `DecryptData(encryptedData, key)` - 解密数据

### ETWebRetryHelper - 重试机制
**文件**: `Helpers/ETWebRetryHelper.cs`
**职责**: 智能重试策略

#### 主要方法
- `ExecuteWithRetryAsync<T>(func, config)` - 执行带重试的异步操作
- `ExecuteWithRetry<T>(func, config)` - 执行带重试的同步操作
- `CreateDefaultRetryConfig()` - 创建默认重试配置
- `CreateApiRetryConfig()` - 创建API重试配置
- `CreateAuthRetryConfig()` - 创建认证重试配置
- `ShouldRetry(exception, attempt, config)` - 判断是否应该重试

### ETWebPerformanceHelper - 性能监控
**文件**: `Helpers/ETWebPerformanceHelper.cs`
**职责**: 性能监控和资源统计

#### 主要方法
- `StartTimer(operationName)` - 开始计时
- `StopTimer(operationName)` - 停止计时
- `RecordApiCall(endpoint, duration, success)` - 记录API调用
- `GetPerformanceReport()` - 获取性能报告
- `MonitorMemoryUsage()` - 监控内存使用
- `CheckResourceLimits()` - 检查资源限制

### ETWebAutoReloginHelper - 自动重登
**文件**: `Helpers/ETWebAutoReloginHelper.cs`
**职责**: 自动重新登录逻辑

#### 主要方法
- `SetCredentials(username, password, baseUrl)` - 设置登录凭据
- `AttemptReloginAsync()` - 尝试重新登录
- `IsReloginNeeded(response)` - 判断是否需要重登
- `ClearStoredCredentials()` - 清除存储的凭据
- `GetLastReloginTime()` - 获取最后重登时间

## 📊 Models数据模型详解

### ETWebLoginInfo - 登录信息模型
**文件**: `Models/ETWebLoginInfo.cs`
**用途**: 存储登录认证信息

#### 主要属性
- `IsSuccess` - 登录是否成功
- `Username` / `UserId` / `DisplayName` - 用户信息
- `Token` / `SessionId` - 认证标识
- `Cookies` / `Headers` - 请求信息
- `BaseUrl` / `LoginUrl` / `RedirectUrl` - URL信息
- `Permissions` / `Roles` - 权限信息
- `LoginTime` - 登录时间

#### 主要方法
- `IsValid()` - 检查登录信息有效性
- `GetCookie(cookieName)` - 获取指定Cookie
- `AddPermission(permission)` - 添加权限
- `HasPermission(permission)` - 检查权限

### ETWebApiRequest - API请求模型
**文件**: `Models/ETWebApiRequest.cs`
**用途**: 封装API请求数据

#### 主要属性
- `RequestId` - 请求唯一标识
- `Method` - HTTP方法
- `Endpoint` / `FullUrl` - 请求地址
- `Headers` / `QueryParameters` - 请求参数
- `Body` / `BodyJson` - 请求体
- `FormData` / `FileData` - 表单数据
- `TimeoutSeconds` / `RetryCount` - 请求配置
- `AuthToken` / `Cookies` - 认证信息

#### 主要方法
- `AddHeader(name, value)` - 添加请求头
- `AddQueryParameter(name, value)` - 添加查询参数
- `AddCookie(name, value)` - 添加Cookie
- `SetAuthentication(loginInfo)` - 设置认证信息

### ETWebApiResponse - API响应模型
**文件**: `Models/ETWebApiResponse.cs`
**用途**: 封装API响应数据

#### 主要属性
- `ResponseId` / `RequestId` - 响应标识
- `StatusCode` / `IsSuccess` - 响应状态
- `Headers` / `ContentType` - 响应头信息
- `RawContent` / `Data` / `DataJson` - 响应内容
- `ErrorMessage` / `Exception` - 错误信息
- `RequestTime` / `ResponseTime` - 时间信息
- `Duration` - 请求耗时

#### 主要方法
- `GetData<T>()` - 获取强类型数据
- `IsSuccessStatusCode()` - 检查状态码是否成功
- `GetHeader(name)` - 获取响应头
- `HasError()` - 检查是否有错误

### ETWebSessionData - 会话数据模型
**文件**: `Models/ETWebSessionData.cs`
**用途**: 存储会话状态信息

#### 主要属性
- `SessionId` / `Username` / `UserId` - 会话标识
- `CreatedTime` / `LastActivityTime` / `ExpiryTime` - 时间信息
- `IsValid` / `IsExpired` / `RemainingMinutes` - 状态信息
- `AuthToken` / `RefreshToken` - 认证令牌
- `Cookies` / `Status` - 会话数据
- `HeartbeatInterval` / `LastHeartbeatTime` - 心跳信息

#### 主要方法
- `UpdateActivity()` - 更新活动时间
- `IsExpired()` - 检查是否过期
- `ExtendSession(minutes)` - 延长会话
- `GetRemainingTime()` - 获取剩余时间

### ETWebUploadResult - 上传结果模型
**文件**: `Models/ETWebUploadResult.cs`
**用途**: 存储文件上传结果

#### 主要属性
- `IsSuccess` / `ErrorMessage` - 上传状态
- `FileName` / `FilePath` / `FileSize` - 文件信息
- `UploadedSize` / `Progress` - 上传进度
- `StartTime` / `EndTime` / `Duration` - 时间信息
- `ServerResponse` / `FileId` / `FileUrl` - 服务器响应
- `ChunkCount` / `CompletedChunks` - 分块信息

#### 主要方法
- `UpdateProgress(uploadedSize)` - 更新进度
- `MarkAsCompleted()` - 标记完成
- `MarkAsFailed(error)` - 标记失败
- `GetUploadSpeed()` - 获取上传速度

## 💾 Storage存储管理详解

### ETWebAuthStorage - 认证信息存储
**文件**: `Storage/ETWebAuthStorage.cs`
**职责**: 加密存储认证信息

#### 主要方法
- `SaveLoginInfo(username, loginInfo)` - 保存登录信息
- `LoadLoginInfo(username)` - 加载登录信息
- `DeleteLoginInfo(username)` - 删除登录信息
- `GetStoredUsers()` - 获取已存储用户列表
- `ClearAllLoginInfo()` - 清除所有登录信息

### ETWebSessionStorage - 会话状态存储
**文件**: `Storage/ETWebSessionStorage.cs`
**职责**: 持久化会话数据

#### 主要方法
- `SaveSession(sessionData)` - 保存会话数据
- `LoadSession(sessionId)` - 加载会话数据
- `DeleteSession(sessionId)` - 删除会话数据
- `GetActiveSessions()` - 获取活跃会话列表
- `CleanupExpiredSessions()` - 清理过期会话

## 📝 使用示例

详细的使用示例请参考：
- `示例/基础使用示例.cs` - 基础功能演示
- `文档/API使用文档.md` - 完整API文档
- `文档/最佳实践指南.md` - 开发最佳实践

---

**📅 文档版本**: v1.0
**🔄 最后更新**: 2024年12月
**👨‍💻 维护团队**: ETWebAutomation开发组
