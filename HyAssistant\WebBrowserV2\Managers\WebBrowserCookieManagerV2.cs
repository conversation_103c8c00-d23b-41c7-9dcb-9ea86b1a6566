using ET;
using ET.ETLoginWebBrowser;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using HyAssistant.WebBrowserV2.Utils;

namespace HyAssistant.WebBrowserV2.Managers
{
    /// <summary>
    /// WebBrowser Cookie管理器V2版本，负责HTTP请求标头的捕获、导入导出和管理
    /// 优化重点：异步编程安全、CookiePath生成逻辑、日志统一
    /// </summary>
    public class WebBrowserCookieManagerV2 : IDisposable
    {
        #region 字段和属性

        /// <summary>
        /// 用于记录的对象
        /// </summary>
        private readonly object _logSource;

        /// <summary>
        /// 标头文件路径
        /// </summary>
        public string HeadersPath { get; set; }

        /// <summary>
        /// Cookie文件路径（保持向后兼容，映射到HeadersPath）
        /// </summary>
        public string CookiePath
        {
            get => HeadersPath;
            set => HeadersPath = value;
        }

        /// <summary>
        /// 当前URL
        /// </summary>
        public string CurrentUrl { get; set; }

        /// <summary>
        /// 标头数据的JSON字符串
        /// </summary>
        private string _headersJson = string.Empty;

        /// <summary>
        /// 最后一次捕获的请求标头
        /// </summary>
        private Dictionary<string, string> _lastRequestHeaders = new Dictionary<string, string>();

        /// <summary>
        /// 存储所有捕获的请求标头
        /// </summary>
        private readonly List<Dictionary<string, string>> _allCapturedRequests = new List<Dictionary<string, string>>();

        /// <summary>
        /// Cookie数据（保持向后兼容）
        /// </summary>
        public CookieData CookieData { get; set; } = new CookieData();

        /// <summary>
        /// 是否已释放
        /// </summary>
        private bool _disposed = false;

        /// <summary>
        /// 自动导出标头的定时器
        /// </summary>
        private System.Windows.Forms.Timer _autoExportTimer;

        /// <summary>
        /// 异步操作取消令牌源
        /// </summary>
        private readonly CancellationTokenSource _cancellationTokenSource;

        /// <summary>
        /// 线程安全锁
        /// </summary>
        private readonly object _syncLock = new object();

        /// <summary>
        /// WebView2线程安全操作器（V2新增）
        /// </summary>
        private readonly WebView2ThreadSafeOperatorV2 _webView2Operator;

        /// <summary>
        /// Cookie路径管理器（V2版本关键优化）
        /// </summary>
        private readonly CookiePathManagerV2 _cookiePathManager;

        /// <summary>
        /// 获取捕获的请求总数
        /// </summary>
        public int CapturedRequestsCount
        {
            get
            {
                lock (_syncLock)
                {
                    return _allCapturedRequests.Count;
                }
            }
        }

        /// <summary>
        /// 获取捕获的API请求数量
        /// </summary>
        public int ApiRequestsCount
        {
            get
            {
                lock (_syncLock)
                {
                    return _allCapturedRequests.Count(r =>
                        r.ContainsKey("request-type") && r["request-type"] == "API");
                }
            }
        }

        #endregion 字段和属性

        #region 构造方法

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logSource">用于记录日志的对象</param>
        /// <param name="headersPath">标头文件路径（兼容旧参数名cookiePath）</param>
        /// <param name="url">当前URL</param>
        public WebBrowserCookieManagerV2(object logSource, string headersPath = null, string url = null)
        {
            _logSource = logSource ?? this;
            HeadersPath = headersPath;
            CurrentUrl = url;
            _cancellationTokenSource = new CancellationTokenSource();

            // V2版本新增: 初始化WebView2线程安全操作器
            _webView2Operator = new WebView2ThreadSafeOperatorV2(_logSource);

            // V2版本关键优化: 初始化Cookie路径管理器
            _cookiePathManager = new CookiePathManagerV2(_logSource);

            ETLogManager.Info(_logSource, "WebBrowserCookieManagerV2初始化完成，包含Cookie路径管理器");
        }

        #endregion 构造方法

        #region CookiePath生成优化

        /// <summary>
        /// 生成Cookie文件路径，确保使用当前WebView URL作为第一个'url'项
        /// 这是V2版本的重点优化功能，委托给CookiePathManagerV2处理
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        /// <returns>生成的Cookie文件路径</returns>
        public string GenerateCookiePathV2(WebView2 webView, WebBrowserTabConfig config)
        {
            if (_disposed)
            {
                ETLogManager.Warning(_logSource, "WebBrowserCookieManagerV2已释放，无法生成路径");
                return GetDefaultCookiePath(config);
            }

            try
            {
                ETLogManager.Info(_logSource, $"V2优化：使用CookiePathManagerV2生成路径: {config?.Name ?? "Unknown"}");

                // V2版本关键优化：委托给专门的路径管理器处理
                string cookiePath = _cookiePathManager.GenerateCookiePath(webView, config, true);

                if (!string.IsNullOrEmpty(cookiePath))
                {
                    ETLogManager.Info(_logSource, $"V2优化：Cookie路径生成成功: {cookiePath}");
                    return cookiePath;
                }
                else
                {
                    ETLogManager.Warning(_logSource, "CookiePathManagerV2返回空路径，使用默认路径");
                    return GetDefaultCookiePath(config);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"V2优化：生成Cookie路径失败: {ex.Message}");
                return GetDefaultCookiePath(config);
            }
        }

        /// <summary>
        /// 生成Cookie文件路径（兼容性方法）
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        /// <returns>生成的Cookie文件路径</returns>
        public string GenerateCookiePath(WebView2 webView, WebBrowserTabConfig config)
        {
            // 委托给V2优化方法
            return GenerateCookiePathV2(webView, config);
        }

        /// <summary>
        /// 写入Cookie文件，确保使用当前WebView URL（V2版本关键优化）
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        /// <param name="cookieData">Cookie数据</param>
        /// <param name="extraHeaders">额外的标头信息</param>
        /// <returns>是否写入成功</returns>
        public async Task<bool> WriteCookieFileWithCurrentUrlAsync(WebView2 webView, WebBrowserTabConfig config,
            CookieData cookieData, Dictionary<string, string> extraHeaders = null)
        {
            if (_disposed)
            {
                ETLogManager.Warning(_logSource, "WebBrowserCookieManagerV2已释放，无法写入文件");
                return false;
            }

            try
            {
                ETLogManager.Info(_logSource, $"V2优化：开始写入Cookie文件: {config?.Name ?? "Unknown"}");

                // V2版本关键优化：委托给CookiePathManagerV2处理
                bool result = await _cookiePathManager.WriteCookieFileWithCurrentUrlAsync(webView, config, cookieData, extraHeaders);

                if (result)
                {
                    ETLogManager.Info(_logSource, $"V2优化：Cookie文件写入成功");
                }
                else
                {
                    ETLogManager.Warning(_logSource, "V2优化：Cookie文件写入失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"V2优化：写入Cookie文件异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 写入Cookie文件（同步版本）
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        /// <param name="cookieData">Cookie数据</param>
        /// <param name="extraHeaders">额外的标头信息</param>
        /// <returns>是否写入成功</returns>
        public bool WriteCookieFileWithCurrentUrl(WebView2 webView, WebBrowserTabConfig config,
            CookieData cookieData, Dictionary<string, string> extraHeaders = null)
        {
            try
            {
                return WriteCookieFileWithCurrentUrlAsync(webView, config, cookieData, extraHeaders).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"V2优化：同步写入Cookie文件异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取当前WebView的URL（线程安全）
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        /// <returns>当前URL</returns>
        private string GetCurrentWebViewUrl(WebView2 webView, WebBrowserTabConfig config)
        {
            try
            {
                if (webView?.CoreWebView2 != null)
                {
                    // 使用当前WebView的Source属性，这是V2版本的关键优化
                    string webViewUrl = webView.Source?.ToString();
                    if (!string.IsNullOrEmpty(webViewUrl) && webViewUrl != "about:blank")
                    {
                        return webViewUrl;
                    }
                }

                // 如果WebView URL不可用，使用配置URL
                return config?.Url;
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(_logSource, $"获取WebView URL失败: {ex.Message}");
                return config?.Url;
            }
        }

        /// <summary>
        /// 从URL生成文件名
        /// </summary>
        /// <param name="url">URL</param>
        /// <returns>文件名</returns>
        private string GenerateFileNameFromUrl(string url)
        {
            try
            {
                if (string.IsNullOrEmpty(url))
                    return $"cookie_{DateTime.Now:yyyyMMddHHmmss}";

                var uri = new Uri(url);
                string fileName = $"{uri.Host}_{DateTime.Now:yyyyMMdd}";

                // 移除无效字符
                foreach (char c in Path.GetInvalidFileNameChars())
                {
                    fileName = fileName.Replace(c, '_');
                }

                return fileName;
            }
            catch
            {
                return $"cookie_{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        /// <summary>
        /// 获取默认Cookie路径
        /// </summary>
        /// <param name="config">标签页配置</param>
        /// <returns>默认路径</returns>
        private string GetDefaultCookiePath(WebBrowserTabConfig config)
        {
            string cookieDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "cookies");
            string fileName = config?.SectionId ?? $"default_{DateTime.Now:yyyyMMddHHmmss}";
            return Path.Combine(cookieDir, $"{fileName}.json");
        }

        #endregion CookiePath生成优化

        #region 标头捕获方法

        /// <summary>
        /// 设置WebView2的标头捕获（异步安全版本）
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <returns>设置任务</returns>
        public async Task SetupHeadersCaptureAsync(WebView2 webView)
        {
            try
            {
                if (webView == null)
                {
                    ETLogManager.Warning(_logSource, "设置标头捕获失败: WebView2为空");
                    return;
                }

                if (_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    ETLogManager.Info(_logSource, "标头捕获设置被取消");
                    return;
                }

                // 确保在UI线程上执行
                if (webView.InvokeRequired)
                {
                    var tcs = new TaskCompletionSource<bool>();
                    webView.BeginInvoke(new Action(async () =>
                    {
                        try
                        {
                            await SetupHeadersCaptureInternalAsync(webView).ConfigureAwait(false);
                            tcs.SetResult(true);
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error(_logSource, $"设置标头捕获失败（UI线程）: {ex.Message}");
                            tcs.SetException(ex);
                        }
                    }));

                    await tcs.Task.ConfigureAwait(false);
                }
                else
                {
                    await SetupHeadersCaptureInternalAsync(webView).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"设置WebView2标头捕获失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 内部标头捕获设置方法，必须在UI线程上调用
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <returns>设置任务</returns>
        private Task SetupHeadersCaptureInternalAsync(WebView2 webView)
        {
            try
            {
                // 在UI线程上安全地检查CoreWebView2
                CoreWebView2 coreWebView2 = null;
                try
                {
                    coreWebView2 = webView.CoreWebView2;
                }
                catch (InvalidOperationException ex)
                {
                    ETLogManager.Warning(_logSource, $"CoreWebView2未初始化，跳过标头捕获设置: {ex.Message}");
                    return Task.CompletedTask;
                }

                if (coreWebView2 == null)
                {
                    ETLogManager.Warning(_logSource, "CoreWebView2为空，跳过标头捕获设置");
                    return Task.CompletedTask;
                }

                // 添加WebResourceRequested事件处理器
                coreWebView2.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);

                // 创建事件处理器
                EventHandler<CoreWebView2WebResourceRequestedEventArgs> handler = (sender, e) =>
                {
                    try
                    {
                        ProcessWebResourceRequest(e);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"处理Web资源请求失败: {ex.Message}");
                    }
                };

                coreWebView2.WebResourceRequested += handler;

                ETLogManager.Info(_logSource, "WebView2标头捕获设置完成");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"内部标头捕获设置失败: {ex.Message}");
                return Task.FromException(ex);
            }
        }

        /// <summary>
        /// 处理Web资源请求（线程安全版本）
        /// </summary>
        /// <param name="e">请求事件参数</param>
        private void ProcessWebResourceRequest(CoreWebView2WebResourceRequestedEventArgs e)
        {
            try
            {
                if (e?.Request == null || _disposed)
                    return;

                var request = e.Request;
                string url = request.Uri;
                string method = request.Method;

                // 创建请求标头字典
                var requestHeaders = new Dictionary<string, string>
                {
                    ["url"] = url,
                    ["method"] = method,
                    ["timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")
                };

                // 添加请求标头
                foreach (var header in request.Headers)
                {
                    requestHeaders[header.Key] = header.Value;
                }

                // 判断请求类型
                if (IsApiRequest(url, requestHeaders))
                {
                    requestHeaders["request-type"] = "API";
                }
                else
                {
                    requestHeaders["request-type"] = "Resource";
                }

                // 线程安全地添加到捕获列表
                lock (_syncLock)
                {
                    _allCapturedRequests.Add(requestHeaders);
                    _lastRequestHeaders = new Dictionary<string, string>(requestHeaders);
                }

                ETLogManager.Debug(_logSource, $"捕获请求: {method} {url}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"处理Web资源请求异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否为API请求
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <param name="headers">请求标头</param>
        /// <returns>是否为API请求</returns>
        private bool IsApiRequest(string url, Dictionary<string, string> headers)
        {
            try
            {
                if (string.IsNullOrEmpty(url))
                    return false;

                // 检查URL模式
                if (url.Contains("/api/") || url.Contains("/rest/") || url.Contains("/service/"))
                    return true;

                // 检查Content-Type
                if (headers.TryGetValue("Content-Type", out string contentType))
                {
                    if (contentType.Contains("application/json") || contentType.Contains("application/xml"))
                        return true;
                }

                // 检查Accept头
                if (headers.TryGetValue("Accept", out string accept))
                {
                    if (accept.Contains("application/json") || accept.Contains("application/xml"))
                        return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        #endregion 标头捕获方法

        #region Cookie操作方法

        /// <summary>
        /// 从WebView2获取Cookie并保存到文件（异步安全版本）
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <returns>Cookie数据</returns>
        public async Task<CookieData> GetCookiesFromWebView2Async(WebView2 webView)
        {
            try
            {
                if (webView == null)
                {
                    ETLogManager.Error(_logSource, "获取Cookie失败: WebView2为空");
                    return null;
                }

                if (_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    ETLogManager.Info(_logSource, "获取Cookie操作被取消");
                    return null;
                }

                // V2版本优化: 使用WebView2ThreadSafeOperatorV2确保线程安全
                var cookies = await _webView2Operator.GetCookiesSafelyAsync(webView).ConfigureAwait(false);
                if (cookies == null || cookies.Count == 0)
                {
                    ETLogManager.Warning(_logSource, "未获取到Cookie数据");
                    return new CookieData { Url = _webView2Operator.GetCurrentUrlSafely(webView), Cookies = new List<CookieItem>() };
                }

                // 获取当前URL，这是V2版本的关键优化
                string currentUrl = _webView2Operator.GetCurrentUrlSafely(webView);
                if (string.IsNullOrEmpty(currentUrl))
                {
                    ETLogManager.Warning(_logSource, "无法获取当前URL，使用默认值");
                    currentUrl = CurrentUrl ?? "about:blank";
                }

                // 更新当前URL
                CurrentUrl = currentUrl;

                // 创建Cookie数据对象
                var cookieData = new CookieData
                {
                    Url = currentUrl, // 使用当前WebView URL作为第一个'url'项
                    Cookies = new List<CookieItem>()
                };

                // 转换Cookie格式
                foreach (var cookie in cookies)
                {
                    cookieData.Cookies.Add(new CookieItem
                    {
                        Name = cookie.Name,
                        Value = cookie.Value,
                        Domain = cookie.Domain,
                        Path = cookie.Path,
                        Expires = cookie.Expires,
                        HttpOnly = cookie.IsHttpOnly,
                        Secure = cookie.IsSecure,
                        SameSite = cookie.SameSite.ToString()
                    });
                }

                // 更新内部Cookie数据
                CookieData = cookieData;

                ETLogManager.Info(_logSource, $"成功获取Cookie数据: {cookieData.Cookies.Count}个Cookie，URL: {currentUrl}");
                return cookieData;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"获取Cookie异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 在UI线程上执行的内部获取Cookie方法
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <returns>Cookie数据</returns>
        private async Task<CookieData> GetCookiesFromWebView2InternalAsync(WebView2 webView)
        {
            try
            {
                if (webView?.CoreWebView2 == null)
                {
                    ETLogManager.Error(_logSource, "获取Cookie失败: CoreWebView2为空");
                    return null;
                }

                // 获取当前URL，这是V2版本的关键优化
                string currentUrl = webView.Source?.ToString() ?? CurrentUrl;
                if (string.IsNullOrEmpty(currentUrl))
                {
                    ETLogManager.Warning(_logSource, "获取Cookie时URL为空，使用默认URL");
                    currentUrl = "about:blank";
                }

                // 更新当前URL
                CurrentUrl = currentUrl;

                // 获取所有Cookie
                var cookies = await webView.CoreWebView2.CookieManager.GetCookiesAsync(currentUrl).ConfigureAwait(false);

                // 创建Cookie数据对象
                var cookieData = new CookieData
                {
                    Url = currentUrl, // 使用当前WebView URL作为第一个'url'项
                    Cookies = new List<CookieItem>()
                };

                // 转换Cookie格式
                foreach (var cookie in cookies)
                {
                    var cookieItem = new CookieItem
                    {
                        Name = cookie.Name,
                        Value = cookie.Value,
                        Domain = cookie.Domain,
                        Path = cookie.Path,
                        Expires = cookie.Expires,
                        HttpOnly = cookie.IsHttpOnly,
                        Secure = cookie.IsSecure,
                        SameSite = cookie.SameSite.ToString()
                    };

                    cookieData.Cookies.Add(cookieItem);
                }

                // 更新内部Cookie数据
                CookieData = cookieData;

                ETLogManager.Info(_logSource, $"成功获取{cookieData.Cookies.Count}个Cookie，URL: {currentUrl}");
                return cookieData;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"内部获取Cookie失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 将Cookie设置到WebView2（异步安全版本）
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="cookieData">Cookie数据</param>
        /// <returns>设置任务</returns>
        public async Task SetCookiesToWebView2Async(WebView2 webView, CookieData cookieData = null)
        {
            try
            {
                if (webView == null)
                {
                    ETLogManager.Error(_logSource, "设置Cookie失败: WebView2为空");
                    return;
                }

                // 使用当前Cookie数据或传入的数据
                var dataToSet = cookieData ?? CookieData;
                if (dataToSet?.Cookies == null || dataToSet.Cookies.Count == 0)
                {
                    ETLogManager.Warning(_logSource, "没有Cookie数据需要设置");
                    return;
                }

                if (_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    ETLogManager.Info(_logSource, "设置Cookie操作被取消");
                    return;
                }

                // V2版本优化: 使用WebView2ThreadSafeOperatorV2确保线程安全
                ETLogManager.Info(_logSource, $"开始设置Cookie: {dataToSet.Cookies.Count}个Cookie");

                // 逐个设置Cookie，确保线程安全
                int successCount = 0;
                foreach (var cookieItem in dataToSet.Cookies)
                {
                    try
                    {
                        // 创建CoreWebView2Cookie对象 - 使用CookieManager
                        var cookieManager = webView.CoreWebView2.CookieManager;
                        var coreWebView2Cookie = cookieManager.CreateCookie(
                            cookieItem.Name,
                            cookieItem.Value,
                            cookieItem.Domain,
                            cookieItem.Path);

                        // 设置Cookie属性
                        if (cookieItem.Expires > DateTime.MinValue)
                        {
                            // 直接设置DateTime，WebView2会自动处理
                            coreWebView2Cookie.Expires = cookieItem.Expires.ToUniversalTime();
                        }
                        coreWebView2Cookie.IsHttpOnly = cookieItem.HttpOnly;
                        coreWebView2Cookie.IsSecure = cookieItem.Secure;

                        // 使用线程安全操作器设置Cookie
                        bool success = await _webView2Operator.SetCookieSafelyAsync(webView, coreWebView2Cookie).ConfigureAwait(false);
                        if (success)
                        {
                            successCount++;
                        }
                        else
                        {
                            ETLogManager.Warning(_logSource, $"设置Cookie失败: {cookieItem.Name}");
                        }
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"设置Cookie异常: {cookieItem.Name}, 错误: {ex.Message}");
                    }
                }

                ETLogManager.Info(_logSource, $"Cookie设置完成: 成功{successCount}/{dataToSet.Cookies.Count}个");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"设置Cookie异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 内部Cookie设置方法，必须在UI线程上调用
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="data">Cookie数据</param>
        /// <returns>设置任务</returns>
        private Task SetCookiesInternalAsync(WebView2 webView, CookieData data)
        {
            try
            {
                if (webView?.CoreWebView2 == null)
                {
                    ETLogManager.Error(_logSource, "设置Cookie失败: CoreWebView2为空");
                    return Task.CompletedTask;
                }

                if (data?.Cookies == null || data.Cookies.Count == 0)
                {
                    ETLogManager.Warning(_logSource, "没有Cookie数据需要设置");
                    return Task.CompletedTask;
                }

                var cookieManager = webView.CoreWebView2.CookieManager;
                int successCount = 0;

                foreach (var cookieItem in data.Cookies)
                {
                    try
                    {
                        var cookie = cookieManager.CreateCookie(
                            cookieItem.Name,
                            cookieItem.Value,
                            cookieItem.Domain,
                            cookieItem.Path);

                        // 设置Cookie属性
                        if (cookieItem.Expires > DateTime.MinValue)
                        {
                            // 直接设置DateTime，WebView2会自动处理
                            cookie.Expires = cookieItem.Expires.ToUniversalTime();
                        }

                        cookie.IsHttpOnly = cookieItem.HttpOnly;
                        cookie.IsSecure = cookieItem.Secure;

                        // 设置SameSite属性
                        if (Enum.TryParse<CoreWebView2CookieSameSiteKind>(cookieItem.SameSite, out var sameSite))
                        {
                            cookie.SameSite = sameSite;
                        }

                        cookieManager.AddOrUpdateCookie(cookie);
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Warning(_logSource, $"设置单个Cookie失败: {cookieItem.Name}, 错误: {ex.Message}");
                    }
                }

                ETLogManager.Info(_logSource, $"成功设置{successCount}/{data.Cookies.Count}个Cookie");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"内部设置Cookie失败: {ex.Message}");
                return Task.FromException(ex);
            }
        }

        #endregion Cookie操作方法

        #region 文件操作方法

        /// <summary>
        /// 保存Cookie到JSON文件（异步安全版本）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>保存任务</returns>
        public async Task SaveCookiesToFileAsync(string filePath = null)
        {
            try
            {
                string path = filePath ?? CookiePath;
                if (string.IsNullOrEmpty(path))
                {
                    ETLogManager.Warning(_logSource, "保存Cookie失败: 未指定文件路径");
                    return;
                }

                if (CookieData?.Cookies == null || CookieData.Cookies.Count == 0)
                {
                    ETLogManager.Warning(_logSource, "没有Cookie数据需要保存");
                    return;
                }

                if (_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    ETLogManager.Info(_logSource, "保存Cookie操作被取消");
                    return;
                }

                // 创建完整的数据结构，包含url、headers、cookies三部分
                var fullData = new
                {
                    url = CookieData.Url, // 使用当前WebView URL
                    headers = _lastRequestHeaders,
                    cookies = CookieData.Cookies,
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    version = "2.0" // 标记为V2版本
                };

                // 异步写入文件
                string jsonContent = JsonConvert.SerializeObject(fullData, Formatting.Indented);

                // 确保目录存在
                string directory = Path.GetDirectoryName(path);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                await Task.Run(() => File.WriteAllText(path, jsonContent), _cancellationTokenSource.Token).ConfigureAwait(false);

                ETLogManager.Info(_logSource, $"Cookie数据保存成功: {path}, 共{CookieData.Cookies.Count}个Cookie");
            }
            catch (OperationCanceledException)
            {
                ETLogManager.Info(_logSource, "保存Cookie操作被取消");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"保存Cookie到文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从JSON文件加载Cookie（异步安全版本）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>Cookie数据</returns>
        public async Task<CookieData> LoadCookiesFromFileAsync(string filePath = null)
        {
            try
            {
                string path = filePath ?? CookiePath;
                if (string.IsNullOrEmpty(path))
                {
                    ETLogManager.Warning(_logSource, "加载Cookie失败: 未指定文件路径");
                    return null;
                }

                if (!File.Exists(path))
                {
                    ETLogManager.Warning(_logSource, $"Cookie文件不存在: {path}");
                    return null;
                }

                if (_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    ETLogManager.Info(_logSource, "加载Cookie操作被取消");
                    return null;
                }

                // 异步读取文件
                string jsonContent = await Task.Run(() => File.ReadAllText(path), _cancellationTokenSource.Token).ConfigureAwait(false);

                if (string.IsNullOrEmpty(jsonContent))
                {
                    ETLogManager.Warning(_logSource, $"Cookie文件内容为空: {path}");
                    return null;
                }

                // 解析JSON数据
                var jsonData = JsonConvert.DeserializeObject<dynamic>(jsonContent);

                var cookieData = new CookieData();

                // 处理不同版本的数据格式
                if (jsonData.url != null)
                {
                    cookieData.Url = jsonData.url.ToString();
                }

                if (jsonData.cookies != null)
                {
                    cookieData.Cookies = JsonConvert.DeserializeObject<List<CookieItem>>(jsonData.cookies.ToString());
                }

                // 如果有headers数据，也加载进来
                if (jsonData.headers != null)
                {
                    var headers = JsonConvert.DeserializeObject<Dictionary<string, string>>(jsonData.headers.ToString());
                    lock (_syncLock)
                    {
                        _lastRequestHeaders = headers ?? new Dictionary<string, string>();
                    }
                }

                // 更新内部数据
                CookieData = cookieData;
                CurrentUrl = cookieData.Url;

                ETLogManager.Info(_logSource, $"Cookie数据加载成功: {path}, 共{cookieData.Cookies?.Count ?? 0}个Cookie");
                return cookieData;
            }
            catch (OperationCanceledException)
            {
                ETLogManager.Info(_logSource, "加载Cookie操作被取消");
                return null;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"从文件加载Cookie失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 导入Cookie到WebView2（异步安全版本）
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="cookiePath">Cookie文件路径</param>
        /// <returns>导入的Cookie数据</returns>
        public async Task<CookieData> ImportCookiesAsync(WebView2 webView, string cookiePath)
        {
            try
            {
                if (webView == null)
                {
                    ETLogManager.Error(_logSource, "导入Cookie失败: WebView2为空");
                    return null;
                }

                if (string.IsNullOrEmpty(cookiePath))
                {
                    ETLogManager.Error(_logSource, "导入Cookie失败: 文件路径为空");
                    return null;
                }

                ETLogManager.Info(_logSource, $"开始导入Cookie: {cookiePath}");

                // 从文件加载Cookie数据
                var cookieData = await LoadCookiesFromFileAsync(cookiePath).ConfigureAwait(false);
                if (cookieData?.Cookies == null || cookieData.Cookies.Count == 0)
                {
                    ETLogManager.Warning(_logSource, "没有Cookie数据可导入");
                    return null;
                }

                // 设置Cookie到WebView2
                await SetCookiesToWebView2Async(webView, cookieData).ConfigureAwait(false);

                ETLogManager.Info(_logSource, $"Cookie导入完成: 共{cookieData.Cookies.Count}个Cookie");
                return cookieData;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"导入Cookie异常: {ex.Message}");
                return null;
            }
        }

        #endregion 文件操作方法

        #region 自动导出功能

        /// <summary>
        /// 设置Cookie自动导出
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="cookiePath">Cookie文件路径</param>
        public void SetupAutoCookieExport(WebView2 webView, string cookiePath)
        {
            try
            {
                if (webView == null || string.IsNullOrEmpty(cookiePath))
                {
                    ETLogManager.Warning(_logSource, "设置自动Cookie导出失败: 参数无效");
                    return;
                }

                // 停止现有定时器
                if (_autoExportTimer != null)
                {
                    _autoExportTimer.Stop();
                    _autoExportTimer.Dispose();
                    _autoExportTimer = null;
                }

                // 创建新的定时器，每30秒自动导出一次
                _autoExportTimer = new System.Windows.Forms.Timer
                {
                    Interval = 30000 // 30秒
                };

                _autoExportTimer.Tick += (sender, e) =>
                {
                    try
                    {
                        if (_disposed || _cancellationTokenSource.Token.IsCancellationRequested)
                        {
                            _autoExportTimer?.Stop();
                            return;
                        }

                        // 异步执行Cookie导出，避免阻塞UI
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                var cookieData = await GetCookiesFromWebView2Async(webView).ConfigureAwait(false);
                                if (cookieData?.Cookies != null && cookieData.Cookies.Count > 0)
                                {
                                    await SaveCookiesToFileAsync(cookiePath).ConfigureAwait(false);
                                    ETLogManager.Debug(_logSource, $"自动导出Cookie完成: {cookieData.Cookies.Count}个");
                                }
                            }
                            catch (Exception ex)
                            {
                                ETLogManager.Warning(_logSource, $"自动导出Cookie失败: {ex.Message}");
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"自动导出定时器事件异常: {ex.Message}");
                    }
                };

                _autoExportTimer.Start();
                ETLogManager.Info(_logSource, $"Cookie自动导出已启动: {cookiePath}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"设置Cookie自动导出失败: {ex.Message}");
            }
        }

        #endregion 自动导出功能

        #region 工具方法

        /// <summary>
        /// 获取最后捕获的请求标头（线程安全）
        /// </summary>
        /// <returns>请求标头字典</returns>
        public Dictionary<string, string> GetLastRequestHeaders()
        {
            lock (_syncLock)
            {
                return new Dictionary<string, string>(_lastRequestHeaders);
            }
        }

        /// <summary>
        /// 获取所有捕获的请求（线程安全）
        /// </summary>
        /// <returns>请求列表</returns>
        public List<Dictionary<string, string>> GetAllCapturedRequests()
        {
            lock (_syncLock)
            {
                return new List<Dictionary<string, string>>(_allCapturedRequests);
            }
        }

        /// <summary>
        /// 清除捕获的请求数据（线程安全）
        /// </summary>
        public void ClearCapturedRequests()
        {
            lock (_syncLock)
            {
                _allCapturedRequests.Clear();
                _lastRequestHeaders.Clear();
            }

            ETLogManager.Info(_logSource, "已清除所有捕获的请求数据");
        }

        #endregion 工具方法

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        // 取消所有异步操作
                        _cancellationTokenSource?.Cancel();

                        // 停止并释放自动导出定时器
                        if (_autoExportTimer != null)
                        {
                            _autoExportTimer.Stop();
                            _autoExportTimer.Dispose();
                            _autoExportTimer = null;
                        }

                        // 清理捕获的数据
                        lock (_syncLock)
                        {
                            _allCapturedRequests.Clear();
                            _lastRequestHeaders.Clear();
                        }

                        _cancellationTokenSource?.Dispose();

                        // V2版本新增: 释放WebView2线程安全操作器
                        _webView2Operator?.Dispose();

                        // V2版本关键优化: 释放Cookie路径管理器
                        _cookiePathManager?.Dispose();

                        ETLogManager.Info(_logSource, "WebBrowserCookieManagerV2资源释放完成");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"释放WebBrowserCookieManagerV2资源失败: {ex.Message}");
                    }
                }

                _disposed = true;
            }
        }

        #endregion IDisposable实现
    }
}
