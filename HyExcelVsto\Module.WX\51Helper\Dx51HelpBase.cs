﻿using ET;
using Newtonsoft.Json;
using ServiceStack.Script;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using TextBox = System.Windows.Forms.TextBox;

namespace HyExcelVsto.Extensions.Dx51Helper
{
    /// <summary>
    /// 51平台API接口封装类，提供与服务器交互的基础功能
    /// </summary>
    /// <remarks>
    /// 该类封装了与51平台服务器进行交互的所有基础功能，包括：
    /// - HTTP请求处理（POST、文件上传下载等）
    /// - Cookie管理
    /// - JSON数据处理
    /// - 日志记录
    /// </remarks>
    public static partial class Zn51Helper
    {
        #region 变量定义
        /// <summary>
        /// 存储会话Cookie信息的字典
        /// </summary>
        public static Dictionary<string, string> Cookies;

        /// <summary>
        /// 获取用于HTTP请求的CookieContainer实例
        /// </summary>
        public static CookieContainer cookieContainer => ConvertToCookieContainer(Cookies, vsto51Constant.Url.Domain);

        /// <summary>
        /// 用于显示日志信息的文本框控件
        /// </summary>
        public static TextBox logTextBox = null;
        #endregion 变量定义

        /// <summary>
        /// 异步发送POST请求并获取响应内容
        /// </summary>
        /// <param name="url">请求的目标URL</param>
        /// <param name="formData">POST请求的表单数据</param>
        /// <param name="contentType">请求内容类型，默认为application/x-www-form-urlencoded</param>
        /// <param name="timeout">请求超时时间（毫秒），默认为15000</param>
        /// <returns>返回服务器响应的字符串内容</returns>
        /// <remarks>
        /// 该方法会自动处理：
        /// - Cookie管理
        /// - 授权头设置
        /// - 超时处理
        /// - 错误处理
        /// </remarks>
        public static async Task<string> PostHtmlAsync(
            string url,
            string formData,
            string contentType = "application/x-www-form-urlencoded",
            int timeout = 15000)
        {
            Debug.WriteLine($"请求地址：{url}");

            SynchronizationContext currentContext = SynchronizationContext.Current;
            using HttpClientHandler httpHandler = new() { CookieContainer = cookieContainer };
            using HttpClient httpClient = new(httpHandler) { Timeout = TimeSpan.FromMilliseconds(timeout) };

            StringContent requestContent = new(formData, Encoding.UTF8, contentType);
            string authToken = GetauthorizationFromCookies();

            if (!string.IsNullOrEmpty(authToken))
            {
                httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", authToken);
            }

            try
            {
                Debug.WriteLine("开始发送请求");
                HttpResponseMessage response = await httpClient.PostAsync(url, requestContent).ConfigureAwait(false);
                response.EnsureSuccessStatusCode();

                Debug.WriteLine("请求成功");
                string responseContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

                Debug.WriteLine($"完成请求：{responseContent}");
                return responseContent;
            }
            catch (TaskCanceledException)
            {
                Debug.WriteLine("请求超时");
                return "请求超时";
            }
            catch (HttpRequestException httpEx)
            {
                Debug.WriteLine($"HTTP请求错误: {httpEx.Message}");
                return $"HTTP请求错误: {httpEx.Message}";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"请求错误: {ex.Message}");
                return $"请求错误: {ex.Message}";
            }
        }

        /// <summary>
        /// 异步发送POST请求并将响应解析为JsonObject
        /// </summary>
        /// <param name="url">请求的目标URL</param>
        /// <param name="formData">POST请求的表单数据</param>
        /// <param name="contentType">请求内容类型，默认为application/x-www-form-urlencoded</param>
        /// <param name="timeout">请求超时时间（毫秒），默认为15000</param>
        /// <param name="maxRetries">最大重试次数，默认为1</param>
        /// <returns>返回解析后的JsonObject对象</returns>
        public static async Task<JsonObject> PostJsonObjectAsync(
            string url,
            string formData,
            string contentType = "application/x-www-form-urlencoded",
            int timeout = 15000,
            int maxRetries = 1)
        {
            string responseHtml = await PostHtmlAsync(url, formData ?? string.Empty, contentType, timeout).ConfigureAwait(false);
            return ConvertHtmlToJsonObject(responseHtml);
        }

        /// <summary>
        /// 异步上传文件到服务器
        /// </summary>
        /// <param name="filePath">要上传的文件路径</param>
        /// <returns>返回服务器响应的字符串内容</returns>
        /// <remarks>
        /// 该方法会：
        /// - 验证文件是否存在
        /// - 检查文件类型是否支持
        /// - 自动处理文件内容类型
        /// - 添加必要的授权信息
        /// </remarks>
        public static async Task<string> UploadFileHtmlAsync(string filePath)
        {
            if (!File.Exists(filePath))
                return null;

            byte[] fileContent = File.ReadAllBytes(filePath);
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(vsto51Constant.Url.上传文件);
            request.Method = "POST";
            request.CookieContainer = cookieContainer;

            string authToken = GetauthorizationFromCookies();
            if (!string.IsNullOrEmpty(authToken))
            {
                request.Headers["Authorization"] = $"Bearer {authToken}";
            }

            string fileExtension = Path.GetExtension(filePath).ToLower().TrimStart('.');
            if (!vsto51Constant.uploadContentType.support_file_type_dict.ContainsKey(fileExtension))
            {
                WriteLog($"不支持的文件格式：{fileExtension}");
                return null;
            }
            string fileContentType = vsto51Constant.uploadContentType.support_file_type_dict[fileExtension];

            string boundaryMarker = $"---------------------------{DateTime.Now.Ticks:x}";
            request.ContentType = $"multipart/form-data; boundary={boundaryMarker}";

            using (Stream requestStream = request.GetRequestStreamAsync().Result)
            using (StreamWriter streamWriter = new(requestStream))
            {
                streamWriter.WriteLine($"--{boundaryMarker}");
                streamWriter.WriteLine(
                    $"Content-Disposition: form-data; name=\"files\"; filename=\"{Path.GetFileName(filePath)}\"");
                streamWriter.WriteLine($"Content-Type: {fileContentType}");
                streamWriter.WriteLine();
                streamWriter.Flush();
                requestStream.Write(fileContent, 0, fileContent.Length);

                streamWriter.WriteLine();
                streamWriter.WriteLine($"--{boundaryMarker}--");
            }

            try
            {
                using HttpWebResponse response = (HttpWebResponse)request.GetResponseAsync().Result;
                using StreamReader responseReader = new(response.GetResponseStream());
                string responseContent = await responseReader.ReadToEndAsync().ConfigureAwait(false);

                return string.IsNullOrEmpty(responseContent) ? null : responseContent;
            }
            catch (WebException ex)
            {
                WriteLog($"文件上传失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 上传文件到服务器，并将服务器返回的HTML内容解析为JsonObject。
        /// </summary>
        /// <param name="filePath">要上传的文件路径。</param>
        /// <returns>返回解析后的JsonObject，如果响应为空或解析失败，则返回null。</returns>
        public static async Task<JsonObject> UploadFileJsonObjectAsync(string filePath)
        {
            string html = await UploadFileHtmlAsync(filePath).ConfigureAwait(false);
            return ConvertHtmlToJsonObject(html);
        }

        /// <summary>
        /// 异步从服务器下载文件
        /// </summary>
        /// <param name="url">下载地址</param>
        /// <param name="formData">POST请求的表单数据</param>
        /// <param name="savePath">文件保存路径</param>
        /// <param name="contentType">请求内容类型，默认为application/json</param>
        /// <param name="timeout">请求超时时间（毫秒），默认为30000</param>
        /// <param name="timeStamp">是否在文件名前添加时间戳，默认为false</param>
        /// <returns>返回元组(bool Success, string Result)，Success表示是否成功，Result为保存路径或错误信息</returns>
        /// <remarks>
        /// 该方法会：
        /// - 自动处理文件名（包括时间戳添加）
        /// - 创建必要的目录结构
        /// - 处理各种下载异常情况
        /// </remarks>
        public static async Task<(bool Success, string Result)> DownloadFileAsync(
            string url,
            string formData,
            string savePath,
            string contentType = "application/json",
            int timeout = 30000,
            bool timeStamp = false)
        {
            Debug.WriteLine($"开始下载文件，请求地址：{url}");

            HttpClientHandler httpHandler = new() { CookieContainer = cookieContainer };

            using (HttpClient httpClient = new(httpHandler))
            {
                httpClient.Timeout = TimeSpan.FromMilliseconds(timeout);
                StringContent requestContent = new(formData, Encoding.UTF8, contentType);

                string authToken = GetauthorizationFromCookies();
                if (!string.IsNullOrEmpty(authToken))
                {
                    httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue(
                        "Bearer",
                        authToken);
                }

                try
                {
                    HttpResponseMessage response = await httpClient.PostAsync(url, requestContent).ConfigureAwait(false);
                    response.EnsureSuccessStatusCode();

                    string fileName = string.Empty;
                    if (response.Content.Headers.ContentDisposition != null)
                    {
                        fileName = response.Content.Headers.ContentDisposition.FileName;
                        if (!string.IsNullOrEmpty(fileName))
                        {
                            fileName = WebUtility.UrlDecode(fileName);
                            fileName = fileName.Trim('"', '\'');
                            fileName = ETFile.PathRemoveInvalidChars(fileName);
                        }
                    }

                    fileName = string.IsNullOrEmpty(fileName) ? "download.xlsx" : fileName;

                    if (timeStamp)
                    {
                        string timePrefix = DateTime.Now.ToString("yyyyMMdd-HHmmss-");
                        fileName = $"{timePrefix}{fileName}";
                    }

                    string saveDirectory = Path.GetDirectoryName(savePath);
                    if (!Directory.Exists(saveDirectory))
                    {
                        Directory.CreateDirectory(saveDirectory);
                    }

                    string fullSavePath = Path.Combine(savePath, fileName);

                    using (Stream contentStream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false))
                    using (FileStream fileStream = new(fullSavePath, FileMode.Create, FileAccess.Write, FileShare.None))
                    {
                        await contentStream.CopyToAsync(fileStream).ConfigureAwait(false);
                    }

                    Debug.WriteLine($"文件下载成功：{fullSavePath}");
                    return (true, fullSavePath);
                }
                catch (TaskCanceledException)
                {
                    Debug.WriteLine("下载超时");
                    return (false, "下载超时");
                }
                catch (HttpRequestException httpEx)
                {
                    Debug.WriteLine($"HTTP请求错误: {httpEx.Message}");
                    return (false, $"HTTP请求错误: {httpEx.Message}");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"下载错误: {ex.Message}");
                    return (false, $"下载错误: {ex.Message}");
                }
            }
        }

        #region 辅助方法Public_网络相关

        /// <summary>
        /// 将字典形式的Cookies转换为CookieContainer
        /// </summary>
        /// <param name="cookies">Cookie键值对字典</param>
        /// <param name="domain">Cookie所属域名</param>
        /// <returns>包含所有Cookie的CookieContainer实例</returns>
        /// <remarks>
        /// 遍历Cookie字典，为每个Cookie设置相同的域名，并添加到CookieContainer中
        /// </remarks>
        static CookieContainer ConvertToCookieContainer(Dictionary<string, string> cookies, string domain)
        {
            CookieContainer cookieContainer = new();

            if (cookies != null)
            {
                foreach (KeyValuePair<string, string> cookieItem in cookies)
                {
                    Cookie cookie = new(cookieItem.Key, cookieItem.Value) { Domain = domain };
                    cookieContainer.Add(cookie);
                }
            }

            return cookieContainer;
        }

        /// <summary>
        /// 从Cookies中获取授权令牌
        /// </summary>
        /// <returns>授权令牌字符串，如果未找到则返回空字符串</returns>
        /// <remarks>
        /// 检查Cookies是否包含wyjf-token键，该键用于API授权
        /// </remarks>
        static string GetauthorizationFromCookies()
        {
            if (Cookies != null && Cookies.Count > 3 && Cookies.TryGetValue("wyjf-token", out string token))
            {
                return token;
            }

            WriteLog("无法获取有效授权信息，请检查Cookies");
            return string.Empty;
        }

        /// <summary>
        /// 将HTML响应内容转换为JsonObject对象
        /// </summary>
        /// <param name="html">HTML响应内容</param>
        /// <returns>转换后的JsonObject对象</returns>
        /// <remarks>
        /// 该方法会处理：
        /// - 空响应检查
        /// - 超时响应检查
        /// - JSON解析异常处理
        /// </remarks>
        public static JsonObject ConvertHtmlToJsonObject(string html)
        {
            if (string.IsNullOrEmpty(html))
                return vsto51Constant.ErrorMessage.NoData;

            if (html.Length < 100 && html.IndexOf("超时", StringComparison.Ordinal) >= 0)
                return vsto51Constant.ErrorMessage.Timeout;

            try
            {
                return JsonNode.Parse(html).AsObject();
            }
            catch (Exception ex)
            {
                WriteLog(ex.Message);
                return vsto51Constant.ErrorMessage.Unknown;
            }
        }

        /// <summary>
        /// 检查JsonObject是否包含成功状态码
        /// </summary>
        /// <param name="jsonObject">要检查的JsonObject对象</param>
        /// <returns>如果状态码为"200"则返回true，否则返回false</returns>
        public static bool ReturnCodeIsSuccess(this JsonObject jsonObject)
        {
            return jsonObject != null &&
                   jsonObject.ContainsKey("code") &&
                   jsonObject["code"].AsString() == "200";
        }

        /// <summary>
        /// 计算JsonNode中的子节点数量
        /// </summary>
        /// <param name="jsonNode">要计算的JsonNode对象</param>
        /// <returns>JsonArray的元素数量或JsonObject的属性数量</returns>
        public static int NodeCount(this JsonNode jsonNode)
        {
            if (jsonNode is JsonArray jsonArray)
                return jsonArray.Count;
            if (jsonNode is JsonObject jsonObject)
                return jsonObject.Count;
            return 0;
        }

        /// <summary>
        /// 在给定的 JsonObject 中查找所有满足特定节点名和任务状态值的子对象。
        /// </summary>
        /// <param name="jsonObject">要搜索的主 JsonObject。</param>
        /// <param name="nodeName">在主 JsonObject 中查找的节点名，通常指向一个 JsonArray。</param>
        /// <param name="taskStatusName">用于过滤结果的子对象中的任务状态字段名。</param>
        /// <param name="taskStatusNameValue">期望的任务状态值，用于与每个子对象中的 taskStatusName 字段值进行比较。</param>
        /// <returns>第一个包含所有满足条件的 JsonObject ，这些 JsonObject 的 taskStatusName 字段值等于 taskStatusNameValue。</returns>
        public static JsonObject FindJsonNode(
            JsonObject jsonObject,
            string nodeName,
            string taskStatusName,
            string taskStatusNameValue)
        {
            List<JsonObject> findJsonNode = FindJsonNodes(jsonObject, nodeName, taskStatusName, taskStatusNameValue);
            return findJsonNode.Count > 0 ? findJsonNode[0] : null;
        }

        /// <summary>
        /// 在给定的 JsonObject 中查找所有满足特定节点名和任务状态值的子对象。
        /// </summary>
        /// <param name="jsonObject">要搜索的主 JsonObject。</param>
        /// <param name="nodeName">在主 JsonObject 中查找的节点名，通常指向一个 JsonArray。</param>
        /// <param name="taskStatusName">用于过滤结果的子对象中的任务状态字段名。</param>
        /// <param name="taskStatusNameValue">期望的任务状态值，用于与每个子对象中的 taskStatusName 字段值进行比较。</param>
        /// <returns>一个包含所有满足条件的 JsonObject 的列表，这些 JsonObject 的 taskStatusName 字段值等于 taskStatusNameValue。</returns>
        public static List<JsonObject> FindJsonNodes(
            JsonObject jsonObject,
            string nodeName,
            string taskStatusName,
            string taskStatusNameValue)
        {
            List<JsonObject> result = [];

            if (jsonObject == null ||
                string.IsNullOrEmpty(nodeName) ||
                string.IsNullOrEmpty(taskStatusName) ||
                string.IsNullOrEmpty(taskStatusNameValue))
                return result;

            if (jsonObject.TryGetPropertyValue(nodeName, out JsonNode rowsNode) && rowsNode is JsonArray rowsArray)
            {
                foreach (JsonNode rowNode in rowsArray)
                {
                    if (rowNode is JsonObject rowObject)
                    {
                        if (rowObject.TryGetPropertyValue(taskStatusName, out JsonNode statusNode) &&
                            statusNode is JsonValue statusValue)
                        {
                            if (statusValue.ToString().Equals(taskStatusNameValue, StringComparison.OrdinalIgnoreCase))
                            {
                                result.Add(rowObject);
                            }
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 将指定的 JSON 对象转换为字典，其中字典的键和值分别来自 JSON 对象中每一行的两个指定字段。
        /// </summary>
        /// <param name="jsonObject">包含数据的 JSON 对象。</param>
        /// <param name="keyName">用作字典键的 JSON 字段名称。</param>
        /// <param name="keyValue">用作字典值的 JSON 字段名称。</param>
        /// <returns>
        /// 一个字典，其中键是从 JSON 对象的每一行中提取的 keyName 字段，值是从 JSON 对象的每一行中提取的 keyValue 字段；如果输入无效，则返回 null。
        /// </returns>
        public static Dictionary<string, string> ConvertJsonToDictionary(
            JsonObject jsonObject,
            string keyName,
            string keyValue)
        {
            Dictionary<string, string> resultDictionary = [];
            if (jsonObject == null || string.IsNullOrEmpty(keyName) || string.IsNullOrEmpty(keyValue))
                return null;

            if (!jsonObject.ContainsKey("rows"))
                return resultDictionary;

            JsonArray rows = jsonObject["rows"] as JsonArray;
            foreach (JsonObject row in rows)
            {
                if (!row.ContainsKey(keyName) || !row.ContainsKey(keyValue))
                    continue;
                if (row[keyName] == null || row[keyValue] == null)
                    continue;
                resultDictionary[row[keyName].ToString()] = row[keyValue].ToString();
            }

            return resultDictionary;
        }

        /// <summary>
        /// 将指定的 JSON 对象转换为由字符串元组组成的列表。 每个元组包含从 JSON 对象的每一行中提取的两个特定键的值。
        /// </summary>
        /// <param name="jsonObject">包含数据的 JSON 对象。</param>
        /// <param name="keyName">要从中提取值的第一个键的名称。</param>
        /// <param name="keyValue">要从中提取值的第二个键的名称。</param>
        /// <returns>一个由元组组成的列表，每个元组包含从 JSON 对象的每一行中提取的两个键的值；如果输入无效，则返回 null。</returns>

        public static List<(string, string)> ConvertJsonToTupleList(
            JsonObject jsonObject,
            string keyName,
            string keyValue)
        {
            List<(string, string)> resultList = [];
            if (jsonObject == null || string.IsNullOrEmpty(keyName) || string.IsNullOrEmpty(keyValue))
                return null;

            if (!jsonObject.ContainsKey("rows"))
                return resultList;
            JsonArray rows = jsonObject["rows"] as JsonArray;
            foreach (JsonObject row in rows)
            {
                if (!row.ContainsKey(keyName) || !row.ContainsKey(keyValue))
                    continue;
                if (row[keyName] == null || row[keyValue] == null)
                    continue;
                resultList.Add((row[keyName].ToString(), row[keyValue].ToString()));
            }

            return resultList;
        }

        /// <summary>
        /// 检查个由字符串元组构成的列表中是否包含指定的两个值。
        /// </summary>
        /// <param name="list">由字符串元组构成的列表，每个元组包含两个字符串元素。</param>
        /// <param name="value1">第一个值，用于与列表中元组的第一个元素进行匹配。</param>
        /// <param name="value2">第二个值，用于与列表中元组的第二个元素进行匹配。</param>
        /// <returns>如果列表中存在至少一个元组，其第一个元素等于 value1 且第二个元素等于 value2，则返回 true；否则返回 false。</returns>
        public static bool ContainsKeyValuePair(List<(string, string)> list, string value1, string value2)
        {
            return list.Any(
                item => item.Item1 == "Value1" && item.Item2 == value1 && item.Item1 == "Value2" && item.Item2 == value2);
        }
        #endregion 辅助方法Public_1

        #region 辅助方法Public_日志/文件相关

        /// <summary>
        /// 在TextBox中显示字典内容
        /// </summary>
        /// <param name="dict">要显示的字典</param>
        /// <param name="textBox">目标TextBox控件</param>
        /// <remarks>
        /// 以格式化的方式显示字典的键值对，每对之间添加空行
        /// </remarks>
        public static void DisplayInTextBox(Dictionary<string, string> dict, TextBox textBox)
        {
            textBox.AppendText($"{Environment.NewLine}{Environment.NewLine}");
            foreach (KeyValuePair<string, string> dictItem in dict)
            {
                textBox.AppendText($"[{dictItem.Key}]{Environment.NewLine}{dictItem.Value}{Environment.NewLine}");
            }
            textBox.AppendText($"{Environment.NewLine}");
        }

        /// <summary>
        /// 记录日志信息
        /// </summary>
        /// <param name="msg">日志消息</param>
        /// <remarks>
        /// 同时输出到Debug窗口和logTextBox（如果已设置）
        /// </remarks>
        public static void WriteLog(string msg)
        {
            Debug.WriteLine(msg);
            logTextBox?.WriteLog(msg);
        }

        /// <summary>
        /// 将字典序列化并保存到文件
        /// </summary>
        /// <param name="dict">要保存的字典</param>
        /// <param name="path">目标文件路径</param>
        /// <remarks>
        /// 使用JSON格式序列化，支持格式化输出
        /// </remarks>
        public static void SaveToFile(Dictionary<string, string> dict, string path)
        {
            try
            {
                string jsonContent = JsonConvert.SerializeObject(dict, Formatting.Indented);
                File.WriteAllText(path, jsonContent);
                Console.WriteLine(@"字典已成功保存到文件。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存字典时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 从文件中读取并反序列化字典
        /// </summary>
        /// <param name="path">源文件路径</param>
        /// <returns>反序列化后的字典，如果失败则返回空字典</returns>
        public static Dictionary<string, string> LoadFromFile(string path)
        {
            try
            {
                if (!File.Exists(path))
                {
                    Console.WriteLine("文件不存在。");
                    return [];
                }

                string jsonContent = File.ReadAllText(path);

                // 首先尝试读取新格式的 CookieData JSON
                if (TryLoadNewFormatCookies(jsonContent, out Dictionary<string, string> newFormatCookies))
                {
                    Console.WriteLine("已成功从新格式Cookie文件读取。");
                    return newFormatCookies;
                }

                // 如果新格式失败，尝试读取旧格式的 Dictionary<string, string>
                Dictionary<string, string> dict = JsonConvert.DeserializeObject<Dictionary<string, string>>(jsonContent);
                Console.WriteLine("字典已成功从文件读取。");
                return dict ?? [];
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取字典时发生错误: {ex.Message}");
                return [];
            }
        }

        /// <summary>
        /// 尝试从新格式的 JSON 中读取 cookies 数据
        /// 支持ETLoginWebBrowser返回的标准格式和老的CookieData格式
        /// </summary>
        /// <param name="jsonContent">JSON 内容</param>
        /// <param name="cookies">输出的 cookies 字典</param>
        /// <returns>是否成功读取新格式数据</returns>
        private static bool TryLoadNewFormatCookies(string jsonContent, out Dictionary<string, string> cookies)
        {
            cookies = new Dictionary<string, string>();

            try
            {
                // 首先尝试使用ETWebBrowserJsonFormatter解析标准格式
                if (ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.IsStandardFormat(jsonContent))
                {
                    var loginInfo = ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.ParseLoginInfoJson(jsonContent);
                    if (loginInfo?.Cookies != null && loginInfo.Cookies.Count > 0)
                    {
                        // 将 ETLoginWebBrowser.CookieItem 列表转换为 Dictionary<string, string>
                        foreach (var cookieItem in loginInfo.Cookies)
                        {
                            if (!string.IsNullOrEmpty(cookieItem.Name) && !string.IsNullOrEmpty(cookieItem.Value))
                            {
                                // 如果已存在同名 cookie，则覆盖（保留最后一个）
                                cookies[cookieItem.Name] = cookieItem.Value;
                            }
                        }

                        Console.WriteLine($"已成功从ETLoginWebBrowser标准格式读取 {cookies.Count} 个Cookie");
                        return cookies.Count > 0;
                    }
                }

                // 如果标准格式解析失败，尝试老的CookieData格式
                var cookieData = JsonConvert.DeserializeObject<CookieDataFormat>(jsonContent);
                if (cookieData?.Cookies != null && cookieData.Cookies.Count > 0)
                {
                    // 将 CookieItem 列表转换为 Dictionary<string, string>
                    foreach (var cookieItem in cookieData.Cookies)
                    {
                        if (!string.IsNullOrEmpty(cookieItem.Name) && !string.IsNullOrEmpty(cookieItem.Value))
                        {
                            // 如果已存在同名 cookie，则覆盖（保留最后一个）
                            cookies[cookieItem.Name] = cookieItem.Value;
                        }
                    }

                    Console.WriteLine($"已成功从老格式CookieData读取 {cookies.Count} 个Cookie");
                    return cookies.Count > 0;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"尝试读取新格式Cookie时发生错误: {ex.Message}");
            }

            return false;
        }
        #endregion

        /// <summary>
        /// Cookie数据格式类，用于反序列化新格式的Cookie JSON文件
        /// </summary>
        public class CookieDataFormat
        {
            /// <summary>
            /// 关联的URL
            /// </summary>
            public string Url { get; set; }

            /// <summary>
            /// Cookie列表
            /// </summary>
            public List<CookieItemFormat> Cookies { get; set; } = new List<CookieItemFormat>();
        }

        /// <summary>
        /// Cookie项格式类，表示单个Cookie的信息
        /// </summary>
        public class CookieItemFormat
        {
            /// <summary>
            /// Cookie名称
            /// </summary>
            public string Name { get; set; }

            /// <summary>
            /// Cookie值
            /// </summary>
            public string Value { get; set; }

            /// <summary>
            /// 域名
            /// </summary>
            public string Domain { get; set; }

            /// <summary>
            /// 路径
            /// </summary>
            public string Path { get; set; } = "/";

            /// <summary>
            /// 过期时间
            /// </summary>
            public DateTime Expires { get; set; } = DateTime.MinValue;

            /// <summary>
            /// 是否仅HTTP
            /// </summary>
            public bool HttpOnly { get; set; }

            /// <summary>
            /// 是否安全
            /// </summary>
            public bool Secure { get; set; }

            /// <summary>
            /// 同站策略
            /// </summary>
            public string SameSite { get; set; } = "None";
        }

        /// <summary>
        /// 51平台上传文件信息类
        /// </summary>
        public class Vsto51FileInfo
        {
            /// <summary>
            /// 从JsonObject创建文件信息实例
            /// </summary>
            /// <param name="json">包含文件信息的JsonObject</param>
            public Vsto51FileInfo(JsonObject json)
            {
                name = json[nameof(name)]?.AsString();
                url = json[nameof(url)]?.AsString();
                fileId = json[nameof(fileId)]?.AsString();
                extensionName = json[nameof(extensionName)]?.AsString();
            }

            /// <summary>
            /// 使用指定参数创建文件信息实例
            /// </summary>
            public Vsto51FileInfo(string name, string url, string fileId, string extensionName)
            {
                this.name = name;
                this.url = url;
                this.fileId = fileId;
                this.extensionName = extensionName;
            }

            /// <summary>
            /// 文件扩展名
            /// </summary>
            public string extensionName { get; set; }

            /// <summary>
            /// 文件唯一标识
            /// </summary>
            public string fileId { get; set; }

            /// <summary>
            /// 文件名称
            /// </summary>
            public string name { get; set; }

            /// <summary>
            /// 文件URL地址
            /// </summary>
            public string url { get; set; }
        }

        /// <summary>
        /// 51平台工点信息类
        /// </summary>
        public class Vsto51WorksPointInfo
        {
            /// <summary>
            /// 从JsonObject创建工点信息实例
            /// </summary>
            /// <param name="json">包含工点信息的JsonObject</param>
            public Vsto51WorksPointInfo(JsonObject json)
            {
                id = json[nameof(id)]?.AsString();
                templateId = json[nameof(templateId)]?.AsString();
                templateName = json[nameof(templateName)]?.AsString();
                worksPointName = json[nameof(worksPointName)]?.AsString();
                worksPointNo = json[nameof(worksPointNo)]?.AsString();
            }

            /// <summary>
            /// 使用指定参数创建工点信息实例
            /// </summary>
            public Vsto51WorksPointInfo(
                string id,
                string templateId,
                string templateName,
                string worksPointName,
                string worksPointNo)
            {
                this.id = id;
                this.templateId = templateId;
                this.templateName = templateName;
                this.worksPointName = worksPointName;
                this.worksPointNo = worksPointNo;
            }

            /// <summary>
            /// 工点ID
            /// </summary>
            public string id { get; set; }

            /// <summary>
            /// 模板ID
            /// </summary>
            public string templateId { get; set; }

            /// <summary>
            /// 模板名称
            /// </summary>
            public string templateName { get; set; }

            /// <summary>
            /// 工点名称
            /// </summary>
            public string worksPointName { get; set; }

            /// <summary>
            /// 工点编号
            /// </summary>
            public string worksPointNo { get; set; }
        }

        /// <summary>
        /// 51平台常量定义类
        /// </summary>
        public static class vsto51Constant
        {
            /// <summary>
            /// 搜索起始时间
            /// </summary>
            public const string searchStartTime = "2021-01-01 00:00:00";

            /// <summary>
            /// 错误消息常量类
            /// </summary>
            public static class ErrorMessage
            {
                /// <summary>
                /// 登录信息无效错误
                /// </summary>
                public static JsonObject LoginInfoInvalid = JsonNode.Parse($"{{\"code\":404,\"msg\":\"登录信息无效\"}} ")?.AsObject();

                /// <summary>
                /// 无数据错误
                /// </summary>
                public static JsonObject NoData = JsonNode.Parse($"{{\"code\":600,\"msg\":\"无数据\"}} ")?.AsObject();

                /// <summary>
                /// 无权限错误
                /// </summary>
                public static JsonObject NoPermission = JsonNode.Parse($"{{\"code\":403,\"msg\":\"无权限\"}} ")?.AsObject();

                /// <summary>
                /// 参数错误
                /// </summary>
                public static JsonObject Params = JsonNode.Parse($"{{\"code\":400,\"msg\":\"参数错误\"}} ")?.AsObject();

                /// <summary>
                /// 服务器错误
                /// </summary>
                public static JsonObject Server = JsonNode.Parse($"{{\"code\":500,\"msg\":\"服务器错误\"}} ")?.AsObject();

                /// <summary>
                /// 超时错误
                /// </summary>
                public static JsonObject Timeout = JsonNode.Parse($"{{\"code\":504,\"msg\":\"超时\"}} ")?.AsObject();

                /// <summary>
                /// 未知错误
                /// </summary>
                public static JsonObject Unknown = JsonNode.Parse($"{{\"code\":500,\"msg\":\"未知错误\"}} ")?.AsObject();
            }

            /// <summary>
            /// 地址信息常量类
            /// </summary>
            public static class AddressInfo
            {
                /// <summary>
                /// 地址
                /// </summary>
                public const string address = "广东省汕头市";

                /// <summary>
                /// IP地址
                /// </summary>
                public const string ip = "************";

                /// <summary>
                /// 纬度
                /// </summary>
                public const string latitude = "23.37102";

                /// <summary>
                /// 经度
                /// </summary>
                public const string longitude = "116.708463";
            }

            /// <summary>
            /// URL常量类
            /// </summary>
            public static class Url
            {
                /// <summary>
                /// 域名
                /// </summary>
                public static string Domain = "gcszh.gdtel.com.cn";

                /// <summary>
                /// 主机地址
                /// </summary>
                public static string Host = "gcszh.gdtel.com.cn:85";

                /// <summary>
                /// API接口：查询可用任务信息列表
                /// </summary>
                public static string 查询可用任务信息列表 = $"https://{Host}/prod-api/muban/templateTaskStatus/taskStatus/list";

                /// <summary>
                /// API接口：查询衔接表默认数据
                /// </summary>
                public static string 查询衔接表默认数据 = $"https://{Host}/prod-api/manager/task/selectRelationFieldDataList";

                /// <summary>
                /// API接口：查询已有51操作记录
                /// </summary>
                public static string 查询已有51操作记录 = $"https://{Host}/prod-api/manager/task/data/list";

                /// <summary>
                /// API接口：查询站点基本信息
                /// </summary>
                public static string 查询站点基本信息 = $"https://{Host}/prod-api/manager/works/point/detail/base";

                /// <summary>
                /// API接口：查询站点列表
                /// </summary>
                public static string 查询站点列表 = $"https://{Host}/prod-api/manager/project/manage/point/list";

                /// <summary>
                /// API接口：查询站点已操作任务
                /// </summary>
                public static string 查询站点已操作任务 = $"https://{Host}/prod-api/manager/task/list";

                /// <summary>
                /// API接口：登录
                /// </summary>
                public static string 登录 = $"https://{Host}/login";

                /// <summary>
                /// API接口：获得待审批信息
                /// </summary>
                public static string 获得待审批信息 = $"https://{Host}/prod-api/account/task/approveList";

                /// <summary>
                /// API接口：获得动态表字段
                /// </summary>
                public static string 获得动态表字段 = $"https://{Host}/prod-api/muban/dynamicField/not/pagination/list";

                /// <summary>
                /// API接口：获得监理信息
                /// </summary>
                public static string 获得监理信息 = $"https://{Host}/prod-api/muban/approvalProcesses/selectTemplateTaskApproveNodeInfoList";

                /// <summary>
                /// API接口：获取用户信息
                /// </summary>
                public static string 获取用户信息 = $"https://{Host}/prod-api/account/users/getInfo";

                /// <summary>
                /// API接口：上传文件
                /// </summary>
                public static string 上传文件 = $"https://{Host}/prod-api/file/upload/multiple";

                /// <summary>
                /// API接口：提交审批意见
                /// </summary>
                public static string 提交审批意见 = $"https://{Host}/prod-api/account/task/approveTask";

                /// <summary>
                /// API接口：添加上传文件信息
                /// </summary>
                public static string 添加上传文件信息 = $"https://{Host}/prod-api/manager/task/data/add";

                /// <summary>
                /// API接口：导出任务管理
                /// </summary>
                public static string 导出任务管理 = $"https://{Host}/prod-api/manager/task/data/export";

                /// <summary>
                /// API接口：导出交付列表
                /// </summary>
                public static string 导出交付列表 = $"https://{Host}/prod-api/manager/project/manage/point/exportPoint";
            }

            /// <summary>
            /// 上传文件内容类型常量类
            /// </summary>
            public static class uploadContentType
            {
                public const string bmp = "image/bmp";
                public const string doc = "application/msword";
                public const string docx = "application/msword";
                public const string form = "multipart/form-data";
                public const string html = "text/html";
                public const string jpeg = "image/jpeg";
                public const string jpg = "image/jpeg";

                public const string json = "application/json";
                public const string pdf = "application/pdf";
                public const string png = "image/png";
                public const string ppt = "application/vnd.ms-powerpoint";
                public const string pptx = "application/vnd.ms-powerpoint";
                public const string rar = "application/x-rar-compressed";
                public const string text = "text/plain";
                public const string txt = "text/plain";
                public const string wwwform = "application/x-www-form-urlencoded";
                public const string xls = "application/vnd.ms-excel";
                public const string xlsx = "application/vnd.ms-excel";
                public const string xml = "text/xml";
                public const string zip = "application/zip";

                //定义支持文件类型字典
                public static Dictionary<string, string> support_file_type_dict = new()
                {
                    { "jpg", vsto51Constant.uploadContentType.jpg },
                    { "jpeg", vsto51Constant.uploadContentType.jpeg },
                    { "bmp", vsto51Constant.uploadContentType.bmp },
                    { "png", vsto51Constant.uploadContentType.png },
                    { "pdf", vsto51Constant.uploadContentType.pdf },
                    { "doc", vsto51Constant.uploadContentType.doc },
                    { "docx", vsto51Constant.uploadContentType.docx },
                    { "xls", vsto51Constant.uploadContentType.xls },
                    { "xlsx", vsto51Constant.uploadContentType.xlsx },
                    { "ppt", vsto51Constant.uploadContentType.ppt },
                    { "pptx", vsto51Constant.uploadContentType.pptx },
                    { "txt", vsto51Constant.uploadContentType.txt },
                    { "zip", vsto51Constant.uploadContentType.zip },
                    { "rar", vsto51Constant.uploadContentType.rar },
                    { "json", vsto51Constant.uploadContentType.json },
                    { "form", vsto51Constant.uploadContentType.form },
                    { "text", vsto51Constant.uploadContentType.text },
                    { "html", vsto51Constant.uploadContentType.html },
                    { "xml", vsto51Constant.uploadContentType.xml },
                    { "wwwform", vsto51Constant.uploadContentType.wwwform }
                };
            }
        }
    }
}