# 📚 Flurl.Http 和 CefSharp 技术手册

## 🌐 Flurl.Http 技术详解

### 📋 基础信息
- **官方网站**: https://flurl.dev/
- **NuGet包**: Flurl.Http (当前版本 4.0+)
- **依赖**: .NET Framework 4.6.2+ 或 .NET Core 2.0+
- **许可证**: MIT License

### 🎯 核心特性
- **链式API设计** - 流畅的方法链调用
- **自动序列化** - JSON/表单数据自动处理
- **Cookie管理** - 内置CookieJar支持
- **错误处理** - 完善的异常处理机制
- **配置灵活** - 多层次配置系统

### 🔧 本项目需要的核心方法

#### 1. 基础HTTP请求
```csharp
// GET请求
var result = await "https://api.example.com/data"
    .GetJsonAsync<ResponseModel>();

// POST请求
var response = await "https://api.example.com/submit"
    .PostJsonAsync(requestData)
    .ReceiveJson<ResponseModel>();

// 带参数的GET请求
var data = await "https://api.example.com/search"
    .SetQueryParam("keyword", "test")
    .SetQueryParam("page", 1)
    .GetJsonAsync<SearchResult>();
```

#### 2. 请求头管理
```csharp
// 单个请求头
await url.WithHeader("Authorization", "Bearer token")
    .GetAsync();

// 多个请求头
await url.WithHeaders(new { 
    Authorization = "Bearer token",
    User_Agent = "MyApp/1.0",
    Accept = "application/json"
}).GetAsync();

// 基础认证
await url.WithBasicAuth("username", "password")
    .GetAsync();

// OAuth Bearer Token
await url.WithOAuthBearerToken("access_token")
    .GetAsync();
```

#### 3. Cookie管理（重要）
```csharp
// 手动设置Cookie
var response = await "https://example.com"
    .WithCookie("sessionId", "abc123")
    .WithCookies(new { userId = "12345", theme = "dark" })
    .GetAsync();

// 使用CookieJar（推荐用于OA系统）
await "https://oa.company.com/login"
    .WithCookies(out var jar)
    .PostUrlEncodedAsync(credentials);

// 后续请求自动使用Cookie
await "https://oa.company.com/api/data"
    .WithCookies(jar)
    .GetJsonAsync<ApiResponse>();

// Cookie持久化存储
var cookieString = jar.ToString();
var restoredJar = CookieJar.LoadFromString(cookieString);

// 文件存储Cookie
using var writer = new StreamWriter("cookies.txt");
jar.WriteTo(writer);

using var reader = new StreamReader("cookies.txt");
var jarFromFile = CookieJar.LoadFrom(reader);
```

#### 4. 表单数据提交
```csharp
// URL编码表单
await "https://oa.company.com/login"
    .PostUrlEncodedAsync(new { 
        username = "user", 
        password = "pass",
        remember = "true"
    });

// 多部分表单（文件上传）
var response = await "https://oa.company.com/upload"
    .PostMultipartAsync(mp => mp
        .AddString("title", "文档标题")
        .AddString("category", "工作文档")
        .AddFile("file", filePath)
        .AddFile("attachment", stream, "document.pdf")
        .AddJson("metadata", new { author = "张三", date = DateTime.Now })
    );
```

#### 5. 错误处理和重试
```csharp
try {
    var result = await url.PostJsonAsync(data).ReceiveJson<Result>();
}
catch (FlurlHttpException ex) {
    // 获取错误响应
    var errorResponse = await ex.GetResponseStringAsync();
    var statusCode = ex.StatusCode;
    
    // 记录错误日志
    ETLogManager.Error($"API请求失败: {ex.Message}, 状态码: {statusCode}");
}
catch (FlurlHttpTimeoutException ex) {
    // 处理超时
    ETLogManager.Error($"请求超时: {ex.Message}");
}

// 允许特定状态码
var response = await url
    .AllowHttpStatus(400, 401, 403)
    .PostJsonAsync(data);

// 允许所有状态码
var response = await url
    .AllowAnyHttpStatus()
    .GetAsync();
```

#### 6. 配置和超时
```csharp
// 设置超时
await url.WithTimeout(30).GetAsync(); // 30秒
await url.WithTimeout(TimeSpan.FromMinutes(5)).GetAsync();

// 客户端级别配置
var client = new FlurlClient("https://oa.company.com")
    .Configure(settings => {
        settings.Timeout = TimeSpan.FromSeconds(60);
        settings.AllowedHttpStatusRange = "*";
    });

// 全局配置
FlurlHttp.Clients.WithDefaults(builder =>
    builder.WithTimeout(TimeSpan.FromSeconds(30))
);
```

#### 7. 文件下载
```csharp
// 下载文件到本地
var localPath = await "https://oa.company.com/files/document.pdf"
    .WithCookies(jar)
    .DownloadFileAsync("C:\\Downloads", "document.pdf");

// 获取文件流
var stream = await "https://oa.company.com/files/data.xlsx"
    .WithCookies(jar)
    .GetStreamAsync();
```

## 🖥️ CefSharp 技术详解

### 📋 基础信息
- **官方网站**: https://github.com/cefsharp/CefSharp
- **NuGet包**: CefSharp.WinForms 或 CefSharp.Wpf
- **基于**: Chromium Embedded Framework (CEF)
- **支持平台**: Windows (.NET Framework 4.6.2+)

### 🎯 核心特性
- **完整浏览器** - 基于Chromium的完整浏览器功能
- **JavaScript交互** - C#与JavaScript双向通信
- **事件监控** - 网络请求、页面事件监控
- **Cookie访问** - 完整的Cookie容器访问
- **无头模式** - 支持后台自动化操作

### 🔧 本项目需要的核心方法

#### 1. 基础浏览器控件
```csharp
// 创建浏览器控件
var browser = new ChromiumWebBrowser("https://oa.company.com/login");
browser.Dock = DockStyle.Fill;
this.Controls.Add(browser);

// 导航到URL
browser.Load("https://oa.company.com/dashboard");

// 等待初始化完成
await browser.WaitForInitializationAsync();
```

#### 2. JavaScript执行（DOM操作方式）
```csharp
// 页面加载完成后执行脚本
browser.LoadingStateChanged += async (sender, args) => {
    if (!args.IsLoading) {
        // 填写表单
        var script = @"
            document.getElementById('username').value = '用户名';
            document.getElementById('password').value = '密码';
            document.getElementById('loginBtn').click();
        ";
        await browser.ExecuteScriptAsync(script);
    }
};

// 使用ExecuteScriptAsyncWhenPageLoaded
var script = @"
    // 查找并填写表单字段
    var usernameField = document.querySelector('input[name=""username""]');
    var passwordField = document.querySelector('input[name=""password""]');
    var submitButton = document.querySelector('button[type=""submit""]');
    
    if (usernameField) usernameField.value = '用户名';
    if (passwordField) passwordField.value = '密码';
    if (submitButton) submitButton.click();
";
browser.ExecuteScriptAsyncWhenPageLoaded(script);

// 获取页面数据
var result = await browser.EvaluateScriptAsync(@"
    // 获取表格数据
    var rows = document.querySelectorAll('table tbody tr');
    var data = [];
    rows.forEach(row => {
        var cells = row.querySelectorAll('td');
        data.push({
            id: cells[0].textContent,
            name: cells[1].textContent,
            status: cells[2].textContent
        });
    });
    JSON.stringify(data);
");

if (result.Success) {
    var jsonData = result.Result.ToString();
    var tableData = JsonConvert.DeserializeObject<List<TableRow>>(jsonData);
}
```

#### 3. 鼠标键盘事件模拟（坐标操作方式）
```csharp
// 鼠标点击事件
public void ClickAt(int x, int y) {
    var host = browser.GetBrowser().GetHost();
    
    // 鼠标按下
    host.SendMouseClickEvent(x, y, MouseButtonType.Left, false, 1, CefEventFlags.None);
    Thread.Sleep(50);
    
    // 鼠标释放
    host.SendMouseClickEvent(x, y, MouseButtonType.Left, true, 1, CefEventFlags.None);
}

// 鼠标拖拽
public void DragFromTo(int fromX, int fromY, int toX, int toY) {
    var host = browser.GetBrowser().GetHost();
    
    // 开始拖拽
    host.SendMouseClickEvent(fromX, fromY, MouseButtonType.Left, false, 1, CefEventFlags.None);
    Thread.Sleep(100);
    
    // 移动鼠标
    host.SendMouseMoveEvent(toX, toY, false, CefEventFlags.None);
    Thread.Sleep(100);
    
    // 结束拖拽
    host.SendMouseClickEvent(toX, toY, MouseButtonType.Left, true, 1, CefEventFlags.None);
}

// 键盘事件
public void SendKey(int keyCode) {
    var host = browser.GetBrowser().GetHost();
    
    var keyEvent = new KeyEvent {
        WindowsKeyCode = keyCode,
        Type = KeyEventType.KeyDown,
        Modifiers = CefEventFlags.None
    };
    host.SendKeyEvent(keyEvent);
    
    keyEvent.Type = KeyEventType.KeyUp;
    host.SendKeyEvent(keyEvent);
}

// 发送文本
public void SendText(string text) {
    foreach (char c in text) {
        var keyEvent = new KeyEvent {
            WindowsKeyCode = c,
            Type = KeyEventType.Char,
            Modifiers = CefEventFlags.None
        };
        browser.GetBrowser().GetHost().SendKeyEvent(keyEvent);
        Thread.Sleep(10);
    }
}

// 组合键（如Ctrl+A）
public void SendCtrlA() {
    var host = browser.GetBrowser().GetHost();
    
    var ctrlDown = new KeyEvent {
        WindowsKeyCode = 0x11, // VK_CONTROL
        Type = KeyEventType.KeyDown,
        Modifiers = CefEventFlags.ControlDown
    };
    host.SendKeyEvent(ctrlDown);
    
    var aDown = new KeyEvent {
        WindowsKeyCode = 0x41, // 'A'
        Type = KeyEventType.KeyDown,
        Modifiers = CefEventFlags.ControlDown
    };
    host.SendKeyEvent(aDown);
    
    var aUp = new KeyEvent {
        WindowsKeyCode = 0x41,
        Type = KeyEventType.KeyUp,
        Modifiers = CefEventFlags.ControlDown
    };
    host.SendKeyEvent(aUp);
    
    var ctrlUp = new KeyEvent {
        WindowsKeyCode = 0x11,
        Type = KeyEventType.KeyUp,
        Modifiers = CefEventFlags.None
    };
    host.SendKeyEvent(ctrlUp);
}
```

#### 4. Cookie管理
```csharp
// 获取所有Cookie
public async Task<List<Cookie>> GetAllCookiesAsync() {
    var cookieManager = browser.GetCookieManager();
    var visitor = new CookieVisitor();
    
    if (cookieManager.VisitAllCookies(visitor)) {
        await visitor.Task;
        return visitor.Cookies;
    }
    return new List<Cookie>();
}

// Cookie访问器实现
public class CookieVisitor : ICookieVisitor {
    private readonly TaskCompletionSource<bool> _taskCompletionSource = new TaskCompletionSource<bool>();
    public List<Cookie> Cookies { get; } = new List<Cookie>();
    public Task Task => _taskCompletionSource.Task;

    public bool Visit(Cookie cookie, int count, int total, ref bool deleteCookie) {
        Cookies.Add(cookie);
        
        if (count == total - 1) {
            _taskCompletionSource.SetResult(true);
        }
        
        return true;
    }
}

// 设置Cookie
public void SetCookie(string url, string name, string value, string domain = null) {
    var cookieManager = browser.GetCookieManager();
    var cookie = new Cookie {
        Name = name,
        Value = value,
        Domain = domain ?? new Uri(url).Host,
        Path = "/",
        HttpOnly = false,
        Secure = false
    };
    
    cookieManager.SetCookie(url, cookie);
}
```

#### 5. 网络请求监控
```csharp
// 监控所有网络请求
browser.RequestHandler = new CustomRequestHandler();

public class CustomRequestHandler : IRequestHandler {
    public bool OnBeforeBrowse(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request, bool userGesture, bool isRedirect) {
        // 请求前处理
        ETLogManager.Info($"导航到: {request.Url}");
        return false;
    }

    public IResourceRequestHandler GetResourceRequestHandler(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request, bool isNavigation, bool isDownload, string requestInitiator, ref bool disableDefaultHandling) {
        return new CustomResourceRequestHandler();
    }
}

public class CustomResourceRequestHandler : IResourceRequestHandler {
    public CefReturnValue OnBeforeResourceLoad(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request, IRequestCallback callback) {
        // 修改请求头
        request.SetHeaderByName("User-Agent", "CustomAgent/1.0", true);
        
        // 记录API请求
        if (request.Url.Contains("/api/")) {
            ETLogManager.Info($"API请求: {request.Method} {request.Url}");
        }
        
        return CefReturnValue.Continue;
    }

    public void OnResourceResponse(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request, IResponse response) {
        // 响应处理
        if (response.StatusCode == 200 && request.Url.Contains("/api/")) {
            ETLogManager.Info($"API响应成功: {request.Url}");
        }
    }
}
```

#### 6. 页面事件处理
```csharp
// 页面加载状态变化
browser.LoadingStateChanged += (sender, args) => {
    if (args.IsLoading) {
        ETLogManager.Info("页面开始加载");
        OnPageLoadStart?.Invoke();
    } else {
        ETLogManager.Info("页面加载完成");
        OnPageLoadComplete?.Invoke();
    }
};

// 导航完成事件
browser.LoadEnd += (sender, args) => {
    if (args.Frame.IsMain) {
        ETLogManager.Info($"主框架加载完成: {args.Frame.Url}");
        OnMainFrameLoadEnd?.Invoke(args.Frame.Url);
    }
};

// JavaScript异常处理
browser.JavascriptException += (sender, args) => {
    ETLogManager.Error($"JavaScript错误: {args.Message} 在 {args.SourceLine}:{args.LineNumber}");
};

// 控制台消息
browser.ConsoleMessage += (sender, args) => {
    ETLogManager.Info($"控制台消息 [{args.Level}]: {args.Message}");
};
```

#### 7. 元素定位辅助方法
```csharp
// 获取元素位置
public async Task<Point?> GetElementPositionAsync(string selector) {
    var script = $@"
        var element = document.querySelector('{selector}');
        if (element) {{
            var rect = element.getBoundingClientRect();
            JSON.stringify({{
                x: Math.round(rect.left + rect.width / 2),
                y: Math.round(rect.top + rect.height / 2)
            }});
        }} else {{
            null;
        }}
    ";
    
    var result = await browser.EvaluateScriptAsync(script);
    if (result.Success && result.Result != null) {
        var position = JsonConvert.DeserializeObject<Point>(result.Result.ToString());
        return position;
    }
    return null;
}

// 等待元素出现
public async Task<bool> WaitForElementAsync(string selector, int timeoutMs = 10000) {
    var startTime = DateTime.Now;
    
    while ((DateTime.Now - startTime).TotalMilliseconds < timeoutMs) {
        var script = $"document.querySelector('{selector}') !== null";
        var result = await browser.EvaluateScriptAsync(script);
        
        if (result.Success && (bool)result.Result) {
            return true;
        }
        
        await Task.Delay(100);
    }
    
    return false;
}
```

## 🔗 两个库的集成使用

### 认证信息传递
```csharp
// 从CefSharp获取Cookie并传递给Flurl.Http
var cookies = await GetAllCookiesAsync();
var cookieJar = new CookieJar();

foreach (var cookie in cookies) {
    cookieJar.AddOrReplace(cookie.Name, cookie.Value, cookie.Domain, cookie.Path);
}

// 在Flurl.Http中使用Cookie
var apiResponse = await "https://oa.company.com/api/data"
    .WithCookies(cookieJar)
    .GetJsonAsync<ApiData>();
```

### 请求头同步
```csharp
// 从浏览器请求中提取认证头
var authHeaders = ExtractAuthHeaders(capturedRequest);

// 在API请求中使用
var response = await "https://oa.company.com/api/submit"
    .WithHeaders(authHeaders)
    .WithCookies(cookieJar)
    .PostJsonAsync(data);
```

## 🎯 最佳实践和注意事项

### Flurl.Http 最佳实践

#### 1. 性能优化
```csharp
// 使用客户端实例复用连接
private static readonly FlurlClient _oaClient = new FlurlClient("https://oa.company.com")
    .Configure(settings => {
        settings.Timeout = TimeSpan.FromSeconds(30);
        settings.AllowedHttpStatusRange = "200-299,400-499";
    });

// 避免频繁创建客户端
public async Task<T> GetApiDataAsync<T>(string endpoint) {
    return await _oaClient.Request(endpoint)
        .WithCookies(_cookieJar)
        .GetJsonAsync<T>();
}
```

#### 2. 错误处理策略
```csharp
public async Task<ApiResult<T>> SafeApiCallAsync<T>(string url, object data = null) {
    try {
        var response = data == null
            ? await url.WithCookies(_cookieJar).GetJsonAsync<T>()
            : await url.WithCookies(_cookieJar).PostJsonAsync(data).ReceiveJson<T>();

        return ApiResult<T>.Success(response);
    }
    catch (FlurlHttpTimeoutException ex) {
        ETLogManager.Error($"API请求超时: {url}", ex);
        return ApiResult<T>.Failure("请求超时，请稍后重试");
    }
    catch (FlurlHttpException ex) when (ex.StatusCode == 401) {
        ETLogManager.Warn($"认证失效: {url}");
        // 触发重新登录
        await RefreshAuthenticationAsync();
        return ApiResult<T>.Failure("认证已过期，请重新登录");
    }
    catch (FlurlHttpException ex) {
        var errorMsg = await ex.GetResponseStringAsync();
        ETLogManager.Error($"API请求失败: {url}, 状态码: {ex.StatusCode}, 错误: {errorMsg}", ex);
        return ApiResult<T>.Failure($"请求失败: {errorMsg}");
    }
}
```

### CefSharp 最佳实践

#### 1. 资源管理
```csharp
// 正确的浏览器生命周期管理
public class ETWebSimulationBrowser : Form, IDisposable {
    private ChromiumWebBrowser _browser;
    private bool _disposed = false;

    protected override void Dispose(bool disposing) {
        if (!_disposed && disposing) {
            _browser?.Dispose();
            _disposed = true;
        }
        base.Dispose(disposing);
    }

    // 在应用程序退出时清理CEF
    public static void Shutdown() {
        Cef.Shutdown();
    }
}
```

#### 2. 线程安全操作
```csharp
// 确保UI操作在主线程执行
public async Task ExecuteScriptSafelyAsync(string script) {
    if (InvokeRequired) {
        await Task.Run(() => Invoke(new Action(async () => await ExecuteScriptSafelyAsync(script))));
        return;
    }

    if (_browser?.IsBrowserInitialized == true) {
        await _browser.ExecuteScriptAsync(script);
    }
}
```

#### 3. 等待策略
```csharp
// 智能等待页面元素
public async Task<bool> WaitForElementWithRetryAsync(string selector, int maxRetries = 30) {
    for (int i = 0; i < maxRetries; i++) {
        try {
            var exists = await CheckElementExistsAsync(selector);
            if (exists) return true;

            await Task.Delay(500); // 等待500ms
        }
        catch (Exception ex) {
            ETLogManager.Warn($"检查元素存在性时出错: {ex.Message}");
            await Task.Delay(1000); // 出错时等待更长时间
        }
    }
    return false;
}
```

## 🔧 集成ExtensionsTools的方法

### 配置管理集成
```csharp
// 使用ETIniFile管理OA配置
public class ETWebConfigHelper {
    private static readonly string ConfigFile = Path.Combine(
        Application.StartupPath, "Config", "ETWebConfig.ini");

    public static string GETWebBaseUrl() {
        return ETIniFile.ReadString(ConfigFile, "OA", "BaseUrl", "");
    }

    public static void SETWebBaseUrl(string url) {
        ETIniFile.WriteString(ConfigFile, "OA", "BaseUrl", url);
    }

    public static int GetRequestTimeout() {
        return ETIniFile.ReadInt(ConfigFile, "Network", "TimeoutSeconds", 30);
    }

    public static bool GetAutoRetryEnabled() {
        return ETIniFile.ReadBool(ConfigFile, "Network", "AutoRetry", true);
    }
}
```

### 日志记录集成
```csharp
// 使用ETLogManager记录操作日志
public class ETWebLogger {
    public static void LogApiRequest(string method, string url, object data = null) {
        var message = $"API请求: {method} {url}";
        if (data != null) {
            message += $", 数据: {JsonConvert.SerializeObject(data)}";
        }
        ETLogManager.Info(message);
    }

    public static void LogBrowserAction(string action, string details = "") {
        ETLogManager.Info($"浏览器操作: {action} {details}");
    }

    public static void LogAuthenticationEvent(string eventType, bool success, string details = "") {
        var level = success ? "Info" : "Warn";
        var message = $"认证事件: {eventType}, 成功: {success}";
        if (!string.IsNullOrEmpty(details)) {
            message += $", 详情: {details}";
        }

        if (success) {
            ETLogManager.Info(message);
        } else {
            ETLogManager.Warn(message);
        }
    }
}
```

### 异常处理集成
```csharp
// 使用ETException统一异常处理
public class ETWebException : ETException {
    public ETWebException(string message) : base(message) { }
    public ETWebException(string message, Exception innerException) : base(message, innerException) { }

    public static class ErrorCodes {
        public const string AUTHENTICATION_FAILED = "OA_AUTH_001";
        public const string API_REQUEST_FAILED = "OA_API_002";
        public const string BROWSER_OPERATION_FAILED = "OA_BROWSER_003";
        public const string SESSION_EXPIRED = "OA_SESSION_004";
        public const string FILE_UPLOAD_FAILED = "OA_UPLOAD_005";
    }
}

// 统一异常处理方法
public static class ETWebExceptionHandler {
    public static void HandleException(Exception ex, string operation) {
        var errorCode = GetErrorCode(ex);
        var message = $"操作失败: {operation}, 错误: {ex.Message}";

        ETLogManager.Error(message, ex);

        // 根据异常类型决定是否需要用户干预
        if (IsUserInterventionRequired(ex)) {
            MessageBox.Show(message, "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private static string GetErrorCode(Exception ex) {
        return ex switch {
            FlurlHttpException when ((FlurlHttpException)ex).StatusCode == 401 => ETWebException.ErrorCodes.AUTHENTICATION_FAILED,
            FlurlHttpException => ETWebException.ErrorCodes.API_REQUEST_FAILED,
            CefSharpException => ETWebException.ErrorCodes.BROWSER_OPERATION_FAILED,
            _ => "UNKNOWN_ERROR"
        };
    }

    private static bool IsUserInterventionRequired(Exception ex) {
        return ex is FlurlHttpException httpEx && httpEx.StatusCode == 401;
    }
}
```

## 📊 性能监控和调试

### 性能指标收集
```csharp
public class ETWebPerformanceMonitor {
    private static readonly Dictionary<string, List<TimeSpan>> _operationTimes = new();

    public static IDisposable MeasureOperation(string operationName) {
        return new OperationTimer(operationName);
    }

    private class OperationTimer : IDisposable {
        private readonly string _operationName;
        private readonly Stopwatch _stopwatch;

        public OperationTimer(string operationName) {
            _operationName = operationName;
            _stopwatch = Stopwatch.StartNew();
        }

        public void Dispose() {
            _stopwatch.Stop();
            RecordOperationTime(_operationName, _stopwatch.Elapsed);
        }
    }

    private static void RecordOperationTime(string operation, TimeSpan duration) {
        if (!_operationTimes.ContainsKey(operation)) {
            _operationTimes[operation] = new List<TimeSpan>();
        }

        _operationTimes[operation].Add(duration);

        // 记录到日志
        ETLogManager.Info($"性能指标: {operation} 耗时 {duration.TotalMilliseconds:F2}ms");

        // 如果操作时间过长，记录警告
        if (duration.TotalSeconds > 10) {
            ETLogManager.Warn($"操作耗时过长: {operation} 耗时 {duration.TotalSeconds:F2}秒");
        }
    }

    public static void LogPerformanceSummary() {
        foreach (var kvp in _operationTimes) {
            var times = kvp.Value;
            var avgTime = times.Average(t => t.TotalMilliseconds);
            var maxTime = times.Max(t => t.TotalMilliseconds);
            var minTime = times.Min(t => t.TotalMilliseconds);

            ETLogManager.Info($"性能统计 {kvp.Key}: 平均 {avgTime:F2}ms, 最大 {maxTime:F2}ms, 最小 {minTime:F2}ms, 次数 {times.Count}");
        }
    }
}

// 使用示例
public async Task<ApiResult> PerformApiOperationAsync() {
    using (ETWebPerformanceMonitor.MeasureOperation("API_CALL")) {
        return await CallApiAsync();
    }
}
```

## 🔒 安全考虑

### 敏感信息保护
```csharp
public class ETWebSecurityHelper {
    // 简单的字符串加密（用于本地存储）
    public static string EncryptString(string plainText, string key) {
        // 实现简单的XOR加密或使用.NET的加密API
        var keyBytes = Encoding.UTF8.GetBytes(key);
        var plainBytes = Encoding.UTF8.GetBytes(plainText);
        var encryptedBytes = new byte[plainBytes.Length];

        for (int i = 0; i < plainBytes.Length; i++) {
            encryptedBytes[i] = (byte)(plainBytes[i] ^ keyBytes[i % keyBytes.Length]);
        }

        return Convert.ToBase64String(encryptedBytes);
    }

    public static string DecryptString(string encryptedText, string key) {
        var keyBytes = Encoding.UTF8.GetBytes(key);
        var encryptedBytes = Convert.FromBase64String(encryptedText);
        var decryptedBytes = new byte[encryptedBytes.Length];

        for (int i = 0; i < encryptedBytes.Length; i++) {
            decryptedBytes[i] = (byte)(encryptedBytes[i] ^ keyBytes[i % keyBytes.Length]);
        }

        return Encoding.UTF8.GetString(decryptedBytes);
    }

    // 生成机器唯一标识作为加密密钥
    public static string GetMachineKey() {
        var machineId = Environment.MachineName + Environment.UserName;
        using (var sha256 = SHA256.Create()) {
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(machineId));
            return Convert.ToBase64String(hash).Substring(0, 16);
        }
    }
}
```

这个技术手册现在涵盖了本项目开发所需的所有核心功能、方法、最佳实践和安全考虑，确保开发过程中有准确和全面的技术参考。
