using ET;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using HyAssistant.WebBrowserV2.Utils;
using HyAssistant.WebBrowserV2.Forms;

namespace HyAssistant.WebBrowserV2.Managers
{
    /// <summary>
    /// WebBrowser标签页管理器V2版本，负责标签页的创建、管理和销毁 优化重点：异步编程安全、日志统一、线程安全
    /// </summary>
    public class WebBrowserTabManagerV2 : IDisposable
    {
        #region 字段和属性

        /// <summary>
        /// TabControl控件引用
        /// </summary>
        private readonly TabControl _tabControl;

        /// <summary>
        /// 配置管理器V2（JSON格式）
        /// </summary>
        private readonly WebBrowserConfigManagerV2 _configManager;

        /// <summary>
        /// Cookie管理器V2
        /// </summary>
        private readonly WebBrowserCookieManagerV2 _cookieManager;

        /// <summary>
        /// 会话管理器V2
        /// </summary>
        private readonly WebBrowserSessionManagerV2 _sessionManager;

        /// <summary>
        /// Cookie传递管理器V2（V2版本关键优化）
        /// </summary>
        private readonly CookieTransferManagerV2 _cookieTransferManager;

        /// <summary>
        /// 用于记录的对象
        /// </summary>
        private readonly object _logSource;

        /// <summary>
        /// 异步操作取消令牌源
        /// </summary>
        private readonly CancellationTokenSource _cancellationTokenSource;

        /// <summary>
        /// 异步操作安全执行器
        /// </summary>
        private readonly AsyncOperationExecutorV2 _asyncExecutor;

        /// <summary>
        /// WebView2线程安全操作器（V2新增）
        /// </summary>
        private readonly WebView2ThreadSafeOperatorV2 _webView2Operator;

        /// <summary>
        /// 标签页URL变更事件委托
        /// </summary>
        public delegate void TabUrlChangedEventHandler(string tabName, string url);

        /// <summary>
        /// 标签页标题变更事件委托
        /// </summary>
        public delegate void TabTitleChangedEventHandler(string tabName, string title);

        /// <summary>
        /// 标签页切换事件委托
        /// </summary>
        public delegate void TabSwitchedEventHandler(string sectionId);

        /// <summary>
        /// 标签页URL变更事件
        /// </summary>
        public event TabUrlChangedEventHandler TabUrlChanged;

        /// <summary>
        /// 标签页标题变更事件
        /// </summary>
        public event TabTitleChangedEventHandler TabTitleChanged;

        /// <summary>
        /// 标签页切换事件
        /// </summary>
        public event TabSwitchedEventHandler TabSwitched;

        /// <summary>
        /// 标签页数据字典，使用SectionId作为键
        /// </summary>
        public Dictionary<string, TabDataV2> TabDataDictionary { get; } = new Dictionary<string, TabDataV2>();

        /// <summary>
        /// 标签页名称到SectionId的映射，用于兼容旧代码
        /// </summary>
        private Dictionary<string, string> TabNameToSectionId { get; } = new Dictionary<string, string>();

        /// <summary>
        /// 用于保护TabDataDictionary和TabNameToSectionId的并发访问锁
        /// </summary>
        private readonly object _tabDataLock = new object();

        /// <summary>
        /// 当前选中的标签页的SectionId
        /// </summary>
        public string CurrentTabSectionId { get; private set; }

        /// <summary>
        /// 当前选中的标签页名称（兼容旧代码）
        /// </summary>
        public string CurrentTabName
        {
            get
            {
                if (string.IsNullOrEmpty(CurrentTabSectionId) || !TabDataDictionary.TryGetValue(CurrentTabSectionId, out TabDataV2 tabData))
                    return string.Empty;

                return tabData.Config.Name;
            }
        }

        // 常量字符串，避免重复创建相同字符串
        private const string HTTP_PREFIX = "http://";

        private const string HTTPS_PREFIX = "https://";
        private const string FILE_PREFIX = "file://";
        private const string ABOUT_PREFIX = "about:";
        private const string ABOUT_BLANK = "about:blank";
        private const string USER_AGENT_ARG = "--user-agent=\"{0}\"";
        private const string PROXY_SERVER_ARG = "--proxy-server={0}:{1}";

        /// <summary>
        /// 线程安全地获取所有标签页配置的副本
        /// </summary>
        /// <returns>标签页配置列表</returns>
        public List<WebBrowserTabConfig> GetAllTabConfigsSafely()
        {
            lock (_tabDataLock)
            {
                List<WebBrowserTabConfig> configs = new List<WebBrowserTabConfig>();
                foreach (var pair in TabDataDictionary)
                {
                    configs.Add(pair.Value.Config);
                }
                return configs;
            }
        }

        /// <summary>
        /// 线程安全地检查标签页是否存在
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>是否存在</returns>
        public bool ContainsTabSafely(string sectionId)
        {
            lock (_tabDataLock)
            {
                return TabDataDictionary.ContainsKey(sectionId);
            }
        }

        /// <summary>
        /// 线程安全地获取标签页配置
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>标签页配置，如果不存在则返回null</returns>
        public WebBrowserTabConfig GetTabConfig(string sectionId)
        {
            if (string.IsNullOrEmpty(sectionId))
                return null;

            lock (_tabDataLock)
            {
                if (TabDataDictionary.TryGetValue(sectionId, out TabDataV2 tabData))
                {
                    return tabData.Config;
                }
                return null;
            }
        }

        #endregion 字段和属性

        #region 内部类

        /// <summary>
        /// 标签页数据类V2版本
        /// </summary>
        public class TabDataV2
        {
            /// <summary>
            /// 标签页
            /// </summary>
            public TabPage TabPage { get; set; }

            /// <summary>
            /// WebView2控件
            /// </summary>
            public WebView2 WebView { get; set; }

            /// <summary>
            /// 标签页配置
            /// </summary>
            public WebBrowserTabConfig Config { get; set; }

            /// <summary>
            /// 最后访问的URL
            /// </summary>
            public string LastUrl { get; set; }

            /// <summary>
            /// WebView2环境
            /// </summary>
            public CoreWebView2Environment Environment { get; set; }

            /// <summary>
            /// 是否已初始化WebView2
            /// </summary>
            public bool IsWebViewInitialized { get; set; }

            /// <summary>
            /// WebResourceRequested事件处理器引用，用于在清理资源时解除事件绑定
            /// </summary>
            public EventHandler<CoreWebView2WebResourceRequestedEventArgs> WebResourceRequestedHandler { get; set; }

            /// <summary>
            /// 是否为副本标签页
            /// </summary>
            public bool IsClone { get; set; }

            /// <summary>
            /// 父标签页的SectionId
            /// </summary>
            public string ParentSectionId { get; set; }

            /// <summary>
            /// 异步操作取消令牌
            /// </summary>
            public CancellationToken CancellationToken { get; set; }
        }

        #endregion 内部类

        #region 构造方法

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="tabControl">TabControl控件</param>
        /// <param name="configManager">配置管理器V2</param>
        /// <param name="cookieManager">Cookie管理器V2</param>
        /// <param name="sessionManager">会话管理器V2</param>
        /// <param name="logSource">用于记录日志的对象</param>
        public WebBrowserTabManagerV2(TabControl tabControl, WebBrowserConfigManagerV2 configManager,
            WebBrowserCookieManagerV2 cookieManager, WebBrowserSessionManagerV2 sessionManager, object logSource)
        {
            _tabControl = tabControl ?? throw new ArgumentNullException(nameof(tabControl));
            _configManager = configManager ?? throw new ArgumentNullException(nameof(configManager));
            _cookieManager = cookieManager ?? throw new ArgumentNullException(nameof(cookieManager));
            _sessionManager = sessionManager ?? throw new ArgumentNullException(nameof(sessionManager));
            _logSource = logSource ?? this;
            _cancellationTokenSource = new CancellationTokenSource();
            _asyncExecutor = new AsyncOperationExecutorV2(_logSource);
            _webView2Operator = new WebView2ThreadSafeOperatorV2(_logSource);

            // V2版本关键优化：初始化Cookie传递管理器
            _cookieTransferManager = new CookieTransferManagerV2(_logSource);

            // 注册标签页切换事件
            _tabControl.SelectedIndexChanged += TabControl_SelectedIndexChanged;

            ETLogManager.Info(_logSource, "WebBrowserTabManagerV2初始化完成，包含Cookie传递管理器");
        }

        #endregion 构造方法

        #region 标签页管理方法

        /// <summary>
        /// 根据配置创建新标签页（异步安全版本）
        /// </summary>
        /// <param name="config">标签页配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>新标签页的SectionId</returns>
        public async Task<string> CreateNewTabAsync(WebBrowserTabConfig config, CancellationToken cancellationToken = default)
        {
            try
            {
                // V2版本优化: 检查取消令牌
                cancellationToken.ThrowIfCancellationRequested();

                // 检查配置是否为空
                if (config == null)
                {
                    ETLogManager.Error(_logSource, "创建标签页失败：配置不能为空");
                    MessageBox.Show("标签页配置不能为空！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return null;
                }

                // 确保名称不为空
                if (string.IsNullOrEmpty(config.Name))
                {
                    ETLogManager.Error(_logSource, "创建标签页失败：名称不能为空");
                    MessageBox.Show("标签页名称不能为空！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return null;
                }

                // 确保SectionId不为空
                if (string.IsNullOrEmpty(config.SectionId))
                {
                    config.GenerateNewSectionId();
                    ETLogManager.Info(_logSource, $"为新标签页生成SectionId: {config.SectionId}");
                }

                // 使用配置创建标签页
                return await CreateTabWithConfigAsync(config).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"创建新标签页异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 使用配置创建标签页（异步安全版本）
        /// </summary>
        /// <param name="config">标签页配置</param>
        /// <returns>标签页的SectionId</returns>
        private async Task<string> CreateTabWithConfigAsync(WebBrowserTabConfig config)
        {
            try
            {
                ETLogManager.Info(_logSource, $"开始创建标签页: {config.Name}");

                // 在UI线程上创建TabPage和WebView2
                TabPage tabPage = null;
                WebView2 webView = null;

                if (_tabControl.InvokeRequired)
                {
                    await Task.Run(() =>
                    {
                        _tabControl.Invoke(new Action(() =>
                        {
                            CreateUIComponents(config, out tabPage, out webView);
                        }));
                    }).ConfigureAwait(false);
                }
                else
                {
                    CreateUIComponents(config, out tabPage, out webView);
                }

                // 创建标签页数据
                TabDataV2 tabData = new TabDataV2
                {
                    TabPage = tabPage,
                    WebView = webView,
                    Config = config,
                    LastUrl = config.Url,
                    IsWebViewInitialized = false,
                    IsClone = false,
                    CancellationToken = _cancellationTokenSource.Token
                };

                // 线程安全地添加到字典
                lock (_tabDataLock)
                {
                    TabDataDictionary[config.SectionId] = tabData;
                    TabNameToSectionId[config.Name] = config.SectionId;
                }

                // 异步初始化WebView2
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await InitializeWebView2SafelyAsync(config.SectionId).ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"异步初始化WebView2失败: {ex.Message}");
                    }
                });

                ETLogManager.Info(_logSource, $"标签页创建成功: {config.Name}, SectionId: {config.SectionId}");
                return config.SectionId;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"创建标签页失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建UI组件（必须在UI线程上调用）
        /// </summary>
        /// <param name="config">标签页配置</param>
        /// <param name="tabPage">输出的TabPage</param>
        /// <param name="webView">输出的WebView2</param>
        private void CreateUIComponents(WebBrowserTabConfig config, out TabPage tabPage, out WebView2 webView)
        {
            // 创建新的TabPage，使用配置中的名称
            tabPage = new TabPage(config.Name)
            {
                UseVisualStyleBackColor = true,
                Name = config.SectionId // 使用SectionId作为TabPage的Name属性
            };

            // 将TabPage添加到TabControl
            _tabControl.TabPages.Add(tabPage);

            // 创建WebView2控件，注意不要设置Source属性，避免自动初始化
            webView = new WebView2
            {
                Dock = DockStyle.Fill,
                DefaultBackgroundColor = System.Drawing.Color.White
            };

            // 将WebView2添加到TabPage
            tabPage.Controls.Add(webView);

            ETLogManager.Info(_logSource, $"UI组件创建完成: {config.Name}");
        }

        /// <summary>
        /// 安全地初始化WebView2（异步版本）- V2优化版本
        /// </summary>
        /// <param name="tabSectionId">标签页的SectionId</param>
        /// <returns>初始化任务</returns>
        private async Task InitializeWebView2SafelyAsync(string tabSectionId)
        {
            await _asyncExecutor.ExecuteAsync(async (cancellationToken) =>
            {
                if (string.IsNullOrEmpty(tabSectionId))
                {
                    ETLogManager.Error(_logSource, "初始化WebView2失败: SectionId为空");
                    return;
                }

                TabDataV2 tabData;
                lock (_tabDataLock)
                {
                    if (!TabDataDictionary.TryGetValue(tabSectionId, out tabData))
                    {
                        ETLogManager.Error(_logSource, $"初始化WebView2失败: 找不到标签页数据: {tabSectionId}");
                        return;
                    }
                }

                // V2优化: 增强的重复初始化检查
                if (tabData.IsWebViewInitialized)
                {
                    ETLogManager.Warning(_logSource, $"[{tabData.Config.Name}] WebView2已经初始化，跳过重复初始化");
                    return;
                }

                // V2优化: 检查WebView2控件状态
                if (tabData.WebView == null)
                {
                    ETLogManager.Error(_logSource, $"[{tabData.Config.Name}] WebView2控件为空，无法初始化");
                    return;
                }

                if (tabData.WebView.IsDisposed)
                {
                    ETLogManager.Error(_logSource, $"[{tabData.Config.Name}] WebView2控件已释放，无法初始化");
                    return;
                }

                ETLogManager.Info(_logSource, $"开始初始化WebView2: {tabData.Config.Name}");

                // V2优化: 使用重试机制的异步初始化
                bool initSuccess = await InitializeWebView2WithRetryAsync(tabSectionId, tabData, cancellationToken).ConfigureAwait(false);

                if (initSuccess)
                {
                    ETLogManager.Info(_logSource, $"WebView2初始化成功: {tabData.Config.Name}");
                }
                else
                {
                    ETLogManager.Error(_logSource, $"WebView2初始化失败: {tabData.Config.Name}");
                }
            }, $"初始化WebView2[{tabSectionId}]", 60000, false, $"SectionId: {tabSectionId}").ConfigureAwait(false);
        }

        /// <summary>
        /// 带重试机制的WebView2初始化（V2新增）
        /// </summary>
        /// <param name="tabSectionId">标签页SectionId</param>
        /// <param name="tabData">标签页数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>初始化是否成功</returns>
        private async Task<bool> InitializeWebView2WithRetryAsync(string tabSectionId, TabDataV2 tabData, CancellationToken cancellationToken)
        {
            const int maxRetries = 3;
            const int baseDelayMs = 1000;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    // 检查取消令牌
                    cancellationToken.ThrowIfCancellationRequested();
                    if (tabData.CancellationToken.IsCancellationRequested)
                    {
                        ETLogManager.Info(_logSource, $"WebView2初始化被取消: {tabData.Config.Name}");
                        return false;
                    }

                    ETLogManager.Info(_logSource, $"WebView2初始化尝试 {attempt}/{maxRetries}: {tabData.Config.Name}");

                    // 使用异步执行器在UI线程上安全执行
                    bool success = await _asyncExecutor.ExecuteOnUIThreadAsync(tabData.WebView, async (token) =>
                    {
                        await InitializeWebView2InternalAsync(tabSectionId, tabData, token).ConfigureAwait(false);
                    }, $"初始化WebView2[{tabData.Config.Name}]-尝试{attempt}", 30000, false).ConfigureAwait(false);

                    if (success)
                    {
                        if (attempt > 1)
                        {
                            ETLogManager.Info(_logSource, $"WebView2初始化在第{attempt}次尝试后成功: {tabData.Config.Name}");
                        }
                        return true;
                    }
                }
                catch (OperationCanceledException)
                {
                    ETLogManager.Info(_logSource, $"WebView2初始化被取消: {tabData.Config.Name}");
                    return false;
                }
                catch (Exception ex)
                {
                    ETLogManager.Warning(_logSource, $"WebView2初始化第{attempt}次尝试失败: {tabData.Config.Name}, 错误: {ex.Message}");

                    if (attempt == maxRetries)
                    {
                        ETLogManager.Error(_logSource, $"WebView2初始化在{maxRetries}次尝试后仍然失败: {tabData.Config.Name}");
                        WebBrowserExceptionHandlerV2.HandleAsyncException(_logSource, ex, $"WebView2初始化[{tabData.Config.Name}]");
                        return false;
                    }
                }

                // 等待后重试（递增延迟）
                if (attempt < maxRetries)
                {
                    int delayMs = baseDelayMs * attempt;
                    ETLogManager.Info(_logSource, $"等待{delayMs}ms后重试WebView2初始化: {tabData.Config.Name}");
                    await Task.Delay(delayMs, cancellationToken).ConfigureAwait(false);
                }
            }

            return false;
        }

        /// <summary>
        /// 内部WebView2初始化方法（V2增强异步安全版本）
        /// </summary>
        /// <param name="tabSectionId">标签页SectionId</param>
        /// <param name="tabData">标签页数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>初始化任务</returns>
        private async Task InitializeWebView2InternalAsync(string tabSectionId, TabDataV2 tabData, CancellationToken cancellationToken = default)
        {
            WebBrowserTabConfig config = tabData.Config;
            WebView2 webView = tabData.WebView;

            try
            {
                // V2优化: 增强的取消令牌检查
                cancellationToken.ThrowIfCancellationRequested();
                if (tabData.CancellationToken.IsCancellationRequested)
                {
                    ETLogManager.Info(_logSource, $"WebView2初始化被取消: {config.Name}");
                    return;
                }

                // V2优化: 验证WebView2控件状态
                if (webView == null || webView.IsDisposed)
                {
                    throw new InvalidOperationException($"WebView2控件无效: {config.Name}");
                }

                ETLogManager.Info(_logSource, $"开始WebView2核心初始化: {config.Name}");

                // V2优化: 安全的环境创建
                CoreWebView2Environment environment = await CreateWebView2EnvironmentSafelyAsync(config, cancellationToken).ConfigureAwait(false);
                tabData.Environment = environment;

                // V2优化: 安全的核心初始化
                await InitializeWebView2CoreSafelyAsync(webView, environment, config, cancellationToken).ConfigureAwait(false);

                // V2优化: 原子性标记初始化完成
                lock (_tabDataLock)
                {
                    if (TabDataDictionary.ContainsKey(tabSectionId))
                    {
                        tabData.IsWebViewInitialized = true;
                    }
                }

                // V2优化: 安全的事件注册
                RegisterWebViewEventsSafely(webView, config);

                // V2优化: 异步配置WebView2设置
                await ConfigureWebView2SafelyAsync(webView, config).ConfigureAwait(false);

                // V2优化: 安全的初始导航
                await NavigateToInitialUrlSafelyAsync(webView, config, cancellationToken).ConfigureAwait(false);

                ETLogManager.Info(_logSource, $"WebView2初始化完成: {config.Name}");
            }
            catch (OperationCanceledException)
            {
                ETLogManager.Info(_logSource, $"WebView2初始化被取消: {config.Name}");
                ResetInitializationState(tabData);
                throw;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"WebView2内部初始化失败: {config.Name}, 错误: {ex.Message}");

                // V2优化: 安全的状态重置
                ResetInitializationState(tabData);

                // 重新抛出异常以便上层处理
                throw;
            }
        }

        /// <summary>
        /// 注册WebView事件处理器
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        private void RegisterWebViewEvents(WebView2 webView, WebBrowserTabConfig config)
        {
            try
            {
                // 导航开始事件
                webView.CoreWebView2.NavigationStarting += WebView_NavigationStarting;

                // 导航完成事件
                webView.CoreWebView2.NavigationCompleted += WebView_NavigationCompleted;

                // 文档标题变更事件
                webView.CoreWebView2.DocumentTitleChanged += WebView_DocumentTitleChanged;

                ETLogManager.Info(_logSource, $"WebView事件处理器注册完成: {config.Name}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"注册WebView事件处理器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安全地配置WebView2设置（异步版本）
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        /// <returns>配置任务</returns>
        private Task ConfigureWebView2SafelyAsync(WebView2 webView, WebBrowserTabConfig config)
        {
            try
            {
                if (webView?.CoreWebView2 == null)
                {
                    ETLogManager.Error(_logSource, "配置WebView2失败: CoreWebView2为空");
                    return Task.CompletedTask;
                }

                // 设置用户代理
                if (!string.IsNullOrEmpty(config.UserAgent))
                {
                    webView.CoreWebView2.Settings.UserAgent = config.UserAgent;
                    ETLogManager.Info(_logSource, $"已设置用户代理: {config.UserAgent}");
                }

                // 配置标头和Cookie管理
                if (!string.IsNullOrEmpty(config.CookiePath))
                {
                    // 设置标头路径（兼容使用CookiePath）
                    _cookieManager.HeadersPath = config.CookiePath;

                    // 导入Cookie（如果文件存在）
                    if (File.Exists(config.CookiePath))
                    {
                        // 异步导入Cookie，避免阻塞
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await _cookieManager.ImportCookiesAsync(webView, config.CookiePath).ConfigureAwait(false);
                                ETLogManager.Info(_logSource, $"Cookie导入完成: {config.CookiePath}");
                            }
                            catch (Exception ex)
                            {
                                ETLogManager.Warning(_logSource, $"Cookie导入失败: {ex.Message}");
                            }
                        });
                    }

                    // 设置Cookie自动导出
                    _cookieManager.SetupAutoCookieExport(webView, config.CookiePath);

                    ETLogManager.Info(_logSource, $"已配置标头和Cookie管理，路径: {config.CookiePath}");
                }

                // 异步设置标头捕获，避免阻塞
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _cookieManager.SetupHeadersCaptureAsync(webView).ConfigureAwait(false);
                        ETLogManager.Info(_logSource, "已设置WebView2标头捕获");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Warning(_logSource, $"设置标头捕获失败: {ex.Message}");
                    }
                });

                // 配置其他设置
                webView.CoreWebView2.Settings.IsStatusBarEnabled = true;
                webView.CoreWebView2.Settings.AreDefaultContextMenusEnabled = true;
                webView.CoreWebView2.Settings.IsBuiltInErrorPageEnabled = true;
                webView.CoreWebView2.Settings.AreDevToolsEnabled = true;

                // 禁用新窗口打开
                webView.CoreWebView2.NewWindowRequested += (s, e) =>
                {
                    e.Handled = true;
                    webView.CoreWebView2.Navigate(e.Uri);
                };

                ETLogManager.Info(_logSource, "WebView2配置已应用");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"配置WebView2失败: {ex.Message}");
                return Task.CompletedTask;
            }
        }

        /// <summary>
        /// WebView导航开始事件处理（线程安全版本）
        /// </summary>
        public void WebView_NavigationStarting(object sender, CoreWebView2NavigationStartingEventArgs e)
        {
            try
            {
                if (sender is WebView2 webView)
                {
                    // 线程安全地查找对应的标签页数据
                    TabDataV2 foundTabData = null;
                    string foundSectionId = null;

                    lock (_tabDataLock)
                    {
                        foreach (KeyValuePair<string, TabDataV2> pair in TabDataDictionary)
                        {
                            if (pair.Value.WebView == webView)
                            {
                                foundTabData = pair.Value;
                                foundSectionId = pair.Key;
                                break;
                            }
                        }
                    }

                    if (foundTabData != null)
                    {
                        string url = e.Uri;
                        string tabName = foundTabData.Config?.Name ?? "未命名";
                        bool isClone = foundTabData.IsClone;

                        // 更新LastUrl并触发URL变更事件，以便立即更新地址栏
                        foundTabData.LastUrl = url;
                        TabUrlChanged?.Invoke(tabName, url);

                        ETLogManager.Info(_logSource, $"开始导航: {foundSectionId} {(isClone ? "(副本)" : string.Empty)} -> {url}");
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"处理导航开始事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// WebView导航完成事件处理（线程安全版本）
        /// </summary>
        public void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            try
            {
                if (sender is WebView2 webView)
                {
                    // 线程安全地查找对应的标签页数据
                    TabDataV2 foundTabData = null;
                    string foundSectionId = null;

                    lock (_tabDataLock)
                    {
                        foreach (KeyValuePair<string, TabDataV2> pair in TabDataDictionary)
                        {
                            if (pair.Value.WebView == webView)
                            {
                                foundTabData = pair.Value;
                                foundSectionId = pair.Key;
                                break;
                            }
                        }
                    }

                    if (foundTabData != null)
                    {
                        bool success = e.IsSuccess;
                        string status = success ? "成功" : $"失败（错误码：{e.WebErrorStatus}）";
                        string tabName = foundTabData.Config?.Name ?? "未命名";
                        bool isClone = foundTabData.IsClone;

                        ETLogManager.Info(_logSource, $"导航完成: {foundSectionId} {(isClone ? "(副本)" : string.Empty)} - {status}");

                        if (success)
                        {
                            // 异步启动会话保持，避免阻塞UI
                            _ = Task.Run(async () =>
                            {
                                try
                                {
                                    await _sessionManager.StartSessionAsync(foundSectionId, foundTabData.Config).ConfigureAwait(false);
                                }
                                catch (Exception ex)
                                {
                                    ETLogManager.Warning(_logSource, $"启动会话保持失败: {ex.Message}");
                                }
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"处理导航完成事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// WebView文档标题变更事件处理
        /// </summary>
        public void WebView_DocumentTitleChanged(object sender, object e)
        {
            try
            {
                if (sender is WebView2 webView)
                {
                    // 线程安全地查找对应的标签页数据
                    TabDataV2 foundTabData = null;

                    lock (_tabDataLock)
                    {
                        foreach (KeyValuePair<string, TabDataV2> pair in TabDataDictionary)
                        {
                            if (pair.Value.WebView == webView)
                            {
                                foundTabData = pair.Value;
                                break;
                            }
                        }
                    }

                    if (foundTabData != null)
                    {
                        string title = webView.CoreWebView2.DocumentTitle;
                        string tabName = foundTabData.Config?.Name ?? "未命名";

                        // 触发标题变更事件
                        TabTitleChanged?.Invoke(tabName, title);

                        ETLogManager.Debug(_logSource, $"标题变更: {tabName} -> {title}");
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"处理标题变更事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 标签页切换事件处理
        /// </summary>
        private void TabControl_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (_tabControl.SelectedTab != null)
                {
                    string sectionId = _tabControl.SelectedTab.Name;

                    if (!string.IsNullOrEmpty(sectionId))
                    {
                        CurrentTabSectionId = sectionId;
                        TabSwitched?.Invoke(sectionId);

                        ETLogManager.Debug(_logSource, $"标签页切换到: {sectionId}");
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"处理标签页切换事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据SectionId获取WebView2控件（线程安全）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>WebView2控件，如果不存在则返回null</returns>
        public WebView2 GetWebViewBySectionId(string sectionId)
        {
            if (string.IsNullOrEmpty(sectionId))
                return null;

            lock (_tabDataLock)
            {
                if (TabDataDictionary.TryGetValue(sectionId, out TabDataV2 tabData))
                {
                    return tabData.WebView;
                }
            }

            return null;
        }

        /// <summary>
        /// 根据标签页名称获取WebView2控件（兼容旧代码，线程安全）
        /// </summary>
        /// <param name="tabName">标签页名称</param>
        /// <returns>WebView2控件，如果不存在则返回null</returns>
        public WebView2 GetWebViewByTabName(string tabName)
        {
            if (string.IsNullOrEmpty(tabName))
                return null;

            lock (_tabDataLock)
            {
                if (TabNameToSectionId.TryGetValue(tabName, out string sectionId))
                {
                    return GetWebViewBySectionId(sectionId);
                }
            }

            return null;
        }

        /// <summary>
        /// 关闭标签页（异步安全版本）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>关闭任务</returns>
        public async Task<bool> CloseTabAsync(string sectionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId))
                {
                    ETLogManager.Warning(_logSource, "关闭标签页失败: SectionId为空");
                    return false;
                }

                TabDataV2 tabData;
                lock (_tabDataLock)
                {
                    if (!TabDataDictionary.TryGetValue(sectionId, out tabData))
                    {
                        ETLogManager.Warning(_logSource, $"关闭标签页失败: 找不到标签页数据: {sectionId}");
                        return false;
                    }
                }

                ETLogManager.Info(_logSource, $"开始关闭标签页: {tabData.Config.Name}");

                // 停止会话保持
                await _sessionManager.StopSessionAsync(sectionId).ConfigureAwait(false);

                // 在UI线程上移除UI组件
                if (_tabControl.InvokeRequired)
                {
                    await Task.Run(() =>
                    {
                        _tabControl.Invoke(new Action(() =>
                        {
                            RemoveUIComponents(tabData);
                        }));
                    }).ConfigureAwait(false);
                }
                else
                {
                    RemoveUIComponents(tabData);
                }

                // 清理资源
                await CleanupTabResourcesAsync(tabData).ConfigureAwait(false);

                // 从字典中移除
                lock (_tabDataLock)
                {
                    TabDataDictionary.Remove(sectionId);
                    TabNameToSectionId.Remove(tabData.Config.Name);
                }

                ETLogManager.Info(_logSource, $"标签页关闭完成: {tabData.Config.Name}");
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"关闭标签页异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 移除UI组件（必须在UI线程上调用）
        /// </summary>
        /// <param name="tabData">标签页数据</param>
        private void RemoveUIComponents(TabDataV2 tabData)
        {
            try
            {
                if (tabData.TabPage != null)
                {
                    _tabControl.TabPages.Remove(tabData.TabPage);
                    tabData.TabPage.Dispose();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(_logSource, $"移除UI组件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理标签页资源
        /// </summary>
        /// <param name="tabData">标签页数据</param>
        /// <returns>清理任务</returns>
        private Task CleanupTabResourcesAsync(TabDataV2 tabData)
        {
            try
            {
                // 清理WebView2资源
                if (tabData.WebView != null)
                {
                    if (tabData.WebView.CoreWebView2 != null)
                    {
                        // 解除事件绑定
                        tabData.WebView.CoreWebView2.NavigationStarting -= WebView_NavigationStarting;
                        tabData.WebView.CoreWebView2.NavigationCompleted -= WebView_NavigationCompleted;
                        tabData.WebView.CoreWebView2.DocumentTitleChanged -= WebView_DocumentTitleChanged;

                        // 解除WebResourceRequested事件绑定
                        if (tabData.WebResourceRequestedHandler != null)
                        {
                            tabData.WebView.CoreWebView2.WebResourceRequested -= tabData.WebResourceRequestedHandler;
                        }
                    }

                    tabData.WebView.Dispose();
                }

                // 清理环境资源
                if (tabData.Environment != null)
                {
                    // CoreWebView2Environment没有提供显式的Dispose方法，将引用设为null帮助GC回收
                    tabData.Environment = null;
                }

                ETLogManager.Debug(_logSource, "标签页资源清理完成");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(_logSource, $"清理标签页资源失败: {ex.Message}");
                return Task.CompletedTask;
            }
        }

        #endregion 标签页管理方法

        #region 标签页操作方法（兼容性方法）

        /// <summary>
        /// 获取当前标签页配置
        /// </summary>
        /// <returns>标签页配置，未找到返回null</returns>
        public WebBrowserTabConfig GetCurrentTabConfig()
        {
            try
            {
                string currentSectionId = _tabControl.SelectedTab?.Name;
                if (string.IsNullOrEmpty(currentSectionId))
                    return null;

                lock (_tabDataLock)
                {
                    if (TabDataDictionary.TryGetValue(currentSectionId, out TabDataV2 tabData))
                        return tabData.Config;
                }

                return null;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"获取当前标签页配置失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取当前WebView2控件
        /// </summary>
        /// <returns>WebView2控件，未找到返回null</returns>
        public WebView2 GetCurrentWebView()
        {
            try
            {
                string currentSectionId = _tabControl.SelectedTab?.Name;
                if (string.IsNullOrEmpty(currentSectionId))
                    return null;

                lock (_tabDataLock)
                {
                    if (TabDataDictionary.TryGetValue(currentSectionId, out TabDataV2 tabData))
                        return tabData.WebView;
                }

                return null;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"获取当前WebView控件失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取当前标签页数据
        /// </summary>
        /// <returns>标签页数据，未找到返回null</returns>
        public TabDataV2 GetCurrentTabData()
        {
            try
            {
                string currentSectionId = _tabControl.SelectedTab?.Name;
                if (string.IsNullOrEmpty(currentSectionId))
                    return null;

                lock (_tabDataLock)
                {
                    if (TabDataDictionary.TryGetValue(currentSectionId, out TabDataV2 tabData))
                        return tabData;
                }

                return null;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"获取当前标签页数据失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 根据SectionId获取标签页数据（线程安全）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>标签页数据，如果不存在则返回null</returns>
        public TabDataV2 GetTabDataBySectionId(string sectionId)
        {
            if (string.IsNullOrEmpty(sectionId))
                return null;

            lock (_tabDataLock)
            {
                if (TabDataDictionary.TryGetValue(sectionId, out TabDataV2 tabData))
                {
                    return tabData;
                }
            }

            return null;
        }

        /// <summary>
        /// 切换到指定标签页
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>是否成功切换</returns>
        public bool SwitchToTab(string sectionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId))
                    return false;

                lock (_tabDataLock)
                {
                    if (TabDataDictionary.TryGetValue(sectionId, out TabDataV2 tabData))
                    {
                        if (_tabControl.InvokeRequired)
                        {
                            _tabControl.Invoke(new Action(() =>
                            {
                                _tabControl.SelectedTab = tabData.TabPage;
                            }));
                        }
                        else
                        {
                            _tabControl.SelectedTab = tabData.TabPage;
                        }

                        ETLogManager.Info(_logSource, $"切换到标签页: {tabData.Config.Name}");
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"切换标签页失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 关闭当前标签页
        /// </summary>
        /// <returns>是否成功关闭</returns>
        public async Task<bool> CloseCurrentTab()
        {
            try
            {
                string currentSectionId = _tabControl.SelectedTab?.Name;
                if (string.IsNullOrEmpty(currentSectionId))
                    return false;

                return await CloseTabAsync(currentSectionId).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"关闭当前标签页失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 更新标签页配置
        /// </summary>
        /// <param name="config">标签页配置</param>
        /// <returns>是否成功更新</returns>
        public bool UpdateTabConfig(WebBrowserTabConfig config)
        {
            try
            {
                if (config == null || string.IsNullOrEmpty(config.SectionId))
                    return false;

                lock (_tabDataLock)
                {
                    if (TabDataDictionary.TryGetValue(config.SectionId, out TabDataV2 tabData))
                    {
                        tabData.Config = config;
                        ETLogManager.Info(_logSource, $"更新标签页配置: {config.Name}");
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"更新标签页配置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建副本标签页
        /// </summary>
        /// <param name="sourceConfig">源标签页配置</param>
        /// <returns>新标签页的SectionId</returns>
        public async Task<string> CreateCloneTab(WebBrowserTabConfig sourceConfig)
        {
            try
            {
                if (sourceConfig == null)
                    return null;

                // 创建副本配置
                var cloneConfig = new WebBrowserTabConfig
                {
                    SectionId = Guid.NewGuid().ToString("N"),
                    Name = $"{sourceConfig.Name}_副本",
                    Url = sourceConfig.Url,
                    CookiePath = sourceConfig.CookiePath,
                    ProxyServer = sourceConfig.ProxyServer,
                    ProxyPort = sourceConfig.ProxyPort,
                    UserAgent = sourceConfig.UserAgent,
                    EnableWebView2Refresh = sourceConfig.EnableWebView2Refresh,
                    WebView2RefreshInterval = sourceConfig.WebView2RefreshInterval
                };

                // 创建新标签页
                string newSectionId = await CreateNewTabAsync(cloneConfig).ConfigureAwait(false);

                if (!string.IsNullOrEmpty(newSectionId))
                {
                    // 标记为副本
                    lock (_tabDataLock)
                    {
                        if (TabDataDictionary.TryGetValue(newSectionId, out TabDataV2 tabData))
                        {
                            tabData.IsClone = true;
                        }
                    }

                    ETLogManager.Info(_logSource, $"创建副本标签页成功: {cloneConfig.Name}");
                }

                return newSectionId;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"创建副本标签页失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 退出当前标签页登录
        /// </summary>
        /// <returns>是否成功退出</returns>
        public async Task<bool> LogoutCurrentTab()
        {
            try
            {
                var currentTabData = GetCurrentTabData();
                if (currentTabData?.WebView == null)
                    return false;

                // 清除Cookie和会话数据
                if (currentTabData.WebView.CoreWebView2 != null)
                {
                    currentTabData.WebView.CoreWebView2.CookieManager.DeleteAllCookies();
                    await currentTabData.WebView.CoreWebView2.ClearServerCertificateErrorActionsAsync();

                    // 导航到登录页面或首页
                    currentTabData.WebView.CoreWebView2.Navigate(currentTabData.Config.Url);

                    ETLogManager.Info(_logSource, $"标签页退出登录成功: {currentTabData.Config.Name}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"标签页退出登录失败: {ex.Message}");
                return false;
            }
        }

        #endregion 标签页操作方法（兼容性方法）

        #region Cookie数据传递方法（V2版本关键优化）

        /// <summary>
        /// 安全地打开Cookie管理窗体并传递数据（V2版本关键优化）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>是否成功打开</returns>
        public async Task<bool> OpenCookieManagerWithSafeTransferAsync(string sectionId)
        {
            if (_disposed)
            {
                ETLogManager.Warning(_logSource, "WebBrowserTabManagerV2已释放，无法打开Cookie管理窗体");
                return false;
            }

            try
            {
                ETLogManager.Info(_logSource, $"V2优化：开始安全打开Cookie管理窗体: {sectionId}");

                // 获取标签页数据
                var tabData = GetTabDataBySectionId(sectionId);
                if (tabData?.WebView == null || tabData.Config == null)
                {
                    ETLogManager.Warning(_logSource, $"无法找到标签页数据: {sectionId}");
                    return false;
                }

                // 更新Cookie管理器配置
                _cookieManager.HeadersPath = tabData.Config.CookiePath;
                _cookieManager.CurrentUrl = tabData.Config.Url;

                // 创建Cookie管理窗体
                using (var cookieManagerForm = new CookieManagerFormV2(_cookieManager, tabData.WebView, _logSource))
                {
                    // V2版本关键优化：使用CookieTransferManagerV2安全传递数据
                    bool transferResult = await _cookieTransferManager.TransferCookieDataSafelyAsync(
                        tabData.WebView,
                        cookieManagerForm,
                        tabData.Config);

                    if (transferResult)
                    {
                        ETLogManager.Info(_logSource, $"V2优化：Cookie数据传递成功，显示管理窗体");
                    }
                    else
                    {
                        ETLogManager.Warning(_logSource, $"V2优化：Cookie数据传递失败，仍显示管理窗体");
                    }

                    // 显示窗体
                    cookieManagerForm.ShowDialog();

                    // 检查路径是否有更新
                    if (!string.IsNullOrEmpty(_cookieManager.HeadersPath) &&
                        _cookieManager.HeadersPath != tabData.Config.CookiePath)
                    {
                        tabData.Config.CookiePath = _cookieManager.HeadersPath;
                        _configManager.SaveTabConfig(tabData.Config);
                        ETLogManager.Info(_logSource, $"V2优化：Cookie路径已更新: {_cookieManager.HeadersPath}");
                    }
                }

                ETLogManager.Info(_logSource, $"V2优化：Cookie管理窗体关闭完成");
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"V2优化：打开Cookie管理窗体失败: {ex.Message}");
                return false;
            }
        }

        #endregion Cookie数据传递方法（V2版本关键优化）

        #region IDisposable实现

        private bool _disposed = false;

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        // 取消所有异步操作
                        _cancellationTokenSource?.Cancel();

                        // 解除事件绑定
                        if (_tabControl != null)
                        {
                            _tabControl.SelectedIndexChanged -= TabControl_SelectedIndexChanged;
                        }

                        // 清理所有标签页资源
                        var allSectionIds = new List<string>();
                        lock (_tabDataLock)
                        {
                            allSectionIds.AddRange(TabDataDictionary.Keys);
                        }

                        foreach (string sectionId in allSectionIds)
                        {
                            _ = CloseTabAsync(sectionId);
                        }

                        _cancellationTokenSource?.Dispose();
                        _asyncExecutor?.Dispose();
                        _webView2Operator?.Dispose();

                        // V2版本关键优化：释放Cookie传递管理器
                        _cookieTransferManager?.Dispose();

                        ETLogManager.Info(_logSource, "WebBrowserTabManagerV2资源释放完成，包含Cookie传递管理器");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"释放WebBrowserTabManagerV2资源失败: {ex.Message}");
                    }
                }

                _disposed = true;
            }
        }

        #endregion IDisposable实现

        #region V2新增辅助方法

        /// <summary>
        /// 安全创建WebView2环境（V2新增）
        /// </summary>
        /// <param name="config">标签页配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>WebView2环境</returns>
        private async Task<CoreWebView2Environment> CreateWebView2EnvironmentSafelyAsync(WebBrowserTabConfig config, CancellationToken cancellationToken)
        {
            try
            {
                // 创建用户数据目录
                string userDataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "WebView2Data", config.SectionId);

                // 确保目录存在
                if (!Directory.Exists(userDataFolder))
                {
                    Directory.CreateDirectory(userDataFolder);
                    ETLogManager.Info(_logSource, $"创建WebView2用户数据目录: {userDataFolder}");
                }

                // 检查取消令牌
                cancellationToken.ThrowIfCancellationRequested();

                // 创建环境
                var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder).ConfigureAwait(false);
                ETLogManager.Info(_logSource, $"WebView2环境创建成功: {config.Name}");

                return environment;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"创建WebView2环境失败: {config.Name}, 错误: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 安全初始化WebView2核心（V2新增）
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="environment">WebView2环境</param>
        /// <param name="config">标签页配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>初始化任务</returns>
        private async Task InitializeWebView2CoreSafelyAsync(WebView2 webView, CoreWebView2Environment environment, WebBrowserTabConfig config, CancellationToken cancellationToken)
        {
            try
            {
                // 检查取消令牌
                cancellationToken.ThrowIfCancellationRequested();

                // 确保在UI线程上执行
                if (webView.InvokeRequired)
                {
                    throw new InvalidOperationException("WebView2核心初始化必须在UI线程上执行");
                }

                // 初始化WebView2核心
                await webView.EnsureCoreWebView2Async(environment).ConfigureAwait(false);

                // 验证初始化结果
                if (webView.CoreWebView2 == null)
                {
                    throw new InvalidOperationException($"WebView2核心初始化失败: CoreWebView2为空 - {config.Name}");
                }

                ETLogManager.Info(_logSource, $"WebView2核心初始化成功: {config.Name}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"WebView2核心初始化失败: {config.Name}, 错误: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 安全注册WebView事件（V2新增）
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        private void RegisterWebViewEventsSafely(WebView2 webView, WebBrowserTabConfig config)
        {
            try
            {
                if (webView?.CoreWebView2 == null)
                {
                    ETLogManager.Warning(_logSource, $"跳过事件注册: WebView2未初始化 - {config.Name}");
                    return;
                }

                // 调用原有的事件注册方法
                RegisterWebViewEvents(webView, config);

                ETLogManager.Info(_logSource, $"WebView2事件注册成功: {config.Name}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"WebView2事件注册失败: {config.Name}, 错误: {ex.Message}");
                // 事件注册失败不应该阻止整个初始化过程
            }
        }

        /// <summary>
        /// 安全导航到初始URL（V2新增）
        /// </summary>
        /// <param name="webView">WebView2控件</param>
        /// <param name="config">标签页配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>导航任务</returns>
        private async Task NavigateToInitialUrlSafelyAsync(WebView2 webView, WebBrowserTabConfig config, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(config.Url) || config.Url == ABOUT_BLANK)
                {
                    ETLogManager.Info(_logSource, $"跳过初始导航: URL为空或为about:blank - {config.Name}");
                    return;
                }

                // 检查取消令牌
                cancellationToken.ThrowIfCancellationRequested();

                // 验证WebView2状态
                if (webView?.CoreWebView2 == null)
                {
                    ETLogManager.Warning(_logSource, $"跳过初始导航: WebView2未初始化 - {config.Name}");
                    return;
                }

                // 异步导航
                await Task.Run(() =>
                {
                    if (webView.InvokeRequired)
                    {
                        webView.Invoke(new Action(() =>
                        {
                            webView.CoreWebView2.Navigate(config.Url);
                        }));
                    }
                    else
                    {
                        webView.CoreWebView2.Navigate(config.Url);
                    }
                }, cancellationToken).ConfigureAwait(false);

                ETLogManager.Info(_logSource, $"导航到初始URL: {config.Url} - {config.Name}");
            }
            catch (OperationCanceledException)
            {
                ETLogManager.Info(_logSource, $"初始导航被取消: {config.Name}");
                throw;
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(_logSource, $"初始导航失败: {config.Name}, URL: {config.Url}, 错误: {ex.Message}");
                // 导航失败不应该阻止整个初始化过程
            }
        }

        /// <summary>
        /// 重置初始化状态（V2新增）
        /// </summary>
        /// <param name="tabData">标签页数据</param>
        private void ResetInitializationState(TabDataV2 tabData)
        {
            try
            {
                lock (_tabDataLock)
                {
                    tabData.IsWebViewInitialized = false;

                    // 清理环境资源
                    if (tabData.Environment != null)
                    {
                        try
                        {
                            // CoreWebView2Environment没有提供显式的Dispose方法，将引用设为null帮助GC回收
                            tabData.Environment = null;
                            ETLogManager.Debug(_logSource, "WebView2环境引用已清理");
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Warning(_logSource, $"清理WebView2环境失败: {ex.Message}");
                        }
                    }
                }

                ETLogManager.Info(_logSource, $"已重置WebView2初始化状态: {tabData.Config.Name}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"重置初始化状态失败: {tabData.Config.Name}, 错误: {ex.Message}");
            }
        }

        #endregion V2新增辅助方法

        #region WebView2线程安全操作方法（V2新增）

        /// <summary>
        /// 线程安全地导航到指定URL（V2新增）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <param name="url">目标URL</param>
        /// <returns>导航是否成功</returns>
        public bool NavigateToUrlSafely(string sectionId, string url)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId) || string.IsNullOrEmpty(url))
                {
                    ETLogManager.Warning(_logSource, "导航失败: SectionId或URL为空");
                    return false;
                }

                TabDataV2 tabData;
                lock (_tabDataLock)
                {
                    if (!TabDataDictionary.TryGetValue(sectionId, out tabData))
                    {
                        ETLogManager.Warning(_logSource, $"导航失败: 找不到标签页数据: {sectionId}");
                        return false;
                    }
                }

                // V2版本优化: 使用WebView2ThreadSafeOperatorV2的专门导航方法
                return _webView2Operator.NavigateToUrlSafely(tabData.WebView, url);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"导航到URL失败: {url}, 错误: {ex.Message}");
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, $"导航到URL[{url}]");
                return false;
            }
        }

        /// <summary>
        /// 线程安全地获取WebView2当前URL（V2新增）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>当前URL，失败返回空字符串</returns>
        public string GetCurrentUrlSafely(string sectionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId))
                {
                    return string.Empty;
                }

                TabDataV2 tabData;
                lock (_tabDataLock)
                {
                    if (!TabDataDictionary.TryGetValue(sectionId, out tabData))
                    {
                        return string.Empty;
                    }
                }

                // V2版本优化: 使用WebView2ThreadSafeOperatorV2的专门URL获取方法
                return _webView2Operator.GetCurrentUrlSafely(tabData.WebView);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"获取当前URL失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 线程安全地获取WebView2当前标题（V2新增）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>当前标题，失败返回空字符串</returns>
        public string GetCurrentTitleSafely(string sectionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId))
                {
                    return string.Empty;
                }

                TabDataV2 tabData;
                lock (_tabDataLock)
                {
                    if (!TabDataDictionary.TryGetValue(sectionId, out tabData))
                    {
                        return string.Empty;
                    }
                }

                // V2版本优化: 使用WebView2ThreadSafeOperatorV2的专门标题获取方法
                return _webView2Operator.GetCurrentTitleSafely(tabData.WebView);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"获取当前标题失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 线程安全地执行JavaScript代码（V2新增）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <param name="script">JavaScript代码</param>
        /// <returns>执行是否成功</returns>
        public async Task<bool> ExecuteScriptSafelyAsync(string sectionId, string script)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId) || string.IsNullOrEmpty(script))
                {
                    ETLogManager.Warning(_logSource, "执行脚本失败: SectionId或脚本为空");
                    return false;
                }

                TabDataV2 tabData;
                lock (_tabDataLock)
                {
                    if (!TabDataDictionary.TryGetValue(sectionId, out tabData))
                    {
                        ETLogManager.Warning(_logSource, $"执行脚本失败: 找不到标签页数据: {sectionId}");
                        return false;
                    }
                }

                return await _webView2Operator.ExecuteSafelyAsync(
                    tabData.WebView,
                    async (webView, token) =>
                    {
                        await webView.CoreWebView2.ExecuteScriptAsync(script);
                    },
                    $"执行JavaScript脚本",
                    30000,
                    true).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"执行JavaScript脚本失败: {ex.Message}");
                WebBrowserExceptionHandlerV2.HandleAsyncException(_logSource, ex, "执行JavaScript脚本");
                return false;
            }
        }

        /// <summary>
        /// 线程安全地刷新WebView2页面（V2新增）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>刷新是否成功</returns>
        public bool RefreshPageSafely(string sectionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId))
                {
                    ETLogManager.Warning(_logSource, "刷新页面失败: SectionId为空");
                    return false;
                }

                TabDataV2 tabData;
                lock (_tabDataLock)
                {
                    if (!TabDataDictionary.TryGetValue(sectionId, out tabData))
                    {
                        ETLogManager.Warning(_logSource, $"刷新页面失败: 找不到标签页数据: {sectionId}");
                        return false;
                    }
                }

                // V2版本优化: 使用WebView2ThreadSafeOperatorV2的专门刷新方法
                return _webView2Operator.RefreshPageSafely(tabData.WebView);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"刷新页面失败: {ex.Message}");
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, "刷新页面");
                return false;
            }
        }

        /// <summary>
        /// 线程安全地停止页面加载（V2新增）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>停止是否成功</returns>
        public bool StopLoadingSafely(string sectionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId))
                {
                    ETLogManager.Warning(_logSource, "停止页面加载失败: SectionId为空");
                    return false;
                }

                TabDataV2 tabData;
                lock (_tabDataLock)
                {
                    if (!TabDataDictionary.TryGetValue(sectionId, out tabData))
                    {
                        ETLogManager.Warning(_logSource, $"停止页面加载失败: 找不到标签页数据: {sectionId}");
                        return false;
                    }
                }

                // V2版本优化: 使用WebView2ThreadSafeOperatorV2的专门停止方法
                return _webView2Operator.StopLoadingSafely(tabData.WebView);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"停止页面加载失败: {ex.Message}");
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, "停止页面加载");
                return false;
            }
        }

        /// <summary>
        /// 线程安全地检查是否可以后退（V2新增）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>是否可以后退</returns>
        public bool CanGoBackSafely(string sectionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId))
                {
                    return false;
                }

                TabDataV2 tabData;
                lock (_tabDataLock)
                {
                    if (!TabDataDictionary.TryGetValue(sectionId, out tabData))
                    {
                        return false;
                    }
                }

                // V2版本优化: 使用WebView2ThreadSafeOperatorV2的专门检查方法
                return _webView2Operator.CanGoBackSafely(tabData.WebView);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"检查是否可以后退失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 线程安全地检查是否可以前进（V2新增）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>是否可以前进</returns>
        public bool CanGoForwardSafely(string sectionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId))
                {
                    return false;
                }

                TabDataV2 tabData;
                lock (_tabDataLock)
                {
                    if (!TabDataDictionary.TryGetValue(sectionId, out tabData))
                    {
                        return false;
                    }
                }

                // V2版本优化: 使用WebView2ThreadSafeOperatorV2的专门检查方法
                return _webView2Operator.CanGoForwardSafely(tabData.WebView);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"检查是否可以前进失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 线程安全地后退（V2新增）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>后退是否成功</returns>
        public bool GoBackSafely(string sectionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId))
                {
                    ETLogManager.Warning(_logSource, "页面后退失败: SectionId为空");
                    return false;
                }

                TabDataV2 tabData;
                lock (_tabDataLock)
                {
                    if (!TabDataDictionary.TryGetValue(sectionId, out tabData))
                    {
                        ETLogManager.Warning(_logSource, $"页面后退失败: 找不到标签页数据: {sectionId}");
                        return false;
                    }
                }

                // V2版本优化: 使用WebView2ThreadSafeOperatorV2的专门后退方法
                return _webView2Operator.GoBackSafely(tabData.WebView);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"页面后退失败: {ex.Message}");
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, "页面后退");
                return false;
            }
        }

        /// <summary>
        /// 线程安全地前进（V2新增）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>前进是否成功</returns>
        public bool GoForwardSafely(string sectionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId))
                {
                    ETLogManager.Warning(_logSource, "页面前进失败: SectionId为空");
                    return false;
                }

                TabDataV2 tabData;
                lock (_tabDataLock)
                {
                    if (!TabDataDictionary.TryGetValue(sectionId, out tabData))
                    {
                        ETLogManager.Warning(_logSource, $"页面前进失败: 找不到标签页数据: {sectionId}");
                        return false;
                    }
                }

                // V2版本优化: 使用WebView2ThreadSafeOperatorV2的专门前进方法
                return _webView2Operator.GoForwardSafely(tabData.WebView);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"页面前进失败: {ex.Message}");
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, "页面前进");
                return false;
            }
        }

        /// <summary>
        /// 线程安全地获取Cookie数据（V2新增）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <param name="url">目标URL，为空时使用当前URL</param>
        /// <returns>Cookie列表</returns>
        public async Task<System.Collections.Generic.IList<CoreWebView2Cookie>> GetCookiesSafelyAsync(string sectionId, string url = null)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId))
                {
                    ETLogManager.Warning(_logSource, "获取Cookie失败: SectionId为空");
                    return new System.Collections.Generic.List<CoreWebView2Cookie>();
                }

                TabDataV2 tabData;
                lock (_tabDataLock)
                {
                    if (!TabDataDictionary.TryGetValue(sectionId, out tabData))
                    {
                        ETLogManager.Warning(_logSource, $"获取Cookie失败: 找不到标签页数据: {sectionId}");
                        return new System.Collections.Generic.List<CoreWebView2Cookie>();
                    }
                }

                // V2版本优化: 使用WebView2ThreadSafeOperatorV2的专门Cookie获取方法
                return await _webView2Operator.GetCookiesSafelyAsync(tabData.WebView, url).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"获取Cookie失败: {ex.Message}");
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, "获取Cookie");
                return new System.Collections.Generic.List<CoreWebView2Cookie>();
            }
        }

        /// <summary>
        /// 线程安全地设置Cookie（V2新增）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <param name="cookie">要设置的Cookie</param>
        /// <returns>设置是否成功</returns>
        public async Task<bool> SetCookieSafelyAsync(string sectionId, CoreWebView2Cookie cookie)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId))
                {
                    ETLogManager.Warning(_logSource, "设置Cookie失败: SectionId为空");
                    return false;
                }

                if (cookie == null)
                {
                    ETLogManager.Warning(_logSource, "设置Cookie失败: Cookie为空");
                    return false;
                }

                TabDataV2 tabData;
                lock (_tabDataLock)
                {
                    if (!TabDataDictionary.TryGetValue(sectionId, out tabData))
                    {
                        ETLogManager.Warning(_logSource, $"设置Cookie失败: 找不到标签页数据: {sectionId}");
                        return false;
                    }
                }

                // V2版本优化: 使用WebView2ThreadSafeOperatorV2的专门Cookie设置方法
                return await _webView2Operator.SetCookieSafelyAsync(tabData.WebView, cookie).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"设置Cookie失败: {ex.Message}");
                WebBrowserExceptionHandlerV2.HandleException(_logSource, ex, "设置Cookie");
                return false;
            }
        }

        #endregion WebView2线程安全操作方法（V2新增）
    }
}