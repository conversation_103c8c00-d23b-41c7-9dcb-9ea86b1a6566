using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;

namespace ET
{
    /// <summary>
    /// 用于标记INI文件中的属性
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public class IniPropertyAttribute : Attribute
    {
        /// <summary>
        /// 键名
        /// </summary>
        public string Key { get; }

        /// <summary>
        /// 初始化INI属性特性
        /// </summary>
        /// <param name="key">键名</param>
        public IniPropertyAttribute(string key)
        {
            Key = key;
        }
    }

    /// <summary>
    /// INI文件读写操作类，提供完整的INI配置文件管理功能
    /// </summary>
    /// <remarks>
    /// 主要功能包括：
    /// 1. 基本的INI文件读写操作（字符串、整数、布尔值、日期时间）
    /// 2. 节和键的管理（获取、删除）
    /// 3. 对象序列化/反序列化到INI文件
    /// 4. 使用Windows API确保高性能和兼容性
    /// 5. 自动文件创建和目录管理
    /// 6. 完善的错误处理和日志记录
    /// 7. 向后兼容的方法支持
    ///
    /// 使用示例：
    /// <code>
    /// ETIniFile ini = new ETIniFile("config.ini");
    /// ini.IniWriteValue("Settings", "UserName", "张三");
    /// string userName = ini.IniReadValue("Settings", "UserName");
    /// </code>
    /// </remarks>
    public class ETIniFile
    {
        /// <summary>
        /// INI文件的完整路径
        /// </summary>
        readonly string _filePath;

        /// <summary>
        /// 文件编码格式，默认使用Unicode编码
        /// </summary>
        readonly Encoding _encoding;

        [DllImport("kernel32", CharSet = CharSet.Unicode)]
        static extern int GetPrivateProfileString(string section, string key, string def, StringBuilder retVal, int size, string filePath);

        [DllImport("kernel32", CharSet = CharSet.Unicode)]
        static extern long WritePrivateProfileString(string section, string key, string val, string filePath);

        [DllImport("kernel32", CharSet = CharSet.Unicode)]
        static extern int GetPrivateProfileSection(string section, byte[] buffer, int size, string filePath);

        [DllImport("kernel32", CharSet = CharSet.Unicode)]
        static extern int GetPrivateProfileSectionNames(byte[] buffer, int size, string filePath);

        /// <summary>
        /// 初始化INI文件操作类
        /// </summary>
        /// <param name="filePath">INI文件路径</param>
        public ETIniFile(string filePath)
        {
            _filePath = filePath;
            _encoding = Encoding.Unicode; // 使用Unicode编码，与Windows API保持一致

            // 确保文件存在且可访问
            EnsureFileExists();
        }

        /// <summary>
        /// 确保INI文件存在且可访问
        /// </summary>
        void EnsureFileExists()
        {
            try
            {
                if (!File.Exists(_filePath))
                {
                    // 创建目录（如果不存在）
                    string directory = Path.GetDirectoryName(_filePath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    // 创建文件
                    using (FileStream fs = File.Create(_filePath))
                    using (StreamWriter writer = new(fs, _encoding))
                    {
                        writer.WriteLine("; Configuration File");
                        writer.WriteLine($"; Created: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    }
                }
                // 文件已存在，不需要重新创建或检查编码
                // 避免不必要的文件读写操作以提高性能
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"确保INI文件存在时发生错误: {ex.Message}", ex);
                //throw;
            }
        }

        /// <summary>
        /// 读取INI文件值
        /// </summary>
        /// <param name="section">节名</param>
        /// <param name="key">键名</param>
        /// <returns>读取的值</returns>
        public string IniReadValue(string section, string key)
        {
            return IniReadValue(section, key, string.Empty);
        }

        /// <summary>
        /// 读取INI文件值（支持默认值）
        /// </summary>
        /// <param name="section">节名</param>
        /// <param name="key">键名</param>
        /// <param name="defaultValue">默认值，当键不存在或读取失败时返回此值</param>
        /// <returns>读取的值，如果键不存在或读取失败则返回默认值</returns>
        public string IniReadValue(string section, string key, string defaultValue)
        {
            try
            {
                StringBuilder temp = new(2048);
                GetPrivateProfileString(section, key, defaultValue ?? string.Empty, temp, 2048, _filePath);
                string result = temp.ToString().Trim();

                // 如果读取结果为空且不等于默认值，则返回默认值
                if (string.IsNullOrEmpty(result) && !string.IsNullOrEmpty(defaultValue))
                {
                    return defaultValue;
                }

                return result;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"读取INI文件值时发生错误: Section={section}, Key={key}, DefaultValue={defaultValue}", ex);
                return defaultValue ?? string.Empty;
            }
        }

        /// <summary>
        /// 写入INI文件值
        /// </summary>
        /// <param name="section">节名</param>
        /// <param name="key">键名</param>
        /// <param name="value">要写入的值</param>
        /// <returns>是否写入成功</returns>
        public bool IniWriteValue(string section, string key, string value)
        {
            try
            {
                long result = WritePrivateProfileString(section, key, value, _filePath);
                return result != 0;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"写入INI文件值时发生错误: Section={section}, Key={key}, Value={value}", ex);
                return false;
            }
        }

        /// <summary>
        /// 保存INI文件
        /// </summary>
        public void IniWriteFile()
        {
            // 由于使用 WritePrivateProfileString，不需要额外的保存操作
            // 此方法保留用于兼容性
        }

        #region 增强功能

        /// <summary>
        /// 读取INI文件中的整数值
        /// </summary>
        /// <param name="section">节名称</param>
        /// <param name="key">键名称</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>读取的值</returns>
        public int ReadInt(string section, string key, int defaultValue = 0)
        {
            string value = IniReadValue(section, key);
            if (string.IsNullOrEmpty(value))
                return defaultValue;

            if (int.TryParse(value, out int result))
            {
                return result;
            }
            return defaultValue;
        }

        /// <summary>
        /// 读取INI文件中的布尔值
        /// </summary>
        /// <param name="section">节名称</param>
        /// <param name="key">键名称</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>读取的值</returns>
        public bool ReadBool(string section, string key, bool defaultValue = false)
        {
            string value = IniReadValue(section, key);
            if (string.IsNullOrEmpty(value))
                return defaultValue;

            if (bool.TryParse(value, out bool result))
            {
                return result;
            }

            if (int.TryParse(value, out int intResult))
            {
                return intResult != 0;
            }

            return defaultValue;
        }

        /// <summary>
        /// 读取INI文件中的DateTime值
        /// </summary>
        /// <param name="section">节名称</param>
        /// <param name="key">键名称</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>读取的值</returns>
        public DateTime ReadDateTime(string section, string key, DateTime defaultValue = default)
        {
            string value = IniReadValue(section, key);
            if (string.IsNullOrEmpty(value))
                return defaultValue;

            if (DateTime.TryParse(value, out DateTime result))
            {
                return result;
            }
            return defaultValue;
        }

        /// <summary>
        /// 写入INI文件中的整数值
        /// </summary>
        /// <param name="section">节名称</param>
        /// <param name="key">键名称</param>
        /// <param name="value">要写入的值</param>
        /// <returns>是否写入成功</returns>
        public bool WriteInt(string section, string key, int value)
        {
            return IniWriteValue(section, key, value.ToString());
        }

        /// <summary>
        /// 写入INI文件中的布尔值
        /// </summary>
        /// <param name="section">节名称</param>
        /// <param name="key">键名称</param>
        /// <param name="value">要写入的值</param>
        /// <returns>是否写入成功</returns>
        public bool WriteBool(string section, string key, bool value)
        {
            return IniWriteValue(section, key, value.ToString());
        }

        /// <summary>
        /// 写入INI文件中的DateTime值
        /// </summary>
        /// <param name="section">节名称</param>
        /// <param name="key">键名称</param>
        /// <param name="value">要写入的值</param>
        /// <returns>是否写入成功</returns>
        public bool WriteDateTime(string section, string key, DateTime value)
        {
            return IniWriteValue(section, key, value.ToString("yyyy-MM-dd HH:mm:ss"));
        }

        /// <summary>
        /// 获取所有节名称
        /// </summary>
        /// <returns>节名称列表</returns>
        public List<string> GetSectionNames()
        {
            List<string> result = [];

            try
            {
                // 使用文件流直接读取INI文件内容
                if (File.Exists(_filePath))
                {
                    string[] lines = File.ReadAllLines(_filePath, _encoding);

                    foreach (string line in lines)
                    {
                        string trimmedLine = line.Trim();
                        if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]") && trimmedLine.Length > 2)
                        {
                            string sectionName = trimmedLine.Substring(1, trimmedLine.Length - 2);
                            if (!string.IsNullOrEmpty(sectionName) && !result.Contains(sectionName))
                            {
                                result.Add(sectionName);
                            }
                        }
                    }

                    return result;
                }

                // 以下为备用方法，如果直接读取文件失败则使用API
                byte[] buffer = new byte[65536];
                int bytesReturned = GetPrivateProfileSectionNames(buffer, buffer.Length, _filePath);

                if (bytesReturned == 0 || bytesReturned >= buffer.Length - 2)
                {
                    // API调用失败或缓冲区太小
                    ETLogManager.Warning($"获取INI文件节名称API调用失败或返回数据太大: 返回字节数={bytesReturned}");
                    return result;
                }

                int position = 0;
                while (position < bytesReturned - 1) // 确保不会越界
                {
                    // 查找字符串结束位置
                    int endPosition = position;
                    while (endPosition < bytesReturned - 1 &&
                           !(buffer[endPosition] == 0 && buffer[endPosition + 1] == 0))
                    {
                        endPosition += 2;
                    }

                    if (endPosition > position)
                    {
                        int length = endPosition - position;
                        // 确保长度不会导致越界
                        if (position + length <= buffer.Length)
                        {
                            string section = Encoding.Unicode.GetString(buffer, position, length);
                            if (!string.IsNullOrEmpty(section))
                            {
                                result.Add(section);
                            }
                        }
                        position = endPosition + 2;
                    }
                    else
                    {
                        // 避免无限循环
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"获取INI文件节名称时发生错误: {ex.Message}", ex);
            }

            return result;
        }

        /// <summary>
        /// 获取节中所有的键值对
        /// </summary>
        /// <param name="section">节名称</param>
        /// <returns>键值对字典</returns>
        public Dictionary<string, string> GetSection(string section)
        {
            byte[] buffer = new byte[65536];
            int count = GetPrivateProfileSection(section, buffer, buffer.Length, _filePath);

            Dictionary<string, string> result = [];
            if (count > 0)
            {
                int position = 0;
                while (position < count && buffer[position] != 0)
                {
                    string entry = Encoding.Unicode.GetString(buffer, position,
                        Array.IndexOf(buffer, (byte)0, position) - position);

                    int equalIndex = entry.IndexOf('=');
                    if (equalIndex > 0)
                    {
                        string key = entry.Substring(0, equalIndex);
                        string value = entry.Substring(equalIndex + 1);
                        result[key] = value;
                    }

                    position += entry.Length * 2 + 2;
                }
            }

            return result;
        }

        /// <summary>
        /// 删除一个节
        /// </summary>
        /// <param name="section">节名称</param>
        /// <returns>是否删除成功</returns>
        public bool DeleteSection(string section)
        {
            long result = WritePrivateProfileString(section, null, null, _filePath);
            return result != 0;
        }

        /// <summary>
        /// 删除一个键
        /// </summary>
        /// <param name="section">节名称</param>
        /// <param name="key">键名称</param>
        /// <returns>是否删除成功</returns>
        public bool DeleteKey(string section, string key)
        {
            long result = WritePrivateProfileString(section, key, null, _filePath);
            return result != 0;
        }

        /// <summary>
        /// 将对象保存到INI文件
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="section">节名称</param>
        /// <param name="obj">要保存的对象</param>
        public void SaveObject<T>(string section, T obj) where T : class
        {
            try
            {
                Type type = typeof(T);
                PropertyInfo[] properties = type.GetProperties();

                foreach (PropertyInfo property in properties)
                {
                    try
                    {
                        object[] attributes = property.GetCustomAttributes(typeof(IniPropertyAttribute), true);
                        if (attributes.Length > 0 && attributes[0] is IniPropertyAttribute attribute)
                        {
                            string key = attribute.Key;

                            try
                            {
                                object value = property.GetValue(obj);

                                if (value != null)
                                {
                                    if (property.PropertyType == typeof(string))
                                    {
                                        IniWriteValue(section, key, (string)value);
                                    }
                                    else if (property.PropertyType == typeof(int))
                                    {
                                        WriteInt(section, key, (int)value);
                                    }
                                    else if (property.PropertyType == typeof(bool))
                                    {
                                        WriteBool(section, key, (bool)value);
                                    }
                                    else if (property.PropertyType == typeof(DateTime))
                                    {
                                        WriteDateTime(section, key, (DateTime)value);
                                    }
                                    else
                                    {
                                        IniWriteValue(section, key, value.ToString());
                                    }
                                }
                            }
                            catch (TargetParameterCountException ex)
                            {
                                ETLogManager.Error($"反射获取属性值时参数错误: Property={property.Name}", ex);
                            }
                            catch (ArgumentException ex)
                            {
                                ETLogManager.Error($"反射获取属性值时参数异常: Property={property.Name}", ex);
                            }
                            catch (TargetInvocationException ex)
                            {
                                ETLogManager.Error($"反射获取属性值时调用异常: Property={property.Name}", ex);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error($"处理属性时发生错误: Property={property.Name}", ex);
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"保存对象到INI文件时发生错误: Section={section}", ex);
            }
        }

        /// <summary>
        /// 从INI文件加载对象
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="section">节名称</param>
        /// <returns>加载的对象</returns>
        public T LoadObject<T>(string section) where T : class, new()
        {
            T obj = new();

            try
            {
                Type type = typeof(T);
                PropertyInfo[] properties = type.GetProperties();

                foreach (PropertyInfo property in properties)
                {
                    try
                    {
                        object[] attributes = property.GetCustomAttributes(typeof(IniPropertyAttribute), true);
                        if (attributes.Length > 0 && attributes[0] is IniPropertyAttribute attribute)
                        {
                            string key = attribute.Key;

                            try
                            {
                                if (property.PropertyType == typeof(string))
                                {
                                    string defaultValue = (string)property.GetValue(obj) ?? string.Empty;
                                    property.SetValue(obj, IniReadValue(section, key));
                                }
                                else if (property.PropertyType == typeof(int))
                                {
                                    int defaultValue = (int)property.GetValue(obj);
                                    property.SetValue(obj, ReadInt(section, key, defaultValue));
                                }
                                else if (property.PropertyType == typeof(bool))
                                {
                                    bool defaultValue = (bool)property.GetValue(obj);
                                    property.SetValue(obj, ReadBool(section, key, defaultValue));
                                }
                                else if (property.PropertyType == typeof(DateTime))
                                {
                                    DateTime defaultValue = (DateTime)property.GetValue(obj);
                                    property.SetValue(obj, ReadDateTime(section, key, defaultValue));
                                }
                            }
                            catch (TargetParameterCountException ex)
                            {
                                ETLogManager.Error($"反射设置属性值时参数错误: Property={property.Name}", ex);
                            }
                            catch (ArgumentException ex)
                            {
                                ETLogManager.Error($"反射设置属性值时参数异常: Property={property.Name}", ex);
                            }
                            catch (TargetInvocationException ex)
                            {
                                ETLogManager.Error($"反射设置属性值时调用异常: Property={property.Name}", ex);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error($"处理属性时发生错误: Property={property.Name}", ex);
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"从INI文件加载对象时发生错误: Section={section}", ex);
            }

            return obj;
        }

        /// <summary>
        /// 获取所有指定类型的对象
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <returns>对象列表</returns>
        public List<T> LoadAllObjects<T>() where T : class, new()
        {
            List<T> result = [];
            List<string> sections = GetSectionNames();

            foreach (string section in sections)
            {
                T obj = LoadObject<T>(section);
                result.Add(obj);
            }

            return result;
        }
        #endregion

        #region 兼容旧版本的方法

        /// <summary>
        /// 获取INI文件值（兼容旧版本）
        /// </summary>
        public string GetValue(string section, string key, string defaultValue = "")
        {
            string value = IniReadValue(section, key);
            return string.IsNullOrEmpty(value) ? defaultValue : value;
        }

        /// <summary>
        /// 获取INI文件布尔值
        /// </summary>
        /// <param name="section">节名</param>
        /// <param name="key">键名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>如果值为true或1则返回true，否则返回false</returns>
        public bool GetBool(string section, string key, bool defaultValue = false)
        {
            return ReadBool(section, key, defaultValue);
        }

        /// <summary>
        /// 设置INI文件值（兼容旧版本）
        /// </summary>
        public void SetValue(string section, string key, string value)
        {
            IniWriteValue(section, key, value);
        }

        /// <summary>
        /// 保存INI文件（兼容旧版本）
        /// </summary>
        public void Save()
        {
            IniWriteFile();
        }

        /// <summary>
        /// 读取字符串（为兼容IniConfig）
        /// </summary>
        public string ReadString(string section, string key, string defaultValue = "")
        {
            return GetValue(section, key, defaultValue);
        }

        /// <summary>
        /// 写入字符串（为兼容IniConfig）
        /// </summary>
        public bool WriteString(string section, string key, string value)
        {
            return IniWriteValue(section, key, value);
        }

        #endregion
    }
}