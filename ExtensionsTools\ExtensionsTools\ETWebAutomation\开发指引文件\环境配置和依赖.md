# 🔧 ETWebAutomation 环境配置和依赖管理

## 📦 NuGet包依赖

### 🌐 Flurl.Http 相关包
```xml
<!-- 主要HTTP客户端库 -->
<PackageReference Include="Flurl.Http" Version="4.0.2" />

<!-- URL构建支持 -->
<PackageReference Include="Flurl" Version="4.0.0" />

<!-- 如果需要Newtonsoft.Json支持（可选） -->
<PackageReference Include="Flurl.Http.Newtonsoft" Version="4.0.2" />
```

### 🖥️ CefSharp 相关包
```xml
<!-- WinForms版本的CefSharp -->
<PackageReference Include="CefSharp.WinForms" Version="126.2.180" />

<!-- 如果使用WPF，则使用这个包 -->
<!-- <PackageReference Include="CefSharp.Wpf" Version="126.2.180" /> -->

<!-- CefSharp公共库（自动包含） -->
<PackageReference Include="CefSharp.Common" Version="126.2.180" />
```

### 🔧 辅助工具包
```xml
<!-- JSON序列化 -->
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />

<!-- 加密支持 -->
<PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />

<!-- 异步支持 -->
<PackageReference Include="System.Threading.Tasks.Extensions" Version="4.5.4" />

<!-- 配置文件支持 -->
<PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
```

## 🎯 目标框架要求

### .NET Framework 支持
```xml
<TargetFramework>net472</TargetFramework>
<!-- 或者 -->
<TargetFramework>net48</TargetFramework>
```

### 平台要求
```xml
<PropertyGroup>
  <TargetFramework>net472</TargetFramework>
  <Platform>x64</Platform>
  <PlatformTarget>x64</PlatformTarget>
  <UseWindowsForms>true</UseWindowsForms>
</PropertyGroup>
```

## 🔧 CefSharp 特殊配置

### App.config 配置
```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <!-- CefSharp 程序集重定向 -->
      <dependentAssembly>
        <assemblyIdentity name="CefSharp" publicKeyToken="40c4b6fc221f4138" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-126.2.180.0" newVersion="126.2.180.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="CefSharp.Core" publicKeyToken="40c4b6fc221f4138" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-126.2.180.0" newVersion="126.2.180.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="CefSharp.WinForms" publicKeyToken="40c4b6fc221f4138" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-126.2.180.0" newVersion="126.2.180.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  
  <!-- CefSharp 设置 -->
  <appSettings>
    <add key="CefSharpAnyCpuSupport" value="true" />
    <add key="CefSharpLogSeverity" value="Info" />
  </appSettings>
</configuration>
```

### CefSharp 初始化代码
```csharp
// 在Program.cs或应用程序启动时
public static class CefSharpInitializer {
    public static void Initialize() {
        // 设置CefSharp设置
        var settings = new CefSettings() {
            // 设置缓存路径
            CachePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "ETWebAutomation", "CefCache"),
            
            // 设置用户代理
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36",
            
            // 启用日志
            LogSeverity = LogSeverity.Info,
            LogFile = Path.Combine(Application.StartupPath, "Logs", "cef.log"),
            
            // 禁用GPU加速（如果有问题）
            CefCommandLineArgs.Add("disable-gpu", "1"),
            CefCommandLineArgs.Add("disable-gpu-compositing", "1"),
            
            // 允许不安全的内容
            CefCommandLineArgs.Add("allow-running-insecure-content", "1"),
            
            // 禁用Web安全（仅用于开发）
            CefCommandLineArgs.Add("disable-web-security", "1"),
            
            // 设置语言
            Locale = "zh-CN",
            AcceptLanguageList = "zh-CN,zh,en-US,en"
        };

        // 初始化CefSharp
        if (!Cef.Initialize(settings, performDependencyCheck: true, browserProcessHandler: null)) {
            throw new Exception("CefSharp初始化失败");
        }
    }

    public static void Shutdown() {
        Cef.Shutdown();
    }
}

// 在Main方法中调用
[STAThread]
static void Main() {
    Application.EnableVisualStyles();
    Application.SetCompatibleTextRenderingDefault(false);
    
    try {
        // 初始化CefSharp
        CefSharpInitializer.Initialize();
        
        Application.Run(new MainForm());
    }
    finally {
        // 清理CefSharp
        CefSharpInitializer.Shutdown();
    }
}
```

## 📁 文件部署要求

### CefSharp 运行时文件
CefSharp需要以下文件与应用程序一起部署：

```
应用程序目录/
├── YourApp.exe
├── CefSharp.dll
├── CefSharp.Core.dll
├── CefSharp.WinForms.dll
├── libcef.dll
├── chrome_elf.dll
├── d3dcompiler_47.dll
├── libEGL.dll
├── libGLESv2.dll
├── snapshot_blob.bin
├── v8_context_snapshot.bin
├── icudtl.dat
├── locales/
│   ├── zh-CN.pak
│   ├── en-US.pak
│   └── ... (其他语言包)
├── swiftshader/
│   ├── libEGL.dll
│   └── libGLESv2.dll
└── CefSharp.BrowserSubprocess.exe
```

### 自动复制配置
在项目文件中添加：
```xml
<PropertyGroup>
  <CefSharpAnyCpuSupport>true</CefSharpAnyCpuSupport>
</PropertyGroup>

<Target Name="CopyFilesToOutputDirectory" AfterTargets="Build">
  <ItemGroup>
    <CefSharpFiles Include="$(OutputPath)*.dll" />
    <CefSharpFiles Include="$(OutputPath)*.exe" />
    <CefSharpFiles Include="$(OutputPath)*.pak" />
    <CefSharpFiles Include="$(OutputPath)*.bin" />
    <CefSharpFiles Include="$(OutputPath)*.dat" />
  </ItemGroup>
</Target>
```

## 🔧 开发环境配置

### Visual Studio 配置
1. **目标平台**：设置为 x64
2. **调试设置**：
   ```xml
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
     <DebugType>full</DebugType>
     <DebugSymbols>true</DebugSymbols>
     <DefineConstants>DEBUG;TRACE</DefineConstants>
   </PropertyGroup>
   ```

3. **后期生成事件**：
   ```batch
   xcopy "$(TargetDir)locales" "$(TargetDir)locales\" /E /I /Y
   xcopy "$(TargetDir)swiftshader" "$(TargetDir)swiftshader\" /E /I /Y
   ```

### 项目引用配置
```xml
<ItemGroup>
  <!-- 引用现有的ExtensionsTools -->
  <ProjectReference Include="..\ExtensionsTools.csproj" />
  
  <!-- 如果需要引用其他项目 -->
  <Reference Include="System.Windows.Forms" />
  <Reference Include="System.Drawing" />
  <Reference Include="System.Configuration" />
</ItemGroup>
```

## 🚨 常见问题和解决方案

### 1. CefSharp初始化失败
**问题**：应用程序启动时CefSharp初始化失败
**解决方案**：
```csharp
// 检查运行时文件
public static bool CheckCefSharpFiles() {
    var requiredFiles = new[] {
        "libcef.dll",
        "CefSharp.Core.dll",
        "CefSharp.dll",
        "CefSharp.BrowserSubprocess.exe"
    };

    var appPath = Application.StartupPath;
    foreach (var file in requiredFiles) {
        var filePath = Path.Combine(appPath, file);
        if (!File.Exists(filePath)) {
            MessageBox.Show($"缺少必要文件: {file}", "初始化错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return false;
        }
    }
    return true;
}
```

### 2. Flurl.Http SSL证书问题
**问题**：HTTPS请求时SSL证书验证失败
**解决方案**：
```csharp
// 配置忽略SSL证书错误（仅用于开发环境）
FlurlHttp.Clients.WithDefaults(builder =>
    builder.ConfigureInnerHandler(hch => {
        hch.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
    })
);
```

### 3. 内存泄漏问题
**问题**：长时间运行后内存占用过高
**解决方案**：
```csharp
// 定期清理资源
public class ResourceManager {
    private Timer _cleanupTimer;

    public ResourceManager() {
        _cleanupTimer = new Timer(CleanupResources, null, TimeSpan.FromMinutes(30), TimeSpan.FromMinutes(30));
    }

    private void CleanupResources(object state) {
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        
        // 清理CefSharp缓存
        if (Cef.IsInitialized) {
            // 可以在这里添加缓存清理逻辑
        }
    }
}
```

### 4. 高DPI显示问题
**问题**：在高DPI显示器上界面显示异常
**解决方案**：
```csharp
// 在Program.cs中添加
[STAThread]
static void Main() {
    // 设置DPI感知
    if (Environment.OSVersion.Version.Major >= 6) {
        SetProcessDPIAware();
    }

    Application.EnableVisualStyles();
    Application.SetCompatibleTextRenderingDefault(false);
    
    // 其他初始化代码...
}

[System.Runtime.InteropServices.DllImport("user32.dll")]
private static extern bool SetProcessDPIAware();
```

## 📋 部署检查清单

### 开发环境检查
- [ ] Visual Studio 2019/2022 已安装
- [ ] .NET Framework 4.7.2 或更高版本
- [ ] 项目目标平台设置为 x64
- [ ] 所有NuGet包已正确安装
- [ ] CefSharp运行时文件完整

### 生产环境检查
- [ ] 目标机器安装了.NET Framework 4.7.2+
- [ ] Visual C++ Redistributable 已安装
- [ ] 所有依赖文件已正确部署
- [ ] 应用程序具有必要的文件系统权限
- [ ] 网络连接正常，可访问OA系统

### 性能优化检查
- [ ] 启用了连接池复用
- [ ] 配置了合理的超时时间
- [ ] 实现了资源清理机制
- [ ] 添加了性能监控日志

这个配置文档确保了开发和部署环境的正确设置，避免了常见的配置问题。
