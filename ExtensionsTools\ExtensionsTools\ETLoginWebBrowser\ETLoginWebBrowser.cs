using Microsoft.Web.WebView2.WinForms;
using Microsoft.Web.WebView2.Core;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ET.ETLoginWebBrowser
{
    /// <summary>
    /// 登录WebBrowser对话框，用于向其他功能模块提供cookies数据
    /// </summary>
    public partial class ETLoginWebBrowser : Form
    {
        #region 私有字段

        private WebView2 _webView;
        private StatusStrip _statusStrip;
        private ToolStripStatusLabel _statusLabel;
        private string _headersJson = string.Empty;
        private Dictionary<string, string> _lastResponseHeaders = new Dictionary<string, string>();
        private List<CookieItem> _currentCookies = new List<CookieItem>();

        #endregion 私有字段

        #region 公共属性

        /// <summary>
        /// 获取HTTP请求标头的JSON数据（新格式：包含url、headers、cookies三部分）
        /// </summary>
        public string HeadersJson => GetNewFormatHeadersJson();

        /// <summary>
        /// 获取最后一次请求的标头字典
        /// </summary>
        public Dictionary<string, string> LastRequestHeaders => new Dictionary<string, string>(_lastResponseHeaders);

        /// <summary>
        /// 获取所有捕获的请求信息的JSON数据
        /// </summary>
        public string AllCapturedRequestsJson => GetAllCapturedRequestsJson();

        /// <summary>
        /// 获取所有API请求信息的JSON数据
        /// </summary>
        public string ApiRequestsJson => GetApiRequestsJson();

        /// <summary>
        /// 获取捕获的请求总数
        /// </summary>
        public int CapturedRequestsCount => _allCapturedRequests.Count;

        /// <summary>
        /// 获取捕获的API请求数量
        /// </summary>
        public int ApiRequestsCount => _allCapturedRequests.Count(r =>
            r.ContainsKey("request-type") && r["request-type"] == "API");

        /// <summary>
        /// 获取当前Cookie数据
        /// </summary>
        public List<CookieItem> CurrentCookies => new List<CookieItem>(_currentCookies);

        /// <summary>
        /// 要导航的初始URL
        /// </summary>
        public string InitialUrl { get; set; }

        /// <summary>
        /// 用户操作提示内容
        /// </summary>
        public string UserTipText
        {
            get => lblUserTip?.Text ?? string.Empty;
            set
            {
                if (lblUserTip != null)
                {
                    if (InvokeRequired)
                    {
                        Invoke(new Action(() => lblUserTip.Text = value ?? string.Empty));
                    }
                    else
                    {
                        lblUserTip.Text = value ?? string.Empty;
                    }
                }
            }
        }

        /// <summary>
        /// 是否显示用户操作提示
        /// </summary>
        public bool ShowUserTip
        {
            get => lblUserTip?.Visible ?? false;
            set
            {
                if (lblUserTip != null)
                {
                    if (InvokeRequired)
                    {
                        Invoke(new Action(() => SetUserTipVisibility(value)));
                    }
                    else
                    {
                        SetUserTipVisibility(value);
                    }
                }
            }
        }

        /// <summary>
        /// 是否允许弹出地理信息权限提示，默认为false（不允许获取地理信息）
        /// </summary>
        public bool AllowGeolocationPrompt { get; set; } = false;

        #endregion 公共属性

        #region 构造函数

        /// <summary>
        /// 初始化LoginWebBrowser
        /// </summary>
        public ETLoginWebBrowser()
        {
            InitializeComponent();
            InitializeWebView();
            SetupStatusBar();
        }

        /// <summary>
        /// 初始化LoginWebBrowser并设置初始URL
        /// </summary>
        /// <param name="initialUrl">初始URL</param>
        public ETLoginWebBrowser(string initialUrl) : this()
        {
            InitialUrl = initialUrl;
        }

        /// <summary>
        /// 初始化LoginWebBrowser并设置初始URL和用户提示内容
        /// </summary>
        /// <param name="initialUrl">初始URL</param>
        /// <param name="userTipText">用户操作提示内容</param>
        public ETLoginWebBrowser(string initialUrl, string userTipText) : this(initialUrl)
        {
            UserTipText = userTipText;
        }

        /// <summary>
        /// 初始化LoginWebBrowser并设置初始URL、用户提示内容和地理信息权限
        /// </summary>
        /// <param name="initialUrl">初始URL</param>
        /// <param name="userTipText">用户操作提示内容</param>
        /// <param name="allowGeolocationPrompt">是否允许弹出地理信息权限提示，默认为false</param>
        public ETLoginWebBrowser(string initialUrl, string userTipText, bool allowGeolocationPrompt) : this(initialUrl, userTipText)
        {
            AllowGeolocationPrompt = allowGeolocationPrompt;
        }

        /// <summary>
        /// 初始化LoginWebBrowser并设置地理信息权限
        /// </summary>
        /// <param name="allowGeolocationPrompt">是否允许弹出地理信息权限提示，默认为false</param>
        public ETLoginWebBrowser(bool allowGeolocationPrompt) : this()
        {
            AllowGeolocationPrompt = allowGeolocationPrompt;
        }

        #endregion 构造函数

        #region 初始化方法

        /// <summary>
        /// 初始化WebView2控件
        /// </summary>
        private void InitializeWebView()
        {
            _webView = new WebView2
            {
                Dock = DockStyle.Fill
            };

            // 添加到窗体
            this.Controls.Add(_webView);

            // 设置事件处理
            _webView.NavigationCompleted += WebView_NavigationCompleted;
            _webView.CoreWebView2InitializationCompleted += WebView_CoreWebView2InitializationCompleted;
        }

        /// <summary>
        /// 设置状态栏
        /// </summary>
        private void SetupStatusBar()
        {
            _statusStrip = new StatusStrip();
            _statusLabel = new ToolStripStatusLabel
            {
                Text = "准备就绪",
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };

            _statusStrip.Items.Add(_statusLabel);
            this.Controls.Add(_statusStrip);
        }

        #endregion 初始化方法

        #region 事件处理

        /// <summary>
        /// WebView2初始化完成事件
        /// </summary>
        private void WebView_CoreWebView2InitializationCompleted(object sender, CoreWebView2InitializationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                try
                {
                    // 添加网络请求过滤器，监控所有请求
                    _webView.CoreWebView2.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);
                    Debug.Print($"[初始化] 已添加网络请求过滤器，监控所有请求类型");

                    // 注册WebResourceRequested事件，用于获取完整的HTTP请求标头
                    _webView.CoreWebView2.WebResourceRequested += WebView_WebResourceRequested;
                    Debug.Print($"[初始化] 已注册WebResourceRequested事件处理器");

                    // 注册DOM内容加载完成事件，用于获取页面基本信息
                    _webView.CoreWebView2.DOMContentLoaded += WebView_DOMContentLoaded;
                    Debug.Print($"[初始化] 已注册DOMContentLoaded事件处理器");

                    // 注册权限请求事件，用于控制地理信息等权限
                    _webView.CoreWebView2.PermissionRequested += WebView_PermissionRequested;
                    Debug.Print($"[初始化] 已注册PermissionRequested事件处理器，地理信息权限设置: {AllowGeolocationPrompt}");

                    if (!string.IsNullOrEmpty(InitialUrl))
                    {
                        _webView.CoreWebView2.Navigate(InitialUrl);
                        SetStatus($"正在导航到: {InitialUrl}");
                        Debug.Print($"[初始化] 开始导航到: {InitialUrl}");
                    }
                    else
                    {
                        Debug.Print($"[初始化] 无初始URL，等待手动导航");
                    }
                }
                catch (Exception ex)
                {
                    SetStatus($"导航失败: {ex.Message}", true);
                }
            }
        }

        /// <summary>
        /// 存储所有捕获的请求标头
        /// </summary>
        private readonly List<Dictionary<string, string>> _allCapturedRequests = new List<Dictionary<string, string>>();

        /// <summary>
        /// 网络资源请求事件处理，用于获取完整的HTTP请求标头（包括API接口）
        /// </summary>
        private void WebView_WebResourceRequested(object sender, CoreWebView2WebResourceRequestedEventArgs e)
        {
            try
            {
                var request = e.Request;
                if (request == null) return;

                var requestUrl = request.Uri ?? "";
                var method = request.Method ?? "";

                // 判断是否为API请求（通常包含这些特征）
                bool isApiRequest = IsApiRequest(requestUrl, method, e.ResourceContext);

                // 创建请求信息字典
                var requestInfo = new Dictionary<string, string>();

                // 获取请求基本信息
                requestInfo["request-method"] = method;
                requestInfo["request-uri"] = requestUrl;
                requestInfo["resource-context"] = e.ResourceContext.ToString();
                requestInfo["timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");

                // 获取所有请求标头
                if (request.Headers != null)
                {
                    foreach (var header in request.Headers)
                    {
                        requestInfo[header.Key] = header.Value;
                    }
                }

                // 获取请求内容信息（如果有）
                if (request.Content != null)
                {
                    requestInfo["request-has-content"] = "true";
                }

                // 标记请求类型
                requestInfo["request-type"] = isApiRequest ? "API" : "Resource";
                requestInfo["source"] = "WebResourceRequested";

                // 添加到捕获列表
                _allCapturedRequests.Add(requestInfo);

                // 调试输出：打印捕获到的请求信息
                Debug.Print($"=== 捕获到请求 ===");
                Debug.Print($"时间: {requestInfo["timestamp"]}");
                Debug.Print($"类型: {requestInfo["request-type"]}");
                Debug.Print($"方法: {method}");
                Debug.Print($"URL: {requestUrl}");
                Debug.Print($"上下文: {e.ResourceContext}");
                Debug.Print($"标头数量: {request.Headers?.Count() ?? 0}");

                // 打印所有请求标头
                if (request.Headers != null && request.Headers.Any())
                {
                    Debug.Print("--- 请求标头 ---");
                    foreach (var header in request.Headers)
                    {
                        Debug.Print($"{header.Key}: {header.Value}");
                    }
                }
                else
                {
                    Debug.Print("--- 无请求标头 ---");
                }

                Debug.Print($"==================");

                // 如果是主文档或API请求，更新当前显示的标头
                if (e.ResourceContext == CoreWebView2WebResourceContext.Document || isApiRequest)
                {
                    _lastResponseHeaders.Clear();
                    foreach (var item in requestInfo)
                    {
                        _lastResponseHeaders[item.Key] = item.Value;
                    }

                    // 序列化为格式化的JSON
                    _headersJson = JsonConvert.SerializeObject(_lastResponseHeaders, Formatting.Indented);

                    var requestType = isApiRequest ? "API接口" : "主文档";
                    SetStatus($"已捕获{requestType}请求标头: {method} {requestUrl}");

                    // 调试输出：重要请求的详细信息
                    Debug.Print($"*** 重要请求已更新到当前标头 ***");
                    Debug.Print($"请求类型: {requestType}");
                    Debug.Print($"JSON长度: {_headersJson.Length} 字符");
                    Debug.Print($"标头总数: {_lastResponseHeaders.Count}");
                }

                // 限制捕获列表大小，避免内存过多占用
                if (_allCapturedRequests.Count > 100)
                {
                    _allCapturedRequests.RemoveAt(0);
                    Debug.Print($"[统计] 捕获列表已满，移除最旧的请求");
                }

                // 输出统计信息
                var totalRequests = _allCapturedRequests.Count;
                var apiRequests = _allCapturedRequests.Count(r => r.ContainsKey("request-type") && r["request-type"] == "API");
                var resourceRequests = totalRequests - apiRequests;
                Debug.Print($"[统计] 总请求: {totalRequests}, API请求: {apiRequests}, 资源请求: {resourceRequests}");
            }
            catch (Exception ex)
            {
                SetStatus($"获取HTTP请求标头失败: {ex.Message}", true);
                Debug.Print($"[WebResourceRequested] 异常: {ex.Message}");
                Debug.Print($"[WebResourceRequested] 堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 判断是否为API请求
        /// </summary>
        private bool IsApiRequest(string url, string method, CoreWebView2WebResourceContext context)
        {
            if (string.IsNullOrEmpty(url))
            {
                Debug.Print($"[API判断] URL为空，返回false");
                return false;
            }

            // 排除静态资源
            if (context == CoreWebView2WebResourceContext.Image ||
                context == CoreWebView2WebResourceContext.Stylesheet ||
                context == CoreWebView2WebResourceContext.Script ||
                context == CoreWebView2WebResourceContext.Font ||
                context == CoreWebView2WebResourceContext.Media)
            {
                Debug.Print($"[API判断] 静态资源类型 {context}，排除: {url}");
                return false;
            }

            // API请求的常见特征
            var lowerUrl = url.ToLower();

            // 包含API路径的请求
            if (lowerUrl.Contains("api") ||
                lowerUrl.Contains("rest") ||
                lowerUrl.Contains("/service/") ||
                lowerUrl.Contains("/v1/") ||
                lowerUrl.Contains("/v2/") ||
                lowerUrl.Contains("/graphql"))
            {
                Debug.Print($"[API判断] 包含API路径，识别为API: {url}");
                return true;
            }

            // 非GET方法通常是API请求
            if (method != "GET")
            {
                Debug.Print($"[API判断] 非GET方法 {method}，识别为API: {url}");
                return true;
            }

            // XHR或Fetch请求
            if (context == CoreWebView2WebResourceContext.XmlHttpRequest ||
                context == CoreWebView2WebResourceContext.Fetch)
            {
                Debug.Print($"[API判断] XHR/Fetch请求 {context}，识别为API: {url}");
                return true;
            }

            // JSON数据请求
            if (lowerUrl.Contains(".json") && !lowerUrl.Contains("manifest.json"))
            {
                Debug.Print($"[API判断] JSON数据请求，识别为API: {url}");
                return true;
            }

            Debug.Print($"[API判断] 普通资源请求 {context} {method}: {url}");
            return false;
        }

        /// <summary>
        /// DOM内容加载完成事件处理，用于补充页面基本信息
        /// </summary>
        private async void WebView_DOMContentLoaded(object sender, CoreWebView2DOMContentLoadedEventArgs e)
        {
            try
            {
                SetStatus("DOM内容加载完成，补充页面基本信息...");

                // 如果已经通过WebResourceRequested获取到了请求标头，则只补充页面信息
                var hasRequestHeaders = _lastResponseHeaders.ContainsKey("source") &&
                                       _lastResponseHeaders["source"] == "WebResourceRequested";

                if (hasRequestHeaders)
                {
                    // 只补充一些页面基本信息
                    var pageInfoScript = @"
                        JSON.stringify({
                            'page-title': document.title || '',
                            'page-ready-state': document.readyState,
                            'page-content-type': document.contentType || '',
                            'page-charset': document.characterSet || document.charset || '',
                            'page-last-modified': document.lastModified || '',
                            'page-referrer': document.referrer || ''
                        });
                    ";

                    var result = await _webView.CoreWebView2.ExecuteScriptAsync(pageInfoScript);

                    // 移除JavaScript返回值的引号
                    if (result.StartsWith("\"") && result.EndsWith("\""))
                    {
                        result = result.Substring(1, result.Length - 2);
                        result = result.Replace("\\\"", "\"");
                    }

                    // 解析并补充页面信息
                    var pageInfo = JsonConvert.DeserializeObject<Dictionary<string, string>>(result);
                    foreach (var info in pageInfo)
                    {
                        _lastResponseHeaders[info.Key] = info.Value ?? "";
                    }

                    // 重新序列化
                    _headersJson = JsonConvert.SerializeObject(_lastResponseHeaders, Formatting.Indented);

                    SetStatus($"已补充页面信息，总计 {_lastResponseHeaders.Count} 项");
                }
                else
                {
                    SetStatus("未获取到HTTP请求标头，DOM事件将等待请求事件...");
                }
            }
            catch (Exception ex)
            {
                SetStatus($"DOM加载完成事件处理失败: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 导航完成事件
        /// </summary>
        private async void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                var url = _webView.Source?.ToString() ?? "未知";
                SetStatus($"已加载: {url}");

                // 自动获取标头信息
                await GetHeadersAsync();
            }
            else
            {
                SetStatus("页面加载失败", true);
            }
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private async void ETLoginWebBrowser_Load(object sender, EventArgs e)
        {
            try
            {
                // 初始化用户提示框布局
                SetUserTipVisibility(ShowUserTip);

                SetStatus("正在检查WebView2运行时...");

                // 检查WebView2运行时是否可用
                if (!WebView2Helper.IsWebView2RuntimeAvailable())
                {
                    string errorMsg = "WebView2运行时未安装或不可用。\n\n请从以下地址下载并安装WebView2运行时：\nhttps://developer.microsoft.com/zh-cn/microsoft-edge/webview2/";
                    SetStatus("WebView2运行时不可用", true);
                    MessageBox.Show(errorMsg, "WebView2运行时错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                SetStatus("正在初始化WebView2...");

                // 使用WebView2Helper安全地创建环境
                var environment = await WebView2Helper.CreateWebView2EnvironmentSafelyAsync();

                // 确保WebView2已初始化
                await _webView.EnsureCoreWebView2Async(environment);

                SetStatus("WebView2初始化成功");

                if (!string.IsNullOrEmpty(InitialUrl))
                {
                    _webView.CoreWebView2.Navigate(InitialUrl);
                }
            }
            catch (Exception ex)
            {
                SetStatus($"初始化失败: {ex.Message}", true);
                WebView2Helper.ShowWebView2ErrorDialog(ex, this);
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private async void ETLoginWebBrowser_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // 在关闭前获取最新的标头信息和Cookie
                await GetHeadersAsync();

                // 设置对话框结果
                if (this.DialogResult == DialogResult.None)
                {
                    // 检查是否有有效的数据（标头或Cookie）
                    bool hasValidData = !string.IsNullOrEmpty(_headersJson) || _currentCookies.Count > 0;
                    this.DialogResult = hasValidData ? DialogResult.OK : DialogResult.Cancel;
                }
            }
            catch (Exception ex)
            {
                SetStatus($"获取标头失败: {ex.Message}", true);
                this.DialogResult = DialogResult.Cancel;
            }
        }

        /// <summary>
        /// 窗体大小改变事件
        /// </summary>
        private void ETLoginWebBrowser_Resize(object sender, EventArgs e)
        {
            // 重新调整布局
            if (ShowUserTip)
            {
                SetUserTipVisibility(true);
            }
        }

        /// <summary>
        /// WebView2权限请求事件处理，用于控制地理信息等权限
        /// </summary>
        private void WebView_PermissionRequested(object sender, CoreWebView2PermissionRequestedEventArgs e)
        {
            try
            {
                Debug.Print($"[权限请求] 权限类型: {e.PermissionKind}, URI: {e.Uri}");

                // 处理地理信息权限请求
                if (e.PermissionKind == CoreWebView2PermissionKind.Geolocation)
                {
                    if (AllowGeolocationPrompt)
                    {
                        // 允许弹出权限提示，使用默认行为
                        e.State = CoreWebView2PermissionState.Default;
                        Debug.Print($"[权限请求] 地理信息权限: 允许弹出提示");
                        SetStatus("已允许地理信息权限提示");
                    }
                    else
                    {
                        // 不允许获取地理信息，直接拒绝
                        e.State = CoreWebView2PermissionState.Deny;
                        Debug.Print($"[权限请求] 地理信息权限: 已拒绝");
                        SetStatus("已拒绝地理信息权限请求");
                    }
                }
                else
                {
                    // 其他权限使用默认行为
                    e.State = CoreWebView2PermissionState.Default;
                    Debug.Print($"[权限请求] 其他权限 {e.PermissionKind}: 使用默认行为");
                }
            }
            catch (Exception ex)
            {
                Debug.Print($"[权限请求] 处理权限请求时发生异常: {ex.Message}");
                SetStatus($"处理权限请求失败: {ex.Message}", true);
            }
        }

        #endregion 事件处理

        #region 标头操作方法

        /// <summary>
        /// 获取当前页面的HTTP请求标头并转换为JSON格式
        /// </summary>
        private async Task GetHeadersAsync()
        {
            try
            {
                if (_webView?.CoreWebView2 == null)
                {
                    SetStatus("WebView2未初始化", true);
                    return;
                }

                var currentUrl = _webView.Source?.ToString();
                if (string.IsNullOrEmpty(currentUrl))
                {
                    SetStatus("当前URL为空", true);
                    return;
                }

                SetStatus("正在获取HTTP请求标头...");
                Debug.Print($"[GetHeadersAsync] 开始获取标头，当前URL: {currentUrl}");

                // 检查是否已经通过WebResourceRequested事件获取到请求标头数据
                var hasRequestHeaders = _lastResponseHeaders.ContainsKey("source") &&
                                       _lastResponseHeaders["source"] == "WebResourceRequested";

                Debug.Print($"[GetHeadersAsync] 检查事件标头: hasRequestHeaders={hasRequestHeaders}, 标头数量={_lastResponseHeaders.Count}");

                if (hasRequestHeaders)
                {
                    // 刷新Cookie数据
                    await RefreshCookiesAsync();

                    // 直接使用已经通过WebResourceRequested事件获取的完整HTTP请求标头
                    _headersJson = JsonConvert.SerializeObject(_lastResponseHeaders, Formatting.Indented);
                    SetStatus($"已获取 {_lastResponseHeaders.Count} 个HTTP请求标头和 {_currentCookies.Count} 个Cookie（来自请求事件）");
                    Debug.Print($"[GetHeadersAsync] 使用事件标头，JSON长度: {_headersJson.Length}，Cookie数量: {_currentCookies.Count}");
                    return;
                }

                // 如果事件中没有获取到标头，则等待一小段时间后重试
                SetStatus("等待WebResourceRequested事件获取请求标头数据...");
                Debug.Print($"[GetHeadersAsync] 等待事件标头，延迟1秒...");
                await Task.Delay(1000); // 等待1秒

                hasRequestHeaders = _lastResponseHeaders.ContainsKey("source") &&
                                  _lastResponseHeaders["source"] == "WebResourceRequested";

                Debug.Print($"[GetHeadersAsync] 延迟后检查: hasRequestHeaders={hasRequestHeaders}, 标头数量={_lastResponseHeaders.Count}");

                if (hasRequestHeaders)
                {
                    // 刷新Cookie数据
                    await RefreshCookiesAsync();

                    _headersJson = JsonConvert.SerializeObject(_lastResponseHeaders, Formatting.Indented);
                    SetStatus($"已获取 {_lastResponseHeaders.Count} 个HTTP请求标头和 {_currentCookies.Count} 个Cookie（延迟获取）");
                    Debug.Print($"[GetHeadersAsync] 延迟获取成功，JSON长度: {_headersJson.Length}，Cookie数量: {_currentCookies.Count}");
                    return;
                }

                // 如果仍然没有数据，则提供基本的URL信息
                SetStatus("未能通过事件获取HTTP请求标头，提供基本URL信息...");
                Debug.Print($"[GetHeadersAsync] 事件未获取到标头，使用降级方案");

                // 尝试获取Cookie数据
                await RefreshCookiesAsync();

                _lastResponseHeaders.Clear();
                _lastResponseHeaders["url"] = currentUrl;
                _lastResponseHeaders["timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                _lastResponseHeaders["source"] = "ETLoginWebBrowser";
                _lastResponseHeaders["request-type"] = "Page";
                _lastResponseHeaders["note"] = "无法通过WebResourceRequested事件获取HTTP请求标头，可能是因为页面已加载完成或事件未触发";

                // 序列化为格式化的JSON
                _headersJson = JsonConvert.SerializeObject(_lastResponseHeaders, Formatting.Indented);

                SetStatus($"已提供基本信息 {_lastResponseHeaders.Count} 项和 {_currentCookies.Count} 个Cookie");
                Debug.Print($"[GetHeadersAsync] 降级方案完成，提供基本信息 {_lastResponseHeaders.Count} 项，Cookie数量: {_currentCookies.Count}");
            }
            catch (Exception ex)
            {
                SetStatus($"获取HTTP请求标头失败: {ex.Message}", true);
                _headersJson = string.Empty;
                Debug.Print($"[GetHeadersAsync] 异常: {ex.Message}");
                Debug.Print($"[GetHeadersAsync] 堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 手动刷新HTTP请求标头
        /// </summary>
        public async Task RefreshHeadersAsync()
        {
            await GetHeadersAsync();
        }

        /// <summary>
        /// 获取所有捕获的请求信息
        /// </summary>
        /// <returns>所有捕获的请求信息的JSON字符串</returns>
        public string GetAllCapturedRequestsJson()
        {
            try
            {
                if (_allCapturedRequests.Count == 0)
                {
                    return JsonConvert.SerializeObject(new { message = "暂无捕获的请求", count = 0 }, Formatting.Indented);
                }

                var result = new
                {
                    total_requests = _allCapturedRequests.Count,
                    api_requests = _allCapturedRequests.Count(r => r.ContainsKey("request-type") && r["request-type"] == "API"),
                    resource_requests = _allCapturedRequests.Count(r => r.ContainsKey("request-type") && r["request-type"] == "Resource"),
                    requests = _allCapturedRequests
                };

                return JsonConvert.SerializeObject(result, Formatting.Indented);
            }
            catch (Exception ex)
            {
                return JsonConvert.SerializeObject(new { error = $"获取请求信息失败: {ex.Message}" }, Formatting.Indented);
            }
        }

        /// <summary>
        /// 获取仅API请求的信息
        /// </summary>
        /// <returns>API请求信息的JSON字符串</returns>
        public string GetApiRequestsJson()
        {
            try
            {
                var apiRequests = _allCapturedRequests.Where(r =>
                    r.ContainsKey("request-type") && r["request-type"] == "API").ToList();

                if (apiRequests.Count == 0)
                {
                    return JsonConvert.SerializeObject(new { message = "暂无捕获的API请求", count = 0 }, Formatting.Indented);
                }

                var result = new
                {
                    api_requests_count = apiRequests.Count,
                    api_requests = apiRequests
                };

                return JsonConvert.SerializeObject(result, Formatting.Indented);
            }
            catch (Exception ex)
            {
                return JsonConvert.SerializeObject(new { error = $"获取API请求信息失败: {ex.Message}" }, Formatting.Indented);
            }
        }

        /// <summary>
        /// 清空所有捕获的请求信息
        /// </summary>
        public void ClearCapturedRequests()
        {
            _allCapturedRequests.Clear();
            SetStatus("已清空所有捕获的请求信息");
        }

        /// <summary>
        /// 获取新格式的标头JSON（包含url、headers、cookies三部分）
        /// 使用通用的ETWebBrowserJsonFormatter
        /// </summary>
        /// <returns>新格式的JSON字符串</returns>
        private string GetNewFormatHeadersJson()
        {
            try
            {
                var options = new ETWebBrowserJsonFormatter.HeadersOptions
                {
                    Source = "ETLoginWebBrowser",
                    RequestType = "Page",
                    ExtraHeaders = _lastResponseHeaders?.ToDictionary(k => k.Key, v => (object)v.Value) ?? new Dictionary<string, object>()
                };

                return ETWebBrowserJsonFormatter.CreateLoginInfoJson(
                    _webView?.Source?.ToString() ?? string.Empty,
                    _lastResponseHeaders,
                    _currentCookies,
                    options);
            }
            catch (Exception ex)
            {
                // 使用通用模块的错误处理
                return ETWebBrowserJsonFormatter.CreateLoginInfoJson(
                    _webView?.Source?.ToString() ?? string.Empty,
                    new Dictionary<string, string> { ["error"] = $"获取标头数据失败: {ex.Message}" },
                    new List<CookieItem>(),
                    new ETWebBrowserJsonFormatter.HeadersOptions { Source = "ETLoginWebBrowser" });
            }
        }

        /// <summary>
        /// 刷新Cookie数据（使用通用模块）
        /// </summary>
        private async Task RefreshCookiesAsync()
        {
            try
            {
                // 使用通用模块获取Cookie数据
                string newFormatJson = await ETWebBrowserJsonFormatter.CreateLoginInfoJsonFromWebViewAsync(_webView);

                if (!string.IsNullOrEmpty(newFormatJson) && ETWebBrowserJsonFormatter.IsStandardFormat(newFormatJson))
                {
                    var loginInfo = ETWebBrowserJsonFormatter.ParseLoginInfoJson(newFormatJson);
                    _currentCookies = loginInfo.Cookies ?? new List<CookieItem>();
                    Debug.Print($"[RefreshCookies] 已刷新Cookie数据，共 {_currentCookies.Count} 个");
                }
                else
                {
                    _currentCookies.Clear();
                    Debug.Print("[RefreshCookies] 获取Cookie数据失败，清空Cookie列表");
                }
            }
            catch (Exception ex)
            {
                Debug.Print($"[RefreshCookies] 刷新Cookie失败: {ex.Message}");
                _currentCookies.Clear();
            }
        }

        #endregion 标头操作方法

        #region 辅助方法

        /// <summary>
        /// 设置状态栏信息
        /// </summary>
        /// <param name="message">状态消息</param>
        /// <param name="isError">是否为错误信息</param>
        private void SetStatus(string message, bool isError = false)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => SetStatus(message, isError)));
                return;
            }

            _statusLabel.Text = message;
            _statusLabel.ForeColor = isError ? Color.Red : SystemColors.ControlText;
        }

        /// <summary>
        /// 导航到指定URL
        /// </summary>
        /// <param name="url">目标URL</param>
        public void Navigate(string url)
        {
            try
            {
                if (_webView?.CoreWebView2 != null)
                {
                    _webView.CoreWebView2.Navigate(url);
                    SetStatus($"正在导航到: {url}");
                }
                else
                {
                    InitialUrl = url;
                    SetStatus("WebView2未初始化，已设置初始URL");
                }
            }
            catch (Exception ex)
            {
                SetStatus($"导航失败: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 设置用户提示框的可见性
        /// </summary>
        /// <param name="visible">是否可见</param>
        private void SetUserTipVisibility(bool visible)
        {
            if (lblUserTip != null)
            {
                lblUserTip.Visible = visible;

                // 调整WebView的布局
                if (_webView != null)
                {
                    if (visible)
                    {
                        // 显示提示框时，WebView需要避开提示框区域
                        _webView.Dock = DockStyle.None;
                        _webView.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
                        _webView.Location = new System.Drawing.Point(0, lblUserTip.Height);
                        _webView.Size = new System.Drawing.Size(this.ClientSize.Width, this.ClientSize.Height - lblUserTip.Height - (_statusStrip?.Height ?? 0));
                    }
                    else
                    {
                        // 隐藏提示框时，WebView占满整个客户区域（除了状态栏）
                        _webView.Dock = DockStyle.Fill;
                    }
                }
            }
        }

        /// <summary>
        /// 设置用户提示内容并显示
        /// </summary>
        /// <param name="tipText">提示内容</param>
        public void SetUserTip(string tipText)
        {
            UserTipText = tipText;
            ShowUserTip = !string.IsNullOrEmpty(tipText);
        }

        /// <summary>
        /// 隐藏用户提示框
        /// </summary>
        public void HideUserTip()
        {
            ShowUserTip = false;
        }

        /// <summary>
        /// 设置地理信息权限策略
        /// </summary>
        /// <param name="allowGeolocationPrompt">是否允许弹出地理信息权限提示</param>
        public void SetGeolocationPermission(bool allowGeolocationPrompt)
        {
            AllowGeolocationPrompt = allowGeolocationPrompt;
            Debug.Print($"[权限设置] 地理信息权限已更新为: {AllowGeolocationPrompt}");
            SetStatus($"地理信息权限已设置为: {(AllowGeolocationPrompt ? "允许提示" : "拒绝访问")}");
        }

        #endregion 辅助方法
    }

    #region 数据模型类

    /// <summary>
    /// Cookie数据类，用于JSON序列化
    /// </summary>
    public class CookieData
    {
        /// <summary>
        /// 关联的URL
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// Cookie列表
        /// </summary>
        public List<CookieItem> Cookies { get; set; } = new List<CookieItem>();
    }

    /// <summary>
    /// Cookie项，表示单个Cookie
    /// </summary>
    public class CookieItem
    {
        /// <summary>
        /// Cookie名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Cookie值
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// 域名
        /// </summary>
        public string Domain { get; set; }

        /// <summary>
        /// 路径
        /// </summary>
        public string Path { get; set; } = "/";

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime Expires { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 是否仅HTTP
        /// </summary>
        public bool HttpOnly { get; set; }

        /// <summary>
        /// 是否安全
        /// </summary>
        public bool Secure { get; set; }

        /// <summary>
        /// 同站策略
        /// </summary>
        public string SameSite { get; set; } = "None";
    }

    #endregion 数据模型类
}