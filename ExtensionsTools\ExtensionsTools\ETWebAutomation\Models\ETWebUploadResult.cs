using System;
using System.Collections.Generic;

namespace ET.ETWebAutomation.Models
{
    /// <summary>
    /// 上传结果模型
    /// </summary>
    public class ETWebUploadResult
    {
        /// <summary>
        /// 上传结果ID
        /// </summary>
        public string ResultId { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 原始文件路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件扩展名
        /// </summary>
        public string FileExtension { get; set; }

        /// <summary>
        /// 文件MIME类型
        /// </summary>
        public string MimeType { get; set; }

        /// <summary>
        /// 上传是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 上传消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 服务器返回的文件ID
        /// </summary>
        public string FileId { get; set; }

        /// <summary>
        /// 服务器文件URL
        /// </summary>
        public string FileUrl { get; set; }

        /// <summary>
        /// 服务器文件路径
        /// </summary>
        public string ServerFilePath { get; set; }

        /// <summary>
        /// 文件MD5哈希值
        /// </summary>
        public string MD5Hash { get; set; }

        /// <summary>
        /// 文件SHA1哈希值
        /// </summary>
        public string SHA1Hash { get; set; }

        /// <summary>
        /// 上传开始时间
        /// </summary>
        public DateTime UploadStartTime { get; set; }

        /// <summary>
        /// 上传完成时间
        /// </summary>
        public DateTime UploadTime { get; set; }

        /// <summary>
        /// 上传耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds => (long)(UploadTime - UploadStartTime).TotalMilliseconds;

        /// <summary>
        /// 上传进度（0-100）
        /// </summary>
        public int Progress { get; set; }

        /// <summary>
        /// 上传速度（字节/秒）
        /// </summary>
        public long UploadSpeed { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// 上传端点URL
        /// </summary>
        public string UploadEndpoint { get; set; }

        /// <summary>
        /// 表单数据
        /// </summary>
        public Dictionary<string, string> FormData { get; set; }

        /// <summary>
        /// 服务器响应数据
        /// </summary>
        public object ResponseData { get; set; }

        /// <summary>
        /// 服务器响应JSON
        /// </summary>
        public string ResponseJson { get; set; }

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtendedProperties { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ETWebUploadResult()
        {
            ResultId = Guid.NewGuid().ToString();
            FormData = new Dictionary<string, string>();
            ExtendedProperties = new Dictionary<string, object>();
            UploadStartTime = DateTime.Now;
            UploadTime = DateTime.Now;
            Progress = 0;
            RetryCount = 0;
        }

        /// <summary>
        /// 构造函数（指定文件名）
        /// </summary>
        /// <param name="fileName">文件名</param>
        public ETWebUploadResult(string fileName) : this()
        {
            FileName = fileName;
            if (!string.IsNullOrEmpty(fileName))
            {
                FileExtension = System.IO.Path.GetExtension(fileName);
            }
        }

        /// <summary>
        /// 设置成功结果
        /// </summary>
        /// <param name="fileId">服务器文件ID</param>
        /// <param name="message">成功消息</param>
        public void SetSuccess(string fileId, string message = "上传成功")
        {
            IsSuccess = true;
            FileId = fileId;
            Message = message;
            Progress = 100;
            UploadTime = DateTime.Now;
        }

        /// <summary>
        /// 设置失败结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="exception">异常对象</param>
        public void SetFailure(string errorMessage, Exception exception = null)
        {
            IsSuccess = false;
            ErrorMessage = errorMessage;
            Exception = exception;
            Message = "上传失败";
            UploadTime = DateTime.Now;
        }

        /// <summary>
        /// 更新上传进度
        /// </summary>
        /// <param name="progress">进度值（0-100）</param>
        public void UpdateProgress(int progress)
        {
            Progress = Math.Max(0, Math.Min(100, progress));
            
            if (Progress == 100)
            {
                UploadTime = DateTime.Now;
            }
        }

        /// <summary>
        /// 计算上传速度
        /// </summary>
        public void CalculateUploadSpeed()
        {
            var elapsedSeconds = (DateTime.Now - UploadStartTime).TotalSeconds;
            if (elapsedSeconds > 0)
            {
                UploadSpeed = (long)(FileSize / elapsedSeconds);
            }
        }

        /// <summary>
        /// 设置服务器响应数据
        /// </summary>
        /// <param name="responseData">响应数据</param>
        public void SetResponseData(object responseData)
        {
            ResponseData = responseData;
            
            if (responseData != null)
            {
                try
                {
                    ResponseJson = Newtonsoft.Json.JsonConvert.SerializeObject(responseData);
                }
                catch
                {
                    ResponseJson = responseData.ToString();
                }
            }
        }

        /// <summary>
        /// 获取强类型的响应数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <returns>强类型数据</returns>
        public T GetResponseData<T>()
        {
            if (ResponseData is T directCast)
            {
                return directCast;
            }

            if (ResponseData != null)
            {
                try
                {
                    return (T)Convert.ChangeType(ResponseData, typeof(T));
                }
                catch
                {
                    // 转换失败，返回默认值
                }
            }

            return default(T);
        }

        /// <summary>
        /// 获取格式化的文件大小
        /// </summary>
        /// <returns>格式化的文件大小</returns>
        public string GetFormattedFileSize()
        {
            if (FileSize < 1024)
                return $"{FileSize} B";
            else if (FileSize < 1024 * 1024)
                return $"{FileSize / 1024.0:F2} KB";
            else if (FileSize < 1024 * 1024 * 1024)
                return $"{FileSize / (1024.0 * 1024.0):F2} MB";
            else
                return $"{FileSize / (1024.0 * 1024.0 * 1024.0):F2} GB";
        }

        /// <summary>
        /// 获取格式化的上传速度
        /// </summary>
        /// <returns>格式化的上传速度</returns>
        public string GetFormattedUploadSpeed()
        {
            if (UploadSpeed < 1024)
                return $"{UploadSpeed} B/s";
            else if (UploadSpeed < 1024 * 1024)
                return $"{UploadSpeed / 1024.0:F2} KB/s";
            else if (UploadSpeed < 1024 * 1024 * 1024)
                return $"{UploadSpeed / (1024.0 * 1024.0):F2} MB/s";
            else
                return $"{UploadSpeed / (1024.0 * 1024.0 * 1024.0):F2} GB/s";
        }

        /// <summary>
        /// 获取扩展属性值
        /// </summary>
        /// <typeparam name="T">属性值类型</typeparam>
        /// <param name="propertyName">属性名称</param>
        /// <returns>属性值</returns>
        public T GetExtendedProperty<T>(string propertyName)
        {
            if (ExtendedProperties.ContainsKey(propertyName))
            {
                return (T)ExtendedProperties[propertyName];
            }
            return default(T);
        }

        /// <summary>
        /// 设置扩展属性值
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <param name="propertyValue">属性值</param>
        public void SetExtendedProperty(string propertyName, object propertyValue)
        {
            if (ExtendedProperties.ContainsKey(propertyName))
            {
                ExtendedProperties[propertyName] = propertyValue;
            }
            else
            {
                ExtendedProperties.Add(propertyName, propertyValue);
            }
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"{FileName} - {(IsSuccess ? "成功" : "失败")} - {GetFormattedFileSize()} - 耗时: {ElapsedMilliseconds}ms";
        }
    }
}
