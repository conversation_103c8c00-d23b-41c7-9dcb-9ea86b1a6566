<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Security.AccessControl</name>
    </assembly>
    <members>
        <member name="P:System.SR.AccessControl_AclTooLong">
            <summary>Length of the access control list exceed the allowed maximum.</summary>
        </member>
        <member name="P:System.SR.AccessControl_InvalidAccessRuleType">
            <summary>The access rule is not the correct type.</summary>
        </member>
        <member name="P:System.SR.AccessControl_InvalidAuditRuleType">
            <summary>The audit rule is not the correct type.</summary>
        </member>
        <member name="P:System.SR.AccessControl_InvalidOwner">
            <summary>The security identifier is not allowed to be the owner of this object.</summary>
        </member>
        <member name="P:System.SR.AccessControl_InvalidGroup">
            <summary>The security identifier is not allowed to be the primary group of this object.</summary>
        </member>
        <member name="P:System.SR.AccessControl_InvalidHandle">
            <summary>The supplied handle is invalid. This can happen when trying to set an ACL on an anonymous kernel object.</summary>
        </member>
        <member name="P:System.SR.AccessControl_InvalidSecurityDescriptorRevision">
            <summary>Security descriptor with revision other than '1' are not legal.</summary>
        </member>
        <member name="P:System.SR.AccessControl_InvalidSecurityDescriptorSelfRelativeForm">
            <summary>Security descriptor must be in the self-relative form.</summary>
        </member>
        <member name="P:System.SR.AccessControl_InvalidSidInSDDLString">
            <summary>The SDDL string contains an invalid sid or a sid that cannot be translated.</summary>
        </member>
        <member name="P:System.SR.AccessControl_MustSpecifyContainerAcl">
            <summary>The named parameter must be a container ACL.</summary>
        </member>
        <member name="P:System.SR.AccessControl_MustSpecifyDirectoryObjectAcl">
            <summary>The named parameter must be a directory-object ACL.</summary>
        </member>
        <member name="P:System.SR.AccessControl_MustSpecifyLeafObjectAcl">
            <summary>The named parameter must be a non-container ACL.</summary>
        </member>
        <member name="P:System.SR.AccessControl_MustSpecifyNonDirectoryObjectAcl">
            <summary>The named parameter must be a non-directory-object ACL.</summary>
        </member>
        <member name="P:System.SR.AccessControl_NoAssociatedSecurity">
            <summary>Unable to perform a security operation on an object that has no associated security. This can happen when trying to get an ACL of an anonymous kernel object.</summary>
        </member>
        <member name="P:System.SR.AccessControl_UnexpectedError">
            <summary>Method failed with unexpected error code {0}.</summary>
        </member>
        <member name="P:System.SR.Arg_EnumAtLeastOneFlag">
            <summary>Must set at least one flag.</summary>
        </member>
        <member name="P:System.SR.Arg_EnumIllegalVal">
            <summary>Illegal enum value: {0}.</summary>
        </member>
        <member name="P:System.SR.Arg_InvalidOperationException">
            <summary>Operation is not valid due to the current state of the object.</summary>
        </member>
        <member name="P:System.SR.Arg_MustBeIdentityReferenceType">
            <summary>Type must be an IdentityReference, such as NTAccount or SecurityIdentifier.</summary>
        </member>
        <member name="P:System.SR.Argument_ArgumentZero">
            <summary>Argument cannot be zero.</summary>
        </member>
        <member name="P:System.SR.Argument_InvalidAnyFlag">
            <summary>No flags can be set.</summary>
        </member>
        <member name="P:System.SR.Argument_InvalidEnumValue">
            <summary>The value '{0}' is not valid for this usage of the type {1}.</summary>
        </member>
        <member name="P:System.SR.Argument_InvalidName">
            <summary>Invalid name.</summary>
        </member>
        <member name="P:System.SR.Argument_InvalidPrivilegeName">
            <summary>Privilege '{0}' is not valid on this system.</summary>
        </member>
        <member name="P:System.SR.Argument_InvalidSafeHandle">
            <summary>The SafeHandle is invalid.</summary>
        </member>
        <member name="P:System.SR.ArgumentException_InvalidAceBinaryForm">
            <summary>The binary form of an ACE object is invalid.</summary>
        </member>
        <member name="P:System.SR.ArgumentException_InvalidAclBinaryForm">
            <summary>The binary form of an ACL object is invalid.</summary>
        </member>
        <member name="P:System.SR.ArgumentException_InvalidSDSddlForm">
            <summary>The SDDL form of a security descriptor object is invalid.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_ArrayLength">
            <summary>The length of the array must be between {0} and {1}, inclusive.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_ArrayLengthMultiple">
            <summary>The length of the array must be a multiple of {0}.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_ArrayTooSmall">
            <summary>Destination array is not long enough to copy all the required data. Check array length and offset.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_Enum">
            <summary>Enum value was out of legal range.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_InvalidUserDefinedAceType">
            <summary>User-defined ACEs must not have a well-known ACE type.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_NeedNonNegNum">
            <summary>Non-negative number required.</summary>
        </member>
        <member name="P:System.SR.InvalidOperation_ModificationOfNonCanonicalAcl">
            <summary>This access control list is not in canonical form and therefore cannot be modified.</summary>
        </member>
        <member name="P:System.SR.InvalidOperation_MustBeSameThread">
            <summary>This operation must take place on the same thread on which the object was created.</summary>
        </member>
        <member name="P:System.SR.InvalidOperation_MustLockForReadOrWrite">
            <summary>Object must be locked for read or write.</summary>
        </member>
        <member name="P:System.SR.InvalidOperation_MustLockForWrite">
            <summary>Object must be locked for read.</summary>
        </member>
        <member name="P:System.SR.InvalidOperation_MustRevertPrivilege">
            <summary>Must revert the privilege prior to attempting this operation.</summary>
        </member>
        <member name="P:System.SR.InvalidOperation_NoSecurityDescriptor">
            <summary>The object does not contain a security descriptor.</summary>
        </member>
        <member name="P:System.SR.InvalidOperation_OnlyValidForDS">
            <summary>Adding ACEs with Object Flags and Object GUIDs is only valid for directory-object ACLs.</summary>
        </member>
        <member name="P:System.SR.InvalidOperation_DisconnectedPipe">
            <summary>The pipe has been disconnected.</summary>
        </member>
        <member name="P:System.SR.NotSupported_SetMethod">
            <summary>The 'set' method is not supported on this property.</summary>
        </member>
        <member name="P:System.SR.PrivilegeNotHeld_Default">
            <summary>The process does not possess some privilege required for this operation.</summary>
        </member>
        <member name="P:System.SR.PrivilegeNotHeld_Named">
            <summary>The process does not possess the '{0}' privilege which is required for this operation.</summary>
        </member>
        <member name="P:System.SR.Rank_MultiDimNotSupported">
            <summary>Only single dimension arrays are supported here.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_AccessControl">
            <summary>Access Control List (ACL) APIs are part of resource management on Windows and are not supported on this platform.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Runtime.Versioning.OSPlatformAttribute">
            <summary>
            Base type for all platform-specific API attributes.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.TargetPlatformAttribute">
            <summary>
            Records the platform that the project targeted.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformAttribute">
             <summary>
             Records the operating system (and minimum version) that supports an API. Multiple attributes can be
             applied to indicate support on multiple operating systems.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformAttribute" />
             or use guards to prevent calls to APIs on unsupported operating systems.
            
             A given platform should only be specified once.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformAttribute">
            <summary>
            Marks APIs that were removed in a given operating system version.
            </summary>
            <remarks>
            Primarily used by OS bindings to indicate APIs that are only available in
            earlier versions.
            </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute">
             <summary>
             Annotates a custom guard field, property or method with a supported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple supported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that field, property or method in a conditional or assert statements in order to safely call platform specific APIs.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute">
             <summary>
             Annotates the custom guard field, property or method with an unsupported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple unsupported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that  field, property or method in a conditional or assert statements as a guard to safely call APIs unsupported on those platforms.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
    </members>
</doc>
