using ET;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant.WebBrowserV2.Managers
{
    /// <summary>
    /// WebBrowser会话管理器V2版本，整合会话维持和定时刷新功能
    /// 优化重点：异步编程安全、资源管理、日志统一
    /// </summary>
    public class WebBrowserSessionManagerV2 : IDisposable
    {
        #region 字段和属性

        /// <summary>
        /// 标签页刷新定时器字典，使用SectionId作为键
        /// </summary>
        private readonly Dictionary<string, System.Windows.Forms.Timer> _refreshTimers = new Dictionary<string, System.Windows.Forms.Timer>();

        /// <summary>
        /// 标签页会话维持器字典，使用SectionId作为键
        /// </summary>
        private readonly Dictionary<string, SessionKeeperV2> _sessionKeepers = new Dictionary<string, SessionKeeperV2>();

        /// <summary>
        /// 标签页上次活动时间字典，使用SectionId作为键
        /// </summary>
        private readonly Dictionary<string, DateTime> _lastActivityTime = new Dictionary<string, DateTime>();

        /// <summary>
        /// 用于记录的对象
        /// </summary>
        private readonly object _logSource;

        /// <summary>
        /// 同步锁对象
        /// </summary>
        private readonly object _syncLock = new object();

        /// <summary>
        /// 标记类是否已释放
        /// </summary>
        private bool _disposed = false;

        /// <summary>
        /// 用于取消异步操作的CancellationTokenSource
        /// </summary>
        private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();

        /// <summary>
        /// 会话日志更新事件委托
        /// </summary>
        public delegate void SessionLogUpdateEventHandler(string message);

        /// <summary>
        /// 会话日志更新事件
        /// </summary>
        public event SessionLogUpdateEventHandler SessionLogUpdated;

        /// <summary>
        /// 获取取消令牌
        /// </summary>
        public CancellationToken CancellationToken => _cancellationTokenSource.Token;

        #endregion 字段和属性

        #region 内部类

        /// <summary>
        /// 会话维持器V2版本（内部类）
        /// </summary>
        private class SessionKeeperV2 : IDisposable
        {
            public WebBrowserTabConfig TabConfig { get; }
            public HttpClient HttpClient { get; private set; }
            public CookieContainer CookieContainer { get; private set; }
            public Dictionary<string, string> RequestHeaders { get; } = new Dictionary<string, string>();
            public System.Threading.Timer RefreshTimer { get; private set; }
            public DateTime LastTimerTick { get; set; } = DateTime.MinValue;
            public bool IsDisposed { get; private set; } = false;

            private readonly object _logSource;
            private readonly string _refererUrl;

            public SessionKeeperV2(WebBrowserTabConfig config, string refererUrl, object logSource)
            {
                TabConfig = config ?? throw new ArgumentNullException(nameof(config));
                _refererUrl = refererUrl;
                _logSource = logSource;

                InitializeHttpClient();
            }

            private void InitializeHttpClient()
            {
                try
                {
                    CookieContainer = new CookieContainer();
                    
                    var handler = new HttpClientHandler()
                    {
                        CookieContainer = CookieContainer,
                        UseCookies = true
                    };

                    HttpClient = new HttpClient(handler);
                    
                    // 设置默认请求头
                    HttpClient.DefaultRequestHeaders.Add("User-Agent", TabConfig.UserAgent ?? "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
                    
                    if (!string.IsNullOrEmpty(_refererUrl))
                    {
                        HttpClient.DefaultRequestHeaders.Add("Referer", _refererUrl);
                    }

                    ETLogManager.Info(_logSource, $"HttpClient初始化完成: {TabConfig.Name}");
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, $"HttpClient初始化失败: {ex.Message}");
                }
            }

            public async Task<bool> PerformKeepAliveAsync(CancellationToken cancellationToken = default)
            {
                if (IsDisposed || cancellationToken.IsCancellationRequested)
                    return false;

                try
                {
                    if (string.IsNullOrEmpty(TabConfig.Url))
                        return false;

                    using (var response = await HttpClient.GetAsync(TabConfig.Url, cancellationToken).ConfigureAwait(false))
                    {
                        bool success = response.IsSuccessStatusCode;
                        
                        if (success)
                        {
                            LastTimerTick = DateTime.Now;
                            ETLogManager.Debug(_logSource, $"会话保持成功: {TabConfig.Name}");
                        }
                        else
                        {
                            ETLogManager.Warning(_logSource, $"会话保持失败: {TabConfig.Name}, 状态码: {response.StatusCode}");
                        }

                        return success;
                    }
                }
                catch (OperationCanceledException)
                {
                    ETLogManager.Info(_logSource, $"会话保持被取消: {TabConfig.Name}");
                    return false;
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, $"会话保持异常: {TabConfig.Name}, 错误: {ex.Message}");
                    return false;
                }
            }

            public void Dispose()
            {
                if (!IsDisposed)
                {
                    RefreshTimer?.Dispose();
                    HttpClient?.Dispose();
                    IsDisposed = true;
                    
                    ETLogManager.Debug(_logSource, $"SessionKeeperV2资源释放完成: {TabConfig.Name}");
                }
            }
        }

        #endregion 内部类

        #region 构造方法

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logSource">用于记录日志的对象</param>
        public WebBrowserSessionManagerV2(object logSource)
        {
            _logSource = logSource ?? this;
            ETLogManager.Info(_logSource, "WebBrowserSessionManagerV2初始化完成");
        }

        #endregion 构造方法

        #region 会话管理方法

        /// <summary>
        /// 启动会话保持（异步安全版本）
        /// </summary>
        /// <param name="sectionId">标签页的SectionId</param>
        /// <param name="config">标签页配置</param>
        /// <returns>启动任务</returns>
        public async Task<bool> StartSessionAsync(string sectionId, WebBrowserTabConfig config)
        {
            if (string.IsNullOrEmpty(sectionId) || config == null || _disposed)
            {
                ETLogManager.Warning(_logSource, "启动会话失败：参数无效或已释放");
                return false;
            }

            try
            {
                ETLogManager.Info(_logSource, $"启动会话保持：{config.Name}, SectionId: {sectionId}");

                lock (_syncLock)
                {
                    // 如果已存在，先停止旧的会话
                    if (_sessionKeepers.ContainsKey(sectionId))
                    {
                        StopSessionInternal(sectionId);
                    }

                    // 更新活动时间
                    _lastActivityTime[sectionId] = DateTime.Now;
                }

                // 创建会话维持器
                var sessionKeeper = new SessionKeeperV2(config, config.Url, _logSource);

                // 设置定时刷新
                if (config.HttpClientRefreshInterval > 0)
                {
                    await SetupRefreshTimerAsync(sectionId, config, sessionKeeper).ConfigureAwait(false);
                }

                lock (_syncLock)
                {
                    _sessionKeepers[sectionId] = sessionKeeper;
                }

                ETLogManager.Info(_logSource, $"会话保持启动成功：{config.Name}");
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"启动会话保持异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 停止会话保持（异步安全版本）
        /// </summary>
        /// <param name="sectionId">标签页的SectionId</param>
        /// <returns>停止任务</returns>
        public async Task<bool> StopSessionAsync(string sectionId)
        {
            if (string.IsNullOrEmpty(sectionId))
            {
                ETLogManager.Warning(_logSource, "停止会话失败：SectionId为空");
                return false;
            }

            try
            {
                await Task.Run(() =>
                {
                    lock (_syncLock)
                    {
                        StopSessionInternal(sectionId);
                    }
                }).ConfigureAwait(false);

                ETLogManager.Info(_logSource, $"会话保持停止成功：{sectionId}");
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"停止会话保持异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 内部停止会话方法（必须在锁内调用）
        /// </summary>
        /// <param name="sectionId">标签页的SectionId</param>
        private void StopSessionInternal(string sectionId)
        {
            try
            {
                // 停止并清理刷新定时器
                if (_refreshTimers.TryGetValue(sectionId, out System.Windows.Forms.Timer refreshTimer))
                {
                    refreshTimer.Stop();
                    refreshTimer.Dispose();
                    _refreshTimers.Remove(sectionId);
                }

                // 停止并清理会话维持器
                if (_sessionKeepers.TryGetValue(sectionId, out SessionKeeperV2 sessionKeeper))
                {
                    sessionKeeper.Dispose();
                    _sessionKeepers.Remove(sectionId);
                }

                // 清理活动时间记录
                _lastActivityTime.Remove(sectionId);

                ETLogManager.Debug(_logSource, $"会话内部停止完成：{sectionId}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"内部停止会话异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置刷新定时器（异步安全版本）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <param name="config">标签页配置</param>
        /// <param name="sessionKeeper">会话维持器</param>
        /// <returns>设置任务</returns>
        private async Task SetupRefreshTimerAsync(string sectionId, WebBrowserTabConfig config, SessionKeeperV2 sessionKeeper)
        {
            try
            {
                await Task.Run(() =>
                {
                    var refreshTimer = new System.Windows.Forms.Timer
                    {
                        Interval = config.HttpClientRefreshInterval * 1000 // 转换为毫秒
                    };

                    refreshTimer.Tick += (sender, e) =>
                    {
                        try
                        {
                            if (_disposed || _cancellationTokenSource.Token.IsCancellationRequested)
                            {
                                refreshTimer.Stop();
                                return;
                            }

                            // 异步执行会话保持，避免阻塞UI线程
                            _ = Task.Run(async () =>
                            {
                                try
                                {
                                    bool success = await sessionKeeper.PerformKeepAliveAsync(_cancellationTokenSource.Token).ConfigureAwait(false);

                                    string message = success
                                        ? $"[{config.Name}] 会话保持成功 - {DateTime.Now:HH:mm:ss}"
                                        : $"[{config.Name}] 会话保持失败 - {DateTime.Now:HH:mm:ss}";

                                    SessionLogUpdated?.Invoke(message);

                                    // 更新活动时间
                                    lock (_syncLock)
                                    {
                                        _lastActivityTime[sectionId] = DateTime.Now;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    ETLogManager.Error(_logSource, $"定时会话保持异常: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error(_logSource, $"刷新定时器事件异常: {ex.Message}");
                        }
                    };

                    lock (_syncLock)
                    {
                        _refreshTimers[sectionId] = refreshTimer;
                    }

                    refreshTimer.Start();

                    ETLogManager.Info(_logSource, $"刷新定时器设置完成：{config.Name}, 间隔: {config.HttpClientRefreshInterval}秒");
                }).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"设置刷新定时器异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新标签页活动时间
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        public void UpdateActivityTime(string sectionId)
        {
            if (string.IsNullOrEmpty(sectionId) || _disposed)
                return;

            try
            {
                lock (_syncLock)
                {
                    _lastActivityTime[sectionId] = DateTime.Now;
                }

                ETLogManager.Debug(_logSource, $"活动时间更新：{sectionId}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"更新活动时间异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取标签页最后活动时间
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>最后活动时间，如果不存在则返回DateTime.MinValue</returns>
        public DateTime GetLastActivityTime(string sectionId)
        {
            if (string.IsNullOrEmpty(sectionId) || _disposed)
                return DateTime.MinValue;

            lock (_syncLock)
            {
                return _lastActivityTime.TryGetValue(sectionId, out DateTime lastTime) ? lastTime : DateTime.MinValue;
            }
        }

        /// <summary>
        /// 检查会话是否活跃
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>是否活跃</returns>
        public bool IsSessionActive(string sectionId)
        {
            if (string.IsNullOrEmpty(sectionId) || _disposed)
                return false;

            lock (_syncLock)
            {
                return _sessionKeepers.ContainsKey(sectionId) && _refreshTimers.ContainsKey(sectionId);
            }
        }

        /// <summary>
        /// 获取活跃会话数量
        /// </summary>
        /// <returns>活跃会话数量</returns>
        public int GetActiveSessionCount()
        {
            if (_disposed)
                return 0;

            lock (_syncLock)
            {
                return _sessionKeepers.Count;
            }
        }

        /// <summary>
        /// 手动执行会话保持（异步安全版本）
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>执行结果</returns>
        public async Task<bool> ManualKeepAliveAsync(string sectionId)
        {
            if (string.IsNullOrEmpty(sectionId) || _disposed)
            {
                ETLogManager.Warning(_logSource, "手动会话保持失败：参数无效或已释放");
                return false;
            }

            try
            {
                SessionKeeperV2 sessionKeeper;
                lock (_syncLock)
                {
                    if (!_sessionKeepers.TryGetValue(sectionId, out sessionKeeper))
                    {
                        ETLogManager.Warning(_logSource, $"手动会话保持失败：找不到会话维持器: {sectionId}");
                        return false;
                    }
                }

                bool success = await sessionKeeper.PerformKeepAliveAsync(_cancellationTokenSource.Token).ConfigureAwait(false);

                if (success)
                {
                    UpdateActivityTime(sectionId);
                    ETLogManager.Info(_logSource, $"手动会话保持成功：{sessionKeeper.TabConfig.Name}");
                }
                else
                {
                    ETLogManager.Warning(_logSource, $"手动会话保持失败：{sessionKeeper.TabConfig.Name}");
                }

                return success;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"手动会话保持异常: {ex.Message}");
                return false;
            }
        }

        #endregion 会话管理方法

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        // 取消所有异步操作
                        _cancellationTokenSource?.Cancel();

                        lock (_syncLock)
                        {
                            // 停止所有会话
                            var allSectionIds = new List<string>(_sessionKeepers.Keys);
                            foreach (string sectionId in allSectionIds)
                            {
                                StopSessionInternal(sectionId);
                            }

                            _refreshTimers.Clear();
                            _sessionKeepers.Clear();
                            _lastActivityTime.Clear();
                        }

                        _cancellationTokenSource?.Dispose();

                        ETLogManager.Info(_logSource, "WebBrowserSessionManagerV2资源释放完成");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"释放WebBrowserSessionManagerV2资源失败: {ex.Message}");
                    }
                }

                _disposed = true;
            }
        }

        #endregion IDisposable实现

        #region 兼容性方法

        /// <summary>
        /// 获取会话维持器
        /// </summary>
        /// <param name="sectionId">标签页SectionId</param>
        /// <returns>会话维持器，未找到返回null</returns>
        public object GetSessionKeeper(string sectionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sectionId))
                {
                    ETLogManager.Warning(_logSource, "获取会话维持器失败: SectionId为空");
                    return null;
                }

                if (_disposed)
                {
                    ETLogManager.Warning(_logSource, "WebBrowserSessionManagerV2已释放，无法获取会话维持器");
                    return null;
                }

                lock (_syncLock)
                {
                    if (_sessionKeepers.TryGetValue(sectionId, out SessionKeeperV2 keeper))
                    {
                        ETLogManager.Debug(_logSource, $"获取会话维持器成功: {sectionId}");
                        return keeper;
                    }
                }

                ETLogManager.Debug(_logSource, $"未找到会话维持器: {sectionId}");
                return null;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"获取会话维持器失败: {ex.Message}");
                return null;
            }
        }

        #endregion 兼容性方法
    }
}
