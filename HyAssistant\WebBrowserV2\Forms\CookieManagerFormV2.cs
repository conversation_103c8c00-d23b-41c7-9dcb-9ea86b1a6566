using ET;
using ET.ETLoginWebBrowser;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using HyAssistant.WebBrowserV2.Utils;
using HyAssistant.WebBrowserV2.Managers;

namespace HyAssistant.WebBrowserV2.Forms
{
    /// <summary>
    /// 标头管理窗体V2版本，用于显示当前标签页的HTTP请求标头（兼容Cookie显示） 优化重点：异步编程安全、Cookie数据传递优化、日志统一
    /// </summary>
    public partial class CookieManagerFormV2 : Form
    {
        #region 字段和属性

        /// <summary>
        /// 标头管理器V2（兼容Cookie管理）
        /// </summary>
        private readonly WebBrowserCookieManagerV2 _cookieManager;

        /// <summary>
        /// WebView2控件
        /// </summary>
        private readonly WebView2 _webView;

        /// <summary>
        /// 用于记录的对象
        /// </summary>
        private readonly object _logSource;

        /// <summary>
        /// 异步操作取消令牌源
        /// </summary>
        private readonly CancellationTokenSource _cancellationTokenSource;

        /// <summary>
        /// 标签页配置
        /// </summary>
        private WebBrowserTabConfig _tabConfig;

        /// <summary>
        /// 当前Cookie数据
        /// </summary>
        private CookieData _currentCookieData;

        /// <summary>
        /// 是否已释放
        /// </summary>
        private bool _disposed = false;

        /// <summary>
        /// Cookie传递管理器（V2版本关键优化）
        /// </summary>
        private readonly CookieTransferManagerV2 _cookieTransferManager;

        #endregion 字段和属性

        #region 构造方法

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="cookieManager">标头管理器V2（兼容Cookie管理器）</param>
        /// <param name="webView">WebView2控件</param>
        /// <param name="logSource">用于记录日志的对象</param>
        public CookieManagerFormV2(WebBrowserCookieManagerV2 cookieManager, WebView2 webView, object logSource = null)
        {
            InitializeComponent();

            _cookieManager = cookieManager ?? throw new ArgumentNullException(nameof(cookieManager), "Cookie管理器不能为空");
            _webView = webView ?? throw new ArgumentNullException(nameof(webView), "WebView2控件不能为空");
            _logSource = logSource ?? this;
            _cancellationTokenSource = new CancellationTokenSource();

            // V2版本关键优化：初始化Cookie传递管理器
            _cookieTransferManager = new CookieTransferManagerV2(_logSource);

            // 设置窗体图标
            try
            {
                Icon = System.Drawing.Icon.ExtractAssociatedIcon(Application.ExecutablePath);
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(_logSource, $"设置窗体图标失败: {ex.Message}");
            }

            // 注册事件处理程序
            Load += CookieManagerFormV2_Load;

            // 注册WebView2的源变更事件
            if (_webView.CoreWebView2 != null)
            {
                _webView.CoreWebView2.SourceChanged += WebView_SourceChanged;
            }

            ETLogManager.Info(_logSource, "CookieManagerFormV2初始化完成");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected override void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    // 取消所有异步操作
                    _cancellationTokenSource?.Cancel();

                    // 取消注册事件
                    if (_webView?.CoreWebView2 != null)
                    {
                        _webView.CoreWebView2.SourceChanged -= WebView_SourceChanged;
                    }

                    _cancellationTokenSource?.Dispose();

                    // V2版本关键优化：释放Cookie传递管理器
                    _cookieTransferManager?.Dispose();

                    if (components != null)
                    {
                        components.Dispose();
                    }

                    ETLogManager.Info(_logSource, "CookieManagerFormV2资源释放完成，包含Cookie传递管理器");
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(_logSource, $"释放CookieManagerFormV2资源失败: {ex.Message}");
                }

                _disposed = true;
            }

            base.Dispose(disposing);
        }

        #endregion 构造方法

        #region 事件处理

        /// <summary>
        /// 窗体加载事件（异步安全版本）
        /// </summary>
        private async void CookieManagerFormV2_Load(object sender, EventArgs e)
        {
            try
            {
                if (_disposed || _cancellationTokenSource.Token.IsCancellationRequested)
                    return;

                ETLogManager.Info(_logSource, "开始加载Cookie管理窗体");

                // 异步加载Cookie数据
                await LoadCookieDataAsync().ConfigureAwait(false);

                // 异步刷新显示
                await RefreshDisplayAsync().ConfigureAwait(false);

                ETLogManager.Info(_logSource, "Cookie管理窗体加载完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"Cookie管理窗体加载失败: {ex.Message}");
                MessageBox.Show($"加载Cookie数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// WebView源变更事件处理（异步安全版本）
        /// </summary>
        private void WebView_SourceChanged(object sender, CoreWebView2SourceChangedEventArgs e)
        {
            try
            {
                if (_disposed || _cancellationTokenSource.Token.IsCancellationRequested)
                    return;

                ETLogManager.Debug(_logSource, $"WebView源变更: {_webView.Source}");

                // 异步刷新Cookie数据
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await Task.Delay(1000, _cancellationTokenSource.Token).ConfigureAwait(false); // 等待页面稳定
                        await LoadCookieDataAsync().ConfigureAwait(false);
                        await RefreshDisplayAsync().ConfigureAwait(false);
                    }
                    catch (OperationCanceledException)
                    {
                        ETLogManager.Info(_logSource, "WebView源变更处理被取消");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"WebView源变更处理失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"WebView源变更事件处理异常: {ex.Message}");
            }
        }

        #endregion 事件处理

        #region 数据操作方法

        /// <summary>
        /// 异步加载Cookie数据（V2版本优化：使用CookieTransferManagerV2）
        /// </summary>
        /// <returns>加载任务</returns>
        private async Task LoadCookieDataAsync()
        {
            try
            {
                if (_disposed || _cancellationTokenSource.Token.IsCancellationRequested)
                    return;

                ETLogManager.Debug(_logSource, "V2优化：开始使用CookieTransferManagerV2加载Cookie数据");

                // 创建临时CookieData对象用于接收数据
                var tempCookieData = new CookieData();

                // 使用CookieTransferManagerV2内部方法获取Cookie数据
                var cookieData = await _cookieTransferManager.GetCookieDataAsync(_webView, _tabConfig).ConfigureAwait(false);

                if (cookieData != null)
                {
                    _currentCookieData = cookieData;
                    ETLogManager.Info(_logSource, $"V2优化：成功加载{_currentCookieData.Cookies.Count}个Cookie，URL: {_currentCookieData.Url}");
                }
                else
                {
                    // 回退到原始方法
                    ETLogManager.Warning(_logSource, "V2优化：CookieTransferManagerV2获取失败，回退到原始方法");
                    _currentCookieData = await _cookieManager.GetCookiesFromWebView2Async(_webView).ConfigureAwait(false);

                    if (_currentCookieData?.Cookies != null)
                    {
                        ETLogManager.Info(_logSource, $"回退方法成功加载{_currentCookieData.Cookies.Count}个Cookie");
                    }
                    else
                    {
                        ETLogManager.Warning(_logSource, "未获取到Cookie数据");
                        _currentCookieData = new CookieData { Cookies = new List<CookieItem>() };
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"V2优化：加载Cookie数据失败: {ex.Message}");
                _currentCookieData = new CookieData { Cookies = new List<CookieItem>() };
            }
        }

        /// <summary>
        /// 异步刷新显示
        /// </summary>
        /// <returns>刷新任务</returns>
        private async Task RefreshDisplayAsync()
        {
            try
            {
                if (_disposed || _cancellationTokenSource.Token.IsCancellationRequested)
                    return;

                // 在UI线程上更新显示
                if (InvokeRequired)
                {
                    await Task.Run(() =>
                    {
                        Invoke(new Action(() => UpdateDisplayInternal()));
                    }).ConfigureAwait(false);
                }
                else
                {
                    UpdateDisplayInternal();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"刷新显示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 内部更新显示方法（必须在UI线程上调用）
        /// </summary>
        private void UpdateDisplayInternal()
        {
            try
            {
                // 更新窗体标题
                string url = _webView.Source?.ToString() ?? "未知";
                Text = $"Cookie管理器 - {url}";

                // 这里可以添加更多UI更新逻辑 例如更新ListView、DataGridView等控件

                ETLogManager.Debug(_logSource, "显示更新完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"内部更新显示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安全地设置Cookie数据（V2版本的重点优化功能） 确保使用当前WebView URL，这是V2版本的关键优化
        /// </summary>
        /// <param name="cookieData">Cookie数据</param>
        /// <param name="config">标签页配置</param>
        public void SetCookieDataSafely(CookieData cookieData, WebBrowserTabConfig config)
        {
            try
            {
                if (_disposed)
                {
                    ETLogManager.Warning(_logSource, "窗体已释放，无法设置Cookie数据");
                    return;
                }

                _currentCookieData = cookieData;
                _tabConfig = config;

                // V2版本关键优化：验证URL是否为当前WebView URL
                if (cookieData != null && !string.IsNullOrEmpty(cookieData.Url))
                {
                    string currentWebViewUrl = _webView?.Source?.ToString();
                    if (!string.IsNullOrEmpty(currentWebViewUrl) && currentWebViewUrl != "about:blank")
                    {
                        if (cookieData.Url != currentWebViewUrl)
                        {
                            ETLogManager.Info(_logSource, $"V2优化：Cookie URL已更新为当前WebView URL: {currentWebViewUrl}");
                            cookieData.Url = currentWebViewUrl;
                        }
                    }
                }

                ETLogManager.Info(_logSource, $"V2优化：安全设置Cookie数据完成，共{cookieData?.Cookies?.Count ?? 0}个Cookie，URL: {cookieData?.Url}");

                // 异步刷新显示
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await RefreshDisplayAsync().ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(_logSource, $"设置Cookie数据后刷新显示失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"V2优化：安全设置Cookie数据失败: {ex.Message}");
            }
        }

        #endregion 数据操作方法

        #region 按钮事件处理

        /// <summary>
        /// 获取Cookie按钮点击事件
        /// </summary>
        private async void BtnGetCookies_Click(object sender, EventArgs e)
        {
            try
            {
                if (_disposed || _cancellationTokenSource.Token.IsCancellationRequested)
                    return;

                btnGetCookies.Enabled = false;
                lblStatus.Text = "正在获取Cookie...";

                await LoadCookieDataAsync().ConfigureAwait(false);
                await RefreshDisplayAsync().ConfigureAwait(false);

                // 在UI线程上更新状态
                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        lblStatus.Text = $"获取完成，共{_currentCookieData?.Cookies?.Count ?? 0}个Cookie";
                        btnGetCookies.Enabled = true;
                    }));
                }
                else
                {
                    lblStatus.Text = $"获取完成，共{_currentCookieData?.Cookies?.Count ?? 0}个Cookie";
                    btnGetCookies.Enabled = true;
                }

                ETLogManager.Info(_logSource, "手动获取Cookie完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"获取Cookie按钮事件失败: {ex.Message}");

                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        lblStatus.Text = "获取Cookie失败";
                        btnGetCookies.Enabled = true;
                    }));
                }
                else
                {
                    lblStatus.Text = "获取Cookie失败";
                    btnGetCookies.Enabled = true;
                }
            }
        }

        /// <summary>
        /// 更新网址按钮点击事件
        /// </summary>
        private async void btnUpdateCookies_Click(object sender, EventArgs e)
        {
            try
            {
                if (_disposed || _cancellationTokenSource.Token.IsCancellationRequested)
                    return;

                string newUrl = txtUrl.Text?.Trim();
                if (string.IsNullOrEmpty(newUrl))
                {
                    MessageBox.Show("请输入有效的URL", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                btnUpdateCookies.Enabled = false;
                lblStatus.Text = "正在更新网址...";

                // 导航到新URL
                if (_webView.InvokeRequired)
                {
                    await Task.Run(() =>
                    {
                        _webView.Invoke(new Action(() =>
                        {
                            _webView.CoreWebView2?.Navigate(newUrl);
                        }));
                    }).ConfigureAwait(false);
                }
                else
                {
                    _webView.CoreWebView2?.Navigate(newUrl);
                }

                // 等待导航完成
                await Task.Delay(2000, _cancellationTokenSource.Token).ConfigureAwait(false);

                // 重新获取Cookie
                await LoadCookieDataAsync().ConfigureAwait(false);
                await RefreshDisplayAsync().ConfigureAwait(false);

                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        lblStatus.Text = "网址更新完成";
                        btnUpdateCookies.Enabled = true;
                    }));
                }
                else
                {
                    lblStatus.Text = "网址更新完成";
                    btnUpdateCookies.Enabled = true;
                }

                ETLogManager.Info(_logSource, $"网址更新完成: {newUrl}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"更新网址按钮事件失败: {ex.Message}");

                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        lblStatus.Text = "网址更新失败";
                        btnUpdateCookies.Enabled = true;
                    }));
                }
                else
                {
                    lblStatus.Text = "网址更新失败";
                    btnUpdateCookies.Enabled = true;
                }
            }
        }

        /// <summary>
        /// 复制按钮点击事件
        /// </summary>
        private void btnCopy_Click(object sender, EventArgs e)
        {
            try
            {
                string url = txtUrl.Text?.Trim();
                if (!string.IsNullOrEmpty(url))
                {
                    Clipboard.SetText(url);
                    lblStatus.Text = "URL已复制到剪贴板";
                    ETLogManager.Info(_logSource, "URL复制到剪贴板");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"复制URL失败: {ex.Message}");
                lblStatus.Text = "复制失败";
            }
        }

        /// <summary>
        /// 粘贴并更新网址按钮点击事件
        /// </summary>
        private void btnPasteAndUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                if (Clipboard.ContainsText())
                {
                    string clipboardText = Clipboard.GetText()?.Trim();
                    if (!string.IsNullOrEmpty(clipboardText))
                    {
                        txtUrl.Text = clipboardText;
                        btnUpdateCookies_Click(sender, e); // 触发更新网址
                        ETLogManager.Info(_logSource, "从剪贴板粘贴并更新网址");
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"粘贴并更新网址失败: {ex.Message}");
                lblStatus.Text = "粘贴失败";
            }
        }

        /// <summary>
        /// 复制配置文件到剪贴板按钮点击事件
        /// </summary>
        private async void btnCopyConfig_Click(object sender, EventArgs e)
        {
            try
            {
                if (_disposed || _cancellationTokenSource.Token.IsCancellationRequested)
                    return;

                btnCopyConfig.Enabled = false;
                lblStatus.Text = "正在生成配置...";

                // 确保有最新的Cookie数据
                if (_currentCookieData?.Cookies == null || _currentCookieData.Cookies.Count == 0)
                {
                    await LoadCookieDataAsync().ConfigureAwait(false);
                }

                // 生成配置JSON
                var configData = new
                {
                    url = _webView.Source?.ToString() ?? txtUrl.Text,
                    headers = _cookieManager.GetLastRequestHeaders(),
                    cookies = _currentCookieData?.Cookies ?? new List<CookieItem>(),
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    version = "2.0"
                };

                string configJson = JsonConvert.SerializeObject(configData, Formatting.Indented);

                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        Clipboard.SetText(configJson);
                        lblStatus.Text = "配置已复制到剪贴板";
                        btnCopyConfig.Enabled = true;
                    }));
                }
                else
                {
                    Clipboard.SetText(configJson);
                    lblStatus.Text = "配置已复制到剪贴板";
                    btnCopyConfig.Enabled = true;
                }

                ETLogManager.Info(_logSource, "配置文件复制到剪贴板完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(_logSource, $"复制配置文件失败: {ex.Message}");

                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        lblStatus.Text = "复制配置失败";
                        btnCopyConfig.Enabled = true;
                    }));
                }
                else
                {
                    lblStatus.Text = "复制配置失败";
                    btnCopyConfig.Enabled = true;
                }
            }
        }

        #endregion 按钮事件处理
    }
}